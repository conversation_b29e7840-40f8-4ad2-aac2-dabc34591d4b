# HMThinkBaseHar 文件分类和优先级方案

## 🔥 第1层：核心架构层（详细解释）

### ⭐⭐⭐⭐⭐ 核心必学文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/ets/base/engine/TKAppEngine.ets` | 框架引擎核心，单例模式，管理整个框架生命周期 | 高级 | P0 |
| `src/main/ets/base/engine/module/TKModuleEngine.ets` | 模块通信引擎，发布订阅模式，实现模块解耦 | 高级 | P0 |
| `src/main/ets/base/mvc/ability/TKUIAbility.ets` | 应用能力基类，模板方法模式，生命周期管理 | 中级 | P0 |

### ⭐⭐⭐⭐ 重要推荐文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/ets/base/engine/module/TKModuleDelegate.ets` | 模块代理接口定义，定义模块通信协议 | 中级 | P1 |
| `src/main/ets/base/engine/module/TKModuleMessage.ets` | 模块消息对象，消息传递的数据结构 | 中级 | P1 |
| `src/main/ets/base/router/TKRouterPageManager.ets` | 路由页面管理器，统一路由管理 | 中级 | P1 |
| `src/main/ets/base/router/TKRouterHelper.ets` | 路由辅助类，提供路由跳转功能 | 中级 | P1 |

## ⚡ 第2层：基础服务层（重点解释）

### ⭐⭐⭐⭐ 重要推荐文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/ets/base/mvc/service/dao/TKBaseDao.ets` | 数据访问对象基类，DAO模式实现 | 中级 | P1 |
| `src/main/ets/base/mvc/service/dao/http/TKBaseHttpDao.ets` | HTTP数据访问基类，网络请求封装 | 中级 | P1 |
| `src/main/ets/base/mvc/service/dao/http/TKHttpDao.ets` | HTTP数据访问实现类，具体网络操作 | 中级 | P1 |
| `src/main/ets/base/mvc/service/dao/TKDaoFactory.ets` | DAO工厂类，工厂模式创建DAO对象 | 中级 | P1 |
| `src/main/ets/base/theme/TKThemeManager.ets` | 主题管理器，换肤功能核心 | 中级 | P1 |
| `src/main/ets/base/version/TKVersionManager.ets` | 版本管理器，热更新功能核心 | 中级 | P1 |

### ⭐⭐⭐ 一般了解文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/ets/base/network/download/TKDownLoadManager.ets` | 下载管理器，文件下载功能 | 中级 | P2 |
| `src/main/ets/base/network/upload/TKUploadManager.ets` | 上传管理器，文件上传功能 | 中级 | P2 |
| `src/main/ets/base/notification/TKNotification.ets` | 通知管理，系统通知功能 | 初级 | P2 |
| `src/main/ets/base/notification/TKNotificationCenter.ets` | 通知中心，通知分发管理 | 中级 | P2 |
| `src/main/ets/base/plugin/TKPluginInvokeCenter.ets` | 插件调用中心，插件管理核心 | 中级 | P2 |

## 🎨 第3层：UI组件层（功能解释）

### ⭐⭐⭐ 一般了解文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/ets/components/common/TKPageContainer.ets` | 页面容器组件，页面布局基础 | 初级 | P2 |
| `src/main/ets/components/webview/TKWeb.ets` | WebView组件，H5混合开发核心 | 中级 | P2 |
| `src/main/ets/components/dialog/TKNormalDialog.ets` | 普通对话框组件，通用弹窗 | 初级 | P2 |
| `src/main/ets/components/dialog/TKLoadingDialog.ets` | 加载对话框组件，加载状态显示 | 初级 | P2 |
| `src/main/ets/components/titilebar/TKTitleBar.ets` | 标题栏组件，页面标题栏 | 初级 | P2 |

### ⭐⭐ 选择性学习文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/ets/components/keyboard/view/TKKeyboard.ets` | 自定义键盘组件，特殊输入场景 | 中级 | P3 |
| `src/main/ets/components/patternlock/TKPatternLock.ets` | 图案锁组件，安全验证 | 中级 | P3 |
| `src/main/ets/components/gif/TKAnimatedImage.ets` | GIF动画组件，动画显示 | 初级 | P3 |
| `src/main/ets/components/vcode/TKImageCode.ets` | 图片验证码组件，验证功能 | 初级 | P3 |
| `src/main/ets/components/bottombar/TKBottomBar.ets` | 底部导航栏组件，导航功能 | 初级 | P3 |

## 🔧 第4层：工具类层（模式提炼）

### ⭐⭐⭐ 一般了解文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/ets/util/cache/TKCacheManager.ets` | 缓存管理器，多级缓存实现 | 中级 | P2 |
| `src/main/ets/util/logger/TKLog.ets` | 日志工具类，日志记录功能 | 初级 | P2 |
| `src/main/ets/util/file/TKFileHelper.ets` | 文件操作工具类，文件管理 | 初级 | P2 |
| `src/main/ets/util/string/TKStringHelper.ets` | 字符串工具类，字符串处理 | 初级 | P2 |
| `src/main/ets/util/data/TKDataHelper.ets` | 数据处理工具类，数据转换 | 初级 | P2 |

### ⭐⭐ 选择性学习文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/ets/util/crypto/TKAesHelper.ets` | AES加密工具类，数据加密 | 中级 | P3 |
| `src/main/ets/util/crypto/TKRsaHelper.ets` | RSA加密工具类，非对称加密 | 中级 | P3 |
| `src/main/ets/util/net/TKNetHelper.ets` | 网络工具类，网络状态检测 | 初级 | P3 |
| `src/main/ets/util/ui/TKDialogHelper.ets` | 对话框工具类，弹窗辅助 | 初级 | P3 |
| `src/main/ets/util/system/TKSystemHelper.ets` | 系统工具类，系统信息获取 | 初级 | P3 |

### ⭐ 参考性质文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/ets/util/array/TKArrayHelper.ets` | 数组工具类，数组操作辅助 | 初级 | P4 |
| `src/main/ets/util/number/TKNumberHelper.ets` | 数字工具类，数字处理 | 初级 | P4 |
| `src/main/ets/util/date/TKDateHelper.ets` | 日期工具类，日期时间处理 | 初级 | P4 |
| `src/main/ets/util/format/TKFormatHelper.ets` | 格式化工具类，数据格式化 | 初级 | P4 |
| `src/main/ets/util/map/TKMapHelper.ets` | Map工具类，Map操作辅助 | 初级 | P4 |

## ⚙️ 第5层：配置文件层（配置说明）

### ⭐⭐ 选择性学习文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `oh-package.json5` | 包配置文件，项目依赖和元信息 | 初级 | P3 |
| `src/main/module.json` | 模块配置文件，HAR模块配置 | 初级 | P3 |
| `build-profile.json5` | 构建配置文件，编译和混淆配置 | 初级 | P3 |

### ⭐ 参考性质文件

| 文件路径 | 功能描述 | 学习难度 | 优先级 |
|---------|---------|---------|--------|
| `src/main/resources/base/profile/route_map.json` | 路由映射配置，页面路由定义 | 初级 | P4 |
| `hvigorfile.ts` | 构建脚本配置，Hvigor构建工具 | 初级 | P4 |
| `Index.ets` | 导出索引文件，模块导出定义 | 初级 | P4 |

## 学习路径建议

### 🎯 初学者路径（从易到难）

1. **P4优先级**：配置文件 → 基础工具类
2. **P3优先级**：UI组件 → 高级工具类  
3. **P2优先级**：基础服务 → 核心组件
4. **P1优先级**：重要架构组件
5. **P0优先级**：核心架构文件

### 🚀 进阶者路径（架构优先）

1. **P0优先级**：核心架构文件
2. **P1优先级**：重要架构组件
3. **P2优先级**：基础服务和核心组件
4. **P3优先级**：UI组件和高级工具
5. **P4优先级**：配置和辅助工具

### ⚡ 实战者路径（功能优先）

1. **P2优先级**：核心组件和基础服务
2. **P1优先级**：重要架构组件
3. **P3优先级**：UI组件和工具类
4. **P0优先级**：深入理解架构
5. **P4优先级**：配置优化

## 优先级说明

- **P0**: 立即学习，框架核心，必须掌握
- **P1**: 重要学习，常用功能，建议掌握  
- **P2**: 按需学习，特定场景，了解即可
- **P3**: 选择学习，高级功能，按需深入
- **P4**: 参考学习，辅助工具，查阅使用

## 依赖关系图

```
TKAppEngine (P0)
├── TKModuleEngine (P0)
│   ├── TKModuleDelegate (P1)
│   └── TKModuleMessage (P1)
├── TKRouterPageManager (P1)
├── TKCacheManager (P2)
├── TKVersionManager (P1)
└── TKThemeManager (P1)

TKUIAbility (P0)
├── TKAppEngine (P0)
└── TKRouterHelper (P1)

基础服务层 (P1-P2)
├── DAO层 (P1)
├── 网络层 (P1-P2)
├── 通知层 (P2)
└── 插件层 (P2)

UI组件层 (P2-P3)
├── 通用组件 (P2)
├── 对话框组件 (P2-P3)
└── 特殊组件 (P3)

工具类层 (P2-P4)
├── 核心工具 (P2)
├── 加密工具 (P3)
└── 辅助工具 (P4)
```
