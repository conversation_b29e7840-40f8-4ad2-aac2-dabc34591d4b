{"app": {"bundleName": "com.thinkive.app", "debug": true, "versionCode": 1000001, "versionName": "1.0.1", "minAPIVersion": 50000012, "targetAPIVersion": 50002014, "apiReleaseType": "Release", "compileSdkVersion": "5.0.2.126", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug"}, "module": {"name": "thinkive_harmony_base", "type": "har", "deviceTypes": ["default", "tablet", "2in1"], "routerMap": "$profile:route_map", "packageName": "@thinkive/tk-harmony-base", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}