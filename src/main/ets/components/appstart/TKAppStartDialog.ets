import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKAppStartManager } from './TKAppStartManager';

@Observed
export class TKAppStartDialogOption {
  //图片
  image?: Resource = $r("app.media.startIcon");
  //延迟隐藏时间，单位毫秒
  delay?: number = -1;
  //加载完成回调函数
  finishLaunch?: () => void;
  //是否隐藏
  isDisappear?: boolean = false;
}

@Builder
export function buildTKAppStartDialog(options: TKAppStartDialogOption) {
  TKAppStartDialog({ options })
}

/**
 * 启动页面
 */
@Component
export struct TKAppStartDialog {
  @State options: TKAppStartDialogOption = new TKAppStartDialogOption();

  aboutToAppear(options?: TKAppStartDialogOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKAppStartDialogOption, options);
    if (this.options && this.options.delay! > 0) {
      TKAppStartManager.shareInstance().hide(this.options.delay);
    }
  }

  aboutToDisappear(): void {
    this.options = new TKAppStartDialogOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKAppStartDialogOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  build() {
    Column() {
      Image(this.options.image)
        .objectFit(ImageFit.ScaleDown)
    }.width("100%").height("100%").alignItems(HorizontalAlign.Center).justifyContent(FlexAlign.Center)
  }
}