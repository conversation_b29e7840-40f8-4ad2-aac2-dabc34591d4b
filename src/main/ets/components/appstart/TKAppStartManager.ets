import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKComponentContent, TKDialogHelper } from '../../util/ui/TKDialogHelper';
import { buildTKAppStartDialog, TKAppStartDialogOption } from './TKAppStartDialog';
import { buildTKAppStartPage, TKAppStartPageOption } from './TKAppStartPage';


/**
 * App启动图管理器
 */
export class TKAppStartManager {
  //单例对象
  private static instance: TKAppStartManager | undefined = undefined;
  //当前弹层对象
  private appStartContent?: TKComponentContent<Object> = undefined;
  private finishLaunch?: () => void;

  private constructor() {
  }

  public static shareInstance(): TKAppStartManager {
    if (!TKAppStartManager.instance) {
      TKAppStartManager.instance = new TKAppStartManager();
    }
    return TKAppStartManager.instance;
  }

  /**
   *  加载App启动插画,保持与启动时效果的一致，并支持设定自动消失的时间
   */
  public launch(options?: TKAppStartDialogOption) {
    options = TKObjectHelper.fixDefault(options, TKAppStartDialogOption);
    this.finishLaunch = options?.finishLaunch;
    let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKAppStartDialog) as WrappedBuilder<[Object]>;
    this.appStartContent =
      TKDialogHelper.createCommonDialog<TKAppStartDialogOption>(builder, options);
    this.appStartContent.open();
  }

  /**
   *  加载启动导航页
   */
  public launchStartPage(options: TKAppStartPageOption) {
    options = TKObjectHelper.fixDefault(options, TKAppStartPageOption);
    this.finishLaunch = options?.finishLaunch;
    let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKAppStartPage) as WrappedBuilder<[Object]>;
    this.appStartContent =
      TKDialogHelper.createCommonDialog<TKAppStartPageOption>(builder, options);
    this.appStartContent.open();
  }


  /**
   *  以默认动画效果隐藏启动界面，延迟执行
   * @param delay
   */
  public hide(delay: number = 0) {
    if (this.appStartContent) {
      let t = setTimeout(() => {
        this.appStartContent?.close();
        this.appStartContent = undefined;
        clearTimeout(t);
        if (this.finishLaunch) {
          this.finishLaunch();
          this.finishLaunch = undefined;
        }
      }, delay);
    }
  }
}