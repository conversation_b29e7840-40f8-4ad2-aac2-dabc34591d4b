import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKWindowHelper } from '../../util/ui/TKWindowHelper';
import { TKAppStartManager } from './TKAppStartManager';

/**
 * 数据项
 */
@Observed
export class TKAppStartPageData {
  builder?: WrappedBuilder<[Object]>;
  option?: Object;
}

/**
 * 数据源
 */
export class TKAppStartPageDataSource implements IDataSource {
  private builderList: Array<TKAppStartPageData> = new Array();

  constructor(builderList: Array<TKAppStartPageData>) {
    this.builderList = builderList
  }

  totalCount(): number {
    return this.builderList.length
  }

  getData(index: number): TKAppStartPageData {
    return this.builderList[index];
  }

  registerDataChangeListener(listener: DataChangeListener) {
  }

  unregisterDataChangeListener(listener: DataChangeListener) {
  }
}

@Observed
export class TKAppStartPageOption {
  //自动滚动间隔,单位毫秒
  playInterval?: number = 3000;
  //边缘滑动效果
  effectMode?: EdgeEffect;
  //是否显示指示条
  isShowPageIndicator?: boolean = true;
  //是否翻页标签到最后一个点的时候自动隐藏
  isLastPageIndicatorHidden?: boolean = false;
  //是否禁止手工滚动
  disableSwipe?: boolean = false;
  //界面
  builderList?: Array<TKAppStartPageData> = [];
  //加载完成回调函数
  finishLaunch?: () => void;
  //是否隐藏
  isDisappear?: boolean = false;
}

@Builder
export function buildTKAppStartPage(options: TKAppStartPageOption) {
  TKAppStartPage({ options })
}

/**
 * 启动页面
 */
@Component
export struct TKAppStartPage {
  private swiperController: SwiperController = new SwiperController();
  @State options: TKAppStartPageOption = new TKAppStartPageOption();
  @State data: TKAppStartPageDataSource = new TKAppStartPageDataSource([]);

  aboutToAppear(options?: TKAppStartPageOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKAppStartPageOption, options);
    this.options.isShowPageIndicator = this.options.isLastPageIndicatorHidden ? true : this.options.isShowPageIndicator;
    this.data = new TKAppStartPageDataSource(this.options.builderList ?? []);
  }

  aboutToDisappear(): void {
    this.options = new TKAppStartPageOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKAppStartPageOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  build() {
    Column() {
      Swiper(this.swiperController) {
        LazyForEach(this.data, (item: TKAppStartPageData, index: number) => {
          item.builder?.builder(item.option)
        }, (item: WrappedBuilder<[Object]>, index: number) => `${index}`)
      }
      .cachedCount(2)
      .autoPlay(true)
      .interval(this.options.playInterval)
      .loop(false)
      .effectMode(this.options.effectMode)
      .indicatorInteractive(true)
      .duration(300)
      .itemSpace(0)
      .indicator(this.options.isShowPageIndicator ?
      new DotIndicator()
        .itemWidth(8)
        .itemHeight(8)
        .selectedItemWidth(8)
        .selectedItemHeight(8)
        .color(Color.Gray)
        .selectedColor(Color.Blue)
        .bottom(px2vp(TKWindowHelper.getNavigationBottomBarHeightSync())) : this.options.isShowPageIndicator
      )
      .displayArrow(false, false)
      .curve(Curve.Linear)
      .disableSwipe(this.options.disableSwipe)
      .onChange((index: number) => {
        if (this.options.isLastPageIndicatorHidden) {
          if (index == (this.data.totalCount() - 1)) {
            this.options.isShowPageIndicator = false;
          } else {
            this.options.isShowPageIndicator = true;
          }
        }
        if (this.options.disableSwipe) {
          if (index == (this.data.totalCount() - 1)) {
            TKAppStartManager.shareInstance().hide(this.options.playInterval);
          }
        }
      })
      .onGestureSwipe((index: number, extraInfo: SwiperAnimationEvent) => {

      })
      .onAnimationStart((index: number, targetIndex: number, extraInfo: SwiperAnimationEvent) => {

      })
      .onAnimationEnd((index: number, extraInfo: SwiperAnimationEvent) => {

      })
    }.width('100%').height('100%').backgroundColor(Color.White)
  }
}

