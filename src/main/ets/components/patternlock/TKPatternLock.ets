/**
 * 手势密码属性
 */
import { drawing } from '@kit.ArkGraphics2D';
import { TKColorHelper } from '../../util/ui/TKColorHelper';
import { TKWindowHelper } from '../../util/ui/TKWindowHelper';
import { TKSystemHelper } from '../../util/system/TKSystemHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKCacheManager } from '../../util/cache/TKCacheManager';
import { TKSm3Helper } from '../../util/crypto/TKSm3Helper';
import { display, LengthUnit } from '@kit.ArkUI';
import { TKDialogHelper } from '../../util/ui/TKDialogHelper';
import { TKDeviceHelper } from '../../util/dev/TKDeviceHelper';
import { TKStyleAttribute } from '../common/attribute/TKStyleAttribute';
import { TKThemeManager } from '../../base/theme/TKThemeManager';
import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKFoldDisplayListener } from '../../base/device/listener/TKFoldDisplayListener';
import { TKNotificationCenter } from '../../base/notification/TKNotificationCenter';
import { TKNotification } from '../../base/notification/TKNotification';

/**
 * 手势密码的操作类型
 */
export enum TKPatternLockType {
  /**
   *  设置
   */
  Setting,

  /**
   *  验证
   */
  Verify,

  /**
   *  取消
   */
  Cancel,

  /**
   *  修改
   */
  Reset,
}

/**
 * 手势密码属性配置
 */
export class TKPatternLockOption {
  //手势密码账号
  userAccount?: string;
  //手势密码头像
  userImage?: string;
  //是否有返回键
  isCanBack?: boolean;
  //验证后是否自动关闭手势
  isCloseAfterVerify?: boolean;
  //返回键位置
  backPosition?: string;
  //是否防截屏
  isInterceptScreenshot?: boolean;
  //最大错误次数
  maxErrorNum?: number;
  //最小密码长度
  minPasswordLength?: number;
  //手势密码模式
  type?: TKPatternLockType;
  //改变账号登陆
  onChangeAccount?: () => void;
  //忘记手势密码
  onForgetPassword?: () => void;
  //设置手势密码
  onSetPassword?: (result: string) => void;
  //重置手势密码
  onReSetPassword?: (result: string) => void;
  //校验手势密码
  onVerifyPassword?: (result: string) => void;
  //取消手势密码
  onCancelPassword?: (result: string) => void;
  //返回关闭手势密码
  onBackPassword?: () => void;
  //关闭弹框
  close?: () => void;
  //是否关闭
  isDisappear?: boolean;
}

@Builder
export function buildTKPatternLock(options: TKPatternLockOption) {
  TKPatternLock(options)
}

/**
 * 手势密码样式属性配置
 */
@Observed
export class TKPatternLockStyle extends TKStyleAttribute {
  //背景色
  backgroundColor?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKPatternLock").backgroundColor ?? '#FFFFFFFF';
  //默认颜色
  color?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKPatternLock").color ?? '#FF757575';
  //选中颜色
  selectedColor?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKPatternLock").selectedColor ?? '#FF1A9BFF';
  //高亮颜色
  highlightedColor?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKPatternLock").highlightedColor ?? '#FF90EE90';
  //标题栏背景色
  titleBackgroundColor?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKPatternLockTitle").backgroundColor ?? '#FFFFFFFF';
  //标题栏文字景色
  titleColor?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKPatternLockTitle").color ?? '#FF333333';
  //底部按钮文字颜色
  bottomColor?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKPatternLockBottom").color ?? '#FF6B5955';
  //错误颜色
  errorColor?: ResourceColor = "#FFD02424";
  //返回按钮图片
  backImage?: ResourceStr = $r('app.media.tk_framework_gesback_btn');
  //跳过按钮图片
  jumpImage?: ResourceStr = $r('app.media.tk_framework_gesjump_btn');
}

/**
 * 手势密码
 */
@Component
export struct TKPatternLock {
  //手势密码样式
  @State styleAttribute: TKPatternLockStyle = new TKPatternLockStyle();
  //手势密码账号
  @State userAccount: string = "";
  //手势密码头像
  @State userImage: string = "";
  //手势密码
  private userPassword: string = "";
  //是否有返回键
  @State isCanBack: boolean = false;
  //验证后是否自动关闭手势
  public isCloseAfterVerify: boolean = true;
  //返回键位置
  @State backPosition: string = "0";
  //是否防截屏
  public isInterceptScreenshot: boolean = false;
  //最大错误次数
  public maxErrorNum: number = 5;
  //已经输入错误次数
  private errorInputNum: number = 0;
  //最小密码长度
  public minPasswordLength: number = 4;
  //提示信息
  @State private message: string = "";
  //手势状态
  @State private state: string = "0";
  //手势密码模式
  @State type: TKPatternLockType = TKPatternLockType.Setting;
  //前一次设置的密码
  @State @Watch('drawMinPatternLock') private prevPassword: string = "";
  //手势密码控制器
  private patternLockController: PatternLockController = new PatternLockController();
  //当前的隐私模式
  private privacyMode?: boolean;
  // 状态栏高度
  private statusBarHeight: number = TKWindowHelper.getStatusBarHeightSync();
  //标题栏高度
  private titleBarHeight: number = 160;
  //标题栏内容
  @State private title: string = "";
  //手势小图案大小
  @State private minPatternLockSize: number = 40;
  //头像大小
  @State private userImageSize: number = 65;
  //是否折叠屏展开
  @State private isFoldExpanded: boolean = false;
  //手势小图案绘制上下文
  private context: CanvasRenderingContext2D = new CanvasRenderingContext2D(new RenderingContextSettings(true))
  //改变账号登陆
  public onChangeAccount?: () => void;
  //忘记手势密码
  public onForgetPassword?: () => void;
  //设置手势密码
  public onSetPassword?: (result: string) => void;
  //重置手势密码
  public onReSetPassword?: (result: string) => void;
  //校验手势密码
  public onVerifyPassword?: (result: string) => void;
  //取消手势密码
  public onCancelPassword?: (result: string) => void;
  //返回关闭手势密码
  public onBackPassword?: () => void;
  //关闭弹框
  public close?: () => void;

  aboutToAppear(options?: TKPatternLockOption): void {
    TKObjectHelper.fixDefault(this.styleAttribute, TKPatternLockStyle);
    let screenHWRadio: number = TKDeviceHelper.getScreenHeight() / TKDeviceHelper.getScreenWidth();
    let isFoldExpanded: boolean =
      TKFoldDisplayListener.shareInstance().getFoldStatus() == display.FoldStatus.FOLD_STATUS_EXPANDED ||
        TKFoldDisplayListener.shareInstance().getFoldStatus() == display.FoldStatus.FOLD_STATUS_HALF_FOLDED;
    this.isFoldExpanded = screenHWRadio > 1 && screenHWRadio < 1.5 && isFoldExpanded;
    this.isCanBack = options?.isCanBack ?? this.isCanBack ?? false;
    this.isCloseAfterVerify = options?.isCloseAfterVerify ?? this.isCloseAfterVerify ?? true;
    this.backPosition = options?.backPosition ?? this.backPosition ?? "0";
    this.maxErrorNum = options?.maxErrorNum ?? this.maxErrorNum ?? 5;
    this.minPasswordLength = options?.minPasswordLength ?? this.minPasswordLength ?? 4;
    this.isInterceptScreenshot = options?.isInterceptScreenshot ?? this.isInterceptScreenshot ?? false;
    this.type = options?.type ?? this.type ?? TKPatternLockType.Setting;
    this.title = (this.type == TKPatternLockType.Setting) ? "设置手势密码" :
      (this.type == TKPatternLockType.Cancel) ? "取消手势密码" :
        (this.type == TKPatternLockType.Reset) ? "重置手势密码" : "";
    this.userAccount = options?.userAccount ?? this.userAccount;
    this.userImage = options?.userImage ?? this.userImage;
    this.onChangeAccount = options?.onChangeAccount ?? this.onChangeAccount;
    this.onForgetPassword = options?.onForgetPassword ?? this.onForgetPassword;
    this.onSetPassword = options?.onSetPassword ?? this.onSetPassword;
    this.onReSetPassword = options?.onReSetPassword ?? this.onReSetPassword;
    this.onVerifyPassword = options?.onVerifyPassword ?? this.onVerifyPassword;
    this.onCancelPassword = options?.onCancelPassword ?? this.onCancelPassword;
    this.onBackPassword = options?.onBackPassword ?? this.onBackPassword;
    this.close = options?.close ?? this.close;
    this.prevPassword = "";
    this.message = (this.type == TKPatternLockType.Setting) ? "绘制手势密码" :
      (this.type == TKPatternLockType.Cancel) ? "请输入原手势密码" :
        (this.type == TKPatternLockType.Reset) ? "请输入原手势密码" : "";
    this.state = "0";
    this.errorInputNum = 0;
    this.saveUserAccountAndImage();
    if (this.privacyMode === undefined) {
      TKWindowHelper.getWindowPrivacyMode().then((privacyMode) => {
        this.privacyMode = privacyMode;
        if (this.isInterceptScreenshot) {
          TKWindowHelper.setWindowPrivacyMode(true);
        }
      });
    }
    this.onFoldDisplayListener();
  }

  /**
   * 监听折叠屏
   */
  private onFoldDisplayListener() {
    TKNotificationCenter.defaultCenter.addObserver(this, async (notification: TKNotification) => {
      let screenHWRadio: number = TKDeviceHelper.getScreenHeight() / TKDeviceHelper.getScreenWidth();
      let isFoldExpanded: boolean = notification.obj == display.FoldStatus.FOLD_STATUS_EXPANDED ||
        notification.obj == display.FoldStatus.FOLD_STATUS_HALF_FOLDED;
      this.isFoldExpanded = screenHWRadio > 1 && screenHWRadio < 1.5 && isFoldExpanded;
    }, TKFoldDisplayListener.NOTE_FOLD_CHANGE)
  }

  /**
   * 取消监听折叠屏
   */
  private unFoldDisplayListener() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }

  aboutToDisappear(): void {
    if (this.isInterceptScreenshot) {
      if (this.privacyMode != undefined) {
        TKWindowHelper.setWindowPrivacyMode(this.privacyMode);
      }
    }
    this.privacyMode = undefined;
    this.patternLockController.reset();
    this.isCanBack = false;
    this.isCloseAfterVerify = true;
    this.backPosition = "0";
    this.maxErrorNum = 5;
    this.minPasswordLength = 4;
    this.errorInputNum = 0;
    this.prevPassword = "";
    this.isInterceptScreenshot = false;
    this.state = "0";
    this.type = TKPatternLockType.Setting;
    this.userAccount = "";
    this.userImage = "";
    this.userPassword = "";
    this.privacyMode = undefined;
    this.onChangeAccount = undefined;
    this.onForgetPassword = undefined;
    this.onSetPassword = undefined;
    this.onReSetPassword = undefined;
    this.onVerifyPassword = undefined;
    this.onCancelPassword = undefined;
    this.onBackPassword = undefined;
    this.close = undefined;
    this.unFoldDisplayListener();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKPatternLockOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  /**
   * 清理账号手势密码
   * @param userAccount
   */
  public static clearUserPassword(userAccount?: string) {
    let service: string = TKSm3Helper.stringWithSm3Sync(TKSystemHelper.getAppIdentifier());
    userAccount =
      userAccount ?? TKCacheManager.shareInstance().getFileCacheData(`${service}_Gesture_Account`) as string ?? "";
    let user: string = TKSm3Helper.stringWithSm3Sync(userAccount);
    TKCacheManager.shareInstance().saveFileCacheData(`${service}_Gesture_Password_${user}`, "");
    TKCacheManager.shareInstance().saveFileCacheData(`${service}_Gesture_ErrorInputNum_${user}`, 0);
  }

  /**
   * 获取用户手势密码
   * @param userAccount
   * @returns
   */
  public static getUserPassword(userAccount?: string) {
    let service: string = TKSm3Helper.stringWithSm3Sync(TKSystemHelper.getAppIdentifier());
    userAccount =
      userAccount ?? TKCacheManager.shareInstance().getFileCacheData(`${service}_Gesture_Account`) as string ?? "";
    let user: string = TKSm3Helper.stringWithSm3Sync(userAccount);
    return TKCacheManager.shareInstance().getFileCacheData(`${service}_Gesture_Password_${user}`) as string ?? "";
  }

  /**
   * 保存账号及用户头像
   */
  private saveUserAccountAndImage() {
    //用户账号
    let service: string = TKSm3Helper.stringWithSm3Sync(TKSystemHelper.getAppIdentifier());
    if (TKStringHelper.isBlank(this.userAccount)) {
      this.userAccount = TKCacheManager.shareInstance().getFileCacheData(`${service}_Gesture_Account`) as string ?? "";
    }
    //用户照片
    let user: string = TKSm3Helper.stringWithSm3Sync(this.userAccount);
    if (TKStringHelper.isBlank(this.userImage)) {
      this.userImage =
        TKCacheManager.shareInstance().getFileCacheData(`${service}_Gesture_Image_${user}`) as string ?? "";
    }
    //用户密码
    this.userPassword =
      TKCacheManager.shareInstance().getFileCacheData(`${service}_Gesture_Password_${user}`) as string ?? "";
    //用户错误次数
    this.errorInputNum =
      TKCacheManager.shareInstance().getFileCacheData(`${service}_Gesture_ErrorInputNum_${user}`) as number ?? 0;
    //保存当前账号和头像
    TKCacheManager.shareInstance().saveFileCacheData(`${service}_Gesture_Account`, this.userAccount);
    TKCacheManager.shareInstance().saveFileCacheData(`${service}_Gesture_Image_${user}`, this.userImage);
  }

  /**
   * 保存用户密码
   * @param password
   */
  private saveUserPassword(password: string) {
    let service: string = TKSm3Helper.stringWithSm3Sync(TKSystemHelper.getAppIdentifier());
    let user: string = TKSm3Helper.stringWithSm3Sync(this.userAccount);
    this.userPassword = TKStringHelper.isNotBlank(password) ? TKSm3Helper.stringWithSm3Sync(password) : "";
    this.errorInputNum = 0;
    TKCacheManager.shareInstance().saveFileCacheData(`${service}_Gesture_Password_${user}`, this.userPassword);
    TKCacheManager.shareInstance().saveFileCacheData(`${service}_Gesture_ErrorInputNum_${user}`, this.errorInputNum);
  }

  /**
   * 保存用户密码输入错误次数
   */
  private saveUserErrorInputNum() {
    let service: string = TKSm3Helper.stringWithSm3Sync(TKSystemHelper.getAppIdentifier());
    let user: string = TKSm3Helper.stringWithSm3Sync(this.userAccount);
    TKCacheManager.shareInstance().saveFileCacheData(`${service}_Gesture_ErrorInputNum_${user}`, this.errorInputNum);
  }

  build() {
    RelativeContainer() {
      this.TKTitleBarComponent()
      this.TKMinPatternLockComponent()
      this.TKPatternLockComponent()
      this.TKPatternLockTipComponent()
    }
    .width("100%")
    .height("100%")
    .backgroundColor(this.styleAttribute.backgroundColor)
  }

  @Builder
  TKTitleBarComponent() {
    Row() {
      Row() {
        if (this.isCanBack && (this.type != TKPatternLockType.Setting || this.backPosition == "0")) {
          Image(this.styleAttribute.backImage)
            .width(22)
            .height(22)
            .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB(this.styleAttribute.titleColor as string),
              drawing.BlendMode.SRC_IN))
            .onClick((event) => {
              if (this.close) {
                this.close();
              }
              if (this.onBackPassword) {
                this.onBackPassword();
              }
            })
            .margin({ left: 5 })
          Text("返回")
            .fontColor(this.styleAttribute.titleColor)
            .fontFamily('PingFang SC')
            .fontSize(19)
            .width(40)
            .textAlign(TextAlign.Start)
            .onClick((event) => {
              if (this.close) {
                this.close();
              }
              if (this.onBackPassword) {
                this.onBackPassword();
              }
            })
            .margin({ left: -5 })
        }
      }
      .width('20%')
      .height('100%')
      .justifyContent(FlexAlign.Start)
      .zIndex(100)

      Row() {
        Text(this.title)
          .fontFamily('PingFang SC')
          .fontWeight(FontWeight.Bold)
          .fontColor(this.styleAttribute.titleColor)
          .fontSize(19)
          .textAlign(TextAlign.Center)
      }
      .width('60%')
      .height('100%')
      .justifyContent(FlexAlign.Center)

      Row() {
        if (this.type == TKPatternLockType.Setting && this.isCanBack && this.backPosition == "1") {
          Text("跳过")
            .fontFamily('PingFang SC')
            .fontColor(this.styleAttribute.titleColor)
            .fontSize(19)
            .width(40)
            .textAlign(TextAlign.End)
            .onClick((event) => {
              if (this.close) {
                this.close();
              }
              if (this.onBackPassword) {
                this.onBackPassword();
              }
            })
          Image(this.styleAttribute.jumpImage)
            .width(22)
            .height(22)
            .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB(this.styleAttribute.titleColor as string),
              drawing.BlendMode.SRC_IN))
            .onClick((event) => {
              if (this.close) {
                this.close();
              }
              if (this.onBackPassword) {
                this.onBackPassword();
              }
            })
            .margin({ left: -5, right: 5 })
        }
      }
      .width('20%')
      .height('100%')
      .justifyContent(FlexAlign.End)
      .zIndex(100)
    }
    .width("100%")
    .height(px2vp(this.titleBarHeight))
    .backgroundColor(this.styleAttribute.titleBackgroundColor)
    .margin({ top: px2vp(this.statusBarHeight) })
    .alignRules({
      top: { anchor: "__container__", align: VerticalAlign.Top },
      left: { anchor: "__container__", align: HorizontalAlign.Start }
    })
    .id("TKTitleBar")
  }

  @Builder
  TKMinPatternLockComponent() {
    Column() {
      if (this.type == TKPatternLockType.Setting) {
        Canvas(this.context)
          .width(this.minPatternLockSize)
          .height(this.minPatternLockSize)
          .onReady(() => {
            this.drawMinPatternLock();
          })
      } else if (this.type == TKPatternLockType.Verify) {
        Image(TKStringHelper.isNotBlank(this.userImage) ?
          ((this.userImage.startsWith("http://") || this.userImage.startsWith("https://")) ? this.userImage :
          $r(`app.media.${this.userImage}`)) : undefined)
          .width(this.userImageSize)
          .height(this.userImageSize)
        Text(this.userAccount)
          .fontFamily('PingFang SC')
          .fontColor(this.styleAttribute.titleColor)
          .fontSize(15)
          .textAlign(TextAlign.Center)
      }
      Text(this.message)
        .fontFamily('PingFang SC')
        .fontColor(this.state == "0" ? this.styleAttribute.titleColor : this.styleAttribute.errorColor)
        .fontSize(16)
        .textAlign(TextAlign.Center)
        .margin({ top: 5 })
    }
    .width('100%')
    .justifyContent(FlexAlign.Center)
    .margin({ top: 10 })
    .alignRules({
      top: { anchor: "TKTitleBar", align: VerticalAlign.Bottom },
      left: { anchor: "__container__", align: HorizontalAlign.Start }
    })
    .id("TKMinPatternLock")
  }

  /**
   * 绘制手势密码小图
   */
  private drawMinPatternLock() {
    let radius: number = this.minPatternLockSize / 10;
    for (let i = 0; i < 3; i++) {
      for (let j = 0; j < 3; j++) {
        let value: number = i * 3 + j;
        this.context.beginPath();
        this.context.fillStyle = this.prevPassword.includes(`${value}`) ? this.styleAttribute.selectedColor as string :
          this.styleAttribute.color as string;
        this.context.arc(2 * radius + j * radius * 3, 2 * radius + i * radius * 3, radius, 0, 2 * Math.PI);
        this.context.fill();
        this.context.stroke();
      }
    }
  }

  @Builder
  TKPatternLockComponent() {
    PatternLock(this.patternLockController)
      .sideLength("80%")
      .circleRadius(14)
      .pathStrokeWidth(5)
      .regularColor(this.styleAttribute.color)
      .activeColor(this.styleAttribute.selectedColor)
      .selectedColor(this.styleAttribute.selectedColor)
      .pathColor(this.styleAttribute.selectedColor)
      .backgroundColor(this.styleAttribute.backgroundColor)
      .autoReset(true)
      .activateCircleStyle({
        color: this.styleAttribute.highlightedColor,
        radius: { value: 20, unit: LengthUnit.VP },
        enableWaveEffect: true
      })
      .onDotConnect((index: number) => {
      })
      .onPatternComplete((input: Array<number>) => {
        let password: string = input ? input.join("") : "";
        switch (this.type) {
          case TKPatternLockType.Setting: {
            this.processSettingPassword(password);
            break;
          }
          case TKPatternLockType.Verify: {
            this.processVerifyPassword(password);
            break;
          }
          case TKPatternLockType.Reset: {
            this.processResetPassword(password);
            break;
          }
          case TKPatternLockType.Cancel: {
            this.processCancelPassword(password);
            break;
          }
        }
      })
      .width(this.isFoldExpanded ? '50%' : '100%')
      .margin({ top: this.isFoldExpanded ? "-20%" : "-40%", left: this.isFoldExpanded ? '-25%' : '-50%' })
      .alignRules({
        top: { anchor: "__container__", align: VerticalAlign.Center },
        left: { anchor: "__container__", align: HorizontalAlign.Center }
      })
      .id("TKPatternLock")
  }

  @Builder
  TKPatternLockTipComponent() {
    Row() {
      if (this.type == TKPatternLockType.Setting) {
        Text("设置手势密码，防止他人未经授权查看")
          .fontFamily('PingFang SC')
          .fontColor(this.styleAttribute.bottomColor)
          .fontSize(15)
          .textAlign(TextAlign.Center)
      } else if (this.type == TKPatternLockType.Verify) {
        Row() {
          Text("忘记手势密码")
            .fontFamily('PingFang SC')
            .fontColor(this.styleAttribute.titleColor)
            .fontSize(16)
            .textAlign(TextAlign.Center)
            .onClick(() => {
              this.saveUserPassword("");
              if (this.onForgetPassword) {
                this.onForgetPassword();
              }
              setTimeout(() => {
                if (this.close) {
                  this.close();
                }
              }, 250);
            })
        }
        .width('50%')
        .justifyContent(FlexAlign.Center)

        Row() {
          Text("其他账号登录")
            .fontFamily('PingFang SC')
            .fontColor(this.styleAttribute.titleColor)
            .fontSize(16)
            .textAlign(TextAlign.Center)
            .onClick(() => {
              if (this.onChangeAccount) {
                this.onChangeAccount();
              }
              setTimeout(() => {
                if (this.close) {
                  this.close();
                }
              }, 250);
            })
        }
        .width('50%')
        .justifyContent(FlexAlign.Center)
      } else {
        Row() {
        }
        .width('50%')
        .justifyContent(FlexAlign.Center)

        Row() {
          Text("忘记手势密码")
            .fontFamily('PingFang SC')
            .fontColor(this.styleAttribute.titleColor)
            .fontSize(16)
            .textAlign(TextAlign.Center)
            .onClick(() => {
              this.saveUserPassword("");
              if (this.onForgetPassword) {
                this.onForgetPassword();
              }
              setTimeout(() => {
                if (this.close) {
                  this.close();
                }
              }, 250);
            })
        }
        .width('50%')
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .margin({ top: 5 })
    .alignRules({
      top: { anchor: "TKPatternLock", align: VerticalAlign.Bottom },
      left: { anchor: "__container__", align: HorizontalAlign.Start }
    })
    .justifyContent(FlexAlign.Center)
    .id("TKPatternLockTip")
  }

  /**
   *  处理手势密码操作完成动作
   */
  private processFinishPassword(result: string) {
    switch (this.type) {
      case TKPatternLockType.Setting: {
        if (this.onSetPassword) {
          this.onSetPassword(result);
        }
        break;
      }
      case TKPatternLockType.Verify: {
        if (this.onVerifyPassword) {
          this.onVerifyPassword(result);
        }
        break;
      }
      case TKPatternLockType.Cancel: {
        if (this.onCancelPassword) {
          this.onCancelPassword(result);
        }
        break;
      }
      case TKPatternLockType.Reset: {
        if (this.onReSetPassword) {
          this.onReSetPassword(result);
        }
        break;
      }
    }
  }

  /**
   * 关闭手势密码弹层
   */
  private closePassword() {
    if (this.close) {
      this.close();
    }
  }

  /**
   * 设置手势密码
   * @param password
   */
  private processSettingPassword(password: string) {
    if (TKStringHelper.isBlank(this.prevPassword)) {
      // 输入的密码长度小于5时，提示重新输入
      if (password.length < this.minPasswordLength) {
        TKDialogHelper.showToast(`至少连接${this.minPasswordLength}个点，请您重试`);
        TKDeviceHelper.startVibration();
        this.patternLockController.setChallengeResult(PatternLockChallengeResult.WRONG);
        setTimeout(() => {
          this.patternLockController.reset();
        }, 1000);
      } else {
        // 提示第二次输入密码
        this.prevPassword = password;
        this.state = "0";
        this.message = "请您再次绘制手势";
        this.patternLockController.reset();
      }
    } else {
      // 判断两次输入的密码是否相同，相同则提示密码设置成功，否则提示重新输入
      if (this.prevPassword == password) {
        this.saveUserPassword(password);
        TKDialogHelper.showToast("已保存手势密码");
        this.closePassword();
        this.processFinishPassword("0");
      } else {
        this.prevPassword = "";
        this.state = "1";
        this.message = '两次手势不一致，请您重新输入';
        TKDeviceHelper.startVibration();
        this.patternLockController.setChallengeResult(PatternLockChallengeResult.WRONG);
        setTimeout(() => {
          this.patternLockController.reset();
        }, 1000);
      }
    }
  }

  /**
   * 处理验证手势密码
   * @param password
   */
  private processVerifyPassword(password: string): boolean {
    if (TKSm3Helper.stringWithSm3Sync(password) == this.userPassword) {
      if (this.type == TKPatternLockType.Verify) {
        if (this.isCloseAfterVerify) {
          this.closePassword();
        }
        this.processFinishPassword("0");
      }
      this.errorInputNum = 0;
      this.saveUserErrorInputNum();
      this.state = "0";
      this.message = "";
      this.patternLockController.reset();
      return true;
    } else {
      this.errorInputNum++;
      this.saveUserErrorInputNum();
      if (this.errorInputNum >= this.maxErrorNum) {
        if (this.type == TKPatternLockType.Verify) {
          TKDialogHelper.showAlertDialog({
            title: "温馨提示",
            confirmText: "确定",
            message: `您输入错误的次数已经超过${this.maxErrorNum}次，手势密码即将关闭`,
            confirm: () => {
              this.closePassword();
              this.processFinishPassword("-1");
            }
          });
        } else {
          this.closePassword();
          this.processFinishPassword("-1");
        }
      } else {
        this.state = "1";
        this.message = `手势密码错误,还剩余${this.maxErrorNum - this.errorInputNum}次`;
        TKDeviceHelper.startVibration();
        this.patternLockController.setChallengeResult(PatternLockChallengeResult.WRONG);
        setTimeout(() => {
          this.patternLockController.reset();
        }, 1000);
      }
      return false;
    }
  }

  /**
   * 处理取消手势密码
   * @param password
   */
  private processCancelPassword(password: string) {
    if (this.processVerifyPassword(password)) {
      TKPatternLock.clearUserPassword(this.userAccount);
      this.closePassword();
      this.processFinishPassword("0");
    }
  }

  /**
   * 处理重置手势密码
   * @param password
   */
  private processResetPassword(password: string) {
    if (this.processVerifyPassword(password)) {
      this.processFinishPassword("0");
    }
  }
}