import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKRouterHelper } from '../../base/router/TKRouterHelper';
import { TKWindowHelper } from '../../util/ui/TKWindowHelper';
import { TKAttributeController } from '../common/attribute/TKAttributeController';
import { TKTitleBarAttribute, TKTitleBarStyleAttribute } from './TKTitleBar';

/**
 * 组件的能力接口实现类
 */
@Observed
export class TKTitleBarController extends TKAttributeController<TKTitleBarAttribute, TKTitleBarStyleAttribute> {
  /**
   * 设置标题栏标题文本
   * @param title
   */
  set title(title: string) {
    this.attribute!.title = title;
  }

  /**
   * 设置状态栏风格
   * @param style
   */
  set statusStyle(style: string) {
    this.styleAttribute!.statusStyle = style;
    if (TKStringHelper.isNotBlank(style)) {
      // 处理状态栏的风格
      TKWindowHelper.setWindowSystemBarProperties({
        statusBarContentColor: '1' === style ? '#ffffffff' : '#ff000000'
      });
    }
  }

  /**
   * 设置标题栏背景颜色
   * @param color
   */
  set titleBgColor(color: ResourceColor) {
    this.styleAttribute!.titleBgColor = color;
  }

  /**
   * 页面返回，实现H5页面的逐级返回
   */
  routerBack: (event?: ClickEvent) => void = (event) => {
    const attribute = this.attribute as TKTitleBarAttribute
    if (attribute?.onBackClick) {
      if (!attribute.onBackClick(event)) {
        TKRouterHelper.back({ navComponentOrPathStack: this.getComponent() });
      }
    } else {
      TKRouterHelper.back({ navComponentOrPathStack: this.getComponent() });
    }
  }
  /**
   * 路由返回事件，实现H5页面的逐级返回
   * @param event
   * @param result 路由带返回数据
   */
  routerBackResult: (event?: ClickEvent, result?: Record<string, Object>) => void = (event, result) => {
    const attribute = this.attribute as TKTitleBarAttribute
    if (attribute?.onBackClick) {
      if (!attribute.onBackClick(event)) {
        TKRouterHelper.back({ result: result, navComponentOrPathStack: this.getComponent() });
      }
    } else {
      TKRouterHelper.back({ result: result, navComponentOrPathStack: this.getComponent() });
    }
  }
  /**
   * 页面关闭
   */
  routerClose: (event?: ClickEvent) => void = (event) => {
    const attribute = this.attribute as TKTitleBarAttribute
    if (attribute?.onCloseClick) {
      if (!attribute.onCloseClick(event)) {
        TKRouterHelper.back({ navComponentOrPathStack: this.getComponent() });
      }
    } else {
      TKRouterHelper.back({ navComponentOrPathStack: this.getComponent() });
    }
  }
  /**
   * 页面通用事件
   */
  routerAction: (event?: ClickEvent, action?: string, data?: Record<string, Object>) => void =
    (event, action, data) => {
      const attribute = this.attribute as TKTitleBarAttribute
      if (attribute?.onRightBtnClick) {
        attribute.onRightBtnClick(event, action, data);
      }
    }
}
