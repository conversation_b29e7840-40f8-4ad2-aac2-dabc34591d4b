import { TKWindowHelper } from '../../util/ui/TKWindowHelper';
import { TKAttribute } from '../common/attribute/TKAttribute';
import { TKStyleAttribute } from '../common/attribute/TKStyleAttribute';
import { TKTitleBarController } from './TKTitleBarController';
import { drawing } from '@kit.ArkGraphics2D';
import { TKColorHelper } from '../../util/ui/TKColorHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKThemeManager } from '../../base/theme/TKThemeManager';

/**
 * 子组件的控制属性类
 */
@Observed
export class TKTitleBarAttribute extends TKAttribute {
  /**
   * 是否是全屏
   */
  public isLayoutFullScreen?: boolean = true;
  /**
   * 标题文本
   */
  public title?: string = '';
  /**
   * 状态栏显示/隐藏，设置了全屏才会生效，非全屏无法隐藏系统状态栏
   */
  public statusBarEnable?: boolean = true;
  /**
   * 标题栏显示/隐藏
   */
  public titleBarEnable?: boolean = true;
  /**
   * 左侧按钮模式（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
   */
  public leftBtnMode?: string = "1";
  /**
   * 左侧返回键按钮显示/隐藏
   */
  public backPressEnable?: boolean = true;
  /**
   * 左侧关闭键按钮显示/隐藏
   */
  public closePressEnable?: boolean = false;
  /**
   * 右侧按钮模式（0：文本，1：图片)
   */
  public rightBtnMode?: string = "1";
  /**
   * 右侧按钮文本
   */
  public rightBtnTxt?: string;
  /**
   * 右侧按钮动作
   */
  public rightBtnAction?: string;
  /**
   * 右侧按钮动作参数
   */
  public rightBtnActionParam?: Record<string, Object>;
  /**
   * 回调左上角返回按钮事件
   */
  public onBackClick?: (event?: ClickEvent) => boolean;
  /**
   * 回调左上角关闭按钮事件
   */
  public onCloseClick?: (event?: ClickEvent) => boolean;
  /**
   * 回调右上角按钮事件
   */
  public onRightBtnClick?: (event?: ClickEvent, action?: string, data?: Record<string, Object>) => boolean;
  /**
   * 回调标题栏+状态栏的高度
   */
  public onTitleBarHeight?: (height: number) => void;
}

/**
 * 子组件的样式属性类
 */
@Observed
export class TKTitleBarStyleAttribute extends TKStyleAttribute {
  /**
   * 导航栏背景图
   */
  public titleBgImage?: Resource;
  /**
   * 状态栏风格，状态栏上的小图标颜色风格(0:黑色，1:白色)
   */
  public statusStyle?: string;
  /**
   * 标题的颜色
   */
  public titleColor?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBar").color ?? "#FF000000";
  /**
   * 标题栏背景颜色
   */
  public titleBgColor?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBar").backgroundColor ?? "#00FFFFFF";
  /**
   * 左侧按钮颜色
   */
  public leftBtnColor?: ResourceColor;
  /**
   * 右侧按钮颜色
   */
  public rightBtnColor?: ResourceColor;
  /**
   * 返回按钮图片
   */
  public backImage?: Resource = TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBarBack").image ??
  $r('app.media.tk_framework_back_btn');
  /**
   * 返回按钮图片大小
   */
  public backImageSize?: Size = {
    width: Number(TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBarBack").width ?? 17),
    height: Number(TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBarBack").height ?? 17)
  };
  /**
   * 关闭按钮图片
   */
  public closeImage?: Resource = TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBarClose").image ??
  $r('app.media.tk_framework_close_btn');
  /**
   * 关闭按钮图片大小
   */
  public closeImageSize?: Size = {
    width: Number(TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBarClose").width ?? 15),
    height: Number(TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBarClose").height ?? 15)
  };
}

/**
 * 思迪信息标题栏
 */
@Component
export struct TKTitleBar {
  /**
   * 基本控制属性
   */
  @State attribute: TKTitleBarAttribute = new TKTitleBarAttribute();
  /**
   * 样式属性
   */
  @State styleAttribute: TKTitleBarStyleAttribute = new TKTitleBarStyleAttribute();
  /**
   * 子组件的能力类对象
   */
  @State private titleBarController: TKTitleBarController = new TKTitleBarController();
  /**
   * 自定义标题栏
   */
  @BuilderParam customTitleView?: (titleBarController: TKTitleBarController) => void = this.baseTitleView;
  /**
   * 状态栏高度
   */
  @State private statusBarHeight: number = TKWindowHelper.getStatusBarHeightSync();
  /**
   * 标题栏高度
   */
  @State private titleBarHeight: number = 160;

  aboutToAppear(): void {
    this.titleBarController.setComponent(this);
    this.titleBarController.setAttribute(this.attribute, TKTitleBarAttribute);
    this.titleBarController.setStyleAttribute(this.styleAttribute, TKTitleBarStyleAttribute);

    // 处理页面全屏状态和标题栏高度数据，用来适配安全区域相关的UI适配
    if (this.styleAttribute.statusStyle) {
      this.titleBarController.statusStyle = this.styleAttribute.statusStyle;
    }
    if (this.styleAttribute.titleBgColor) {
      this.titleBarController.titleBgColor = this.styleAttribute.titleBgColor;
    }
    if (this.attribute.isLayoutFullScreen !== undefined) {
      TKWindowHelper.setWindowLayoutFullScreen(this.attribute.isLayoutFullScreen).then((result) => {
        this.updateTitleBarHeight();
      });
    } else {
      this.updateTitleBarHeight();
    }
  }

  /**
   * 因给状态栏和标题栏分配高度的
   */
  updateTitleBarHeight() {
    let height: number = 0
    if (this.attribute.isLayoutFullScreen && this.attribute.statusBarEnable) {
      height += this.statusBarHeight;
    }
    if (this.attribute.titleBarEnable) {
      height += this.titleBarHeight;
    }
    if (this.attribute!.onTitleBarHeight) {
      this.attribute!.onTitleBarHeight!(height);
    }
  }

  @Builder
  baseTitleView(titleBarController: TKTitleBarController) {
    Row() {
      if (this.attribute.backPressEnable) {
        if (["1", "2", "3"].includes(this.attribute.leftBtnMode!)) {
          Image(this.styleAttribute.backImage)
            .width(this.styleAttribute.backImageSize?.width)
            .height(this.styleAttribute.backImageSize?.height)
            .objectFit(ImageFit.Contain)
            .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB((this.styleAttribute.leftBtnColor ??
            this.styleAttribute.titleColor) as string),
              drawing.BlendMode.SRC_IN))
            .onClick((event) => {
              this.titleBarController.routerBack(event)
            })
            .margin({ left: ["2"].includes(this.attribute.leftBtnMode!) ? 5 : 10 })
        }
        if (["0", "2"].includes(this.attribute.leftBtnMode!)) {
          Text("返回")
            .fontColor(this.styleAttribute.leftBtnColor ?? this.styleAttribute.titleColor)
            .fontFamily('PingFang SC')
            .fontSize(19)
            .width(40)
            .textAlign(TextAlign.Start)
            .onClick((event) => {
              this.titleBarController.routerBack(event);
            })
            .margin({ left: ["0"].includes(this.attribute.leftBtnMode!) ? 10 : 0 })
        }
      }
      if (this.attribute.closePressEnable) {
        if (["1", "2"].includes(this.attribute.leftBtnMode!)) {
          Image(this.styleAttribute.closeImage)
            .width(this.styleAttribute.closeImageSize?.width)
            .height(this.styleAttribute.closeImageSize?.height)
            .objectFit(ImageFit.Contain)
            .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB((this.styleAttribute.leftBtnColor ??
            this.styleAttribute.titleColor) as string),
              drawing.BlendMode.SRC_IN))
            .onClick((event) => {
              this.titleBarController.routerClose(event);
            })
            .margin({ left: 10 })
        }
        if (["0", "2", "3"].includes(this.attribute.leftBtnMode!)) {
          Text("关闭")
            .fontFamily('PingFang SC')
            .fontColor((this.styleAttribute.leftBtnColor ?? this.styleAttribute.titleColor))
            .fontSize(19)
            .width(40)
            .textAlign(TextAlign.Start)
            .onClick((event) => {
              this.titleBarController.routerClose(event);
            })
            .margin({
              left: ["2"].includes(this.attribute.leftBtnMode!) ? 0 : 10
            })
        }
      }
    }
    .width('15%')
    .height('100%')
    .justifyContent(FlexAlign.Start)
    .zIndex(100)

    Row() {
      Text(this.attribute.title)
        .fontFamily('PingFang SC')
        .fontColor(this.styleAttribute.titleColor)
        .minFontSize(5)
        .maxFontSize(19)
        .maxLines(1)
        .textAlign(TextAlign.Center)
    }
    .width('70%')
    .height('100%')
    .justifyContent(FlexAlign.Center)

    Row() {
      if (TKStringHelper.isNotBlank(this.attribute.rightBtnTxt)) {
        if (["0"].includes(this.attribute.rightBtnMode!)) {
          Text(this.attribute.rightBtnTxt)
            .fontFamily('PingFang SC')
            .fontColor((this.styleAttribute.rightBtnColor ?? this.styleAttribute.titleColor))
            .fontSize(19)
            .width(40)
            .textAlign(TextAlign.End)
            .onClick((event) => {
              this.titleBarController.routerAction(event, this.attribute.rightBtnAction,
                this.attribute.rightBtnActionParam);
            })
            .margin({ right: 10 })
        }
        if (["1"].includes(this.attribute.rightBtnMode!)) {
          Image($r(`app.media.${this.attribute.rightBtnTxt}`))
            .width(20)
            .height(20)
            .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB((this.styleAttribute.rightBtnColor ??
            this.styleAttribute.titleColor) as string),
              drawing.BlendMode.SRC_IN))
            .onClick((event) => {
              this.titleBarController.routerAction(event, this.attribute.rightBtnAction,
                this.attribute.rightBtnActionParam);
            })
            .margin({ right: 10 })
        }
      }
    }
    .width('15%')
    .height('100%')
    .justifyContent(FlexAlign.End)
    .zIndex(100)
  }

  build() {
    Column() {
      // 占位状态栏的高度
      if (this.attribute.isLayoutFullScreen) {
        if (this.attribute.statusBarEnable) {
          Row() {
          }
          .width('100%')
          .height(px2vp(this.statusBarHeight))
          .backgroundColor(this.styleAttribute.titleBgColor)
        }
      } else {
        if (this.attribute.statusBarEnable) {
          Row() {
          }
          .width('100%')
          .height('0.01%')
          .backgroundColor(this.styleAttribute.titleBgColor)
          .expandSafeArea([SafeAreaType.SYSTEM], [SafeAreaEdge.TOP])
        }
      }
      if (this.attribute.titleBarEnable) {
        Row() {
          if (this.customTitleView) {
            this.customTitleView(this.titleBarController)
          } else {
            this.baseTitleView(this.titleBarController)
          }
        }
        .width('100%')
        .height(px2vp(this.titleBarHeight))
        .backgroundColor(this.styleAttribute.titleBgColor)
        .backgroundImage(this.styleAttribute.titleBgImage)
        .backgroundImageSize(ImageSize.Cover)
      }
    }
    .backgroundColor(this.styleAttribute.titleBgColor)
  }
}