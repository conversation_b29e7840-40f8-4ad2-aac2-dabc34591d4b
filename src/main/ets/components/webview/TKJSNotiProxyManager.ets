import { TKNotification } from '../../base/notification/TKNotification';
import { TKNotificationCenter } from '../../base/notification/TKNotificationCenter';
import { TKArrayHelper } from '../../util/array/TKArrayHelper';
import { TKMapHelper } from '../../util/map/TKMapHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKJSProxyController } from './TKJSProxyController';

/**
 * JS通知代理对象
 */
export class TKJSNotiProxy {
  /**
   * 通知名称
   */
  public notiName: string = "";
  /**
   * 回调H5的功能号
   */
  public callbackFuncNo: string = "";
  /**
   * JS控制器
   */
  private _jsProxyController: WeakRef<TKJSProxyController> | undefined = undefined;

  public set jsProxyController(jsProxyController: TKJSProxyController | undefined) {
    if (jsProxyController) {
      this._jsProxyController = new WeakRef(jsProxyController);
    } else {
      this._jsProxyController = undefined;
    }
  }

  public get jsProxyController(): TKJSProxyController | undefined {
    return this._jsProxyController?.deref();
  }
}

/**
 *  浏览器对象通知代理的管理中心
 */
export class TKJSNotiProxyManager {
  private registerNotiMap: Map<string, Array<TKJSNotiProxy>> = new Map<string, Array<TKJSNotiProxy>>();
  private static instance: TKJSNotiProxyManager | undefined = undefined;

  public static shareInstance(): TKJSNotiProxyManager {
    if (!TKJSNotiProxyManager.instance) {
      TKJSNotiProxyManager.instance = new TKJSNotiProxyManager();
    }
    return TKJSNotiProxyManager.instance;
  }

  /**
   *  注册通知代理
   * @param notiName   通知名称
   * @param jsCallBack 浏览器对象
   * @param callBackFuncNo  H5回调函数功能号
   */
  public registor(notiName: string, jsProxyController: TKJSProxyController, callbackFuncNo: string) {
    if (jsProxyController && TKStringHelper.isNotBlank(notiName) && TKStringHelper.isNotBlank(callbackFuncNo)) {
      let jsNotiProxys: Array<TKJSNotiProxy> | undefined = TKMapHelper.getObject(this.registerNotiMap, notiName);
      if (!jsNotiProxys) {
        jsNotiProxys = [];
        TKNotificationCenter.defaultCenter.addObserver(this, this.handleNotification, notiName);
      }
      let isHasRegister: boolean = false;
      if (jsNotiProxys && jsNotiProxys.length > 0) {
        let tempJsNotiProxys: Array<TKJSNotiProxy> = [...jsNotiProxys];
        for (let jsNotiProxy of jsNotiProxys) {
          if (jsNotiProxy.jsProxyController) {
            if (jsNotiProxy.jsProxyController == jsProxyController) {
              jsNotiProxy.callbackFuncNo = callbackFuncNo;
              isHasRegister = true;
            }
          } else {
            TKArrayHelper.removeObjectInArray(tempJsNotiProxys, jsNotiProxy);
          }
        }
        jsNotiProxys = tempJsNotiProxys;
      }
      if (!isHasRegister) {
        let jsNotiProxy: TKJSNotiProxy = new TKJSNotiProxy();
        jsNotiProxy.notiName = notiName;
        jsNotiProxy.callbackFuncNo = callbackFuncNo;
        jsNotiProxy.jsProxyController = jsProxyController;
        jsNotiProxys.push(jsNotiProxy);
      }
      this.registerNotiMap.set(notiName, jsNotiProxys);
    }
  }

  /**
   *  卸载通知代理
   * @param notiName   通知名称
   * @param jsCallBack 浏览器对象
   */
  public unRegister(notiName: string, jsProxyController: TKJSProxyController) {
    if (jsProxyController && TKStringHelper.isNotBlank(notiName)) {
      let jsNotiProxys: Array<TKJSNotiProxy> | undefined = TKMapHelper.getObject(this.registerNotiMap, notiName);
      if (jsNotiProxys && jsNotiProxys.length > 0) {
        let tempJsNotiProxys: Array<TKJSNotiProxy> = [...jsNotiProxys];
        for (let jsNotiProxy of jsNotiProxys) {
          if (jsNotiProxy.jsProxyController) {
            if (jsNotiProxy.jsProxyController == jsProxyController) {
              TKArrayHelper.removeObjectInArray(tempJsNotiProxys, jsNotiProxy);
            }
          } else {
            TKArrayHelper.removeObjectInArray(tempJsNotiProxys, jsNotiProxy);
          }
        }
        jsNotiProxys = tempJsNotiProxys;
        this.registerNotiMap.set(notiName, jsNotiProxys);
      }
    }
  }

  /**
   *  接受处理消息
   *
   * @param notification 消息对象
   */
  public handleNotification(notification: TKNotification) {
    let notiName: string = notification.name;
    let notiObject: Object | undefined = notification.obj;
    let jsNotiProxys: Array<TKJSNotiProxy> | undefined = TKMapHelper.getObject(this.registerNotiMap, notiName);
    if (jsNotiProxys && jsNotiProxys.length > 0) {
      let tempJsNotiProxys: Array<TKJSNotiProxy> = [...jsNotiProxys];
      for (let jsNotiProxy of jsNotiProxys) {
        if (jsNotiProxy.jsProxyController) {
          let callbackFuncNo: string = jsNotiProxy.callbackFuncNo;
          let param: Record<string, Object> = (notiObject ?? {}) as Record<string, Object>;
          param["funcNo"] = callbackFuncNo;
          let webViewName: string = TKMapHelper.getString(param, "webViewName");
          if (TKStringHelper.isNotBlank(webViewName)) {
            if (jsNotiProxy.jsProxyController.webControllerName == webViewName) {
              jsNotiProxy.jsProxyController.sendPlugMsgToH5(param);
            }
          } else {
            jsNotiProxy.jsProxyController.sendPlugMsgToH5(param);
          }
        } else {
          TKArrayHelper.removeObjectInArray(tempJsNotiProxys, jsNotiProxy);
        }
      }
      jsNotiProxys = tempJsNotiProxys;
      this.registerNotiMap.set(notiName, jsNotiProxys);
    }
  }
}