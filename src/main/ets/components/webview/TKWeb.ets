/**
 * 子组件的控制属性类
 */
import { TKAttribute } from '../common/attribute/TKAttribute';
import { TKStyleAttribute } from '../common/attribute/TKStyleAttribute';
import { TKPageContainer, TKPageShowHideType } from '../common/TKPageContainer';
import { TKJSProxyController } from './TKJSProxyController';
import { TKWebAttrEventController } from './TKWebAttrEventController';
import { TKComponentContent, TKDialogHelper } from '../../util/ui/TKDialogHelper';
import { TKSystemHelper } from '../../util/system/TKSystemHelper';
import { TKNormalDialogOption } from '../dialog/TKNormalDialog';
import { buildTKWebLoadingDialog, TKWebLoadingDialogOption } from '../dialog/TKWebLoadingDialog';
import { TKLog } from '../../util/logger/TKLog';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKFileHelper } from '../../util/file/TKFileHelper';
import { TKWebHelper } from '../../util/ui/TKWebHelper';
import web_webview from '@ohos.web.webview';
import { TKDataHelper } from '../../util/data/TKDataHelper';
import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { call } from '@kit.TelephonyKit';
import { TKNotificationCenter } from '../../base/notification/TKNotificationCenter';
import { TKBasePlugin } from '../../base/plugin/TKBasePlugin';
import { drawing } from '@kit.ArkGraphics2D';
import { TKColorHelper } from '../../util/ui/TKColorHelper';
import { TKRouterHelper } from '../../base/router/TKRouterHelper';

@Observed
export class TKWebAttribute extends TKAttribute {
  /**
   * 模块名称
   */
  public moduleName?: string = 'TKH5';
  /**
   * 加载超时时间
   */
  public loadTimeout?: number = 60000;
  /**
   * 是否进行URL编码
   */
  public isEncodeURL?: boolean = true;
  /**
   * 是否显示就调用H550113
   */
  public isSupportReInitH5?: boolean = false;
  /**
   * 是否同步浏览器的cookie到原生
   */
  public isForceSyncWebViewCookieToNativeCookie?: boolean = true;
  /**
   * 自定义userAgent
   */
  public customUserAgent?: string = TKSystemHelper.getConfig("webViewPool.userAgent");
  /**
   * 是否展示加载框
   */
  public isShowLoading?: boolean = (TKSystemHelper.getConfig("webViewPool.isShowLoading") === "1");
  /**
   * 是否显示错误框
   */
  public isShowError?: boolean = true;
  /**
   * 是否显示错误返回按钮
   */
  public errorBackPressEnable?: boolean = false;
}

/**
 * 子组件的样式属性类
 */
@Observed
export class TKWebStyleAttribute extends TKStyleAttribute {
  /**
   * 进度条颜色
   */
  public progressColor?: ResourceColor;
  /**
   * 页面加载异常的占位图
   */
  public errorImage?: Resource;
  /**
   * 满屏方式显示的加载进度图
   */
  public fullLoadingImage?: Resource
  /**
   * 页面加载的loding弹框图
   */
  public loadingDialogGif?: Resource;
  /**
   * 页面加载的loding弹框图宽和高
   */
  public loadingDialogGifSize?: Size;
  /**
   * Web背景色
   */
  public backgroundColor?: ResourceColor;
  /**
   * Web透明度
   */
  public opacity?: number = 1.0;
  /**
   * 定义Web组件的渲染方式
   */
  public renderMode?: RenderMode;
  /**
   * Web的自定义布局
   */
  public layoutMode?: WebLayoutMode;
  /**
   * 整体页面缩放百分比，默认100
   */
  public initialScale?: number = 100;
  /**
   * 页面文本缩放百分比，默认100
   */
  public textZoomRatio?: number = 100;
}

/**
 * 基础WebView
 */
@Component
export struct TKWeb {
  /**
   * H5页面加载的url
   */
  @Prop @Watch('onSrcUpdated') src: string = '';
  /**
   * H5页面加载的内容
   */
  @Prop @Watch('onDataUpdated') data: string = '';
  /**
   * 组件控制属性类
   */
  @Prop @Watch('onAttributeUpdated') attribute: TKWebAttribute = new TKWebAttribute();
  /**
   * 子组件的样式属性
   */
  @Prop styleAttribute: TKWebStyleAttribute = new TKWebStyleAttribute();
  /**
   * Web 的属性和事件监听控制器，实现上层对 Web 的属性设置以及事件的回调监听
   */
  @State webAttrEventController: TKWebAttrEventController | undefined = undefined;
  /**
   * 原生与 JS 的交互代理控制器，实现原生与H5的数据交互通道
   */
  @State jSProxyController: TKJSProxyController | undefined = undefined;
  /**
   * 是否显示异常页面
   */
  @State private isShowErrorView: boolean = false;
  /**
   * 是否显示全屏加载页面
   */
  @State private isShowFullLoadingView: boolean = false;
  /**
   * 进度条
   */
  @State private progress: number = 0;
  /**
   * 是否loading
   */
  @State private isLoading: boolean = false;
  /**
   * 页面加载的loding弹框图控制器
   */
  private loadingDialog?: TKComponentContent<TKWebLoadingDialogOption>;

  public build() {
    TKPageContainer({
      page: new WeakRef(this),
      isLayoutFitContent: this.styleAttribute.layoutMode == WebLayoutMode.FIT_CONTENT
    }) {
      Stack() {
        if (this.styleAttribute.progressColor &&
        TKStringHelper.isNotBlank(this.styleAttribute.progressColor.toString()) &&
          this.styleAttribute.progressColor.toString().toUpperCase() != "#00000000" &&
          this.styleAttribute.progressColor.toString().toUpperCase() != "#00FFFFFF" &&
        this.isLoading) {
          Progress({ value: this.progress, total: 100, type: ProgressType.Capsule })
            .width('98%')
            .height(3)
            .position({
              x: '1%',
              y: 0
            })
            .color(this.styleAttribute.progressColor)
            .backgroundColor('#00FFFFFF')
            .borderWidth(0)
            .borderColor('#00FFFFFF')
            .borderRadius(1)
            .visibility((this.progress == 0 || this.progress == 100) ? Visibility.Hidden : Visibility.Visible)
            .zIndex(100)
        }
        Web({
          src: TKStringHelper.isNotBlank(this.jSProxyController?.data) ? "" : this.jSProxyController?.url,
          controller: this.jSProxyController?.webviewController,
          renderMode: this.styleAttribute.renderMode
        })
          .nestedScroll({
            scrollForward: NestedScrollMode.PARENT_FIRST,
            scrollBackward: NestedScrollMode.SELF_FIRST
          })
          .initialScale(this.styleAttribute.initialScale)
          .textZoomRatio(this.styleAttribute.textZoomRatio)
          .layoutMode(this.styleAttribute.layoutMode)
          .backgroundColor(this.styleAttribute.backgroundColor)
          .databaseAccess(true)
          .javaScriptAccess(true)
          .domStorageAccess(true)
          .horizontalScrollBarAccess(false)
          .verticalScrollBarAccess(false)
          .fileAccess(this.jSProxyController?.url.toString().startsWith("file://") ? true : false)
          .opacity(this.styleAttribute.opacity)
          .cacheMode(CacheMode.Default)
          .onAlert((event) => {
            if (event) {
              this.showAlertDialog(event)
            }
            return true
          })
          .onConfirm((event) => {
            if (event) {
              this.showConfirmDialog(event)
            }
            return true
          })
          .onControllerAttached(() => {
            if (this.jSProxyController) {
              this.jSProxyController.isPageInit = true;
            }
            if (TKStringHelper.isNotBlank(this.jSProxyController?.data)) {
              this.jSProxyController?.loadData(this.jSProxyController?.data);
            }
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onControllerAttached) {
              this.webAttrEventController.webAttrEvent.onControllerAttached();
            }
          })
          .onAppear(() => {
            this.jSProxyController?.aboutToAppear();
            this.jSProxyController?.prepareForPageLoad();
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onAppear) {
              this.webAttrEventController.webAttrEvent.onAppear();
            }
          })
          .onDisAppear(() => {
            this.jSProxyController?.aboutToDisappear();
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onDisAppear) {
              this.webAttrEventController.webAttrEvent.onDisAppear();
            }
          })
          .onAreaChange((oldValue: Area, newValue: Area) => {
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onAreaChange) {
              this.webAttrEventController.webAttrEvent.onAreaChange(oldValue, newValue);
            }
          })
          .onVisibleAreaChange([0.0, 1.0], (isVisible: boolean, currentRatio: number) => {
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onVisibleAreaChange) {
              this.webAttrEventController.webAttrEvent.onVisibleAreaChange(isVisible, currentRatio);
            }
          })
          .onOverrideUrlLoading((webResourceRequest: WebResourceRequest) => {
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onOverrideUrlLoading) {
              return this.webAttrEventController.webAttrEvent.onOverrideUrlLoading(webResourceRequest);
            } else {
              return false;
            }
          })
          .onLoadIntercept((event?: OnLoadInterceptEvent) => {
            if (!event) {
              return false;
            }
            //拦截内部逻辑
            let requestUrl: string = event.data.getRequestUrl() ?? "";
            //判断是否是写日志
            if (requestUrl.startsWith("ios-log:")) {
              //log日志
              let temp: Array<string> = requestUrl.split(":#iOS#");
              if (temp && temp.length > 1) {
                let logString: string = temp[1];
                TKLog.info("TKWebView console:" + logString);
              }
              return true;
            }
            // 判断链接是否为拨号链接
            if (requestUrl.startsWith("tel:")) {
              // 跳转拨号界面
              requestUrl = TKStringHelper.replace(requestUrl, "tel:|//", "");
              call.makeCall(requestUrl, (err) => {
                if (!err) {
                  TKLog.info('make call succeeded.');
                } else {
                  TKLog.error('make call fail, err is:' + JSON.stringify(err));
                }
              });
              return true;
            }
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onLoadIntercept) {
              return this.webAttrEventController.webAttrEvent.onLoadIntercept(event);
            }
            return false;
          })
          .onInterceptRequest((event?: OnInterceptRequestEvent) => {
            if (!event) {
              return;
            }
            //拦截内部逻辑
            let requestUrl: string = event.request.getRequestUrl() ?? "";
            if (requestUrl.startsWith(TKWebHelper.getLocalDomain())) {
              let suffix: string = TKWebHelper.getUrlPathSuffix(requestUrl);
              let mimeType: string = TKWebHelper.getMIMEType(suffix);
              if (TKStringHelper.isNotBlank(mimeType)) {
                // 构造响应数据，如果本地文件在rawfile下，可以通过如下方式设置
                let resFile: string = TKWebHelper.getLocalFilePath(requestUrl);
                if (TKFileHelper.isFileExists(resFile)) {
                  let response = new WebResourceResponse();
                  response.setResponseMimeType(mimeType);
                  response.setResponseData(TKWebHelper.getLocalFileData(resFile, mimeType));
                  response.setResponseEncoding('utf-8');
                  response.setResponseCode(200);
                  response.setReasonMessage('OK');
                  response.setResponseIsReady(true);
                  return response;
                } else {
                  let response = new WebResourceResponse();
                  response.setResponseMimeType(mimeType);
                  response.setResponseEncoding('utf-8');
                  response.setResponseCode(404);
                  response.setReasonMessage('404');
                  response.setResponseIsReady(true);
                  return response;
                }
              }
            }
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onInterceptRequest) {
              return this.webAttrEventController.webAttrEvent.onInterceptRequest(event);
            }
            return null;
          })
          .onTitleReceive((event) => {
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onTitleReceive) {
              this.webAttrEventController.webAttrEvent.onTitleReceive(event);
            }
          })
          .onPageBegin((event) => {
            if (this.attribute.isShowError) {
              this.isShowErrorView = false;
            }
            this.showLoadingView();
            this.progress = 0;
            this.isLoading = true;
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPageBegin) {
              this.webAttrEventController.webAttrEvent.onPageBegin(event);
            }
          })
          .onPageEnd((event) => {
            // 注入 JavaScript 代码
            this.rejectAtagLongPress();
            this.hideLoadingView();
            this.progress = 100;
            this.isLoading = false;
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPageEnd) {
              this.webAttrEventController.webAttrEvent.onPageEnd(event);
            }
          })
          .onProgressChange((event) => {
            this.progress = event.newProgress;
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onProgressChange) {
              this.webAttrEventController.webAttrEvent.onProgressChange(event);
            }
          })
          .onPageVisible((event) => {
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPageVisible) {
              this.webAttrEventController.webAttrEvent.onPageVisible(event);
            }
          })
          .onErrorReceive((event) => {
            let error = event?.error;
            let request = event?.request;
            let isMainFrame = request?.isMainFrame() ?? true;
            if (isMainFrame) {
              this.rejectAtagLongPress();
              this.hideLoadingView();
              if (this.attribute.isShowError) {
                this.isShowErrorView = true;
              }
              this.progress = 0;
              this.isLoading = false;
              if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onErrorReceive) {
                this.webAttrEventController.webAttrEvent.onErrorReceive();
              }
            }
          })
          .geolocationAccess(this.webAttrEventController ? this.webAttrEventController.webAttrEvent.geolocationAccess :
            false)
          .onGeolocationShow((event) => {
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onGeolocationShow) {
              this.webAttrEventController.webAttrEvent.onGeolocationShow(event);
            }
          })
          .onPermissionRequest((event) => {
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPermissionRequest) {
              this.webAttrEventController.webAttrEvent.onPermissionRequest(event);
            }
          })
          .onShowFileSelector((event) => {
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onShowFileSelector) {
              return this.webAttrEventController.webAttrEvent.onShowFileSelector(event);
            }
            return true; // 当返回值为true时，用户可以调用系统提供的弹窗能力。当回调返回false时，函数中绘制的自定义弹窗无效
          })
          .onConsole((event) => {
            TKLog.info("H5Console======>" + event.message.getMessage());
            if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onConsole) {
              return this.webAttrEventController.webAttrEvent.onConsole(event);
            }
            return false;
          })
        if (this.isShowErrorView) {
          TKErrorView({
            errorImage: this.styleAttribute.errorImage,
            errorBackPressEnable: this.attribute.errorBackPressEnable,
            onClickEvent: () => {
              this.isShowErrorView = false;
              this.jSProxyController?.refresh();
            }
          })
        }
        if (this.isShowFullLoadingView) {
          TKFullLoadingView({ fullLoadingImage: this.styleAttribute.fullLoadingImage })
        }
      }
    }
  }

  /**
   * 禁用A标签
   */
  private rejectAtagLongPress() {
    this.jSProxyController?.runJavaScript(`
      document.documentElement.style.webkitTouchCallout = 'none';
      document.documentElement.style.webkitUserSelect = 'none';
      document.documentElement.style.userSelect = 'none';
      const links = document.getElementsByTagName('a');
      for (let link of links) {
        link.draggable = false;
      }

      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.addedNodes.length) {
            const links = document.getElementsByTagName('a');
            for (let link of links) {
              link.draggable = false;
            }
          }
        });
      });
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
  `);
  }

  private buildSrc(src: string) {
    if (src.startsWith("www/")) {
      src = src.replace("www/", "");
      src = (TKWebHelper.getLocalDomain() + "/" + src);
    }
    return src;
  }

  /**
   * 初始化连接
   */
  private onSrcUpdated() {
    this.jSProxyController = this.jSProxyController ?? new TKJSProxyController(this.attribute.moduleName);
    this.jSProxyController.url = this.buildSrc(this.src);
  }

  /**
   * 初始化连接
   */
  private onDataUpdated() {
    this.jSProxyController = this.jSProxyController ?? new TKJSProxyController(this.attribute.moduleName);
    this.jSProxyController.data = this.data;
  }

  /**
   * 初始化属性
   */
  private onAttributeUpdated() {
    this.attribute = TKObjectHelper.fixDefault(this.attribute, TKWebAttribute);
    this.styleAttribute = TKObjectHelper.fixDefault(this.styleAttribute, TKWebStyleAttribute);
    this.jSProxyController = this.jSProxyController ?? new TKJSProxyController(this.attribute.moduleName);
    this.jSProxyController.customUserAgent = this.attribute.customUserAgent ?? this.jSProxyController.customUserAgent;
  }

  /**
   * 初始化上下文
   */
  private initContext() {
    this.attribute = TKObjectHelper.fixDefault(this.attribute, TKWebAttribute);
    this.styleAttribute = TKObjectHelper.fixDefault(this.styleAttribute, TKWebStyleAttribute);
    this.jSProxyController = this.jSProxyController ?? new TKJSProxyController(this.attribute.moduleName);
    this.jSProxyController.loadTimeout = this.attribute.loadTimeout ?? this.jSProxyController.loadTimeout;
    this.jSProxyController.isEncodeURL =
      this.attribute.isEncodeURL ?? this.jSProxyController.isEncodeURL;
    this.jSProxyController.isForceSyncWebViewCookieToNativeCookie =
      this.attribute.isForceSyncWebViewCookieToNativeCookie ??
      this.jSProxyController.isForceSyncWebViewCookieToNativeCookie;
    this.jSProxyController.customUserAgent = this.attribute.customUserAgent ?? this.jSProxyController.customUserAgent;
    this.jSProxyController.data = this.data;
    this.jSProxyController.url = this.buildSrc(this.src);
    this.jSProxyController.jsProxyControllerEventDelegate = this;
    this.jSProxyController.webComponent = this;
    web_webview.WebviewController.setWebDebuggingAccess(TKDataHelper.getBoolean(TKSystemHelper.getConfig("webViewPool.inspectable")));
  }

  /**
   * 显示加载界面
   */
  private showLoadingView() {
    if (this.attribute.isShowLoading) {
      this.isShowFullLoadingView = this.styleAttribute.fullLoadingImage !== undefined;
      if (!this.isShowFullLoadingView) {
        if (!this.loadingDialog) {
          this.loadingDialog =
            TKDialogHelper.createCommonDialog<TKWebLoadingDialogOption>(wrapBuilder(buildTKWebLoadingDialog), {
              loadingDialogGif: this.styleAttribute.loadingDialogGif,
              loadingDialogGifSize: this.styleAttribute.loadingDialogGifSize
            } as TKWebLoadingDialogOption);
        }
        this.loadingDialog.open();
      }
    }
  }

  /**
   * 关闭加载页面
   */
  private hideLoadingView() {
    if (this.attribute.isShowLoading) {
      this.isShowFullLoadingView = false;
      if (this.loadingDialog) {
        this.loadingDialog.close();
      }
    }
  }

  public aboutToAppear() {
    this.initContext()
  }

  public onPageShow(type: TKPageShowHideType = TKPageShowHideType.Page): void {
    this.jSProxyController?.onPageShow(type);
    if (this.attribute.isSupportReInitH5 && this.jSProxyController) {
      if (this.jSProxyController.isNeedReInitJSModule) {
        this.jSProxyController.sendInitMsgToH5();
      } else {
        this.jSProxyController.isNeedReInitJSModule = true;
      }
    }
  }

  public onPageHide(type: TKPageShowHideType = TKPageShowHideType.Page): void {
    this.jSProxyController?.onPageHide(type);
  }

  public aboutToDisappear() {
  }

  public onPluginEvent(eventId: string, eventParam?: Record<string, Object>, jsProxyController?: TKJSProxyController) {
    jsProxyController = jsProxyController ?? this.jSProxyController;
    if (eventId == "50123") {
      this.hideLoadingView();
      if (this.attribute.isShowError) {
        this.isShowErrorView = true;
      }
      return;
    }
    if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPluginEvent) {
      this.webAttrEventController.webAttrEvent.onPluginEvent(eventId, eventParam, jsProxyController);
    } else {
      TKNotificationCenter.defaultCenter.postNotificationName(TKBasePlugin.NOTE_PLUGIN_EVENT, jsProxyController,
        eventParam);
    }
  }

  /**
   * 实现H5的告警框
   * @param event
   */
  private showAlertDialog(event: OnAlertEvent) {
    TKDialogHelper.showAlertDialog({
      title: '温馨提示',
      message: event.message as string,
      confirmText: "确定",
      confirm: () => {
        let result = event.result as JsResult
        result.handleConfirm();
      }
    } as TKNormalDialogOption);
  }

  /**
   * 实现H5的告警框
   * @param event
   */
  private showConfirmDialog(event: OnConfirmEvent) {
    TKDialogHelper.showAlertDialog({
      title: '温馨提示',
      message: event.message as string,
      confirmText: "确定",
      cancelText: "取消",
      confirm: () => {
        let result = event.result as JsResult
        result?.handleConfirm();
      },
      cancel: () => {
        let result = event.result as JsResult
        result?.handleCancel();
      }
    } as TKNormalDialogOption);
  }
}

/**
 * 页面加载中的loading图
 */
@Component
struct TKFullLoadingView {
  @State fullLoadingImage?: Resource | undefined = undefined
  public onClickEvent?: () => void = () => {
  }

  public aboutToAppear(): void {
  }

  public build() {
    Column() {
      if (this.fullLoadingImage) {
        Image(this.fullLoadingImage)
          .width('100%')
          .height('100%')
          .objectFit(ImageFit.Contain)
      }
    }
  }
}

/**
 * 页面加载异常的显示图
 */
@Component
struct TKErrorView {
  /**
   * 错误页面
   */
  @State errorImage?: Resource = $r('app.media.tk_framework_load_error');
  /**
   * 是否显示错误返回按钮
   */
  @State errorBackPressEnable?: boolean = false;
  public onClickEvent?: () => void = () => {
  }

  public aboutToAppear(): void {
    this.errorImage = this.errorImage ?? $r('app.media.tk_framework_load_error');
  }

  public build() {
    Stack() {
      if (this.errorBackPressEnable && TKRouterHelper.getRouteStackLength(this) > 1) {
        Row() {
          Image($r('app.media.tk_framework_back_btn'))
            .width(17)
            .height(17)
            .objectFit(ImageFit.Contain)
            .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(TKColorHelper.colorToARGB("#FF000000"),
              drawing.BlendMode.SRC_IN))
            .onClick((event) => {
              TKRouterHelper.back({ navComponentOrPathStack: this });
            })
            .margin({ left: 5 })
          Text("返回")
            .fontColor("#FF000000")
            .fontFamily('PingFang SC')
            .fontSize(19)
            .width(40)
            .textAlign(TextAlign.Start)
            .onClick((event) => {
              TKRouterHelper.back({ navComponentOrPathStack: this });
            })
            .margin({ left: 0 })
        }.zIndex(100)
        .position({ left: 0, top: 0 })
        .width('100%')
        .height(px2vp(160))
      }
      Image(this.errorImage)
        .position({ left: 0, top: 0 })
        .width('100%')
        .height('100%')
        .objectFit(ImageFit.Cover)
        .onClick(() => {
          if (this.onClickEvent) {
            this.onClickEvent()
          }
        })
    }
  }
}