import { TKJSProxyController } from './TKJSProxyController';

export interface TKPageUrlEvent {
  url: string
}

export class TKWebAttrEvent {
  onAppear?: () => void;
  onDisAppear?: () => void;
  onControllerAttached?: () => void;
  onAreaChange?: (oldValue: Area, newValue: Area) => void;
  onVisibleAreaChange?: (isVisible: boolean, currentRatio: number) => void;
  onOverrideUrlLoading?: (webResourceRequest: WebResourceRequest) => boolean;
  onLoadIntercept?: (event?: OnLoadInterceptEvent) => boolean;
  onInterceptRequest?: (event?: OnInterceptRequestEvent) => WebResourceResponse | undefined | null;
  onTitleReceive?: (event?: OnTitleReceiveEvent) => void;
  onPageBegin?: (event?: OnPageBeginEvent) => void;
  onPageEnd?: (event?: OnPageEndEvent) => void;
  onPageLoadFinish?: () => void;
  onProgressChange?: (event?: OnProgressChangeEvent) => void;
  onPageVisible?: (event?: OnPageVisibleEvent) => void;
  onErrorReceive?: () => void;
  geolocationAccess?: boolean = false;
  onGeolocationShow?: (event?: OnGeolocationShowEvent) => void;
  onPermissionRequest?: (event?: OnPermissionRequestEvent) => void;
  onShowFileSelector?: (event?: OnShowFileSelectorEvent) => boolean;
  onConsole?: (event?: OnConsoleEvent) => boolean;
  onPluginEvent?: (eventId: string, eventParam?: Record<string, Object>,
    jsProxyController?: TKJSProxyController) => void;
}

/**
 * Web 属性事件控制器
 */
export class TKWebAttrEventController {
  public static builder(): TKWebAttrEventController {
    return new TKWebAttrEventController();
  }

  // 属性和事件类
  public webAttrEvent: TKWebAttrEvent = new TKWebAttrEvent()

  public onAppear(callback: () => void): TKWebAttrEventController {
    this.webAttrEvent.onAppear = callback;
    return this;
  }

  public onDisAppear(callback: () => void): TKWebAttrEventController {
    this.webAttrEvent.onDisAppear = callback;
    return this;
  }

  public onControllerAttached(callback: () => void): TKWebAttrEventController {
    this.webAttrEvent.onControllerAttached = callback;
    return this;
  }

  public onAreaChange(callback: (oldValue: Area, newValue: Area) => void): TKWebAttrEventController {
    this.webAttrEvent.onAreaChange = callback;
    return this;
  }

  public onVisibleAreaChange(callback: (isVisible: boolean, currentRatio: number) => void): TKWebAttrEventController {
    this.webAttrEvent.onVisibleAreaChange = callback;
    return this;
  }

  public onOverrideUrlLoading(callback: (webResourceRequest: WebResourceRequest) => boolean): TKWebAttrEventController {
    this.webAttrEvent.onOverrideUrlLoading = callback;
    return this;
  }

  public onLoadIntercept(callback: (event?: OnLoadInterceptEvent) => boolean): TKWebAttrEventController {
    this.webAttrEvent.onLoadIntercept = callback;
    return this;
  }

  public onInterceptRequest(callback: (event?: OnInterceptRequestEvent) => WebResourceResponse | undefined | null): TKWebAttrEventController {
    this.webAttrEvent.onInterceptRequest = callback;
    return this;
  }

  public onTitleReceive(callback: (event?: OnTitleReceiveEvent) => void): TKWebAttrEventController {
    this.webAttrEvent.onTitleReceive = callback;
    return this;
  }

  public onPageBegin(callback: (event?: OnPageBeginEvent) => void): TKWebAttrEventController {
    this.webAttrEvent.onPageBegin = callback;
    return this;
  }

  public onPageVisible(callback: (event?: OnPageVisibleEvent) => void): TKWebAttrEventController {
    this.webAttrEvent.onPageVisible = callback;
    return this;
  }

  public onPageEnd(callback: (event?: OnPageEndEvent) => void): TKWebAttrEventController {
    this.webAttrEvent.onPageEnd = callback;
    return this;
  }

  public onPageLoadFinish(callback: () => void): TKWebAttrEventController {
    this.webAttrEvent.onPageLoadFinish = callback;
    return this;
  }

  public onProgressChange(callback: (event?: OnProgressChangeEvent) => void): TKWebAttrEventController {
    this.webAttrEvent.onProgressChange = callback;
    return this;
  }

  public onErrorReceive(callback: () => void): TKWebAttrEventController {
    this.webAttrEvent.onErrorReceive = callback;
    return this;
  }

  public geolocationAccess(geolocationAccess: boolean): TKWebAttrEventController {
    this.webAttrEvent.geolocationAccess = geolocationAccess;
    return this;
  }

  public onGeolocationShow(callback: (event?: OnGeolocationShowEvent) => void): TKWebAttrEventController {
    this.webAttrEvent.onGeolocationShow = callback;
    return this
  }

  public onPermissionRequest(callback: (event?: OnPermissionRequestEvent) => void): TKWebAttrEventController {
    this.webAttrEvent.onPermissionRequest = callback;
    return this;
  }

  public onShowFileSelector(callback: (event?: OnShowFileSelectorEvent) => boolean): TKWebAttrEventController {
    this.webAttrEvent.onShowFileSelector = callback;
    return this;
  }

  public onConsole(callback: (event?: OnConsoleEvent) => boolean): TKWebAttrEventController {
    this.webAttrEvent.onConsole = callback;
    return this;
  }

  public onPluginEvent(callback: (eventId: string,
    eventParam?: Record<string, Object>, jsProxyController?: TKJSProxyController) => void): TKWebAttrEventController {
    this.webAttrEvent.onPluginEvent = callback;
    return this;
  }
}