import { TKUUIDHelper } from '../../util/crypto/TKUUIDHelper';
import { TKLog } from '../../util/logger/TKLog';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKNotification } from '../../base/notification/TKNotification';
import { TKNotificationCenter } from '../../base/notification/TKNotificationCenter';
import { TKJSProxyControllerManager } from './TKJSProxyControllerManager';
import web_webview from '@ohos.web.webview';
import { TKSystemHelper } from '../../util/system/TKSystemHelper';
import { TKCookieManager } from '../../base/mvc/service/dao/http/client/cookie/TKCookieManager';
import { TKResultVO } from '../../base/mvc/model/TKResultVO';
import { TKPluginInvokeCenter } from '../../base/plugin/TKPluginInvokeCenter';
import { TKMapHelper } from '../../util/map/TKMapHelper';
import { TKNetworkListener } from '../../base/network/listener/TKNetworkListener';
import { TKDataHelper } from '../../util/data/TKDataHelper';
import { TKPageShowHideType } from '../common/TKPageContainer';
import { WebHeader } from '@kit.ArkUI';
import { TKObjectHelper } from '../../util/data/TKObjectHelper';

/**
 * TKJSBridege桥接代码
 */
export class TKJSBridege {
  private jsProxyController: TKJSProxyController | undefined = undefined;

  public constructor(jsProxyController: TKJSProxyController) {
    this.jsProxyController = jsProxyController;
  }

  /**
   * H5 调用原生的数据通道
   * @param param
   * @returns
   */
  public callMessage(param: string): string {
    if (this.jsProxyController!.isForceSyncWebViewCookieToNativeCookie ?? true) {
      let url = this.jsProxyController!.url;
      if (typeof url === "string") {
        if (TKStringHelper.startsWith(url, "http://") || TKStringHelper.startsWith(url, "https://")) {
          TKCookieManager.shareInstance().syncWebviewCookieToNative(url);
        }
      }
    }
    let argsJSON: Record<string, Object> = JSON.parse(param);
    if (argsJSON && typeof argsJSON == "object") {
      argsJSON["webControllerName"] = this.jsProxyController!.webControllerName;
      Object.entries(argsJSON).forEach((e, i) => {
        let key: string = e[0];
        let value: Object = e[1];
        if (typeof value == "string" &&
          (TKStringHelper.startsWith(value, "http://") || TKStringHelper.startsWith(value, "https://"))) {
          TKCookieManager.shareInstance().syncWebviewCookieToNative(value.toString());
        }
      });
      let funcNo: string = TKMapHelper.getString(argsJSON, "funcNo");
      let moduleName: string = TKMapHelper.getString(argsJSON, "moduleName");
      let resultVO: TKResultVO = TKPluginInvokeCenter.shareInstance().callPlugin({
        funcNo: funcNo,
        param: argsJSON,
        moduleName: moduleName,
        isH5: true
      });
      let jsonStr: string = resultVO.toJsonStr();
      jsonStr = TKStringHelper.replace(jsonStr, "\\\\/", "/", false);
      return jsonStr;
    } else {
      TKLog.error(`H5调用原生插件参数异常，不是合法的Json格式---->${param}`);
      return "{}";
    }
  }
}

/**
 * WebView事件代理
 */
export interface TKJSProxyControllerEventDelegate {
  onPluginEvent: (eventId: string, eventParam?: Record<string, Object>,
    jsProxyController?: TKJSProxyController) => void;
}

/**
 * 鸿蒙调用JS桥拦截机制
 */
export interface TKHarmonyCallJSFunctionFilter {
  //局部拦截，非思迪webview的场景进行拦截
  onFilter?: (webViewName: string, funcName: string, funcParam?: Record<string, Object> | Array<Object>) => boolean;

  //全局拦截，是否为思迪webview的场景均会进行拦截
  onGlobalFilter?: (webViewName: string, funcName: string,
    funcParam?: Record<string, Object> | Array<Object>) => boolean;
}

/**
 * webview控制代理对象
 */
@Observed
export class TKJSProxyController implements TKJSProxyControllerEventDelegate {
  /**
   * 更新完成通知
   */
  private static readonly NOTE_UPDATE_SUCCESS: string = "note_update_success";
  /**
   * 思迪webviewAgent头
   */
  private static readonly THINKIVE_USERAGENT: string = "thinkive_harmony";
  /**
   * 鸿蒙调用JS拦截器
   */
  public static harmonyCallJSFunctionFilter: TKHarmonyCallJSFunctionFilter | undefined = undefined;
  /**
   * 事件代理
   */
  private _jsProxyControllerEventDelegate: WeakRef<TKJSProxyControllerEventDelegate> | undefined = undefined;
  /**
   * H5是否加载完成
   */
  public isH5LoadFinish: boolean = false;
  /**
   * 加载超时时间
   */
  public loadTimeout: number = 60000;
  /**
   * 是否进行URL编码
   */
  public isEncodeURL: boolean = true;
  /**
   * 是否同步浏览器的cookie到原生
   */
  public isForceSyncWebViewCookieToNativeCookie: boolean = true;
  /**
   * Web加载的链接，可以是一个网络地址，也可以是一个本地html资源
   */
  private _url: string | Resource = '';
  /**
   * Web加载的内容
   */
  private _data: string = '';
  /**
   * 自定义userAgent
   */
  public customUserAgent: string = TKSystemHelper.getConfig("webViewPool.userAgent");
  /**
   * Web的控制器对象
   */
  public webviewController: web_webview.WebviewController | undefined;
  /**
   * WebviewController 缓存KEY
   */
  public webControllerName: string = "";
  /**
   * 页面是否展示
   */
  public isPageShow: boolean = false;
  /**
   * 是否初始化页面
   */
  public isPageInit: boolean = false;
  /**
   * 是否需要初始化H550113
   */
  public isNeedReInitJSModule: boolean = true;
  /**
   * 当前的web对象
   */
  private _webComponent?: WeakRef<CustomComponent>;

  /**
   * 设置Web组件
   * @param component
   */
  public set webComponent(webComponent: CustomComponent | undefined) {
    this._webComponent = webComponent ? new WeakRef(webComponent) : undefined;
  }

  /**
   * 获取Web组件
   */
  public get webComponent(): CustomComponent | undefined {
    return this._webComponent?.deref();
  }

  /**
   * 设置Web代理方法
   * @param jsProxyControllerEventDelegate
   */
  public set jsProxyControllerEventDelegate(jsProxyControllerEventDelegate: TKJSProxyControllerEventDelegate | undefined) {
    if (jsProxyControllerEventDelegate) {
      this._jsProxyControllerEventDelegate = new WeakRef(jsProxyControllerEventDelegate);
    } else {
      this._jsProxyControllerEventDelegate = undefined;
    }
  }

  /**
   * 获取Web代理方法
   * @returns
   */
  public get jsProxyControllerEventDelegate(): TKJSProxyControllerEventDelegate | undefined {
    return this._jsProxyControllerEventDelegate?.deref();
  }

  /**
   * 设置加载URL
   * @param url
   */
  public set url(url: string | Resource) {
    if (typeof url === "string" && !url.startsWith("http://") && !url.startsWith("https://") &&
      !url.startsWith("file://")) {
      this._url = "";
      if (TKStringHelper.isNotBlank(url)) {
        this.data = url;
      }
    } else {
      if (typeof url === "string") {
        this._url = this.isEncodeURL ? encodeURI(url) : url;
      } else {
        this._url = url;
      }
      if (TKStringHelper.isBlank(this.data)) {
        this.loadUrl(this._url as string);
      }
      TKLog.info(`浏览器对象[${this.webControllerName}]加载URL===>${this.url}`);
    }
  }

  /**
   * 获取加载URL
   * @returns
   */
  public get url(): string | Resource {
    return this._url;
  }

  /**
   * 设置加载内容
   * @param data
   */
  public set data(data: string) {
    this._data = data;
    this.loadData(this._data);
  }

  /**
   * 获取加载内容
   * @returns
   */
  public get data(): string {
    return this._data;
  }

  /**************************生命周期**********************************/

  public constructor(moduleName: string = "TKH5",
    webviewController: web_webview.WebviewController = new web_webview.WebviewController()) {
    this.webviewController = webviewController ?? new web_webview.WebviewController();
    this.webControllerName = (TKStringHelper.isNotBlank(moduleName) ? moduleName : "TKH5") + "|" + TKUUIDHelper.uuid();
  }

  /**
   * 设置模块名称
   * @param moduleName
   */
  public setModuleName(moduleName: string) {
    this.webControllerName = (TKStringHelper.isNotBlank(moduleName) ? moduleName : "TKH5") + "|" + TKUUIDHelper.uuid();
  }

  /**
   * 创建webview
   */
  public aboutToAppear() {
    web_webview.WebviewController.setConnectionTimeout(this.loadTimeout == 0 ? 60000 : this.loadTimeout);
    this.webviewController!.registerJavaScriptProxy(new TKJSBridege(this), "external", ["callMessage"]);
    this.buildCustomUserAgent();
    TKJSProxyControllerManager.shareInstance().registor(this);
    this.onRegisterNotification();
  }

  /**
   * 构建自定义UserAgent
   */
  private buildCustomUserAgent() {
    let userAgent: string = this.webviewController!.getUserAgent();
    if (TKStringHelper.isNotBlank(this.customUserAgent)) {
      if (userAgent.indexOf("/" + this.customUserAgent) < 0) {
        userAgent += ("/" + this.customUserAgent);
      }
    }
    if (userAgent.indexOf("/" + TKJSProxyController.THINKIVE_USERAGENT) < 0) {
      userAgent += ("/" + TKJSProxyController.THINKIVE_USERAGENT);
    }
    this.webviewController!.setCustomUserAgent(userAgent);
  }

  /**
   * webview显示
   */
  public onPageShow(type: TKPageShowHideType = TKPageShowHideType.Page) {
    this.isPageShow = true;
    TKJSProxyControllerManager.shareInstance().currentJSProxyControllerName = this.webControllerName;
  }

  /**
   * webview加载完成
   */
  public onPageLoadFinish() {
    this.isH5LoadFinish = true;
  }

  /**
   * webview隐藏
   */
  public onPageHide(type: TKPageShowHideType = TKPageShowHideType.Page) {
    this.isPageShow = false;
  }

  /**
   * webview按键
   */
  public onBackPress() {

  }

  /**
   * weview事件分发
   * @param eventId
   * @param eventParam
   */
  public onPluginEvent(eventId: string, eventParam?: Record<string, Object>, jsProxyController?: TKJSProxyController) {
    if (this.jsProxyControllerEventDelegate) {
      this.jsProxyControllerEventDelegate.onPluginEvent(eventId, eventParam, jsProxyController);
    }
  }

  /**
   * webview销毁
   */
  public aboutToDisappear() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
    TKJSProxyControllerManager.shareInstance().unregistor(this.webControllerName);
    this.webviewController!.deleteJavaScriptRegister("external");
    this.jsProxyControllerEventDelegate = undefined;
    this.webviewController = undefined;
  }

  /***************************通用操作*******************************/

  /**
   * 页面刷新
   */
  public refresh() {
    if (this.isPageInit) {
      if (TKStringHelper.isNotBlank(this.data)) {
        this.loadData(this.data);
      } else {
        this.webviewController?.refresh();
      }
    }
  }

  /**
   * 页面加载
   * @param url
   */
  public loadUrl(url: string | Resource, headers?: Array<WebHeader>) {
    if (this.isPageInit) {
      url = url ?? "";
      headers = headers ?? [];
      this.webviewController?.loadUrl(url, headers);
    }
  }

  /**
   * 数据加载
   * @param data  内容
   * @param mimeType 类型
   * @param encoding 编码
   * @param baseUrl BasePath
   * @param historyUrl
   */
  public loadData(data: string, mimeType?: string, encoding?: string, baseUrl?: string, historyUrl?: string) {
    if (this.isPageInit) {
      data = data ?? this.data ?? "";
      mimeType = mimeType ?? "text/html";
      encoding = encoding ?? "UTF-8";
      baseUrl = baseUrl ?? this.url as string ?? "";
      historyUrl = historyUrl ?? this.url as string ?? "";
      this.webviewController?.loadData(data, mimeType, encoding, baseUrl, historyUrl);
    }
  }

  /**
   * 停止数据加载
   */
  public stop() {
    if (this.isPageInit) {
      this.webviewController?.stop();
    }
  }

  /**
   * 预加载页面
   */
  public prepareForPageLoad() {
    // 指定第二个参数为true，代表要进行预连接，如果为false该接口只会对网址进行dns预解析
    // 第三个参数为要预连接socket的个数。最多允许6个。
    if (typeof this.url === "string") {
      if (TKStringHelper.startsWith(this.url, 'http://') || TKStringHelper.startsWith(this.url, 'https://')) {
        web_webview.WebviewController.prepareForPageLoad(this.url, true, 3);
      }
    }
  }

  /************************处理通知*******************************/

  /**
   *  通知列表
   *
   * @return
   */
  public listNotification(): Array<string> {
    let notes: Array<string> = new Array<string>();
    notes.push(TKJSProxyController.NOTE_UPDATE_SUCCESS);
    notes.push(TKNetworkListener.NOTE_NETWORK_CHANGE);
    return notes;
  }

  /**
   *  注册消息
   */
  public onRegisterNotification() {
    let notes: Array<string> = this.listNotification();
    TKNotificationCenter.defaultCenter.addObservers(this, this.handleNotification, notes);
  }

  /**
   *  处理通知
   *
   * @param notification
   */
  public handleNotification(notification: TKNotification) {
    if (notification.name == TKJSProxyController.NOTE_UPDATE_SUCCESS) {
      this.webviewController!.runJavaScript("window.top.location.href").then((result) => {
        this.isH5LoadFinish = false;
        result = TKStringHelper.replace(result, "\"", "");
        let currentUrl: string = result;
        if (TKStringHelper.isNotBlank(currentUrl)) {
          this.webviewController!.loadUrl(currentUrl);
        } else {
          this.webviewController!.loadUrl(this.url);
        }
        TKLog.info(`H5更新完成，重新刷新加载webview[${this.webControllerName}]`);
      });
    } else if (notification.name == TKNetworkListener.NOTE_NETWORK_CHANGE) {
      let networkAvailable: boolean = TKDataHelper.getBoolean(notification.obj);
      this.webviewController!.setNetworkAvailable(networkAvailable);
    }
  }

  /*********************************调用JS*************************************/

  /**
   * JS调用
   * @param javascript
   * @returns
   */
  public async runJavaScript(javascript: string): Promise<string | undefined> {
    try {
      TKLog.debug(`浏览器对象[${this.webControllerName}:${this.url}]调用JS---->${javascript}`);
      let result: string | undefined = await this.webviewController?.runJavaScript(javascript)
      TKLog.debug(`浏览器对象[${this.webControllerName}:${this.url}]调用JS返回结果---->${result}`);
      return result;
    } catch (error) {
      TKLog.error(`浏览器对象[${this.webControllerName}:${this.url}]调用JS异常:${error.message}!`);
      return undefined;
    }
  }

  /**
   *  原生调用js
   * @param funcName          函数名
   * @param funcParam         参数
   */
  public async harmonyCallJSFunction(funcName: string,
    funcParam?: Record<string, Object> | Array<Object>): Promise<string | undefined> {
    try {
      if (TKJSProxyController.harmonyCallJSFunctionFilter &&
      TKJSProxyController.harmonyCallJSFunctionFilter.onGlobalFilter) {
        if (!TKJSProxyController.harmonyCallJSFunctionFilter.onGlobalFilter(this.webControllerName, funcName,
          funcParam)) {
          return undefined;
        }
      }
      if (TKStringHelper.isBlank(funcName)) {
        TKLog.error(`浏览器对象[${this.webControllerName}:${this.url}]调用的JS函数名称不能为空!`);
        return undefined;
      }
      if (funcName == "callMessage") {
        let isCheckH5: string = TKSystemHelper.getConfig("webViewPool.isCheckH5");
        if (!this.isH5LoadFinish && isCheckH5 == "1") {
          TKLog.error(`浏览器对象[${this.webControllerName}:${this.url}]未通知原生加载完成！`);
          return undefined;
        }
      }
      let javascript: string = funcName + "("
      if (funcParam !== undefined) {
        if (Array.isArray(funcParam)) {
          let funcParams: Array<Object> = funcParam as Array<Object>;
          if (funcParams.length > 0) {
            for (let param of funcParams) {
              let paramStr: string = "";
              if (typeof param === 'number' || typeof param === 'boolean') {
                paramStr = param.toString();
              } else {
                paramStr = TKObjectHelper.toJsonStr(param);
              }
              paramStr = TKStringHelper.replace(paramStr, "\\\\/", "/", false);
              javascript += (paramStr + ",");
            }
            javascript = javascript.substring(0, javascript.length - 1);
          }
        } else {
          let paramStr: string = TKObjectHelper.toJsonStr(funcParam);
          paramStr = TKStringHelper.replace(paramStr, "\r", "", false);
          paramStr = TKStringHelper.replace(paramStr, "\n", "", false);
          paramStr = TKStringHelper.replace(paramStr, "\\r", "", false);
          paramStr = TKStringHelper.replace(paramStr, "\\n", "", false);
          javascript += paramStr;
        }
      }
      javascript += ")";
      //调用JS方法
      TKLog.info(`浏览器对象[${this.webControllerName}:${this.url}]调用JS---->${javascript}`);
      let result: string = await this.webviewController!.runJavaScript(javascript);
      TKLog.info(`浏览器对象[${this.webControllerName}:${this.url}]调用JS返回结果---->${result}`);
      return result;
    } catch (error) {
      TKLog.error(`浏览器对象[${this.webControllerName}:${this.url}]调用JS异常:${error.message}!`);
      return undefined;
    }
  }

  /**
   *  发送重新初始化消息给H5
   */
  public sendInitMsgToH5(pageCode: string = "", param: Record<string, Object> = {}) {
    param = param ?? {};
    param["funcNo"] = "50113";
    if (TKStringHelper.isNotBlank(pageCode)) {
      param["pageCode"] = pageCode;
    }
    this.harmonyCallJSFunction("callMessage", param);
  }

  /**
   * 发送插件消息给H5
   * @param plugId
   * @param content
   */
  public sendPlugMsgToH5(param: Record<string, Object> = {}) {
    param = param ?? {};
    this.harmonyCallJSFunction("callMessage", param);
  }

  /**
   * 发送流水号消息给H5
   * @param data
   * @param flowNo
   */
  public sendFlowNoMsgToH5(flowNo: string | number, param: Record<string, Object> = {}) {
    param = param ?? {};
    this.harmonyCallJSFunction("callMessageByFlowNo", [flowNo, param]);
  }

  /**
   * 发送流水号消息给当前H5(是给50118插件专用的)
   * @param data
   * @param flowNo
   */
  public sendHttpsCallbackToH5(flowNo: string | number, param: Record<string, Object> = {}) {
    param = param ?? {};
    this.harmonyCallJSFunction("httpsCallback", [flowNo, param]);
  }
}