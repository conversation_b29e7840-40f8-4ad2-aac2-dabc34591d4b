import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKJSProxyController } from './TKJSProxyController';

/**
 * 浏览器对象管理器
 */
export class TKJSProxyControllerManager {
  /**
   * 浏览器对象Map
   */
  private jsProxyControllerMap: Map<string, TKJSProxyController> = new Map<string, TKJSProxyController>();
  /**
   * 当前浏览器对象
   */
  public currentJSProxyControllerName: string = "";
  private static instance: TKJSProxyControllerManager | undefined = undefined;

  public static shareInstance(): TKJSProxyControllerManager {
    if (!TKJSProxyControllerManager.instance) {
      TKJSProxyControllerManager.instance = new TKJSProxyControllerManager();
    }
    return TKJSProxyControllerManager.instance;
  }

  /**
   *  当前正在运行浏览器对象
   *
   * @return
   */
  public currentJSProxyController(): TKJSProxyController | undefined {
    if (TKStringHelper.isNotBlank(this.currentJSProxyControllerName)) {
      return this.getJSProxyController(this.currentJSProxyControllerName);
    }
    return undefined;
  }

  /**
   *  返回注册中心的对象
   *
   * @param jsProxyControllerName webview控制器名称
   *
   * @return 运行浏览器对象
   */
  public getJSProxyController(jsProxyControllerName?: string): TKJSProxyController | undefined {
    jsProxyControllerName =
      TKStringHelper.isNotBlank(jsProxyControllerName) ? jsProxyControllerName : this.currentJSProxyControllerName;
    let jsProxyController: TKJSProxyController | undefined = undefined;
    if (TKStringHelper.isNotBlank(jsProxyControllerName)) {
      jsProxyController = this.jsProxyControllerMap.get(jsProxyControllerName!);
      if (!jsProxyController) {
        let keys: Array<string> = Array.from(this.jsProxyControllerMap.keys());
        for (let key of keys) {
          if (TKStringHelper.endsWith(key, "|" + jsProxyControllerName)) {
            jsProxyController = this.jsProxyControllerMap.get(key);
            break;
          }
        }
      }
      if (!jsProxyController) {
        let keys: Array<string> = Array.from(this.jsProxyControllerMap.keys());
        for (let key of keys) {
          if (TKStringHelper.startsWith(key, jsProxyControllerName + "|")) {
            let tempJsProxyController: TKJSProxyController | undefined = this.jsProxyControllerMap.get(key);
            if (tempJsProxyController && (tempJsProxyController.isPageShow ||
              tempJsProxyController.webControllerName == this.currentJSProxyControllerName)) {
              jsProxyController = tempJsProxyController;
              break;
            }
          }
        }
      }
      if (!jsProxyController) {
        let keys: Array<string> = Array.from(this.jsProxyControllerMap.keys());
        for (let key of keys) {
          if (TKStringHelper.endsWith(key, "|" + jsProxyControllerName) ||
          TKStringHelper.startsWith(key, jsProxyControllerName + "|")) {
            jsProxyController = this.jsProxyControllerMap.get(key);
            break;
          }
        }
      }
    }
    return jsProxyController;
  }

  /**
   *  注册浏览器控制器对象
   * @param jsCallBack js对象
   */
  public registor(jsProxyController: TKJSProxyController) {
    this.jsProxyControllerMap.set(jsProxyController.webControllerName, jsProxyController);
  }

  /**
   *  卸载浏览器对象
   *
   * @param jsProxyControllerName webview控制器名称
   */
  public unregistor(jsProxyControllerName: string) {
    let jsProxyController: TKJSProxyController | undefined = this.jsProxyControllerMap.get(jsProxyControllerName);
    if (jsProxyController) {
      this.jsProxyControllerMap.delete(jsProxyControllerName);
    }
  }

  /**
   *  获取所有的浏览器控制象
   *
   * @return
   */
  public getAllJSProxyController(): Array<TKJSProxyController> {
    return Array.from(this.jsProxyControllerMap.values());
  }
}