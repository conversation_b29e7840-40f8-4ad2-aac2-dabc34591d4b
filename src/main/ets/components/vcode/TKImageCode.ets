/**
 * 图形验证码类型
 */
export enum TKImageCodeType {
  Default, //英文+数字
  Alpha, //字母
  Number //数字
}

/**
 * 图片验证码控制器
 */
export class TKImageCodeController {
  //重置验证码
  public resetImageCode: () => void = () => {
  };
}

@Component
export struct TKImageCode {
  //用于接收图形验证码的文本值
  @Link imageCode: string
  @Prop imageCodeType: TKImageCodeType = TKImageCodeType.Default
  //指定canvas要绘制的图形的宽（可使用@Prop装饰器装饰，由调用此组件的父组件传递）
  @State imageWidth: number = 100;
  //指定canvas要绘制的图形的高
  @State imageHeight: number = 40;
  @State bgColor: ResourceColor = '#CCC';
  @State imageCodeController: TKImageCodeController = new TKImageCodeController();
  //英文
  private alphas: Array<string> =
    ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
      'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v',
      'w', 'x', 'y', 'z'];
  //数字
  private numbers: Array<string> = ['1', '2', '3', '4', '5', '6', '7', '8', '9'];
  //用来配置CanvasRenderingContext2D对象的参数，包括是否开启抗锯齿，true表明开启抗锯齿。
  //用来创建CanvasRenderingContext2D对象，通过在canvas中调用CanvasRenderingContext2D对象来绘制。
  private context: CanvasRenderingContext2D = new CanvasRenderingContext2D(new RenderingContextSettings(true));

  aboutToAppear(): void {
    this.imageCodeType = this.imageCodeType ?? TKImageCodeType.Default;
    this.imageWidth = this.imageWidth ?? 100;
    this.imageHeight = this.imageHeight ?? 40;
    this.bgColor = this.bgColor ?? '#CCC';
    this.imageCodeController = this.imageCodeController ?? new TKImageCodeController();
    this.imageCodeController.resetImageCode = () => {
      //在这里绘制图形
      this.imageCode = this.drawImgCode();
    };
  }

  build() {
    //在canvas中调用CanvasRenderingContext2D对象。
    Canvas(this.context)
      .width(this.imageWidth)
      .height(this.imageHeight)
      .backgroundColor(this.bgColor)
      .onReady(() => {
        //在这里绘制图形
        this.imageCode = this.drawImgCode()
      })
      .onClick(() => {
        this.imageCode = this.drawImgCode()
      })
  }

  drawImgCode(): string {
    //用于保存验证码
    let imageCode: string = '';
    //清楚当前画布内容，用作刷新画布
    this.context.clearRect(0, 0, this.imageWidth, this.imageHeight);
    let aCode: Array<string> = [];
    if (this.imageCodeType == TKImageCodeType.Alpha) {
      aCode.push(...this.alphas);
    } else if (this.imageCodeType == TKImageCodeType.Number) {
      aCode.push(...this.numbers);
    } else {
      aCode.push(...this.alphas);
      aCode.push(...this.numbers);
    }
    let aLength: number = aCode.length;
    for (let i = 0; i < 4; i++) { //这里的for循环可以控制验证码位数
      const j: number = Math.floor(Math.random() * aLength); //获取到随机的索引值
      const deg: number = Math.random() - 0.5; //产生一个随机弧度
      const txt: string = aCode[j]; //得到随机的一个内容
      imageCode += txt.toLowerCase(); //转小写
      const x = (this.imageWidth / 10) + (i * this.imageWidth / 5); //文字在canvas上的x坐标
      const y = this.imageHeight / 1.5 + Math.random() * 8; //文字在canvas上的y坐标
      this.context.font = "bold 25vp sans-serif";
      this.context.translate(x, y);
      this.context.rotate(deg);
      this.context.fillStyle = this.getColor();
      this.context.fillText(txt, 0, 0);
      this.context.rotate(-deg);
      this.context.translate(-x, -y);
    }
    for (let i = 0; i <= 20; i++) { //验证码上显示线条
      this.context.strokeStyle = this.getColor();
      this.context.beginPath();
      this.context.moveTo(Math.random() * this.imageWidth, Math.random() * this.imageHeight);
      this.context.lineTo(Math.random() * this.imageWidth, Math.random() * this.imageHeight);
      this.context.stroke();
    }
    for (let i = 0; i <= 50; i++) { //验证码上的小点
      this.context.strokeStyle = this.getColor(); //随机生成
      this.context.beginPath();
      const x = Math.random() * this.imageWidth;
      const y = Math.random() * this.imageHeight;
      this.context.moveTo(x, y);
      this.context.lineTo(x + 1, y + 1);
      this.context.stroke();
    }
    return imageCode
  }

  getColor() {
    const r = Math.floor(Math.random() * 256);
    const g = Math.floor(Math.random() * 156);
    const b = Math.floor(Math.random() * 256);
    return "rgb(" + r + "," + g + "," + b + ")";
  }
}