import { TKNotification } from '../../base/notification/TKNotification';
import { TKNotificationCenter } from '../../base/notification/TKNotificationCenter';
import { TKThemeManager } from '../../base/theme/TKThemeManager';
import { TKUUIDHelper } from '../../util/crypto/TKUUIDHelper';
import { TKLog } from '../../util/logger/TKLog';
import { TKStyleAttribute } from './attribute/TKStyleAttribute';
import { uiObserver } from '@kit.ArkUI';
import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKRouterFilter, TKRouterHelper } from '../../base/router/TKRouterHelper';

/**
 * 页面显示隐藏类型
 */
export enum TKPageShowHideType {
  Page,
  Scroll,
  Other
}

export interface TKPageContainerInterface {
  /**
   * 页面容器展示
   */
  onPageShow?(type?: TKPageShowHideType): void;

  /**
   * 页面容器隐藏
   */
  onPageHide?(type?: TKPageShowHideType): void;
}

/**
 * 基础页面容器类
 */
@Component
export struct TKPageContainer {
  /**
   * 是否根据内容自动撑高
   */
  @State public isLayoutFitContent: boolean = false;
  /**
   * 是否监听路由页面展示或者隐藏拦截
   */
  public isOnRouterPageShowOrHide?: boolean = false;
  /**
   * 页面名称
   */
  public pageName?: string;
  /**
   * 页面信息
   */
  public pageInfo?: Record<string, Object>
  /**
   * 页面容器
   */
  public page?: WeakRef<TKPageContainerInterface>;
  /**
   * 目标样式
   */
  public styleAttribute?: TKStyleAttribute | Array<TKStyleAttribute>;
  /**
   * 页面容器内容构建器
   */
  @BuilderParam public contentBuilder: () => void = this.buildContent;
  /**
   * 主题切换事件
   */
  public onThemeChange?: (theme: string,
    styleAttribute?: TKStyleAttribute | Array<TKStyleAttribute>) => void;
  /**
   * 页面标示
   */
  private pageUUID: string = TKUUIDHelper.uuid();
  /**
   * 是否是页面事件
   */
  private isPageEvent: boolean = false;
  /**
   * 是否滚动事件
   */
  private isScrollEvent: boolean = false;
  /**
   * 页面是否展示
   */
  private isPageShow: boolean = false;
  /**
   * 当前主题
   */
  private theme: string = TKThemeManager.shareInstance().theme;

  @Builder
  public buildContent() {
  }

  public build() {
    Column() {
      this.contentBuilder()
    }
    .width('100%')
    .height(this.isLayoutFitContent ? undefined : '100%')
    .onVisibleAreaChange([0.0, 1.0], (isVisible: boolean, currentRatio: number) => {
      if (this.isPageEvent) {
        this.isPageEvent = false;
        return;
      }
      let pageObj = this.page?.deref();
      let pageShowHideType = this.isScrollEvent ? TKPageShowHideType.Scroll : TKPageShowHideType.Other;
      if (!isVisible) {
        if (this.isPageShow) {
          this.onPageHide(pageShowHideType);
          if (pageObj && pageObj.onPageHide) {
            pageObj.onPageHide(pageShowHideType);
          }
        }
      } else if (isVisible) {
        if (!this.isPageShow) {
          this.onPageShow(pageShowHideType);
          if (pageObj && pageObj.onPageShow) {
            pageObj.onPageShow(pageShowHideType);
          }
        }
      }
      TKLog.debug(`页面容器[${this.pageUUID}]===>isVisible:${isVisible},currentRatio:${currentRatio}`);
    })
    .id(this.pageUUID)
  }

  /**
   *  通知列表
   *
   * @return
   */
  private listNotification(): Array<string> {
    let notes: Array<string> = new Array<string>();
    notes.push(TKThemeManager.NOTE_THEME_CHANGED);
    return notes;
  }

  /**
   *  注册消息
   */
  private onRegisterNotification() {
    let notes: Array<string> = this.listNotification();
    TKNotificationCenter.defaultCenter.addObservers(this, this.handleNotification, notes);
  }

  /**
   * 监听通知
   * @param note
   */
  private handleNotification(note: TKNotification) {
    if (note.name == TKThemeManager.NOTE_THEME_CHANGED) {
      let theme: string = note.obj as string;
      this.freshCss(theme);
    }
  }

  /**
   * 刷新样式
   * @param theme
   */
  private freshCss(theme: string) {
    if (this.isPageShow) {
      if (this.theme != theme) {
        this.theme = theme;
        if (this.onThemeChange) {
          this.onThemeChange(theme, this.styleAttribute);
        } else {
          if (this.styleAttribute) {
            if (Array.isArray(this.styleAttribute)) {
              this.styleAttribute.forEach((styleAttribute: TKStyleAttribute, index: number) => {
                let constructor: FunctionConstructor = styleAttribute.constructor as FunctionConstructor;
                let newStyleAttribute: Object = new constructor();
                TKObjectHelper.assign(styleAttribute as Object, newStyleAttribute as Object);
              });
            } else {
              let constructor: FunctionConstructor = this.styleAttribute.constructor as FunctionConstructor;
              let newStyleAttribute: Object = new constructor();
              TKObjectHelper.assign(this.styleAttribute as Object, newStyleAttribute as Object);
            }
          }
        }
      }
    }
  }

  private onRegisterObserverListener() {
    uiObserver.on('routerPageUpdate', this.getUIContext(), this.handleRouterPageInfoListener);
    uiObserver.on('scrollEvent', this.handleScrollEventListener);
  }

  private unRegisterObserverListener() {
    uiObserver.off('routerPageUpdate', this.getUIContext(), this.handleRouterPageInfoListener);
    uiObserver.off('scrollEvent', this.handleScrollEventListener);
  }

  //监听页面显示隐藏事件
  private handleRouterPageInfoListener: Callback<RouterPageInfo> = async (info: RouterPageInfo) => {
    let curRouterInfo: RouterPageInfo | undefined = this.queryRouterPageInfo();
    if (info.pageId == curRouterInfo?.pageId) {
      this.isPageEvent = true;
      let pageObj = this.page?.deref();
      if (info.state == uiObserver.RouterPageState.ON_PAGE_SHOW) {
        if (!this.isPageShow) {
          this.onPageShow(TKPageShowHideType.Page);
          if (pageObj?.onPageShow) {
            pageObj?.onPageShow(TKPageShowHideType.Page);
          }
        }
      } else if (info.state == uiObserver.RouterPageState.ON_PAGE_HIDE) {
        if (this.isPageShow) {
          this.onPageHide(TKPageShowHideType.Page);
          if (pageObj?.onPageHide) {
            pageObj?.onPageHide(TKPageShowHideType.Page);
          }
        }
      }
    } else {
      this.isPageEvent = false;
    }
  }
  private handleScrollEventListener: Callback<uiObserver.ScrollEventInfo> = (info: uiObserver.ScrollEventInfo) => {
    if (info.scrollEvent == uiObserver.ScrollEventType.SCROLL_START) {
      this.isScrollEvent = true;
    } else if (info.scrollEvent == uiObserver.ScrollEventType.SCROLL_STOP) {
      this.isScrollEvent = false;
    }
  }

  /**
   * 页面容器创建
   */
  public aboutToAppear() {
    this.onRegisterNotification();
  }

  /**
   * 页面容器展示
   */
  public onPageShow(type: TKPageShowHideType = TKPageShowHideType.Page) {
    this.isPageShow = true;
    this.freshCss(TKThemeManager.shareInstance().theme);
    this.onRouterPageShow();
    // if (!this.isPageEvent) {
    //   this.onRegisterObserverListener();
    // }
  }

  /**
   * 页面展示路由拦截触发
   */
  private async onRouterPageShow() {
    if (this.isOnRouterPageShowOrHide) {
      let routerFilters: Array<TKRouterFilter> = TKRouterHelper.getRegisterRouterFilters();
      for (let routerFilter of routerFilters) {
        if (routerFilter.pageShow) {
          let isPass: boolean = await routerFilter.pageShow(this.pageName, this.pageInfo);
          if (!isPass) {
            break;
          }
        }
      }
    }
  }

  /**
   * 页面容器隐藏
   */
  public onPageHide(type: TKPageShowHideType = TKPageShowHideType.Page) {
    this.isPageShow = false;
    this.onRouterPageHide();
    // if (!this.isPageEvent) {
    //   this.unRegisterObserverListener();
    // }
  }

  /**
   * 页面展示路由拦截触发
   */
  private async onRouterPageHide() {
    if (this.isOnRouterPageShowOrHide) {
      let routerFilters: Array<TKRouterFilter> = TKRouterHelper.getRegisterRouterFilters();
      for (let routerFilter of routerFilters) {
        if (routerFilter.pageHide) {
          let isPass: boolean = await routerFilter.pageHide(this.pageName, this.pageInfo);
          if (!isPass) {
            break;
          }
        }
      }
    }
  }
  /*
   * 页面容器销毁
   */
  public aboutToDisappear() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }
}