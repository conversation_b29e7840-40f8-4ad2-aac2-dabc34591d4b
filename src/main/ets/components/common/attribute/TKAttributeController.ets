import { TKObjectHelper } from '../../../util/data/TKObjectHelper';
import { TKAttribute } from './TKAttribute';
import { TKStyleAttribute } from './TKStyleAttribute';

/**
 * 属性控制器
 */
@Observed
export class TKAttributeController<T extends TKAttribute, S extends TKStyleAttribute> {
  protected attribute?: T;
  protected styleAttribute?: S;
  private component?: WeakRef<CustomComponent>;

  public constructor(attribute?: T, styleAttribute?: S, component?: CustomComponent) {
    this.attribute = attribute;
    this.styleAttribute = styleAttribute;
    this.component = component ? new WeakRef(component) : undefined;
  }

  /**
   * 给私有的 attribute 对象提供 set 方法，暴露给外层的不可直接get
   * @param attribute
   */
  public setAttribute(attribute: T, TClass: Function) {
    if (this.attribute) {
      TKObjectHelper.assign(this.attribute, attribute);
    } else {
      let AttributeClass: FunctionConstructor = TClass as FunctionConstructor;
      let initAttribute: T = TKObjectHelper.assign(new AttributeClass() as Object as T, attribute);
      this.attribute = TKObjectHelper.assign(attribute, initAttribute);
    }
  }

  /**
   * 给私有的 stylesAttribute 对象提供 set 方法，暴露给外层的不可直接get
   * @param attribute
   */
  public setStyleAttribute(styleAttribute: S, TClass: Function) {
    if (this.styleAttribute) {
      TKObjectHelper.assign(this.styleAttribute, styleAttribute);
    } else {
      let StyleAttributeClass: FunctionConstructor = TClass as FunctionConstructor;
      let initStyleAttribute: S = TKObjectHelper.assign(new StyleAttributeClass() as Object as S, styleAttribute);
      this.styleAttribute = TKObjectHelper.assign(styleAttribute, initStyleAttribute);
    }
  }

  /**
   * 设置组件
   * @param component
   */
  public setComponent(component: CustomComponent | undefined) {
    this.component = component ? new WeakRef(component) : undefined;
  }

  /**
   * 获取组件
   */
  public getComponent(): CustomComponent | undefined {
    return this.component?.deref();
  }
}
