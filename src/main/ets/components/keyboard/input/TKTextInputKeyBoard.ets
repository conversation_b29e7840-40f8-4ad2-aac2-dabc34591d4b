import { TKKeyboardListener } from '../../../base/device/listener/TKKeyboardListener';
import { TKNotification } from '../../../base/notification/TKNotification';
import { TKNotificationCenter } from '../../../base/notification/TKNotificationCenter';
import { TKObjectHelper } from '../../../util/data/TKObjectHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKKeyBoardEventDelegate, TKKeyBoardType } from '../delegate/TKKeyBoardEventDelegate';
import { TKKeyBoardInputDelegate } from '../delegate/TKKeyBoardInputDelegate';
import { buildTKKeyBoard, TKKeyBoardConfirmConfig, TKKeyBoardOption } from '../view/TKKeyBoard';
import { TKTextInputKeyBoardModifier } from './TKTextInputKeyBoardModifier';

/**
 * 自定义键盘可选性
 */
@Observed
export class TKTextInputKeyBoardOption {
  keyBoardType: string = TKKeyBoardType.Stock;
  controller: TextInputController = new TextInputController();
  inputValue: string = "";
  confirmConfig: TKKeyBoardConfirmConfig = new TKKeyBoardConfirmConfig();
  delegate?: TKKeyBoardInputDelegate;
  caretOffset?: number;
  private _modifier?: TKTextInputKeyBoardModifier;
  isUseSystemKeyBoard?: boolean = false;

  public set modifier(modifier: TKTextInputKeyBoardModifier) {
    this._modifier = modifier;
  }

  public get modifier(): TKTextInputKeyBoardModifier {
    if (!this._modifier) {
      this._modifier = new TKTextInputKeyBoardModifier(this);
    }
    return this._modifier;
  }
}

@Builder
export function buildTKTextInputKeyBoard(options: TKTextInputKeyBoardOption) {
  TKTextInputKeyBoard({ options })
}

/**
 * 输入框自定义键盘
 */
@Component
export struct TKTextInputKeyBoard {
  @Watch("onTextInputKeyBoardOptionChange") @State options: TKTextInputKeyBoardOption = new TKTextInputKeyBoardOption();
  @State keyBoardOption?: TKKeyBoardOption = undefined;

  aboutToAppear(): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKTextInputKeyBoardOption);
    this.options.isUseSystemKeyBoard = false;
    this.options.caretOffset = this.options.controller.getCaretOffset()?.index ?? this.options.inputValue.length;
    this.options.modifier = new TKTextInputKeyBoardModifier(this.options);
    this.keyBoardOption = {
      keyBoardType: this.options.keyBoardType,
      delegate: {
        appendChar: (charStr: string) => {
          if (this.options.delegate?.textFieldShouldChangeCharacters &&
            !this.options.delegate.textFieldShouldChangeCharacters(charStr)) {
            return;
          }
          let controller: TextInputController = this.options.controller;
          let selectionIndex: number = controller.getCaretOffset().index;
          let inputText: string = this.options.inputValue;
          let startText: string = inputText.substring(0, selectionIndex);
          let endText: string = inputText.substring(selectionIndex, inputText.length);
          this.options.caretOffset = selectionIndex + charStr.length;
          this.options.inputValue = startText + charStr + endText;
          this.options.modifier.isCustKeyBoardOper = true;
        },
        deleteChar: () => {
          let controller: TextInputController = this.options.controller;
          let selectionIndex: number = controller.getCaretOffset().index;
          let inputText: string = this.options.inputValue;
          let startText: string = inputText.substring(0, selectionIndex);
          let endText: string = inputText.substring(selectionIndex, inputText.length);
          if (startText.length > 0) {
            startText = startText.substring(0, startText.length - 1);
            this.options.caretOffset = selectionIndex - 1;
            this.options.inputValue = startText + endText;
            this.options.modifier.isCustKeyBoardOper = true;
          }
        },
        clearValue: () => {
          if (TKStringHelper.isNotEmpty(this.options.inputValue)) {
            this.options.caretOffset = 0;
            this.options.inputValue = "";
            this.options.modifier.isCustKeyBoardOper = true;
          }
        },
        doConfirm: () => {
          this.options.controller.stopEditing();
          this.options.delegate?.textFieldConfirm?.();
        },
        hideKeyBoard: () => {
          this.options.controller.stopEditing();
        },
        doForward: () => {
          let controller: TextInputController = this.options.controller;
          let selectionIndex: number = controller.getCaretOffset().index;
          if (selectionIndex < this.options.inputValue.length) {
            controller.caretPosition(selectionIndex + 1);
          }
        },
        doGoBack: () => {
          let controller: TextInputController = this.options.controller;
          let selectionIndex: number = controller.getCaretOffset().index;
          if (selectionIndex > 0) {
            controller.caretPosition(selectionIndex - 1);
          }
        },
        doOtherChar: (charStr: string) => {
          this.options.delegate?.doOtherChar?.(charStr);
        },
        doTitleInput: (content: string) => {
          this.options.delegate?.doTitleInput?.(content);
        },
        changeKeyBoardFinish: (keyBoardType: string) => {
          this.options.delegate?.keyBoardChanged?.(keyBoardType);
        },
        changeSysZHCNKeyBoard: () => {
          this.options.isUseSystemKeyBoard = true;
          this.options.delegate?.changeSysZHCNKeyBoard?.();
        }
      } as TKKeyBoardEventDelegate,
      confirmConfig: this.options.confirmConfig
    };
    this.onKeyboardListener();
  }

  /**
   * 监听键盘展示
   */
  private onKeyboardListener() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
    if (TKKeyboardListener.shareInstance().curTextInputKeyboard) {
      TKNotificationCenter.defaultCenter.removeObserver(TKKeyboardListener.shareInstance()
        .curTextInputKeyboard as Object);
    }
    TKNotificationCenter.defaultCenter.addObserver(this, (notification: TKNotification) => {
      let isShow: boolean = (notification.obj as number) > 0;
      if (this.options.isUseSystemKeyBoard && !isShow) {
        this.options.isUseSystemKeyBoard = false;
        TKNotificationCenter.defaultCenter.removeObserver(this);
      }
    }, TKKeyboardListener.NOTE_KEYBOARD_CHANGE);
    TKKeyboardListener.shareInstance().curTextInputKeyboard = this;
  }

  aboutToDisappear(): void {
  }

  onTextInputKeyBoardOptionChange() {
    if (this.keyBoardOption) {
      this.keyBoardOption.confirmConfig = this.options.confirmConfig;
    }
  }

  build() {
    if (this.keyBoardOption) {
      buildTKKeyBoard(this.keyBoardOption)
    }
  }
}