import { TKTextInputKeyBoardOption } from './TKTextInputKeyBoard';

/**
 * 自定义键盘输入框扩展
 */
export class TKTextInputKeyBoardModifier implements AttributeModifier<TextInputAttribute> {
  public textInputKeyBoardOption?: WeakRef<TKTextInputKeyBoardOption>;
  public isCustKeyBoardOper: boolean = false;

  constructor(textInputKeyBoardOption?: TKTextInputKeyBoardOption) {
    this.textInputKeyBoardOption = textInputKeyBoardOption ? new WeakRef(textInputKeyBoardOption) : undefined;
    this.isCustKeyBoardOper = false;
  }

  applyNormalAttribute(instance: TextInputAttribute): void {
    instance.onChange((value) => {
      if (!this.textInputKeyBoardOption?.deref()?.isUseSystemKeyBoard) {
        if (this.isCustKeyBoardOper) {
          this.isCustKeyBoardOper = false;
        } else {
          this.textInputKeyBoardOption!.deref()!.caretOffset = value.length;
        }
        this.textInputKeyBoardOption?.deref()?.controller.caretPosition(this.textInputKeyBoardOption.deref()?.caretOffset);
      }
      this.textInputKeyBoardOption!.deref()!.inputValue = value;
      this.textInputKeyBoardOption?.deref()?.delegate?.textFieldChange?.(value);
    });
    instance.onWillInsert((insetValue) => {
      if (this.textInputKeyBoardOption?.deref()?.isUseSystemKeyBoard) {
        if (this.textInputKeyBoardOption.deref()?.delegate?.textFieldShouldChangeCharacters) {
          return this.textInputKeyBoardOption.deref()?.delegate?.textFieldShouldChangeCharacters?.(insetValue.insertValue);
        }
      }
      return true;
    });
  }
}