import { TKKeyBoardConfirmConfig } from '../view/TKKeyBoard';
import { TKKeyBoardItem } from '../view/TKKeyBoardItem';
import { TKKeyBoardBaseVO } from './TKKeyBoardBaseVO';
import { TKKeyBoardBoxVO } from './TKKeyBoardBoxVO';

/**
 * 键盘模型配置
 */
@Observed
export class TKKeyBoardVO extends TKKeyBoardBaseVO {
  /**
   * 键盘实现类
   */
  public impClass: string = "";
  /**
   * 键盘类型
   */
  public type: string = "";
  /**
   * 是否支持中英文切换
   */
  public isZHCN: string = "0";
  /**
   * 键盘主题
   */
  public theme: string = "";
  /**
   * 键盘宽度
   */
  public width: number = 0;
  /**
   * 键盘高度
   */
  public height: number = 0;
  /**
   * 键盘高度
   */
  public relHeight: number = 0;
  /**
   * 键盘水平布局间距
   */
  public hSpace: number = 0;
  /**
   * 键盘垂直布局间距
   */
  public vSpace: number = 0;
  /**
   * 键盘背景色
   */
  public bgColor?: ResourceColor;
  /**
   * 键盘标题是否默认隐藏
   */
  public isHideTitle: boolean = false;
  /**
   * 键盘隐藏标题栏后保留的顶部间距
   */
  public hideTitleSpan: number = 0;
  /**
   * 键盘标题
   */
  public title?: TKKeyBoardBoxVO;
  /**
   * 键盘内容
   */
  public content?: TKKeyBoardBoxVO;
  /**
   * 键盘是否防止截屏
   */
  public isNoCutScreen: boolean = false;
  /**
   * 键盘描述
   */
  public desc: string = "";
  /**
   * 备用字段
   */
  public userInfo: Map<string, Object> = new Map();
  /**
   * 配置信息
   */
  public config: Map<string, Object> = new Map();
  /**
   * 输入按键
   */
  public inputKeyBoardItems: Array<TKKeyBoardItem> = new Array();
  /**
   * 所有按钮元素
   */
  public keyBoardItemMap: Map<string, TKKeyBoardItem> = new Map();
  /**
   * 随机数字
   */
  public randNums: Array<string> = new Array();
  /**
   * 确定按钮
   */
  public enterKeyBoardItem?: TKKeyBoardItem;
  /**
   * 确定按钮配置
   */
  private _confirmConfig?: TKKeyBoardConfirmConfig;

  public set confirmConfig(confirmConfig: TKKeyBoardConfirmConfig | undefined) {
    this._confirmConfig = confirmConfig;
    if (this.enterKeyBoardItem) {
      this.enterKeyBoardItem.modifier.confirmConfig = confirmConfig;
    }
  }

  public get confirmConfig(): TKKeyBoardConfirmConfig | undefined {
    return this._confirmConfig;
  }

  public reset() {
    this.inputKeyBoardItems.splice(0, this.inputKeyBoardItems.length);
    this.keyBoardItemMap.clear();
    this.confirmConfig = undefined;
    this.enterKeyBoardItem = undefined;
    this.randNums = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
  }

  public randNum(): string {
    let length: number = this.randNums.length;
    if (length > 0) {
      let index: number = Math.floor(Math.random() * length);
      let result: string = this.randNums[index];
      this.randNums.splice(index, 1);
      return result;
    }
    return "";
  }
}