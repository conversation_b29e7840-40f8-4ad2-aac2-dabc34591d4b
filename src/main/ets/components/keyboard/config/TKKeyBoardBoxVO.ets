import { TKKeyBoardBaseVO } from './TKKeyBoardBaseVO';

export enum TKKeyBoardBoxVOType {
  //水平布局
  HBOX,
  //垂直布局
  VBOX
}

/**
 * 容器模型
 */
@Observed
export class TKKeyBoardBoxVO extends TKKeyBoardBaseVO {
  /**
   * 容器类型
   */
  public type: TKKeyBoardBoxVOType = TKKeyBoardBoxVOType.HBOX;
  /**
   *  容器坐标X
   */
  public left: number = 0;
  /**
   *  容器坐标Y
   */
  public top: number = 0;
  /**
   *  容器宽度
   */
  public width: number = 0;
  /**
   *  容器高度
   */
  public height: number = 0;
  /**
   * 元素布局偏移间距，距离左边，右边，上边，下边之类,水平模式下是左边的偏移量，垂直模式下是上面的偏移量
   */
  public span: number = 0;
  /**
   * 容器间距
   */
  public space: number = 0;
  /**
   * 容器背景色
   */
  public bgColor?: ResourceColor;
  /**
   * 容器边框粗细，用于title和content
   */
  public borderWidth: number = 0;
  /**
   * 容器边框颜色，用于title和content
   */
  public borderColor?: ResourceColor;
  /**
   * 容器按键通用背景色
   */
  public itemBgColor?: ResourceColor;
  /**
   * 容器按键通用文字颜色
   */
  public itemFontColor?: ResourceColor;
  /**
   * 容器按键通用文字大小
   */
  public itemFontSize?: number;
  /**
   * 容器按键通用文字粗细
   */
  public itemFontWeight?: number;
  /**
   * 容器按键通用选中文字大小
   */
  public itemSelectedFontSize?: number;
  /**
   * 容器按键通用选中文字粗细
   */
  public itemSelectedFontWeight?: number;
  /**
   * 容器按键通用高亮背景色
   */
  public itemHighlightBgColor?: ResourceColor;
  /**
   * 容器按键通用选中背景色
   */
  public itemSelectedBgColor?: ResourceColor;
  /**
   * 容器按键通用高亮文字颜色
   */
  public itemHighlightFontColor?: ResourceColor;
  /**
   * 容器按键通用选中文字颜色
   */
  public itemSelectedFontColor?: ResourceColor;
  /**
   * 容器按键通用文字位置
   */
  public itemFontPosition: string = "CENTER";
  /**
   * 容器按键通用按钮弧度
   */
  public itemRadian: number = 0;
  /**
   * 容器按键通用按钮底部阴影弧度
   */
  public itemShadow: number = 0;
  /**
   * 容器按键通用宽度
   */
  public itemWidth: number = 0;
  /**
   * 容器按键通用高度
   */
  public itemHeight: number = 0;
  /**
   * 容器是否需要绘制
   */
  public isNeedDraw: boolean = false
  /**
   * 容器子元素
   */
  public children: Array<Object> = new Array();
  /**
   * 备用字段
   */
  public userInfo: Map<string, Object> = new Map();
}