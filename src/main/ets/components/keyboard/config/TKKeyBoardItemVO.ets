import { TKKeyBoardBaseVO } from './TKKeyBoardBaseVO';

/**
 * 键盘子元素
 */
@Observed
export class TKKeyBoardItemVO extends TKKeyBoardBaseVO {
  /**
   * 元素类型，默认是button
   */
  public type: string = "BUTTON";
  /**
   * 元素模式
   */
  public mode: string = "CONTENT";
  /**
   *  元素坐标X
   */
  public left: number = 0;
  /**
   *  元素坐标Y
   */
  public top: number = 0;
  /**
   * 元素宽度
   */
  public width: number = 0;
  /**
   * 元素高度
   */
  public height: number = 0;
  /**
   * 元素布局偏移间距，距离左边，右边，上边，下边之类,水平模式下是左边的偏移量，垂直模式下是上面的偏移量
   */
  public span: number = 0;
  /**
   * 是否隐藏
   */
  public hidden: boolean = false;
  /**
   * 是否有效
   */
  public enabled: boolean = true;
  /**
   * 元素背景色
   */
  public bgColor?: ResourceColor;
  /**
   * 元素文字颜色
   */
  public curFontColor?: ResourceColor;
  /**
   * 元素文字颜色
   */
  public curFontRangeColor?: ResourceColor;
  /**
   * 元素文字位置
   */
  public curFontPosition: string = "CENTER";
  /**
   * 元素文字大小
   */
  public curFontSize?: number;
  /**
   * 元素文字粗细
   */
  public curFontWeight?: number;
  /**
   * 元素文字颜色
   */
  public fontColor?: ResourceColor;
  /**
   * 元素文字颜色
   */
  public fontRangeColor?: ResourceColor;
  /**
   * 元素文字位置
   */
  public fontPosition: string = "CENTER";
  /**
   * 元素文字大小
   */
  public fontSize?: number;
  /**
   * 元素文字粗细
   */
  public fontWeight?: number;
  /**
   * 元素选中文字大小
   */
  public selectedFontSize?: number;
  /**
   * 元素选中文字粗细
   */
  public selectedFontWeight?: number;
  /**
   * 元素高亮背景色
   */
  public highlightBgColor?: ResourceColor;
  /**
   * 元素选中背景色
   */
  public selectedBgColor?: ResourceColor;
  /**
   * 元素高亮文字颜色
   */
  public highlightFontColor?: ResourceColor;
  /**
   * 元素选中文字颜色
   */
  public selectedFontColor?: ResourceColor;
  /**
   * 元素按钮弧度
   */
  public radian: number = 0;
  /**
   * 元素按钮底部阴影弧度
   */
  public shadow: number = 0;
  /**
   * 元素内容是否是Html格式
   */
  public isHtmlText: boolean = false;
  /**
   * 元素内容
   */
  private _text: string = "";

  public set text(text: string) {
    this._text = text;
  }

  public get text(): string {
    return (this.isUppercase ? this._text?.toUpperCase() : this._text?.toLowerCase()) as string;
  }

  /**
   * 元素选中内容
   */
  public selectedText: string = "";
  /**
   * 元素值
   */
  public value: string = "";
  /**
   * 元素选中值
   */
  public selectedValue: string = "";
  /**
   * 元素背景图片
   */
  public image?: Resource;
  /**
   * 默认填充
   */
  public objectFit?: ImageFit = ImageFit.None;
  /**
   * 元素高亮背景图片
   */
  public highlightImage?: Resource;
  /**
   * 元素选中背景图片
   */
  public selectedImage?: Resource;
  /**
   * 元素提示图片
   */
  public tipImage?: Resource;
  /**
   * 元素提示图片显示位置
   */
  public tipImagePosition?: string;
  /**
   * 元素提示图片宽度
   */
  public tipImageWidth?: number;
  /**
   * 元素提示图片高度
   */
  public tipImageHeight?: number;
  /**
   * 元素提示图片偏移量
   */
  public tipImageSpan?: number;
  /**
   * 元素是否随机数字
   */
  public isRandNum: boolean = false;
  /**
   * 元素下标线的颜色
   */
  public indicatorColor?: ResourceColor;
  /**
   * 元素下标线的宽度
   */
  public indicatorWidth?: number;
  /**
   * 元素下标线的高度
   */
  public indicatorHeight?: number;
  /**
   * 元素动作
   */
  public action: string = "INPUT";
  /**
   * 是否获取光标后自动触发事件
   */
  public isFirstResponderAction: boolean = false;
  /**
   * 元素切换目标键盘类型
   */
  public targetKeyBoard: string = "";
  /**
   * 元素切换默认目标键盘类型
   */
  public targetDefaultKeyBoard: string = "";
  /**
   * 是否大小写
   */
  public isUppercase: boolean = false;
  /**
   * 备用字段
   */
  public userInfo: Map<string, Object> = new Map();
}