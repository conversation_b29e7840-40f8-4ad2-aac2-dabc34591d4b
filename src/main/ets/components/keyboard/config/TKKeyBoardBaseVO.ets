import { TKUUIDHelper } from '../../../util/crypto/TKUUIDHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';

@Observed
export class TKKeyBoardBaseVO {
  /**
   * 唯一标示
   */
  private _tag: string = "";

  public set tag(tag: string) {
    this._tag = tag;
  }

  public get tag(): string {
    if (TKStringHelper.isBlank(this._tag)) {
      this._tag = TKUUIDHelper.uuid();
    }
    return this._tag;
  }
}