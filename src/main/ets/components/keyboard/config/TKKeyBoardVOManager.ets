import { TKCalculatorHelper } from '../../../util/calculator/TKCalculatorHelper';
import { TKObjectHelper } from '../../../util/data/TKObjectHelper';
import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKFileHelper } from '../../../util/file/TKFileHelper';
import { TKXMLHelper } from '../../../util/file/xml/TKXMLHelper';
import { TKFormatHelper } from '../../../util/format/TKFormatHelper';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKKeyBoardBoxVO, TKKeyBoardBoxVOType } from './TKKeyBoardBoxVO';
import { TKKeyBoardItemVO } from './TKKeyBoardItemVO';
import { TKKeyBoardVO } from './TKKeyBoardVO';

/**
 *键盘管理器
 */
export class TKKeyBoardVOManager {
  //实际键盘高度
  public static R_KEYBOARD_HEIGHT: number = 230
  //键盘高度
  public static KEYBOARD_HEIGHT: number = TKKeyBoardVOManager.R_KEYBOARD_HEIGHT + 30;
  //单例对象
  private static instance: TKKeyBoardVOManager | undefined = undefined;
  //键盘配置映射Map
  private keyboardElemMap: Map<string, Array<Map<string, Object>>> = new Map();
  //键盘VO映射Map
  private keyBoardVOMap: Map<string, TKKeyBoardVO> = new Map();
  //计算缓存Map
  private calculatorCacheMap: Map<string, number> = new Map();

  private constructor() {
  }

  public static shareInstance(): TKKeyBoardVOManager {
    if (!TKKeyBoardVOManager.instance) {
      TKKeyBoardVOManager.instance = new TKKeyBoardVOManager();
    }
    return TKKeyBoardVOManager.instance;
  }

  /**
   * 获取键盘文件路径
   * @param keyboardPath
   * @returns
   */
  private getKeyboardPath(keyboardPath: string): string {
    if (TKStringHelper.startsWith(keyboardPath, "@bundle:")) {
      return keyboardPath;
    }
    if (TKStringHelper.startsWith(keyboardPath, "/")) {
      return keyboardPath.substring(1, keyboardPath.length);
    }
    let lastKeyboardPath = `thinkive/config/${TKSystemHelper.getEnvironment()}/keyboard/${keyboardPath}`;
    if (TKFileHelper.readFile(lastKeyboardPath).length <= 0) {
      lastKeyboardPath = `thinkive/config/default/keyboard/${keyboardPath}`;
    }
    return lastKeyboardPath;
  }

  /**
   * 加载配置文件
   */
  public loadConfig() {
    this.keyboardElemMap.clear();
    this.keyBoardVOMap.clear();
    this.calculatorCacheMap.clear();
    if (TKSystemHelper.getConfig("keyborad.isUse", "1") != "0") {
      //加载键盘配置
      let keyboardConfigFiles: string = TKSystemHelper.getConfig("keyborad.path", "KeyBoard.xml");
      let files: Array<string> = TKStringHelper.split(keyboardConfigFiles, "|");
      for (let path of files) {
        let keyboardPath: string = this.getKeyboardPath(path);
        let keyboardsElem: Map<string, Object> = TKXMLHelper.readConfigXml(keyboardPath);
        if (keyboardsElem && keyboardsElem.size > 0) {
          let keyboardElems: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(keyboardsElem, "children");
          if (keyboardElems) {
            for (let keyboardElem of keyboardElems) {
              let keyBoardType: string = TKMapHelper.getString(keyboardElem, "type");
              let keyboardTypeElems: Array<Map<string, Object>> =
                TKMapHelper.getObject(this.keyboardElemMap, keyBoardType, new Array<Map<string, Object>>());
              keyboardTypeElems.push(keyboardElem);
              this.keyboardElemMap.set(keyBoardType, keyboardTypeElems);
            }
          }
        }
      }
    }
  }

  /**
   * 清理键盘对象缓存
   */
  public clearKeyBoardVOMap() {
    this.keyBoardVOMap.clear();
  }

  /**
   *  获取键盘配置对象
   */
  public getKeyBoardVO(keyBoardType: string): TKKeyBoardVO | undefined {
    let keyBoardVO: TKKeyBoardVO = TKMapHelper.getObject(this.keyBoardVOMap, keyBoardType);
    if (!keyBoardVO) {
      let keyboardTypeElems: Array<Map<string, Object>> = TKMapHelper.getObject(this.keyboardElemMap, keyBoardType);
      if (keyboardTypeElems) {
        let keyboardTypeElem: Map<string, Object> | undefined = undefined;
        if (keyboardTypeElems.length == 1) {
          keyboardTypeElem = keyboardTypeElems[0];
        } else {
          for (let keyboardElem of keyboardTypeElems) {
            if (TKMapHelper.getString(keyboardElem, "theme") == TKSystemHelper.getConfig("keyborad.theme", "0")) {
              keyboardTypeElem = keyboardElem;
            }
          }
        }
        if (keyboardTypeElem) {
          keyBoardVO = this.buildKeyBoardVO(keyboardTypeElem);
          this.keyBoardVOMap.set(keyBoardType, keyBoardVO);
        }
      }
    }
    return keyBoardVO;
  }

  /**
   * 构建键盘对象
   * @param keyboardElem
   * @returns
   */
  private buildKeyBoardVO(keyboardElem: Map<string, Object>) {
    let keyboardVO: TKKeyBoardVO = new TKKeyBoardVO();
    let keyboardElemPropMap: Map<string, Object> = TKObjectHelper.assign(new Map<string, Object>(), keyboardElem);
    keyboardElemPropMap.delete("children");
    keyboardElemPropMap.delete("$tag");
    //属性字段
    keyboardVO.userInfo = keyboardElemPropMap;
    //原始配置
    keyboardVO.config = keyboardElem;
    //键盘实现类
    let impClass: string = TKMapHelper.getString(keyboardElem, "impClass", "TKBaseKeyBoardView");
    keyboardVO.impClass = impClass;
    //键盘类型
    let type: string = TKMapHelper.getString(keyboardElem, "type");
    keyboardVO.type = type;
    //是否中英文切换
    let isZHCN: string = TKMapHelper.getString(keyboardElem, "isZHCN");
    keyboardVO.isZHCN = isZHCN;
    //键盘主题
    let theme: string = TKMapHelper.getString(keyboardElem, "theme");
    keyboardVO.theme = theme;
    //键盘宽度
    keyboardVO.width = px2vp(TKDeviceHelper.getScreenWidth());
    //键盘高度
    let height: string = TKMapHelper.getString(keyboardElem, "height");
    keyboardVO.relHeight = TKStringHelper.isNotBlank(height) ? Number(height) : TKKeyBoardVOManager.R_KEYBOARD_HEIGHT;
    keyboardVO.height =
      TKKeyBoardVOManager.KEYBOARD_HEIGHT - TKKeyBoardVOManager.R_KEYBOARD_HEIGHT + keyboardVO.relHeight;
    //键盘行间距
    let hSpace: string = TKMapHelper.getString(keyboardElem, "hSpace");
    keyboardVO.hSpace = this.getConfWH(hSpace, 1.0, keyboardVO);
    //键盘列间距
    let vSpace: string = TKMapHelper.getString(keyboardElem, "vSpace");
    keyboardVO.vSpace = this.getConfWH(vSpace, 1.0, keyboardVO);
    //键盘背景色
    let bgColor: string = TKMapHelper.getString(keyboardElem, "bgColor");
    keyboardVO.bgColor = TKStringHelper.isNotBlank(bgColor) ? bgColor : Color.White;
    //是否防止截屏
    keyboardVO.isNoCutScreen = (TKSystemHelper.getConfig("keyborad.isNoCutScreen", "0") == "1");
    //键盘描述
    let description: string = TKMapHelper.getString(keyboardElem, "description");
    keyboardVO.desc = description;
    //是否默认显示标题
    let isHideTitle: boolean = TKMapHelper.getBoolean(keyboardElem, "isHideTitle");
    keyboardVO.isHideTitle = isHideTitle;
    //键盘隐藏标题栏后保留的顶部间距
    let hideTitleSpan: string = TKMapHelper.getString(keyboardElem, "hideTitleSpan");
    keyboardVO.hideTitleSpan = this.getConfWH(hideTitleSpan, 0, keyboardVO);

    let childrenElems: Array<Map<string, Object>> = TKMapHelper.getObject(keyboardElem, "children");
    for (let childrenElem of childrenElems) {
      let tag: string = TKMapHelper.getString(childrenElem, "$tag");
      if (tag == "title") {
        //解析Title
        keyboardVO.title =
          this.buildKeyBoardBoxVO(childrenElem, TKKeyBoardBoxVOType.HBOX, undefined, undefined, keyboardVO, "TITLE");
        keyboardVO.height = keyboardVO.height + keyboardVO.title.height! + 1;
      } else if (tag == "content") {
        //解析内容
        keyboardVO.content =
          this.buildKeyBoardBoxVO(childrenElem, TKKeyBoardBoxVOType.VBOX, undefined, undefined, keyboardVO, "CONTENT");
        keyboardVO.height = keyboardVO.height + 1;
      }
    }
    return keyboardVO;
  }

  private buildKeyBoardBoxVO(boxElem: Map<string, Object>, boxType: TKKeyBoardBoxVOType,
    prevBoxVO: TKKeyBoardBoxVO | undefined, parentBoxVO: TKKeyBoardBoxVO | undefined,
    keyBoardVO: TKKeyBoardVO, mode: string): TKKeyBoardBoxVO {
    let keyBoardBoxVO: TKKeyBoardBoxVO = new TKKeyBoardBoxVO();
    let boxElemPropMap: Map<string, Object> = TKObjectHelper.assign(new Map<string, Object>(), boxElem);
    boxElemPropMap.delete("children");
    boxElemPropMap.delete("$tag");
    //属性字段
    keyBoardBoxVO.userInfo = boxElemPropMap;
    keyBoardBoxVO.type = boxType;
    //宽度
    let width: string = TKMapHelper.getString(boxElem, "width");
    if (parentBoxVO && parentBoxVO.type == TKKeyBoardBoxVOType.HBOX) {
      keyBoardBoxVO.width =
        this.getConfWH(width, (parentBoxVO ? parentBoxVO.itemWidth! : keyBoardVO.width!), keyBoardVO);
    } else {
      keyBoardBoxVO.width = this.getConfWH(width, (parentBoxVO ? parentBoxVO.width! : keyBoardVO.width!), keyBoardVO);
    }
    //高度
    let height: string = TKMapHelper.getString(boxElem, "height");
    if (parentBoxVO && parentBoxVO.type == TKKeyBoardBoxVOType.VBOX) {
      keyBoardBoxVO.height =
        this.getConfWH(height, (parentBoxVO ? parentBoxVO.itemHeight! : keyBoardVO.relHeight!), keyBoardVO);
    } else {
      keyBoardBoxVO.height =
        this.getConfWH(height, (parentBoxVO ? parentBoxVO.height! : keyBoardVO.relHeight!), keyBoardVO);
    }
    //偏移间距，距离左边，右边，上边，下边之类,水平模式下是左边的偏移量，垂直模式下是上面的偏移量
    let span: string = TKMapHelper.getString(boxElem, "span");
    keyBoardBoxVO.span = this.getConfWH(span, 0, keyBoardVO);
    //X坐标
    let left: number = parentBoxVO ? parentBoxVO.left! : 0;
    if (parentBoxVO) {
      if (parentBoxVO.type == TKKeyBoardBoxVOType.HBOX) {
        if (prevBoxVO) {
          left = prevBoxVO.left! + prevBoxVO.width!;
          left += (keyBoardBoxVO.span > 0) ? keyBoardBoxVO.span! : parentBoxVO.space!;
        } else {
          left += (keyBoardBoxVO.span > 0) ? keyBoardBoxVO.span : 0;
        }
      } else if (parentBoxVO.type == TKKeyBoardBoxVOType.VBOX) {
        left += ((parentBoxVO.width! - keyBoardBoxVO.width!) / 2);
      }
    }
    keyBoardBoxVO.left = left;
    //Y坐标
    let top: number = 0;
    if (parentBoxVO) {
      if (parentBoxVO != keyBoardVO.content && parentBoxVO != keyBoardVO.title) {
        top = parentBoxVO.top!;
      }
      if (parentBoxVO.type == TKKeyBoardBoxVOType.HBOX) {
        top += ((parentBoxVO.height! - keyBoardBoxVO.height) / 2);
      } else if (parentBoxVO.type == TKKeyBoardBoxVOType.VBOX) {
        if (prevBoxVO) {
          top = prevBoxVO.top! + prevBoxVO.height!;
          top += (keyBoardBoxVO.span > 0) ? keyBoardBoxVO.span! : parentBoxVO.space!;
        } else {
          top += (keyBoardBoxVO.span > 0) ? keyBoardBoxVO.span! : 0;
        }
      }
    } else {
      let tag: string = TKMapHelper.getString(boxElem, "$tag");
      if (tag == "content") {
        top = (keyBoardBoxVO.span > 0) ? keyBoardBoxVO.span! : keyBoardVO.vSpace!;
        keyBoardVO.content = keyBoardBoxVO;
      } else if (tag == "title") {
        top = (keyBoardBoxVO.span > 0) ? keyBoardBoxVO.span! : 1.0;
        keyBoardVO.title = keyBoardBoxVO;
      }
    }
    keyBoardBoxVO.top = top;
    //间隔
    let space: string = TKMapHelper.getString(boxElem, "space");
    if (boxType == TKKeyBoardBoxVOType.HBOX) {
      keyBoardBoxVO.space =
        this.getConfWH(space, ((parentBoxVO && parentBoxVO.type == boxType) ? parentBoxVO.space! : keyBoardVO.hSpace!),
          keyBoardVO);
    } else {
      keyBoardBoxVO.space =
        this.getConfWH(space, ((parentBoxVO && parentBoxVO.type == boxType) ? parentBoxVO.space! : keyBoardVO.vSpace!),
          keyBoardVO);
    }
    //标题背景色
    let bgColor: string = TKMapHelper.getString(boxElem, "bgColor");
    keyBoardBoxVO.bgColor =
      TKStringHelper.isNotBlank(bgColor) ? bgColor : (parentBoxVO ? parentBoxVO.bgColor : keyBoardVO.bgColor);
    //边框粗细
    let borderWidth: string = TKMapHelper.getString(boxElem, "borderWidth");
    keyBoardBoxVO.borderWidth = this.getConfWH(borderWidth, 0, keyBoardVO);
    //边框颜色
    let borderColor: string = TKMapHelper.getString(boxElem, "borderColor");
    keyBoardBoxVO.borderColor = TKStringHelper.isNotBlank(borderColor) ? borderColor : keyBoardBoxVO.bgColor;
    //是否需要绘制
    keyBoardBoxVO.isNeedDraw =
      parentBoxVO ? keyBoardBoxVO.bgColor != parentBoxVO.bgColor : keyBoardBoxVO.bgColor != keyBoardVO.bgColor;
    //标题按钮背景颜色
    let itemBgColor: string = TKMapHelper.getString(boxElem, "itemBgColor");
    keyBoardBoxVO.itemBgColor =
      TKStringHelper.isNotBlank(itemBgColor) ? itemBgColor :
        (parentBoxVO ? parentBoxVO.itemBgColor : keyBoardBoxVO.bgColor);
    //标题文字颜色
    let itemFontColor: string = TKMapHelper.getString(boxElem, "itemFontColor");
    keyBoardBoxVO.itemFontColor =
      TKStringHelper.isNotBlank(itemFontColor) ? itemFontColor : (parentBoxVO ? parentBoxVO.itemFontColor : undefined);
    //按钮字体位置
    let itemFontPosition: string = TKMapHelper.getString(boxElem, "itemFontPosition");
    keyBoardBoxVO.itemFontPosition = TKStringHelper.isNotBlank(itemFontPosition) ? itemFontPosition :
      (parentBoxVO ? parentBoxVO.itemFontPosition : "CENTER");
    //按钮文字大小
    let itemFontSize: string = TKMapHelper.getString(boxElem, "itemFontSize");
    keyBoardBoxVO.itemFontSize =
      TKStringHelper.isNotBlank(itemFontSize) ? Number(itemFontSize) : (parentBoxVO ? parentBoxVO.itemFontSize : 16.0);
    //按钮文字粗细
    let itemFontWeight: string = TKMapHelper.getString(boxElem, "itemFontWeight");
    keyBoardBoxVO.itemFontWeight = TKStringHelper.isNotBlank(itemFontWeight) ? Number(itemFontWeight) :
      (parentBoxVO ? parentBoxVO.itemFontWeight : 0);
    //按钮选中文字大小
    let itemSelectedFontSize: string = TKMapHelper.getString(boxElem, "itemSelectedFontSize");
    keyBoardBoxVO.itemSelectedFontSize =
      TKStringHelper.isNotBlank(itemSelectedFontSize) ? Number(itemSelectedFontSize) :
        (parentBoxVO ? parentBoxVO.itemSelectedFontSize : keyBoardBoxVO.itemFontSize);
    //按钮选中文字粗细
    let itemSelectedFontWeight: string = TKMapHelper.getString(boxElem, "itemSelectedFontWeight");
    keyBoardBoxVO.itemSelectedFontWeight =
      TKStringHelper.isNotBlank(itemSelectedFontWeight) ? Number(itemSelectedFontWeight) :
        (parentBoxVO ? parentBoxVO.itemSelectedFontWeight : keyBoardBoxVO.itemFontWeight);
    //按钮高亮背景色
    let itemHighlightBgColor: string = TKMapHelper.getString(boxElem, "itemHighlightBgColor");
    keyBoardBoxVO.itemHighlightBgColor = TKStringHelper.isNotBlank(itemHighlightBgColor) ? itemHighlightBgColor :
      (parentBoxVO ? parentBoxVO.itemHighlightBgColor : keyBoardBoxVO.itemBgColor);
    //按钮选中背景色
    let itemSelectedBgColor: string = TKMapHelper.getString(boxElem, "itemSelectedBgColor");
    keyBoardBoxVO.itemSelectedBgColor = TKStringHelper.isNotBlank(itemSelectedBgColor) ? itemSelectedBgColor :
      (parentBoxVO ? parentBoxVO.itemSelectedBgColor : keyBoardBoxVO.itemBgColor);
    //按钮高亮字体颜色
    let itemHighlightFontColor: string = TKMapHelper.getString(boxElem, "itemHighlightFontColor");
    keyBoardBoxVO.itemHighlightFontColor = TKStringHelper.isNotBlank(itemHighlightFontColor) ? itemHighlightFontColor :
      (parentBoxVO ? parentBoxVO.itemHighlightFontColor : keyBoardBoxVO.itemFontColor);
    //按钮选中字体颜色
    let itemSelectedFontColor: string = TKMapHelper.getString(boxElem, "itemSelectedFontColor");
    keyBoardBoxVO.itemSelectedFontColor = TKStringHelper.isNotBlank(itemSelectedFontColor) ? itemSelectedFontColor :
      (parentBoxVO ? parentBoxVO.itemSelectedFontColor : keyBoardBoxVO.itemFontColor);
    //按钮弧度
    let itemRadian: string = TKMapHelper.getString(boxElem, "itemRadian");
    keyBoardBoxVO.itemRadian =
      TKStringHelper.isNotBlank(itemRadian) ? Number(itemRadian) : (parentBoxVO ? parentBoxVO.itemRadian : 0.0);
    //按钮底部阴影弧度
    let itemShadow: string = TKMapHelper.getString(boxElem, "itemShadow");
    keyBoardBoxVO.itemShadow =
      TKStringHelper.isNotBlank(itemShadow) ? Number(itemShadow) : (parentBoxVO ? parentBoxVO.itemShadow : 0.0);
    //按钮通用宽度
    let itemWidth: string = TKMapHelper.getString(boxElem, "itemWidth");
    keyBoardBoxVO.itemWidth =
      this.getConfWH(itemWidth, (parentBoxVO ? parentBoxVO.itemWidth! : keyBoardBoxVO.width!), keyBoardVO);
    //按钮通用高度
    let itemHeight: string = TKMapHelper.getString(boxElem, "itemHeight");
    keyBoardBoxVO.itemHeight =
      this.getConfWH(itemHeight, (parentBoxVO ? parentBoxVO.itemHeight! : keyBoardBoxVO.height!), keyBoardVO);
    let children: Array<Object> = new Array<Object>();
    let childrenElems: Array<Map<string, Object>> = TKMapHelper.getObject(boxElem, "children");
    let prevColumnBoxVO: TKKeyBoardBoxVO | undefined = undefined;
    let prevRowBoxVO: TKKeyBoardBoxVO | undefined = undefined;
    let prevItemVO: TKKeyBoardItemVO | undefined = undefined;
    for (let childrenElem of childrenElems) {
      let tag: string = TKMapHelper.getString(childrenElem, "$tag");
      if (tag == "column") {
        //判断列布局
        let columnElem: Map<string, Object> = childrenElem;
        let columnBoxVO: TKKeyBoardBoxVO =
          this.buildKeyBoardBoxVO(columnElem, TKKeyBoardBoxVOType.VBOX, prevColumnBoxVO, keyBoardBoxVO, keyBoardVO,
            mode);
        children.push(columnBoxVO);
        prevColumnBoxVO = columnBoxVO;
      } else if (tag == "row") {
        //判断行布局
        let rowElem: Map<string, Object> = childrenElem;
        let rowBoxVO: TKKeyBoardBoxVO =
          this.buildKeyBoardBoxVO(rowElem, TKKeyBoardBoxVOType.HBOX, prevRowBoxVO, keyBoardBoxVO, keyBoardVO, mode);
        children.push(rowBoxVO);
        prevRowBoxVO = rowBoxVO;
      } else if (tag == "item") {
        //判断普通元素
        let itemElem: Map<string, Object> = childrenElem;
        let itemVo: TKKeyBoardItemVO = this.buildKeyBoardItemVO(itemElem, prevItemVO, keyBoardBoxVO, keyBoardVO, mode);
        children.push(itemVo);
        prevItemVO = itemVo;
      }
    }
    keyBoardBoxVO.children = children;
    return keyBoardBoxVO;
  }

  private buildKeyBoardItemVO(itemElem: Map<string, Object>, prevItemVO: TKKeyBoardItemVO | undefined,
    parentBoxVO: TKKeyBoardBoxVO, keyBoardVO: TKKeyBoardVO, mode: string) {
    let keyBoardItemVO: TKKeyBoardItemVO = new TKKeyBoardItemVO();
    keyBoardItemVO.mode = mode;
    //构建元素
    let itemElemPropMap: Map<string, Object> = TKObjectHelper.assign(new Map<string, Object>(), itemElem);
    itemElemPropMap.delete("children");
    itemElemPropMap.delete("$tag");
    //属性字段
    keyBoardItemVO.userInfo = itemElemPropMap;
    //宽度
    let width: string = TKMapHelper.getString(itemElem, "width");
    if (parentBoxVO.type == TKKeyBoardBoxVOType.HBOX) {
      keyBoardItemVO.width = this.getConfWH(width, parentBoxVO.itemWidth!, keyBoardVO);
    } else {
      keyBoardItemVO.width = this.getConfWH(width, parentBoxVO.width!, keyBoardVO);
    }
    //高度
    let height: string = TKMapHelper.getString(itemElem, "height");
    if (parentBoxVO.type == TKKeyBoardBoxVOType.VBOX) {
      keyBoardItemVO.height = this.getConfWH(height, parentBoxVO.itemHeight!, keyBoardVO);
    } else {
      keyBoardItemVO.height = this.getConfWH(height, parentBoxVO.height!, keyBoardVO);
    }
    //偏移间距，距离左边，右边，上边，下边之类,水平模式下是左边的偏移量，垂直模式下是上面的偏移量
    let span: string = TKMapHelper.getString(itemElem, "span");
    keyBoardItemVO.span = this.getConfWH(span, 0, keyBoardVO);
    //X坐标
    let left: number = parentBoxVO.left!;
    if (parentBoxVO.type == TKKeyBoardBoxVOType.HBOX) {
      if (prevItemVO) {
        left = prevItemVO.left! + prevItemVO.width!;
        left += ((keyBoardItemVO.span! > 0) ? keyBoardItemVO.span! : parentBoxVO.space!);
      } else {
        left += ((keyBoardItemVO.span > 0) ? keyBoardItemVO.span! : 0);
      }
    } else {
      left += ((parentBoxVO.width! - keyBoardItemVO.width!) / 2);
    }
    keyBoardItemVO.left = left;
    //Y坐标
    let top: number = (parentBoxVO == keyBoardVO.title || parentBoxVO == keyBoardVO.content) ? 0 : parentBoxVO.top!;
    if (parentBoxVO.type == TKKeyBoardBoxVOType.HBOX) {
      top += ((parentBoxVO.height! - keyBoardItemVO.height!) / 2);
    } else {
      if (prevItemVO) {
        top = prevItemVO.top! + prevItemVO.height!;
        top += ((keyBoardItemVO.span > 0) ? keyBoardItemVO.span! : parentBoxVO.space!);
      } else {
        top += ((keyBoardItemVO.span > 0) ? keyBoardItemVO.span! : 0);
      }
    }
    keyBoardItemVO.top = top;
    let hidden: boolean = TKMapHelper.getBoolean(itemElem, "hidden");
    keyBoardItemVO.hidden = hidden;
    //背景色
    let bgColor: string = TKMapHelper.getString(itemElem, "bgColor");
    keyBoardItemVO.bgColor = TKStringHelper.isNotBlank(bgColor) ? bgColor : parentBoxVO.itemBgColor;
    //文字颜色
    let fontColor: string = TKMapHelper.getString(itemElem, "fontColor");
    keyBoardItemVO.fontColor = TKStringHelper.isNotBlank(fontColor) ? fontColor : parentBoxVO.itemFontColor;
    //文字选中区域颜色
    keyBoardItemVO.fontRangeColor = TKMapHelper.getString(itemElem, "fontRangeColor");
    //字体位置
    let fontPosition: string = TKMapHelper.getString(itemElem, "fontPosition");
    keyBoardItemVO.fontPosition = TKStringHelper.isNotBlank(fontPosition) ? fontPosition : parentBoxVO.itemFontPosition;
    //文字大小
    let fontSize: string = TKMapHelper.getString(itemElem, "fontSize");
    keyBoardItemVO.fontSize = TKStringHelper.isNotBlank(fontSize) ? Number(fontSize) : parentBoxVO.itemFontSize;
    //文字粗细
    let fontWeight: string = TKMapHelper.getString(itemElem, "fontWeight");
    keyBoardItemVO.fontWeight = TKStringHelper.isNotBlank(fontWeight) ? Number(fontWeight) : parentBoxVO.itemFontWeight;
    //文字选中大小
    let selectedFontSize: string = TKMapHelper.getString(itemElem, "selectedFontSize");
    keyBoardItemVO.selectedFontSize =
      (TKStringHelper.isNotBlank(selectedFontSize) ? Number(selectedFontSize) : parentBoxVO.itemSelectedFontSize) ??
      keyBoardItemVO.fontSize;
    //文字选中粗细
    let selectedFontWeight: string = TKMapHelper.getString(itemElem, "selectedFontWeight");
    keyBoardItemVO.selectedFontWeight =
      (TKStringHelper.isNotBlank(selectedFontWeight) ? Number(selectedFontWeight) :
      parentBoxVO.itemSelectedFontWeight) ?? keyBoardItemVO.fontWeight;
    //高亮背景色
    let highlightBgColor: string = TKMapHelper.getString(itemElem, "highlightBgColor");
    keyBoardItemVO.highlightBgColor =
      (TKStringHelper.isNotBlank(highlightBgColor) ? highlightBgColor : parentBoxVO.itemHighlightBgColor) ??
      keyBoardItemVO.bgColor;
    //选中背景色
    let selectedBgColor: string = TKMapHelper.getString(itemElem, "selectedBgColor");
    keyBoardItemVO.selectedBgColor =
      (TKStringHelper.isNotBlank(selectedBgColor) ? selectedBgColor : parentBoxVO.itemSelectedBgColor) ??
      keyBoardItemVO.bgColor;
    //高亮文字颜色
    let highlightFontColor: string = TKMapHelper.getString(itemElem, "highlightFontColor");
    keyBoardItemVO.highlightFontColor =
      (TKStringHelper.isNotBlank(highlightFontColor) ? highlightFontColor : parentBoxVO.itemHighlightFontColor) ??
      keyBoardItemVO.fontColor;
    //选中文字颜色
    let selectedFontColor: string = TKMapHelper.getString(itemElem, "selectedFontColor");
    keyBoardItemVO.selectedFontColor =
      (TKStringHelper.isNotBlank(selectedFontColor) ? selectedFontColor : parentBoxVO.itemSelectedFontColor) ??
      keyBoardItemVO.fontColor;
    //按钮弧度
    let radian: string = TKMapHelper.getString(itemElem, "radian");
    keyBoardItemVO.radian = TKStringHelper.isNotBlank(radian) ? Number(radian) : parentBoxVO.itemRadian;
    //按钮底部阴影弧度
    let shadow: string = TKMapHelper.getString(itemElem, "shadow");
    keyBoardItemVO.shadow = TKStringHelper.isNotBlank(shadow) ? Number(shadow) : parentBoxVO.itemShadow;
    //内容
    keyBoardItemVO.text = TKMapHelper.getString(itemElem, "text");
    //是否Html内容
    keyBoardItemVO.isHtmlText = TKMapHelper.getBoolean(itemElem, "isHtmlText");
    //选中内容
    keyBoardItemVO.selectedText = TKMapHelper.getString(itemElem, "selectedText");
    //值
    keyBoardItemVO.value = TKMapHelper.getString(itemElem, "value");
    //选中值
    keyBoardItemVO.selectedValue = TKMapHelper.getString(itemElem, "selectedValue");
    //图片模式
    let objectFit: string = TKMapHelper.getString(itemElem, "objectFit");
    keyBoardItemVO.objectFit = objectFit == "contain" ? ImageFit.Contain : ImageFit.None;
    //背景图片
    let image: string = TKMapHelper.getString(itemElem, "image");
    keyBoardItemVO.image = TKStringHelper.isNotBlank(image) ? $rawfile(`thinkive/res/image/${image}.png`) : undefined;
    //选中背景图片
    let highlightImage: string = TKMapHelper.getString(itemElem, "highlightImage");
    keyBoardItemVO.highlightImage =
      TKStringHelper.isNotBlank(highlightImage) ? $rawfile(`thinkive/res/image/${highlightImage}.png`) :
      keyBoardItemVO.image;
    //高亮背景图片
    let selectedImage: string = TKMapHelper.getString(itemElem, "selectedImage");
    keyBoardItemVO.selectedImage =
      TKStringHelper.isNotBlank(selectedImage) ? $rawfile(`thinkive/res/image/${selectedImage}.png`) :
      keyBoardItemVO.image
    //提示图片
    let tipImage: string = TKMapHelper.getString(itemElem, "tipImage");
    keyBoardItemVO.tipImage =
      TKStringHelper.isNotBlank(tipImage) ? $rawfile(`thinkive/res/image/${tipImage}.png`) : undefined;
    //提示图片位置
    keyBoardItemVO.tipImagePosition = TKMapHelper.getString(itemElem, "tipImagePosition", "CENTER");
    //元素提示图片宽度
    let tipImageWidth: string = TKMapHelper.getString(itemElem, "tipImageWidth");
    keyBoardItemVO.tipImageWidth = this.getConfWH(tipImageWidth, keyBoardItemVO.width! * 2, keyBoardVO)
    //元素提示图片高度
    let tipImageHeight: string = TKMapHelper.getString(itemElem, "tipImageHeight");
    keyBoardItemVO.tipImageHeight = this.getConfWH(tipImageHeight, 110, keyBoardVO);
    //元素提示图片偏移量
    let tipImageSpan: string = TKMapHelper.getString(itemElem, "tipImageSpan");
    keyBoardItemVO.tipImageSpan = this.getConfWH(tipImageSpan, 0, keyBoardVO);
    //是否随机数字
    keyBoardItemVO.isRandNum = TKMapHelper.getBoolean(itemElem, "isRandNum");
    //元素下标线的颜色
    let indicatorColor: string = TKMapHelper.getString(itemElem, "indicatorColor");
    keyBoardItemVO.indicatorColor = TKStringHelper.isNotBlank(indicatorColor) ? indicatorColor : undefined;
    //元素下标线的宽度
    let indicatorWidth: string = TKMapHelper.getString(itemElem, "indicatorWidth");
    keyBoardItemVO.indicatorWidth = this.getConfWH(indicatorWidth, keyBoardItemVO.width!, keyBoardVO);
    //元素下标线的高度
    let indicatorHeight: string = TKMapHelper.getString(itemElem, "indicatorHeight");
    keyBoardItemVO.indicatorHeight = this.getConfWH(indicatorHeight, 1, keyBoardVO);
    //动作
    keyBoardItemVO.action = TKMapHelper.getString(itemElem, "action", "INPUT");
    //获取光标后是否自动触发动作事件
    let isFirstResponderAction: boolean = TKMapHelper.getBoolean(itemElem, "isFirstResponderAction")
    keyBoardItemVO.isFirstResponderAction = isFirstResponderAction;
    //切换目标键盘类型
    keyBoardItemVO.targetKeyBoard = TKMapHelper.getString(itemElem, "targetKeyBoard");
    //切换默认目标键盘类型
    keyBoardItemVO.targetDefaultKeyBoard = TKMapHelper.getString(itemElem, "targetDefaultKeyBoard");
    //元素类型
    keyBoardItemVO.type = TKMapHelper.getString(itemElem, "type", "BUTTON");
    //元素标示
    keyBoardItemVO.tag = TKMapHelper.getString(itemElem, "tag");
    return keyBoardItemVO;
  }

  private getConfWH(value: string, defaultValue: number, keyBoardVO: TKKeyBoardVO): number {
    if (TKStringHelper.isBlank(value)) {
      return defaultValue;
    }
    if (TKFormatHelper.isNumberFloat(value)) {
      return Number(value);
    }
    value = TKStringHelper.replace(value, "$KBW", `${keyBoardVO.width}`, false);
    value = TKStringHelper.replace(value, "$KBH", `${keyBoardVO.relHeight}`, false);
    let calculatorValue: number = TKMapHelper.getNumber(this.calculatorCacheMap, value, 0);
    if (calculatorValue <= 0) {
      calculatorValue = TKCalculatorHelper.evaluate(value);
      this.calculatorCacheMap.set(value, calculatorValue);
    }
    return calculatorValue;
  }
}