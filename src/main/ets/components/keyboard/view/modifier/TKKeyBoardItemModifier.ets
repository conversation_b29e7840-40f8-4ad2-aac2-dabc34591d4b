import { TKKeyBoardItemVO } from '../../config/TKKeyBoardItemVO';
import { TKKeyBoardConfirmConfig } from '../TKKeyBoard';

export enum TKKeyBoardItemStateStyle {
  NORMAL,
  SELECTED,
  PRESSED
}

/**
 * 键盘按钮
 */
export class TKKeyBoardItemModifier implements AttributeModifier<ButtonAttribute> {
  /**
   * 按钮配置项目
   */
  private _confirmConfig?: TKKeyBoardConfirmConfig;
  /**
   * 按钮对象
   */
  public keyBoardItemVO?: TKKeyBoardItemVO;
  /**
   * 风格 normal selected pressed
   */
  public stateStyle: TKKeyBoardItemStateStyle = TKKeyBoardItemStateStyle.NORMAL;
  /**
   * 是否选中
   */
  public selected: boolean = false;
  /**
   * 键盘是否防止截屏
   */
  public isNoCutScreen: boolean = false;

  constructor(keyBoardItemVO?: TKKeyBoardItemVO, confirmConfig?: TKKeyBoardConfirmConfig) {
    this.keyBoardItemVO = keyBoardItemVO;
    this.confirmConfig = confirmConfig;
  }

  public set confirmConfig(confirmConfig: TKKeyBoardConfirmConfig | undefined) {
    this._confirmConfig = confirmConfig;
    this.resetConfirmConfig();
  }

  public get confirmConfig(): TKKeyBoardConfirmConfig | undefined {
    return this._confirmConfig;
  }

  private resetConfirmConfig() {
    if (this.confirmConfig && this.keyBoardItemVO) {
      this.keyBoardItemVO.fontSize = this.confirmConfig.fontSize ?? this.keyBoardItemVO.fontSize;
      this.keyBoardItemVO.fontColor = this.confirmConfig.textColor ?? this.keyBoardItemVO.fontColor;
      this.keyBoardItemVO.highlightFontColor =
        this.confirmConfig.textColor ?? this.keyBoardItemVO.highlightFontColor;
      this.keyBoardItemVO.bgColor = this.confirmConfig.bgColor ?? this.keyBoardItemVO.bgColor;
      this.keyBoardItemVO.highlightBgColor =
        this.confirmConfig.bgColor ?? this.keyBoardItemVO.highlightBgColor;
      this.keyBoardItemVO.text = this.confirmConfig.text ?? this.keyBoardItemVO.text;
      this.keyBoardItemVO.enabled = this.confirmConfig.enabled ?? this.keyBoardItemVO.enabled;
    }
  }

  applyNormalAttribute(instance: ButtonAttribute): void {
    if (this.keyBoardItemVO) {
      this.keyBoardItemVO.curFontColor = this.keyBoardItemVO.fontColor;
      this.keyBoardItemVO.curFontRangeColor = this.keyBoardItemVO.fontRangeColor;
      this.keyBoardItemVO.curFontPosition = this.keyBoardItemVO.fontPosition;
      this.keyBoardItemVO.curFontSize = this.keyBoardItemVO.fontSize;
      this.keyBoardItemVO.curFontWeight = this.keyBoardItemVO.fontWeight;
      instance.enabled(this.keyBoardItemVO.enabled ?? true);
      instance.position({
        left: this.keyBoardItemVO.left,
        top: this.keyBoardItemVO.top
      })
      instance.width(this.keyBoardItemVO.width);
      instance.height(this.keyBoardItemVO.height);
      instance.visibility((this.keyBoardItemVO.hidden) ? Visibility.Hidden : Visibility.Visible);
      instance.backgroundColor(this.keyBoardItemVO.bgColor);
      instance.fontColor(this.keyBoardItemVO.fontColor);
      instance.fontSize(this.keyBoardItemVO.fontSize);
      instance.fontWeight(this.keyBoardItemVO.fontWeight);
      if (this.keyBoardItemVO.objectFit == ImageFit.None) {
        instance.backgroundImage(this.keyBoardItemVO.image);
      }
      instance.backgroundImagePosition(Alignment.Center);
      instance.borderRadius(this.keyBoardItemVO.radian);
      if (this.keyBoardItemVO.shadow) {
        instance.shadow({
          color: "#40000000",
          radius: 0,
          offsetY: this.keyBoardItemVO.shadow,
          offsetX: this.keyBoardItemVO.shadow
        });
      }
      instance.id(this.keyBoardItemVO.tag);
      if (this.selected) {
        this.applySelectedAttribute(instance);
      }
      if (this.stateStyle == TKKeyBoardItemStateStyle.PRESSED) {
        this.applyPressedAttribute(instance);
      }
    }
  }

  applyPressedAttribute(instance: ButtonAttribute): void {
    if (this.keyBoardItemVO && !this.isNoCutScreen) {
      this.keyBoardItemVO.curFontColor = this.keyBoardItemVO.highlightFontColor;
      instance.backgroundColor(this.keyBoardItemVO.highlightBgColor);
      instance.fontColor(this.keyBoardItemVO.highlightFontColor);
      if (this.keyBoardItemVO.objectFit == ImageFit.None) {
        instance.backgroundImage(this.keyBoardItemVO.highlightImage);
      }
    }
  }

  applyFocusedAttribute(instance: ButtonAttribute): void {

  }

  applyDisabledAttribute(instance: ButtonAttribute): void {

  }

  applySelectedAttribute(instance: ButtonAttribute): void {
    if (this.keyBoardItemVO) {
      this.keyBoardItemVO.curFontColor = this.keyBoardItemVO.selectedFontColor;
      this.keyBoardItemVO.curFontSize = this.keyBoardItemVO.selectedFontSize;
      this.keyBoardItemVO.curFontWeight = this.keyBoardItemVO.selectedFontWeight;
      instance.backgroundColor(this.keyBoardItemVO.selectedBgColor);
      instance.fontColor(this.keyBoardItemVO.selectedFontColor);
      instance.fontSize(this.keyBoardItemVO.selectedFontSize);
      instance.fontWeight(this.keyBoardItemVO.selectedFontWeight);
      if (this.keyBoardItemVO.objectFit == ImageFit.None) {
        instance.backgroundImage(this.keyBoardItemVO.selectedImage);
      }
    }
  }
}