import { TKObjectHelper } from '../../../util/data/TKObjectHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKKeyBoardItemVO } from '../config/TKKeyBoardItemVO';
import { TKKeyBoardVO } from '../config/TKKeyBoardVO';
import { TKKeyBoardItemModifier } from './modifier/TKKeyBoardItemModifier';

/**
 * 按钮操作控制器
 */
export class TKKeyBoardItemController {
  onClick?: (keyBoardItem: TKKeyBoardItem, event: ClickEvent | TouchEvent) => void;
  onTouch?: (keyBoardItem: TKKeyBoardItem, event: TouchEvent) => void;
  onLongPressStart?: (keyBoardItem: TKKeyBoardItem, event: GestureEvent) => void;
  onLongPressEnd?: (keyBoardItem: TKKeyBoardItem, event?: GestureEvent) => void;
}

/**
 * 键盘按钮
 */
@Component
export struct TKKeyBoardItem {
  @State keyBoardVO: TKKeyBoardVO = new TKKeyBoardVO();
  @State keyBoardItemVO: TKKeyBoardItemVO = new TKKeyBoardItemVO();
  @State controller: TKKeyBoardItemController = new TKKeyBoardItemController();
  @State modifier: TKKeyBoardItemModifier = new TKKeyBoardItemModifier();
  @State isShowTip: boolean = false;

  aboutToAppear(): void {
    this.keyBoardItemVO = TKObjectHelper.assign(new TKKeyBoardItemVO(), this.keyBoardItemVO);
    this.modifier = new TKKeyBoardItemModifier(this.keyBoardItemVO);
    if (this.keyBoardItemVO.action == "INPUT") {
      if (this.keyBoardVO.inputKeyBoardItems.indexOf(this) < 0) {
        this.keyBoardVO.inputKeyBoardItems.push(this);
      }
    }
    this.keyBoardVO.keyBoardItemMap.set(this.keyBoardItemVO.tag, this);
    if (this.keyBoardItemVO.action == "ENTER") {
      this.modifier.confirmConfig = this.keyBoardVO.confirmConfig;
      this.keyBoardVO.enterKeyBoardItem = this;
    }
    this.modifier.isNoCutScreen = this.keyBoardVO.isNoCutScreen;
  }

  aboutToDisappear(): void {
  }

  build() {
    if (this.isShowTip) {
      this.buildTip();
    } else {
      this.buildButton();
    }
  }

  @Builder
  buildButton() {
    Button() {
      if (this.keyBoardItemVO.image) {
        if (this.keyBoardItemVO.objectFit != ImageFit.None) {
          Image(this.keyBoardItemVO.image).objectFit(this.keyBoardItemVO.objectFit)
        }
      } else {
        Text((TKStringHelper.isEmpty(this.keyBoardItemVO.text) && this.keyBoardItemVO.isRandNum) ?
          ((this.keyBoardItemVO.text = this.keyBoardVO.randNum()) && this.keyBoardItemVO.text) :
        this.keyBoardItemVO.text).fontColor(this.keyBoardItemVO?.curFontColor)
          .fontSize(this.keyBoardItemVO?.curFontSize)
          .fontWeight(this.keyBoardItemVO?.curFontWeight)
      }
    }
    .type(ButtonType.Normal)
    .stateEffect(this.keyBoardItemVO.mode == "CONTENT" && !this.keyBoardVO.isNoCutScreen)
    .attributeModifier(this.modifier)
    .onClick((event) => {
      if (!this.keyBoardItemVO.tipImage || this.keyBoardVO.isNoCutScreen) {
        this.controller.onClick?.(this, event);
      }
    })
    .onTouch((event) => {
      if (this.keyBoardItemVO.tipImage && !this.keyBoardVO.isNoCutScreen) {
        if (event.type == TouchType.Down) {
          this.isShowTip = true;
        } else if (event.type == TouchType.Up || event.type == TouchType.Cancel) {
          setTimeout(() => {
            this.isShowTip = false;
            this.controller.onClick?.(this, event);
          }, 100)
        }
      } else {
        this.controller.onTouch?.(this, event);
      }
    })
    .gesture(LongPressGesture().onAction((event) => {
      this.controller.onLongPressStart?.(this, event);
    }).onActionEnd((event) => {
      this.controller.onLongPressEnd?.(this, event);
    }).onActionCancel(() => {
      this.controller.onLongPressEnd?.(this);
    }))
  }

  @Builder
  buildTip() {
    Stack() {
      Image(this.keyBoardItemVO.tipImage)
        .position({
          left: 0,
          top: 1
        })
        .width(this.keyBoardItemVO.tipImageWidth!)
        .height(this.keyBoardItemVO.tipImageHeight! + 1)
        .objectFit(ImageFit.Fill)
      Text(this.keyBoardItemVO.text)
        .fontFamily('PingFang SC')
        .fontSize(this.keyBoardItemVO.fontSize! * 1.5)
        .fontColor(this.keyBoardItemVO.highlightFontColor)
        .position({
          left: 0,
          top: 0
        })
        .width(this.keyBoardItemVO.tipImageWidth)
        .height(this.keyBoardItemVO.tipImageHeight! * 0.5)
        .textAlign(TextAlign.Center)
      Text(this.keyBoardItemVO.text)
        .fontFamily('PingFang SC')
        .fontSize(this.keyBoardItemVO.fontSize!)
        .fontColor(this.keyBoardItemVO.highlightFontColor)
        .position({
          left: this.keyBoardItemVO.tipImagePosition == "LEFT" ? this.keyBoardItemVO.tipImageSpan :
            (this.keyBoardItemVO.tipImagePosition == "RIGHT" ? (this.keyBoardItemVO.tipImageWidth! -
              (this.keyBoardItemVO.shadow > 0 ? this.keyBoardItemVO.width :
                (this.keyBoardItemVO.tipImageWidth! - this.keyBoardItemVO.tipImageSpan!))) : 0),
          top: (this.keyBoardItemVO.tipImageHeight! - this.keyBoardItemVO.height + 0.5)
        })
        .width((this.keyBoardItemVO.tipImagePosition == "LEFT" || this.keyBoardItemVO.tipImagePosition == "RIGHT") ?
          (this.keyBoardItemVO.width + 0.5) : this.keyBoardItemVO.tipImageWidth! + 0.5)
        .height(this.keyBoardItemVO.height - 1)
        .textAlign(TextAlign.Center)
    }
    .position({
      left: this.keyBoardItemVO.left - this.keyBoardItemVO.tipImageSpan!,
      top: this.keyBoardItemVO.top - (this.keyBoardItemVO.tipImageHeight! - this.keyBoardItemVO.height)
    })
    .width(this.keyBoardItemVO.tipImageWidth)
    .height(this.keyBoardItemVO.tipImageHeight)
    .backgroundColor("#00FFFFFF")
  }
}

