import { TKFoldDisplayListener } from '../../../base/device/listener/TKFoldDisplayListener';
import { TKNotification } from '../../../base/notification/TKNotification';
import { TKNotificationCenter } from '../../../base/notification/TKNotificationCenter';
import { TKObjectHelper } from '../../../util/data/TKObjectHelper';
import { TKLog } from '../../../util/logger/TKLog';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKTimer } from '../../../util/timer/TKTimer';
import { TKKeyBoardBaseVO } from '../config/TKKeyBoardBaseVO';
import { TKKeyBoardBoxVO } from '../config/TKKeyBoardBoxVO';
import { TKKeyBoardItemVO } from '../config/TKKeyBoardItemVO';
import { TKKeyBoardVO } from '../config/TKKeyBoardVO';
import { TKKeyBoardVOManager } from '../config/TKKeyBoardVOManager';
import { TKKeyBoardEventDelegate, TKKeyBoardType } from '../delegate/TKKeyBoardEventDelegate';
import { TKKeyBoardItemStateStyle } from './modifier/TKKeyBoardItemModifier';
import { TKKeyBoardItem } from './TKKeyBoardItem';
import { display } from '@kit.ArkUI';
import { TKKeyboardListener } from '../../../base/device/listener/TKKeyboardListener';

/**
 * 键盘配置项目
 */
@Observed
export class TKKeyBoardOption {
  keyBoardType?: string = TKKeyBoardType.Stock;
  delegate?: TKKeyBoardEventDelegate = {} as TKKeyBoardEventDelegate;
  confirmConfig?: TKKeyBoardConfirmConfig = new TKKeyBoardConfirmConfig();
  isDisappear?: boolean = false;
}

@Builder
export function buildTKKeyBoard(options: TKKeyBoardOption) {
  TKKeyBoard({ options })
}

/**
 *  键盘确认按钮的相关配置
 */
@Observed
export class TKKeyBoardConfirmConfig {
  /**
   *  确定按钮是否可用
   */
  enabled?: boolean;
  /**
   *  确定按钮内容
   */
  text?: string;
  /**
   *  确定按钮背景色
   */
  bgColor?: ResourceColor;
  /**
   *  确定按文字景色
   */
  textColor?: ResourceColor;
  /**
   *  确定按钮文字大小
   */
  fontSize?: number;
  /**
   * 是否隐藏标题栏
   */
  isHideTitle?: boolean;
}

/**
 * 键盘
 */
@Component
export struct TKKeyBoard {
  @State @Watch("onKeyBoardOptionChange") options: TKKeyBoardOption = new TKKeyBoardOption();
  @State private confirmConfig: TKKeyBoardConfirmConfig = new TKKeyBoardConfirmConfig();
  @State private keyBoardVO: TKKeyBoardVO = new TKKeyBoardVO();
  @State private isFoldExpanded: boolean = false;
  @State private isRebuild: boolean = false;
  private keyBoardType: string = "";
  private lastKeyBoardType: string = "";
  private delegate?: TKKeyBoardEventDelegate;
  private deleteKeyTimer?: TKTimer;
  private forwardKeyTimer?: TKTimer;
  private goBackKeyTimer?: TKTimer;

  aboutToAppear(options?: TKKeyBoardOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKKeyBoardOption, options);
    this.delegate = this.options?.delegate ?? this.delegate;
    this.keyBoardType = this.options.keyBoardType ?? TKKeyBoardType.Stock;
    this.confirmConfig = this.options.confirmConfig ?? {};
    this.reset();
    this.isFoldExpanded =
      TKFoldDisplayListener.shareInstance().getFoldStatus() == display.FoldStatus.FOLD_STATUS_EXPANDED ||
        TKFoldDisplayListener.shareInstance().getFoldStatus() == display.FoldStatus.FOLD_STATUS_HALF_FOLDED;
    this.onFoldDisplayListener();
    TKKeyboardListener.shareInstance().curCustomerKeyboard = this;
  }

  onKeyBoardOptionChange() {
    this.confirmConfig = this.options.confirmConfig ?? this.confirmConfig;
    if (this.keyBoardVO.confirmConfig != this.confirmConfig) {
      this.keyBoardVO.confirmConfig = this.confirmConfig;
    }
  }

  private reset() {
    this.keyBoardVO = TKKeyBoardVOManager.shareInstance().getKeyBoardVO(this.keyBoardType) ?? new TKKeyBoardVO();
    this.keyBoardVO.reset();
    this.keyBoardVO.confirmConfig = this.confirmConfig;
    this.deleteKeyTimer?.stop();
    this.deleteKeyTimer = new TKTimer(80, this, () => {
      TKLog.debug(`deleteChar---->longpush`);
      this.delegate?.deleteChar?.();
    });
    this.forwardKeyTimer?.stop();
    this.forwardKeyTimer = new TKTimer(80, this, () => {
      TKLog.debug(`doForward---->longpush`);
      this.delegate?.doForward?.();

    });
    this.goBackKeyTimer?.stop();
    this.goBackKeyTimer = new TKTimer(80, this, () => {
      TKLog.debug(`doGoBack---->longpush`);
      this.delegate?.doGoBack?.();
    });
    this.isRebuild = !this.isRebuild;
    this.showKeyboard();
  }

  /**
   * 监听折叠屏
   */
  private onFoldDisplayListener() {
    TKNotificationCenter.defaultCenter.addObserver(this, async (notification: TKNotification) => {
      setTimeout(() => {
        let isFoldExpanded = notification.obj == display.FoldStatus.FOLD_STATUS_EXPANDED ||
          notification.obj == display.FoldStatus.FOLD_STATUS_HALF_FOLDED;
        if (this.isFoldExpanded != isFoldExpanded) {
          this.isFoldExpanded = isFoldExpanded;
          TKKeyBoardVOManager.shareInstance().clearKeyBoardVOMap();
          this.reset();
        }
      }, 100);
    }, TKFoldDisplayListener.NOTE_FOLD_CHANGE)
  }

  /**
   * 取消监听折叠屏
   */
  private offFoldDisplayListener() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }

  /**
   * 键盘展示
   */
  private showKeyboard() {
    let keyboardHeight: number = this.keyBoardVO.height - (this.keyBoardVO.title ?
      ((!this.keyBoardVO.isHideTitle && !this.confirmConfig.isHideTitle) ? 0 : this.keyBoardVO.title.height) : 0);
    TKKeyboardListener.shareInstance().postKeyboardChangeEvent(vp2px(keyboardHeight));
  }

  /**
   * 键盘隐藏
   */
  private hideKeyboard() {
    if (TKKeyboardListener.shareInstance().curCustomerKeyboard == this &&
      !TKKeyboardListener.shareInstance().isSysKeyboardShow) {
      TKKeyboardListener.shareInstance().curCustomerKeyboard = undefined;
      TKKeyboardListener.shareInstance().postKeyboardChangeEvent(0);
    }
  }

  aboutToDisappear(): void {
    this.keyBoardVO?.reset();
    this.deleteKeyTimer?.stop();
    this.forwardKeyTimer?.stop();
    this.goBackKeyTimer?.stop();
    this.offFoldDisplayListener();
    this.hideKeyboard();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKKeyBoardOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  build() {
    if (!this.isRebuild) {
      this.buildKeyBoar()
    } else {
      this.buildKeyBoar()
    }
  }

  @Builder
  buildKeyBoar() {
    Column() {
      if (this.keyBoardVO.title) {
        if (!this.keyBoardVO.isHideTitle && !this.confirmConfig.isHideTitle) {
          Stack() {
            this.buildKeyBoardBox(this.keyBoardVO.title, this.keyBoardVO, undefined, 0)
          }.width(this.keyBoardVO.title.width)
          .height(this.keyBoardVO.title.height + 1)
        } else {
          Row().width(this.keyBoardVO.title.width)
            .height(this.keyBoardVO.hideTitleSpan > 0 ? this.keyBoardVO.hideTitleSpan : this.keyBoardVO.vSpace)
        }
      }
      if (this.keyBoardVO.content) {
        Stack() {
          this.buildKeyBoardBox(this.keyBoardVO.content, this.keyBoardVO, undefined, 1)
        }.width(this.keyBoardVO.content.width)
        .height(this.keyBoardVO.content.height + 1)
      }
    }.width(this.keyBoardVO.width)
    .height(this.keyBoardVO.height - (this.keyBoardVO.title ?
      ((!this.keyBoardVO.isHideTitle && !this.confirmConfig.isHideTitle) ? 0 : this.keyBoardVO.title.height) : 0))
    .backgroundColor(this.keyBoardVO.bgColor)
  }

  @Builder
  buildKeyBoardBox(keyBoardBoxVO: TKKeyBoardBoxVO, keyBoardVO: TKKeyBoardVO, parentKeyBoardBoxVO?: TKKeyBoardBoxVO,
    mode: number = 0) {
    if (!parentKeyBoardBoxVO || (keyBoardBoxVO.isNeedDraw && keyBoardBoxVO.bgColor)) {
      Row() {
      }
      .backgroundColor(keyBoardBoxVO.bgColor)
      .position({
        left: keyBoardBoxVO.left,
        top: keyBoardBoxVO.top
      })
      .width(keyBoardBoxVO.width)
      .height(keyBoardBoxVO.height)
      .borderWidth(keyBoardBoxVO.borderWidth)
      .borderColor(keyBoardBoxVO.borderColor)
    }
    if (!parentKeyBoardBoxVO && mode == 0) {
      Row() {
      }
      .backgroundColor(keyBoardVO.bgColor == "#ffffff" ? "#BEBEBE" : keyBoardVO.bgColor)
      .position({
        left: keyBoardBoxVO.left,
        top: 0
      })
      .width(keyBoardBoxVO.width)
      .height(keyBoardBoxVO.top)
    }
    if (keyBoardBoxVO.children && keyBoardBoxVO.children.length > 0) {
      ForEach(keyBoardBoxVO.children, (childItemVO: TKKeyBoardBaseVO, index: number) => {
        if (childItemVO instanceof TKKeyBoardBoxVO) {
          this.buildKeyBoardBox(childItemVO as TKKeyBoardBoxVO, keyBoardVO, keyBoardBoxVO, mode)
        } else {
          if ((childItemVO as TKKeyBoardItemVO).type == "BUTTON") {
            this.buildKeyBoardContentItem(childItemVO as TKKeyBoardItemVO, keyBoardVO)
          }
        }
      }, (childItemVO: TKKeyBoardBaseVO) => childItemVO.tag)
    }
  }

  @Builder
  buildKeyBoardContentItem(keyBoardItemVO: TKKeyBoardItemVO, keyBoardVO: TKKeyBoardVO) {
    TKKeyBoardItem({
      keyBoardVO: keyBoardVO,
      keyBoardItemVO: keyBoardItemVO,
      controller: {
        onClick: (keyBoardItem, event) => {
          if (keyBoardItem.keyBoardItemVO.action == "INPUT") {
            let inputText: string = keyBoardItem.keyBoardItemVO.text ?? "";
            TKLog.debug(`appendChar---->${inputText}`);
            this.delegate?.appendChar?.(inputText);
          } else if (keyBoardItem.keyBoardItemVO.action == "DELETE") {
            TKLog.debug(`deleteChar---->${keyBoardItem.keyBoardItemVO.value}`);
            this.delegate?.deleteChar?.();
          } else if (keyBoardItem.keyBoardItemVO.action == "CLEAR") {
            TKLog.debug(`clearValue---->${keyBoardItem.keyBoardItemVO.value}`);
            this.delegate?.clearValue?.();
          } else if (keyBoardItem.keyBoardItemVO.action == "HIDDEN") {
            TKLog.debug(`hideKeyBoard---->${keyBoardItem.keyBoardItemVO.value}`);
            this.hideKeyboard();
            this.delegate?.hideKeyBoard?.();
          } else if (keyBoardItem.keyBoardItemVO.action == "ENTER") {
            TKLog.debug(`doConfirm---->${keyBoardItem.keyBoardItemVO.value}`);
            if (keyBoardItem.keyBoardItemVO.enabled) {
              this.hideKeyboard();
              this.delegate?.hideKeyBoard?.();
              setTimeout(() => {
                this.delegate?.doConfirm?.();
              }, 300);
            }
          } else if (keyBoardItem.keyBoardItemVO.action == "CHANGE") {
            let targetKeyBoard: string = keyBoardItem.keyBoardItemVO.targetKeyBoard;
            if (TKStringHelper.isNotBlank(targetKeyBoard)) {
              if (this.keyBoardType != targetKeyBoard) {
                this.lastKeyBoardType = this.keyBoardType;
              } else {
                targetKeyBoard = "";
              }
            } else {
              targetKeyBoard = keyBoardItem.keyBoardItemVO.targetDefaultKeyBoard;
              targetKeyBoard =
                TKStringHelper.isNotBlank(this.lastKeyBoardType) ? this.lastKeyBoardType : targetKeyBoard;
            }
            if (TKStringHelper.isNotBlank(targetKeyBoard)) {
              TKLog.debug(`changeKeyBoard---->${targetKeyBoard}`);
              this.keyBoardType = targetKeyBoard;
              this.reset();
              this.delegate?.changeKeyBoardFinish?.(this.keyBoardType);
            }
          } else if (keyBoardItem.keyBoardItemVO.action == "ICHANGE") {
            TKLog.debug(`ichangeKeyBoard---->${keyBoardItem.keyBoardItemVO.targetKeyBoard}`);
            this.keyBoardType = keyBoardItem.keyBoardItemVO.targetKeyBoard;
            this.reset();
            this.delegate?.changeKeyBoardFinish?.(this.keyBoardType);
          } else if (keyBoardItem.keyBoardItemVO.action == "LETTERCASE") {
            keyBoardItem.modifier.selected = !keyBoardItem.modifier.selected;
            TKLog.debug(`doLetterCase---->${keyBoardItem.modifier.selected}`);
            keyBoardVO.inputKeyBoardItems.forEach((inputKeyBoardItem) => {
              inputKeyBoardItem.keyBoardItemVO.isUppercase = keyBoardItem.modifier.selected;
            })
          } else if (keyBoardItem.keyBoardItemVO.action == "OTHER") {
            TKLog.debug(`doOtherChar---->${keyBoardItem.keyBoardItemVO.value}`);
            this.delegate?.doOtherChar?.(keyBoardItem.keyBoardItemVO.value);
          } else if (keyBoardItem.keyBoardItemVO.action == "SPACE") {
            TKLog.debug(`appendChar---->${keyBoardItem.keyBoardItemVO.value}`);
            this.delegate?.appendChar?.(keyBoardItem.keyBoardItemVO.value)
          } else if (keyBoardItem.keyBoardItemVO.action == "SELECT") {
            keyBoardItem.modifier.selected = !keyBoardItem.modifier.selected;
            let value: string = keyBoardItem.modifier.selected ? keyBoardItem.keyBoardItemVO.selectedValue :
            keyBoardItem.keyBoardItemVO.value;
            TKLog.debug(`doOtherChar---->${value}`);
            this.delegate?.doOtherChar?.(value);
          } else if (keyBoardItem.keyBoardItemVO.action == "FORWARD") {
            TKLog.debug(`forward---->${keyBoardItem.keyBoardItemVO.value}`);
            this.delegate?.doForward?.();
          } else if (keyBoardItem.keyBoardItemVO.action == "GOBACK") {
            TKLog.debug(`goBack---->${keyBoardItem.keyBoardItemVO.value}`);
            this.delegate?.doGoBack?.();
          } else if (keyBoardItem.keyBoardItemVO.action == "ZHCN") {
            TKLog.debug(`changeSysZHCNKeyBoard---->${keyBoardItem.keyBoardItemVO.value}`);
            this.delegate?.changeSysZHCNKeyBoard?.();
          }
        },
        onTouch: (keyBoardItem, event) => {
        },
        onLongPressStart: (keyBoardItem, event) => {
          if (!keyBoardItem.modifier.isNoCutScreen && !this.keyBoardVO.isNoCutScreen) {
            keyBoardItem.modifier.stateStyle = TKKeyBoardItemStateStyle.PRESSED;
          }
          if (keyBoardItem.keyBoardItemVO.action == "DELETE") {
            this.deleteKeyTimer?.start();
          } else if (keyBoardItem.keyBoardItemVO.action == "FORWARD") {
            this.forwardKeyTimer?.start();
          } else if (keyBoardItem.keyBoardItemVO.action == "GOBACK") {
            this.goBackKeyTimer?.start();
          }
        },
        onLongPressEnd: (keyBoardItem, event) => {
          keyBoardItem.modifier.stateStyle = TKKeyBoardItemStateStyle.NORMAL;
          if (keyBoardItem.keyBoardItemVO.action == "DELETE") {
            this.deleteKeyTimer?.pause();
          } else if (keyBoardItem.keyBoardItemVO.action == "FORWARD") {
            this.forwardKeyTimer?.pause();
          } else if (keyBoardItem.keyBoardItemVO.action == "GOBACK") {
            this.goBackKeyTimer?.pause();
          }
        }
      }
    })
  }
}
