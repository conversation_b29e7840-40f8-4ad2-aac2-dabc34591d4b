/**
 *  自定义键盘输入框代理
 */
export interface TKKeyBoardInputDelegate {
  /**
   * 自定义键盘输入框，点击确定
   */
  textFieldConfirm?: () => void;

  /**
   * 是否可以输入该字符
   * @param charStr
   * @returns
   */
  textFieldShouldChangeCharacters?: (charStr: string) => boolean;

  /**
   * 自定义键盘输入框，文字改变
   */
  textFieldChange?: (value: string) => void;

  /**
   *  输入特殊字符
   *
   * @param charStr
   */
  doOtherChar?: (charStr: string) => void;

  /**
   *  键盘头部输入框联动事件
   * @param content
   */
  doTitleInput?: (content: string) => void;

  /**
   *  键盘类型切换事件
   * @param keyBoardType
   */
  keyBoardChanged?: (keyBoardType: string) => void;

  /**
   * 切换系统中文键盘
   */
  changeSysZHCNKeyBoard?: () => void;
}