/**
 键盘类型
 */
export enum TKKeyBoardType {
  /**
   *  有序数字键盘
   */
  Num = "0",

  /**
   *  随机数字键盘
   */
  RandNum = "1",

  /**
   *  加强版有序数字键盘
   */
  NumStrong = "2",

  /**
   *  加强版随机数字键盘
   */
  RandNumStrong = "3",

  /**
   *  英文键盘
   */
  Alpha = "4",

  /**
   *  股票键盘
   */
  Stock = "5",

  /**
   *  交易买卖键盘
   */
  Trade = "6",

  /**
   *  三板股票键盘
   */
  SBStock = "7",

  /**
   *  科创版股票键盘
   */
  KCBStock = "8",

  /**
   *  招商有序数字键盘
   */
  MSNum = "10",

  /**
   *  招商随机数字键盘
   */
  MSRandNum = "11",

  /**
   *  招商英文键盘
   */
  MSAlpha = "12",

  /**
   *  招商数字符号键盘
   */
  MSNumSymbol = "13",

  /**
   *  招商符号键盘
   */
  MSSymbol = "14",

  /**
   *  天风登录数字键盘
   */
  TFLoginNum = "20",

  /**
   *  天风股票数字键盘
   */
  TFStock = "21",

  /**
   *  天风买入价格数字键盘
   */
  TFBuyPrice = "22",

  /**
   *  天风买入数量数字键盘
   */
  TFBuyNum = "23",

  /**
   *  天风字母键盘
   */
  TFAlpha = "24",

  /**
   *  天风数字符号键盘
   */
  TFNumSymbol = "25",

  /**
   *  天风符号键盘
   */
  TFSymbol = "26",

  /**
   *  天风随机登录数字键盘
   */
  TFLoginRandomNum = "27",

  /**
   * @brief 一创期权通键盘深圳快速报价键盘
   */
  OptionSZFast = "30",

  /**
   * @brief 一创期权通键盘上海快速报价键盘
   */
  OptionSHFast = "31",

  /**
   * @brief 一创期权通深圳数字报价键盘
   */
  OptionSZNum = "32",

  /**
   * @brief 一创期权通上海数字报价键盘
   */
  OptionSHNum = "33",

  /**
   * @brief 华泰英文键盘
   */
  HTAlpha = "40",

  /**
   * @brief 华泰数字键盘
   */
  HTNum = "41",

  /**
   * @brief 华泰符号键盘
   */
  HTSymbol = "42",

  /**
   * @brief 华安买卖数量键盘
   */
  HABuyNum = "50",

  /**
   * @brief 华安买卖金额键盘
   */
  HABuyPrice = "51",

  /**
   *  取消自定义键盘
   */
  None = "99"
}

/**
 * 键盘按键事件代理
 */
export interface TKKeyBoardEventDelegate {
  /**
   *  追加字符
   */
  appendChar?: (charStr: string) => void;

  /**
   *  退格删除字符
   */
  deleteChar?: () => void;

  /**
   * 清空值
   */
  clearValue?: () => void;

  /**
   * 点击确定
   */
  doConfirm?: () => void;

  /**
   * 前进键
   */
  doForward?: () => void;

  /**
   * 后退键
   */
  doGoBack?: () => void;

  /**
   *  其他键
   */
  doOtherChar?: (charStr: string) => void;

  /**
   * 键盘头部输入框事件
   */
  doTitleInput?: (content: string) => void;

  /**
   * 切换系统中文键盘
   */
  changeSysZHCNKeyBoard?: () => void;

  /**
   * 切换键盘回调
   */
  changeKeyBoardFinish?: (keyBoardType: string) => void;

  /**
   * 隐藏自定义键盘
   */
  hideKeyBoard?: () => void;
}