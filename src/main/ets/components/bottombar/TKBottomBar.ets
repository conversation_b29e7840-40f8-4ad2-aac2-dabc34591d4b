import { TKThemeManager } from '../../base/theme/TKThemeManager';
import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKWindowHelper } from '../../util/ui/TKWindowHelper';
import { TKAttribute } from '../common/attribute/TKAttribute';
import { TKStyleAttribute } from '../common/attribute/TKStyleAttribute';

/**
 * 子组件的控制属性类
 */
@Observed
export class TKBottomBarAttribute extends TKAttribute {
  /**
   * 是否是全屏
   */
  isLayoutFullScreen?: boolean = true;
  /**
   * 显示/隐藏，设置了全屏才会生效，非全屏时会显示非安全区
   */
  bottomBarEnable?: boolean = true;
  /**
   * 回调底部栏的高度
   */
  onBottomBarHeight?: (height: number) => void = () => {
  }
}

/**
 * 子组件的样式属性类
 */
@Observed
export class TKBottomBarStyleAttribute extends TKStyleAttribute {
  bottomAreaBgColor?: ResourceColor =
    TKThemeManager.shareInstance().getCssRulesetByClassName("TKBottomBar").backgroundColor ?? "#00FFFFFF";
}

/**
 * 思迪信息底部区域栏目
 */
@Component
export struct TKBottomBar {
  /**
   * 基本控制属性类
   */
  @State attribute: TKBottomBarAttribute = new TKBottomBarAttribute()
  /**
   * 样式属性类
   */
  @State styleAttribute: TKBottomBarStyleAttribute = new TKBottomBarStyleAttribute()
  /**
   * 标题栏高度
   */
  @State private bottomBarHeight: number = TKWindowHelper.getNavigationBottomBarHeightSync();

  aboutToAppear(): void {
    TKObjectHelper.fixDefault(this.attribute, TKBottomBarAttribute);
    TKObjectHelper.fixDefault(this.styleAttribute, TKBottomBarStyleAttribute);

    if (this.attribute.isLayoutFullScreen !== undefined) {
      TKWindowHelper.setWindowLayoutFullScreen(this.attribute.isLayoutFullScreen).then(() => {
        this.updateBottomBarHeight();
      })
    } else {
      this.updateBottomBarHeight();
    }
  }

  /**
   * 因给状态栏和标题栏分配高度的
   */
  updateBottomBarHeight() {
    let height: number = 0;
    if (this.attribute.isLayoutFullScreen && this.attribute.bottomBarEnable) {
      height += this.bottomBarHeight;
    }
    if (this.attribute.onBottomBarHeight) {
      this.attribute.onBottomBarHeight(height);
    }
  }

  build() {
    Column() {
      // 占位状态栏的高度
      if (this.attribute.isLayoutFullScreen) {
        if (this.attribute.bottomBarEnable) {
          Row() {
          }
          .width('100%')
          .height(px2vp(this.bottomBarHeight))
          .backgroundColor(this.styleAttribute.bottomAreaBgColor)
        }
      } else {
        if (this.attribute.bottomBarEnable) {
          Row() {
          }
          .width('100%')
          .height('0.01%')
          .backgroundColor(this.styleAttribute.bottomAreaBgColor)
          .expandSafeArea([SafeAreaType.SYSTEM], [SafeAreaEdge.BOTTOM])
        }
      }
    }
    .backgroundColor(this.styleAttribute.bottomAreaBgColor)
  }
}