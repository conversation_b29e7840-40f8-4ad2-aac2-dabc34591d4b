import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKImageHelper } from '../../util/ui/TKImageHelper';
import { TKAttribute } from '../common/attribute/TKAttribute';

export class TKAnimatedImageAttribute extends TKAttribute {
  /**
   * gif图片 可以是本地资源，或者本地资源名称 不能为ets目录下本地图片
   */
  gifImage?: ResourceStr = undefined;
  /**
   * 图片帧信息集合。不支持动态更新。
   */
  images?: Array<ImageFrameInfo> = [];
  /**
   * 播放状态
   */
  state?: AnimationStatus = AnimationStatus.Running;
  /**
   * 播放时长
   */
  duration?: number = 1000;
  /**
   * 设置播放方向。false表示从第1张图片播放到最后1张图片，true表示从最后1张图片播放到第1张图片。
   */
  reverse?: boolean = false;
  /**
   * 当前播放方向下，动画开始前和结束后的状态。
   * 动画结束后的状态由fillMode和reverse属性共同决定。例如，fillMode为Forwards表示停止时维持动画最后一个关键帧的状态，若reverse为false则维持正播的最后一帧，即最后一张图，若reverse为true则维持逆播的最后一帧，即第一张图
   */
  fillMode?: FillMode = FillMode.None;
  /**
   * 播放次数，-1 无限播放 默认-1
   */
  iterations?: number = -1;
}

@Component
export struct TKAnimatedImage {
  private imagePixelMap: Array<PixelMap> = [];
  @State private images: Array<ImageFrameInfo> = [];
  @State attribute: TKAnimatedImageAttribute = new TKAnimatedImageAttribute();

  async aboutToAppear(): Promise<void> {
    this.attribute = TKObjectHelper.fixDefault(this.attribute, TKAnimatedImageAttribute);
    if (this.attribute.gifImage) {
      this.imagePixelMap = await TKImageHelper.getPixmapListFromResource(this.attribute.gifImage);
      this.imagePixelMap.forEach((item) => {
        this.images.push({ src: item })
      })
    } else if (this.attribute.images && this.attribute.images.length > 0) {
      this.images = this.attribute.images
    }
  }

  build() {
    ImageAnimator()
      .width('100%')
      .height('100%')
      .images(this.images)
      .duration(this.attribute.duration)
      .state(this.attribute.state)
      .reverse(this.attribute.reverse)
      .fillMode(this.attribute.fillMode)
      .iterations(this.attribute.iterations)
  }
}