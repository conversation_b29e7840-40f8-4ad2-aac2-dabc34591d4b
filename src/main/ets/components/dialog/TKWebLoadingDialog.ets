import { Size } from '@kit.ArkUI';
import { TKObjectHelper } from '../../util/data/TKObjectHelper';

/**
 * web加载loading页面选项
 */
@Observed
export class TKWebLoadingDialogOption {
  loadingDialogGif?: Resource = $r("app.media.tk_framework_loading");
  loadingDialogGifSize?: Size = { width: 40, height: 40 };
  angle?: number = 0;
  isDisappear?: boolean = false;
}

@Builder
export function buildTKWebLoadingDialog(options: TKWebLoadingDialogOption) {
  TKWebLoadingDialog({ options })
}

/**
 * web加载loading页面
 */
@Component
export struct TKWebLoadingDialog {
  @State options: TKWebLoadingDialogOption = new TKWebLoadingDialogOption();

  aboutToAppear(options?: TKWebLoadingDialogOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKWebLoadingDialogOption, options);
  }

  aboutToDisappear(): void {
    this.options = new TKWebLoadingDialogOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKWebLoadingDialogOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  // build() {
  //   Row() {
  //     Image(this.loadingDialogGif)
  //       .width(this.loadingDialogGifSize!.width)
  //       .height(this.loadingDialogGifSize!.height)
  //       .objectFit(ImageFit.ScaleDown)
  //   }.width("100%").height("100%").alignItems(VerticalAlign.Center).justifyContent(FlexAlign.Center)
  // }
  build() {
    Column() {
      Image(this.options.loadingDialogGif)
        .width(this.options.loadingDialogGifSize!.width)
        .height(this.options.loadingDialogGifSize!.height)
        .objectFit(ImageFit.ScaleDown)
        .rotate({ angle: this.options.angle })
    }
  }
}

