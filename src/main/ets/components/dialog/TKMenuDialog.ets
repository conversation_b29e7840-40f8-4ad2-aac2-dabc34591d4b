/**
 * 底部按钮
 */
import { TKObjectHelper } from '../../util/data/TKObjectHelper';

export interface TKBottomMenu {
  tag: string;
  name: string;
}

/**
 * 菜单弹框选项
 */
@Observed
export class TKMenuDialogOption {
  mainColor?: ResourceColor = "#FF1A9BFF";
  bottomMenu?: Array<TKBottomMenu> = [];
  onItemClick?: (item: TKBottomMenu) => void;
  onCancel?: () => void;
  close?: () => void;
  isDisappear?: boolean = false;
}

@Builder
export function buildTKMenuDialog(options: TKMenuDialogOption) {
  TKMenuDialog({ options })
}

/**
 * 菜单弹框
 */
@Component
export struct TKMenuDialog {
  @State options: TKMenuDialogOption = new TKMenuDialogOption();

  aboutToAppear(options?: TKMenuDialogOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKMenuDialogOption, options);
  }

  aboutToDisappear(): void {
    this.options = new TKMenuDialogOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKMenuDialogOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  build() {
    Column() {
      List() {
        ForEach(this.options.bottomMenu, (item: TKBottomMenu) => {
          ListItem() {
            Row() {
              Text(item.name)
                .fontSize(18)
                .fontColor(this.options.mainColor)
            }
            .align(Alignment.Center)
            .width('100%')
            .height(50)
            .justifyContent(FlexAlign.Center)
            .onClick(() => {
              if (this.options.close) {
                this.options.close();
              }
              if (this.options.onItemClick) {
                this.options.onItemClick(item);
              }
            })
          }
        }, (item: TKBottomMenu) => item.tag.toString())
      }.borderRadius(10).divider({ strokeWidth: 1, color: "#FFEEEEEE" }).width("95%").backgroundColor("#FFFFFFFF")

      Row() {
        Text("取消")
          .fontSize(18)
          .fontWeight(FontWeight.Bold)
          .fontColor(this.options.mainColor)
      }
      .margin({ top: 8 })
      .align(Alignment.Center)
      .width('100%')
      .height(50)
      .justifyContent(FlexAlign.Center)
      .borderRadius(10)
      .width("95%")
      .backgroundColor("#FFFFFFFF")
      .onClick(() => {
        if (this.options.close) {
          this.options.close();
        }
        if (this.options.onCancel) {
          this.options.onCancel();
        }
      })
    }.width("100%").alignItems(HorizontalAlign.Center)
  }
}
