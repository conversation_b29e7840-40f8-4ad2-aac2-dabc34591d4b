import { TKObjectHelper } from '../../util/data/TKObjectHelper';

@Observed
export class TKToastDialogOption {
  message?: string | Resource = ""; //吐司内容
  duration?: number = 1500; //显示时长(1500ms-10000ms)
  fontSize?: number | string | Resource = 16; //文字大小
  fontColor?: ResourceColor; //文字颜色
  backgroundColor?: ResourceColor = '#DF3F3F3F'; //背景颜色，建议八位色值前两位为透明度
  borderRadius?: Length | BorderRadiuses = 10; //背景圆角
  bottom?: Length;
  padding?: Padding | Length = {
    left: 16,
    right: 16,
    top: 12,
    bottom: 12
  }; //Padding
  isDisappear?: boolean = false;
}

@Builder
export function buildTKToastDialog(options: TKToastDialogOption) {
  TKToastDialog({ options })
}

/**
 *  吐司框
 */
@Component
export struct TKToastDialog {
  @State options: TKToastDialogOption = new TKToastDialogOption();

  aboutToAppear(options?: TKToastDialogOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKToastDialogOption, options);
  }

  aboutToDisappear(): void {
    this.options = new TKToastDialogOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKToastDialogOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  build() {
    Column() {
      Text(this.options.message)
        .fontColor(this.options.fontColor)
        .fontSize(this.options.fontSize)
        .padding({ left: 10, right: 10 })
    }
    .justifyContent(FlexAlign.Center)
    .alignItems(HorizontalAlign.Center)
    .align(Alignment.Center)
    .alignSelf(ItemAlign.Center)
    .constraintSize({ minWidth: 90 })
    .margin({ left: 15, right: 15 })
    .padding(this.options.padding)
    .backgroundColor(this.options.backgroundColor)
    .borderRadius(this.options.borderRadius)
    .shadow(ShadowStyle.OUTER_DEFAULT_SM)
  }
}


