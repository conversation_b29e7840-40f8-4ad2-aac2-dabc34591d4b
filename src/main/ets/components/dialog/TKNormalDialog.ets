/**
 * 标准的自定义弹框
 */
import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';

@Observed
export class TKNormalDialogOption {
  //标题
  title?: string = "";
  //内容
  message?: string = "";
  //取消按钮文字
  cancelText?: string = "";
  //确定按钮文字
  confirmText?: string = '确定';
  //取消按钮回调
  cancel?: () => void;
  //确定按钮回调
  confirm?: () => void;
  //按钮高度
  btnHeight?: number = 50;
  //内容最小高度
  contentMinHeight?: number = 120;
  //是否隐藏
  isDisappear?: boolean = false;
  //是否不关闭
  isNoClose?: boolean = false;
  //关闭回调
  close?: () => void;
  //旋转角度
  angle?: number = 0;
  //标题颜色
  titleColor?: ResourceColor = "#FF1A9BFF";
  //内容颜色
  contentColor?: ResourceColor = "#FF000000";
  //取消按钮颜色
  cancelColor?: ResourceColor = Color.Gray;
  //确定按钮颜色
  confirmColor?: ResourceColor = "#FF1A9BFF";
}

@Builder
export function buildTKNormalDialog(options: TKNormalDialogOption) {
  TKNormalDialog({ options })
}

@Component
export struct TKNormalDialog {
  @State options: TKNormalDialogOption = new TKNormalDialogOption();

  aboutToAppear(options?: TKNormalDialogOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKNormalDialogOption, options);
  }

  aboutToDisappear(): void {
    this.options = new TKNormalDialogOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    if (params) {
      let options = params as Object as TKNormalDialogOption;
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  private btnWidthSpan(): string {
    return TKStringHelper.isNotBlank(this.options.cancelText) && TKStringHelper.isNotBlank(this.options.confirmText) ?
      '50%' : '100%';
  }

  build() {
    Column() {
      Column({ space: 15 }) {
        if (TKStringHelper.isNotBlank(this.options.title)) {
          Text(this.options.title)
            .fontSize(20)
            .fontColor(this.options.titleColor)
            .textAlign(TextAlign.Center)
            .margin({ left: 5, right: 5, top: 10 })
        }
        Text(this.options.message)
          .fontSize(16)
          .textAlign(TextAlign.JUSTIFY)
          .fontColor(this.options.contentColor)
          .margin({ left: 15, right: 15, bottom: 10 })
      }
      .justifyContent(FlexAlign.Center)
      .width('100%')
      .constraintSize({ minHeight: this.options.contentMinHeight })

      Divider().strokeWidth(1)
      Row() {
        if (TKStringHelper.isNotBlank(this.options.cancelText)) {
          Row() {
            Text(this.options.cancelText)
              .fontWeight(FontWeight.Bold)
              .textAlign(TextAlign.Center)
              .width('100%')
              .height('100%')
              .fontColor(this.options.cancelColor)
          }
          .width(this.btnWidthSpan())
          .onClick(() => {
            if (this.options.close) {
              this.options.close();
            }
            if (this.options.cancel) {
              this.options.cancel();
            }
          })
        }
        if (TKStringHelper.isNotBlank(this.options.cancelText) && TKStringHelper.isNotBlank(this.options.confirmText)) {
          Divider()
            .vertical(true)
            .position({ x: '50%' })
            .strokeWidth(1)
        }
        if (TKStringHelper.isNotBlank(this.options.confirmText)) {
          Row() {
            Text(this.options.confirmText)
              .fontWeight(FontWeight.Bold)
              .textAlign(TextAlign.Center)
              .width('100%')
              .height('100%')
              .fontColor(this.options.confirmColor)
          }
          .width(this.btnWidthSpan())
          .onClick(() => {
            if (!this.options.isNoClose) {
              if (this.options.close) {
                this.options.close();
              }
            }
            if (this.options.confirm) {
              this.options.confirm();
            }
          })
        }
      }
      .width('100%')
      .height(this.options.btnHeight)
    }
    .width('80%')
    .borderRadius(4)
    .backgroundColor("#ffffffff")
    .rotate({ angle: this.options.angle })
  }
}