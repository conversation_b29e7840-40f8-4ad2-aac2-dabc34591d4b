import { TKObjectHelper } from '../../util/data/TKObjectHelper';

/**
 * 模糊背景选项
 */
@Observed
export class TKBlurDialogOption {
  blurScale?: number = 0.7;
  isDisappear?: boolean = false;
}

@Builder
export function buildTKBlurDialog(options: TKBlurDialogOption) {
  TKBlurDialog({ options })
}

/**
 * 模糊背景
 */
@Component
export struct TKBlurDialog {
  @State options: TKBlurDialogOption = new TKBlurDialogOption();

  aboutToAppear(options?: TKBlurDialogOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKBlurDialogOption, options);
  }

  aboutToDisappear(): void {
    this.options = new TKBlurDialogOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKBlurDialogOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  build() {
    Column() {
    }
    .backgroundColor(Color.Transparent)
    .foregroundBlurStyle(BlurStyle.Thin, {
      colorMode: ThemeColorMode.LIGHT,
      adaptiveColor: AdaptiveColor.DEFAULT,
      scale: this.options.blurScale
    })
    .width("100%")
    .height("100%")
  }
}


