import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKTimer } from '../../util/timer/TKTimer';

/**
 * 加载loading
 */
@Observed
export class TKLoadingDialogOption {
  tip?: string = "加载中...";
  angle?: number = 0;
  isDisappear?: boolean = false;
}

@Builder
export function buildTKLoadingDialog(options: TKLoadingDialogOption) {
  TKLoadingDialog({ options })
}

/**
 * 加载loading
 */
@Component
export struct TKLoadingDialog {
  @State options: TKLoadingDialogOption = new TKLoadingDialogOption();
  @State private value: number = 0;
  private timer?: TKTimer;

  aboutToAppear(options?: TKLoadingDialogOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKLoadingDialogOption, options);
    this.value = 0;
    if (!this.timer) {
      this.timer = new TKTimer(100, this, this.handleTimer);
    }
    this.timer.start();
  }

  aboutToDisappear(): void {
    if (this.timer) {
      this.timer.stop();
      this.timer = undefined;
    }
    this.options = new TKLoadingDialogOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKLoadingDialogOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  private handleTimer() {
    this.value = (this.value + 10) % 100;
  }

  build() {
    Column({ space: 5 }) {
      Progress({ value: this.value, total: 100, type: ProgressType.ScaleRing }).width(35).height(35)
        .backgroundColor(Color.White)
        .style({ strokeWidth: 5, scaleCount: 10, scaleWidth: 5 })
      Text(this.options.tip)
        .fontSize(14)
        .fontWeight(FontWeight.Bold)
        .fontColor(Color.White)
        .textAlign(TextAlign.JUSTIFY)
    }
    .rotate({ angle: this.options.angle })
    .backgroundColor(Color.Black)
    .width(100)
    .height(100)
    .justifyContent(FlexAlign.Center)
    .borderRadius(10)
    .opacity(0.8);
  }
}


