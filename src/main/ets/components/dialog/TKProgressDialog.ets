/**
 * 加载loading
 */
import { TKObjectHelper } from '../../util/data/TKObjectHelper';

@Observed
export class TKProgressDialogOption {
  value?: number = 0;
  tip?: string = "加载中...";
  angle?: number = 0;
  isDisappear?: boolean = false;
}

@Builder
export function buildTKProgressDialog(options: TKProgressDialogOption) {
  TKProgressDialog({ options })
}

/**
 * 进度条
 */
@Component
export struct TKProgressDialog {
  @State options: TKProgressDialogOption = new TKProgressDialogOption();

  aboutToAppear(options?: TKProgressDialogOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKProgressDialogOption, options);
  }

  aboutToDisappear(): void {
    this.options = new TKProgressDialogOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKProgressDialogOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  build() {
    Column({ space: 5 }) {
      Progress({ value: this.options.value, total: 100, type: ProgressType.Capsule })
        .width(130)
        .height(20)
        .color(Color.Blue)
        .backgroundColor(Color.Black)
        .borderWidth(2)
        .borderColor(Color.White)
        .borderRadius(10)
      Text(this.options.tip)
        .fontSize(16)
        .fontWeight(FontWeight.Bold)
        .fontColor(Color.White)
        .textAlign(TextAlign.JUSTIFY)
    }
    .backgroundColor(Color.Black)
    .width(180)
    .height(90)
    .justifyContent(FlexAlign.Center)
    .borderRadius(10)
    .opacity(0.8)
    .rotate({ angle: this.options.angle })
  }
}


