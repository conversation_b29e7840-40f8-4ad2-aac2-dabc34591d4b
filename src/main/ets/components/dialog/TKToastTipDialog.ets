import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKToastDialogOption } from './TKToastDialog';

export enum TKOrientation {
  VERTICAL, //垂直
  HORIZONTAL //水平
}

@Observed
export class TKToastTipDialogOption extends TKToastDialogOption {
  imageRes?: ResourceStr | PixelMap; //展示的图片。
  imageSize?: SizeOptions = { width: 45, height: 45 }; //自定义图片尺寸。默认值：64*64vp
  orientation?: TKOrientation = TKOrientation.VERTICAL; //吐司布局方向，默认垂直。设置该值时，请重新设置imageSize和margin。
  margin?: number = 10; //吐司的图片与文字间距。
}


@Builder
export function buildTKToastTipDialog(options: TKToastTipDialogOption) {
  TKToastTipDialog({ options })
}


/**
 *  图标吐司框
 */
@Component
export struct TKToastTipDialog {
  @State options: TKToastTipDialogOption = new TKToastTipDialogOption();

  aboutToAppear(options?: TKToastTipDialogOption): void {
    this.options = TKObjectHelper.fixDefault(this.options, TKToastTipDialogOption, options);
  }

  aboutToDisappear(): void {
    this.options = new TKToastTipDialogOption();
  }

  aboutToReuse(params: Record<string, Object>): void {
    let options = params as Object as TKToastTipDialogOption;
    if (options && !options.isDisappear) {
      this.aboutToAppear(options);
    } else {
      this.aboutToDisappear();
    }
  }

  build() {
    if (this.options.orientation == TKOrientation.VERTICAL) {
      Column({ space: this.options.margin ?? 10 }) {
        Image(this.options.imageRes)
          .size(this.options.imageSize)
          .fillColor(this.options.fontColor)
          .margin(2)
        Text(this.options.message)
          .fontColor(this.options.fontColor)
          .fontSize(this.options.fontSize)
      }
      .margin(12)
      .padding(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
      .align(Alignment.Center)
      .alignSelf(ItemAlign.Center)
      .constraintSize({ minWidth: 120 })
      .backgroundColor(this.options.backgroundColor)
      .borderRadius(this.options.borderRadius)
      .shadow(ShadowStyle.OUTER_DEFAULT_SM)
    } else {
      Row({ space: this.options.margin ?? 10 }) {
        Image(this.options.imageRes)
          .size(this.options.imageSize)
          .fillColor(this.options.fontColor)
          .margin({ right: 2 })
        Text(this.options.message)
          .fontColor(this.options.fontColor)
          .fontSize(this.options.fontSize)
      }
      .margin(12)
      .padding(20)
      .justifyContent(FlexAlign.Center)
      .alignItems(VerticalAlign.Center)
      .align(Alignment.Center)
      .alignSelf(ItemAlign.Center)
      .constraintSize({ minWidth: 120 })
      .backgroundColor(this.options.backgroundColor)
      .borderRadius(this.options.borderRadius)
      .shadow(ShadowStyle.OUTER_DEFAULT_SM)
    }
  }
}