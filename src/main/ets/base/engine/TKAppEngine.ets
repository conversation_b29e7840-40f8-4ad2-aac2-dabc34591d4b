/**
 * 框架启动管理器
 */
import { TKBlurDialogOption } from '../../components/dialog/TKBlurDialog';
import { TKCacheManager } from '../../util/cache/TKCacheManager';
import { TKDeviceHelper } from '../../util/dev/TKDeviceHelper';
import { TKLog } from '../../util/logger/TKLog';
import { TKNetHelper } from '../../util/net/TKNetHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKApplicationStateChangeListener, TKContextHelper } from '../../util/system/TKContextHelper';
import { TKSystemHelper } from '../../util/system/TKSystemHelper';
import { TKComponentContent, TKDialogHelper } from '../../util/ui/TKDialogHelper';
import { TKFoldDisplayListener } from '../device/listener/TKFoldDisplayListener';
import { TKResultVO } from '../mvc/model/TKResultVO';
import { TKHttpRoomServerListener } from '../mvc/service/dao/http/client/gateway/TKHttpRoomServerListener';
import { TKBusConfig } from '../mvc/service/dao/socket/client/gateway/TKBusConfig';
import { TKGatewayListener } from '../mvc/service/dao/socket/client/gateway/TKGatewayListener';
import { TKGatewayManager } from '../mvc/service/dao/socket/client/gateway/TKGatewayManager';
import { TKServer } from '../mvc/service/dao/socket/client/gateway/TKServer';
import { TKComQuotePushManager } from '../mvc/service/dao/socket/client/quote/push/TKComQuotePushManager';
import { TKNetworkListener } from '../network/listener/TKNetworkListener';
import { TKNotification } from '../notification/TKNotification';
import { TKNotificationCenter } from '../notification/TKNotificationCenter';
import { TKPluginInvokeCenter, TKPluginInvokeOption } from '../plugin/TKPluginInvokeCenter';
import { TKRouterPageManager } from '../router/TKRouterPageManager';
import { TKVersionManager } from '../version/TKVersionManager';
import { TKModuleDelegate, TKModuleMessageFilterDelegate } from './module/TKModuleDelegate';
import { TKModuleEngine } from './module/TKModuleEngine';
import { TKModuleMessage } from './module/TKModuleMessage';
import { common } from '@kit.AbilityKit';
import { TKKeyBoardVOManager } from '../../components/keyboard/config/TKKeyBoardVOManager';
import { TKKeyboardListener } from '../device/listener/TKKeyboardListener';
import { TKWindowListener } from '../device/listener/TKWindowListener';

/**
 * 引擎启动监听回调
 */
export type TKAppEngineStartFinishCallBack = () => void;

/**
 * 启动类选项
 */
export interface TKAppEngineStartOption {
  /**
   * 上下文
   */
  context?: common.UIAbilityContext;

  /**
   * 动态库的模块名称
   */
  hspModuleName?: string;

  /**
   * 回调函数
   */
  finishCallBack?: TKAppEngineStartFinishCallBack;
}

/**
 * 框架引擎
 */
export class TKAppEngine {
  //单例对象
  private static instance: TKAppEngine | undefined = undefined;
  //是否在运行
  private _isRunning: boolean = false;
  //是否开启后台模糊效果
  public isBlurBackground: boolean = false;
  //模糊背景弹框
  private blurBackgroundDialog?: TKComponentContent<TKBlurDialogOption>;

  private constructor() {
  }

  public static shareInstance(): TKAppEngine {
    if (!TKAppEngine.instance) {
      TKAppEngine.instance = new TKAppEngine();
    }
    return TKAppEngine.instance;
  }

  public initContext(context: common.UIAbilityContext, hspModuleName?: string) {
    if (context) {
      //存储当前上下文对象
      TKContextHelper.setCurrentContext(context, hspModuleName);
    }
  }

  /**
   * 获取版本号
   * @returns
   */
  public getVersion():string{
    return "1.3.0";
  }

  /**
   * 启动引擎
   * @param context
   */
  public start(tkAppEngineStartOption: TKAppEngineStartOption) {
    try {
      if (!this._isRunning) {
        let beginTime: number = new Date().getTime();
        this._isRunning = true;
        //存储当前上下文对象
        TKContextHelper.setCurrentContext(tkAppEngineStartOption.context, tkAppEngineStartOption.hspModuleName);
        //启动前后台监听
        TKContextHelper.bindApplicationLifecycle();
        //初始化版本环境
        TKVersionManager.shareInstance().initContext();
        //初始化日志
        this.initLog();
        //监听系统通知
        this.registerSystemNotification();
        //是否开启模糊效果
        this.isBlurBackground = (TKSystemHelper.getConfig("system.isBlurBackground") !== "0");
        //启动内存管理
        TKCacheManager.shareInstance().startMonitor();
        //启动路由管理
        TKRouterPageManager.shareInstance().startListener();
        //启动折叠屏监听
        TKFoldDisplayListener.shareInstance().startListener();
        setTimeout(() => {
          //获取网络代理
          TKNetHelper.fetchHttpProxy();
          //启动网络监听
          TKNetworkListener.shareInstance().startListener();
          //开启window监听
          TKWindowListener.shareInstance().startListener();
          //开启键盘监听
          TKKeyboardListener.shareInstance().startListener();
          //初始化键盘
          TKKeyBoardVOManager.shareInstance().loadConfig();
        }, 1000);
        //启动框架站点测速逻辑
        new Promise<void>(async (resolve, reject) => {
          if (this.isAppFirstInstall()) {
            await TKDeviceHelper.getDeviceUUIDASync();
          }
          await this.startTestSpeed();
          resolve();
        }).then(() => {
          let endTime: number = new Date().getTime();
          TKLog.info(`框架初始化完成,耗时[${endTime - beginTime}]毫秒`);
          tkAppEngineStartOption.finishCallBack?.();
        });
        //是否开启行情推送服务
        this.startHqPushServer();
        //是否开启自动检测版本更新
        setTimeout(() => {
          if (TKSystemHelper.getConfig("update.isOpen") == "1") {
            this.callPlugin({ funcNo: "50203" });
          }
        }, 2000);
      } else {
        tkAppEngineStartOption.finishCallBack?.();
      }
    } catch (error) {
      TKLog.error(`[框架引擎启动失败]: error code: ${error.code}, message is: ${error.message}`);
      this._isRunning = false;
    }
  }

  /**
   * 关闭引擎
   * @param context
   */
  public stop() {
    try {
      if (this._isRunning) {
        this._isRunning = false;
        //停止前后台监听
        TKContextHelper.unbindApplicationLifecycle();
        //停止内存监控
        TKCacheManager.shareInstance().stopMonitor();
        //停止折叠屏监听
        TKFoldDisplayListener.shareInstance().stopListener();
        //停止网络监控
        TKNetworkListener.shareInstance().stopListener();
        //停止window监听
        TKWindowListener.shareInstance().stopListener();
        //停止键盘监听
        TKKeyboardListener.shareInstance().stopListener();
        //停止网关检测服务
        if (TKSystemHelper.getConfig("system.isLinstenGateway") == "1") {
          TKGatewayListener.shareInstance().stop();
        }
        //停止行情推送服务
        this.stopHqPushServer();
      }
    } catch (error) {
      TKLog.error(`[框架引擎停止失败]: error code: ${error.code}, message is: ${error.message}`);
    }
  }

  /**
   *  运行状态
   *
   * @return
   */
  public isRunning(): boolean {
    return this._isRunning;
  }

  private registerSystemNotification() {
    TKNotificationCenter.defaultCenter.addObservers(this, this.handleNotification,
      [TKApplicationStateChangeListener.NOTE_TKAPPLICATION_FOREGROUND,
        TKApplicationStateChangeListener.NOTE_TKAPPLICATION_BACKGROUND,
        TKApplicationStateChangeListener.NOTE_TKAPPLICATION_PAUSED]);
  }

  private handleNotification(note: TKNotification) {
    if (note.name == TKApplicationStateChangeListener.NOTE_TKAPPLICATION_PAUSED) {
      if (this.isBlurBackground) {
        this.blurBackgroundDialog = TKDialogHelper.createBlurBackground();
        this.blurBackgroundDialog.open();
      }
    } else if (note.name == TKApplicationStateChangeListener.NOTE_TKAPPLICATION_FOREGROUND) {
      if (this.isBlurBackground) {
        this.blurBackgroundDialog?.close();
      }
    } else if (note.name == TKApplicationStateChangeListener.NOTE_TKAPPLICATION_BACKGROUND) {

    }
  }

  /**
   * 初始化日志
   */
  private initLog() {
    let logLevel: string = TKSystemHelper.getConfig("log.logLevel");
    let logType: string = TKSystemHelper.getConfig("log.logType");
    TKLog.setLogLevel(logLevel);
    TKLog.setLogType(logType);
  }

  /**
   *  启动站点测速
   */
  private async startTestSpeed() {
    let testSpeedPromises: Array<Promise<void>> = new Array<Promise<void>>();
    //启动Http测速
    if (TKSystemHelper.getConfig("system.isUseServerTestSpeed") == "1") {
      let testSpeedPromise: Promise<void> = new Promise<void>((resolve, reject) => {
        TKHttpRoomServerListener.shareInstance().start(() => {
          resolve();
        })
      });
      testSpeedPromises.push(testSpeedPromise);
    }
    //开启网关检测服务
    if (TKSystemHelper.getConfig("system.isLinstenGateway") == "1") {
      let testSpeedPromise: Promise<void> = new Promise<void>((resolve, reject) => {
        TKGatewayListener.shareInstance().start(() => {
          this.updateBusConfig(TKBusConfig.shareInstance().updateUrl);
          resolve();
        })
      });
      testSpeedPromises.push(testSpeedPromise);
    }
    if (testSpeedPromises.length > 0) {
      await Promise.all(testSpeedPromises);
    }
  }

  /**
   *  启动行情监听
   */
  private startHqPushServer() {
    if (TKSystemHelper.getConfig("system.isStartHqPush") == "1") {
      setTimeout(() => {
        let pushServers: Map<string, TKServer> = TKGatewayManager.shareInstance().getPushServers();
        if (pushServers && pushServers.size > 0) {
          pushServers.forEach((value, key) => {
            let serverName: string = key;
            TKComQuotePushManager.shareInstance().startQuotePush(serverName);
          });
        }
      }, 1500);
    }
  }

  /**
   *  停止行情推送
   */
  private stopHqPushServer() {
    if (TKSystemHelper.getConfig("system.isStartHqPush") == "1") {
      let pushServers: Map<string, TKServer> = TKGatewayManager.shareInstance().getPushServers();
      if (pushServers && pushServers.size > 0) {
        pushServers.forEach((value, key) => {
          let serverName: string = key;
          TKComQuotePushManager.shareInstance().stopQuotePush(serverName);
        });
      }
    }
  }

  /**
   * 更新配置
   * @param updateUrl
   */
  private updateBusConfig(updateUrl: string) {
    if (TKStringHelper.isNotBlank(updateUrl)) {
      // [[TKConfigUpdateManager shareInstance]updateConfig:updateUrl];
    }
  }

  /**
   *  是否APP第一次安装
   *
   * @return
   */
  public isAppFirstInstall(): boolean {
    let isFirst: string = TKCacheManager.shareInstance()
      .getFileCacheData(TKVersionManager.CACHE_ISFIRSTINSTALL) ?? "0";
    return isFirst != "0";
  }


  /**
   *  App是否进入了后台模式
   *
   * @return
   */
  public isApplicationDidEnterBackground(): boolean {
    return TKApplicationStateChangeListener.isApplicationBackground;
  }

  /**
   * 调用插件
   * @param pluginInvokeOption
   * @returns
   */
  public callPlugin(pluginInvokeOption: TKPluginInvokeOption): TKResultVO {
    if (this._isRunning) {
      pluginInvokeOption.isH5 = false;
      return TKPluginInvokeCenter.shareInstance().callPlugin(pluginInvokeOption);
    } else {
      let resultVO: TKResultVO = new TKResultVO();
      resultVO.errorNo = -1000;
      resultVO.errorInfo = "服务引擎未开启!";
      return resultVO;
    }
  }

  /**
   *  注册模块拦截器
   *
   * @param messageFilter   模块消息拦截器
   */
  public onRegisterModuleMessageFilter(messageFilter: TKModuleMessageFilterDelegate) {
    if (this._isRunning) {
      TKModuleEngine.shareInstance().onRegisterModuleMessageFilter(messageFilter);
    } else {
      TKLog.error("服务引擎未开启!");
    }
  }

  /**
   *  卸载模块拦截器
   *
   * @param messageFilter   模块消息拦截器
   */
  public unRegisterModuleMessageFilter(messageFilter: TKModuleMessageFilterDelegate) {
    if (this._isRunning) {
      TKModuleEngine.shareInstance().unRegisterModuleMessageFilter(messageFilter);
    } else {
      TKLog.error("服务引擎未开启!");
    }
  }

  /**
   * 注册消息模块
   *
   * @param messageModule   消息模块
   */
  public onRegisterMessageModule(messageModule: TKModuleDelegate) {
    if (this._isRunning) {
      TKModuleEngine.shareInstance().onRegisterMessageModule(messageModule);
    } else {
      TKLog.error("服务引擎未开启!");
    }
  }

  /**
   * 卸载消息模块
   * @param messageModuleName
   */
  public unRegisterMessageModule(messageModuleName: string) {
    if (this._isRunning) {
      TKModuleEngine.shareInstance().unRegisterMessageModule(messageModuleName);
    } else {
      TKLog.error("服务引擎未开启!");
    }
  }

  /**
   *  发送模块交互消息，进入模块消息分发引擎
   *
   * @param message    模块间消息
   */
  public sendModuleMessage(message: TKModuleMessage) {
    if (this._isRunning) {
      TKModuleEngine.shareInstance().sendModuleMessage(message);
    } else {
      TKLog.error("服务引擎未开启!");
    }
  }
}