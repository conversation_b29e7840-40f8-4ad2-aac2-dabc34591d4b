import { TKArrayHelper } from '../../../util/array/TKArrayHelper';
import { TKDataHelper } from '../../../util/data/TKDataHelper';
import { TKLog } from '../../../util/logger/TKLog';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKCommonService } from '../../mvc/service/TKCommonService';
import { TKRouterHelper } from '../../router/TKRouterHelper';
import { TKAppEngine } from '../TKAppEngine';
import { TKModuleDelegate, TKModuleMessageEngineDelegate, TKModuleMessageFilterDelegate } from './TKModuleDelegate';
import { TKModuleMessage, TKModuleMessageAction } from './TKModuleMessage';

/**
 * 模块引擎
 */
export class TKModuleEngine implements TKModuleMessageEngineDelegate {
  /*
   * 模块代理类
   */
  private moduleMap: Map<string, WeakRef<TKModuleDelegate>> = new Map();
  /**
   * 模块拦截器
   */
  private moduleFilters: Array<TKModuleMessageFilterDelegate> = new Array();
  /**
   * 服务类
   */
  private commonService: TKCommonService = new TKCommonService();
  /**
   * 单例对象
   */
  private static instance: TKModuleEngine | undefined = undefined;

  public static shareInstance(): TKModuleEngine {
    if (!TKModuleEngine.instance) {
      TKModuleEngine.instance = new TKModuleEngine();
    }
    return TKModuleEngine.instance;
  }

  /**
   * 注册模块拦截器
   * @param messageFilter
   */
  public onRegisterModuleMessageFilter(messageFilter: TKModuleMessageFilterDelegate) {
    if (!this.moduleFilters.includes(messageFilter)) {
      this.moduleFilters.push(messageFilter);
    }
  }

  /**
   *  卸载模块拦截器
   */
  public unRegisterModuleMessageFilter(messageFilter: TKModuleMessageFilterDelegate) {
    TKArrayHelper.removeObjectInArray(this.moduleFilters, messageFilter);
  }

  /**
   * 注册消息模块
   * @param messageModule
   */
  public onRegisterMessageModule(messageModule: TKModuleDelegate) {
    this.moduleMap.set(messageModule.getModuleName(), new WeakRef(messageModule));
  }

  /**
   * 卸载消息模块
   * @param messageModuleName
   */
  public unRegisterMessageModule(messageModuleName: string) {
    this.moduleMap.delete(messageModuleName);
  }

  /**
   * 发送模块消息
   * @param moduleMessage
   */
  public sendModuleMessage(moduleMessage: TKModuleMessage) {
    try {
      if (TKStringHelper.isBlank(moduleMessage.funcNo)) {
        let resultVO: TKResultVO = new TKResultVO();
        resultVO.errorNo = -1000;
        resultVO.errorInfo = "消息号不能为空!";
        if (moduleMessage.callBackFunc) {
          moduleMessage.callBackFunc(resultVO);
        }
        return;
      }
      if (TKStringHelper.isBlank(moduleMessage.targetModule)) {
        let resultVO: TKResultVO = new TKResultVO();
        resultVO.errorNo = -1001;
        resultVO.errorInfo = `消息号${moduleMessage.funcNo}：目标模块名称不能为空!`;
        if (moduleMessage.callBackFunc) {
          moduleMessage.callBackFunc(resultVO);
        }
        return;
      }
      if (!moduleMessage.isGreen) {
        let isFilterFlag: boolean = false;
        this.moduleFilters.forEach((moduleFilter, index) => {
          if (moduleFilter && !moduleFilter.onFilterModuleMessage(moduleMessage)) {
            let resultVO: TKResultVO = new TKResultVO();
            resultVO.errorNo = -1002;
            resultVO.errorInfo = `消息号${moduleMessage.funcNo}：被[${moduleFilter.constructor.name}]拦截！`;
            if (moduleMessage.callBackFunc) {
              moduleMessage.callBackFunc(resultVO);
            }
            isFilterFlag = true;
            return;
          }
        })
        if (isFilterFlag) {
          return;
        }
      }
      switch (moduleMessage.action) {
        case TKModuleMessageAction.Open: {
          this.processOpenModuleMessage(moduleMessage);
          break;
        }
        case TKModuleMessageAction.Push: {
          this.processOpenModuleMessage(moduleMessage);
          break;
        }
        case TKModuleMessageAction.Close: {
          this.processCloseModuleMessage(moduleMessage);
          break;
        }
        case TKModuleMessageAction.Change: {
          this.processChangeModuleMessage(moduleMessage);
          break;
        }
        case TKModuleMessageAction.NSNotify: {
          this.processNotifyModuleMessage(moduleMessage);
          break;
        }
        default:
          break;
      }
    } catch (error) {
      TKLog.error(`消息引擎执行模块消息异常:code为${error.code}, msg为${error.message}`);
    }
  }

  /**
   *  处理弹出消息
   *
   * @param message
   */
  private processOpenModuleMessage(moduleMessage: TKModuleMessage) {
    if (moduleMessage.funcNo == "001") {
      let param: Record<string, Object> = moduleMessage.param ?? {};
      //链接地址
      let url: string = TKMapHelper.getString(param, "url");
      let queryStr: string = "";
      let urlExt: Record<string, Object> = TKMapHelper.getJSON(param, "urlExt");
      if (Object.entries(urlExt).length > 0) {
        queryStr = TKDataHelper.mapToFormatUrl(urlExt);
      }
      url = url.indexOf("?") > 0 ? `${url}&${queryStr}` : `${url}?${queryStr}`;
      param["url"] = url;
      TKAppEngine.shareInstance().callPlugin({ funcNo: "50115", param: param, moduleName: moduleMessage.targetModule });
    } else {
      this.processDefaultModuleMessage(moduleMessage);
    }
  }

  /**
   *  处理弹出消息
   * @param message
   */
  private processCloseModuleMessage(moduleMessage: TKModuleMessage) {
    if (moduleMessage.funcNo == "002") {
      let param: Record<string, Object> = moduleMessage.param ?? {};
      let url: string = TKMapHelper.getString(param, "url");
      TKRouterHelper.back({ navComponentOrPathStack: moduleMessage.navComponentOrPathStack, urlOrName: url });
    } else {
      this.processDefaultModuleMessage(moduleMessage);
    }
  }

  /**
   *  处理切换消息
   *
   * @param message
   */
  private processChangeModuleMessage(moduleMessage: TKModuleMessage) {
    this.processDefaultModuleMessage(moduleMessage);
  }

  /**
   *  处理广播消息
   *
   * @param message
   */
  private processNotifyModuleMessage(moduleMessage: TKModuleMessage) {
    if (moduleMessage.funcNo == "003") {
      if (moduleMessage.targetModule == "*") {
        this.commonService.harmonyBatchCallJSWithParam(moduleMessage.param, moduleMessage.excludeModules);
      } else {
        this.commonService.harmonyCallJSWithParam(moduleMessage.param, moduleMessage.targetModule);
      }
    } else {
      if (moduleMessage.targetModule == "*") {
        this.moduleMap.forEach((value, key) => {
          let messageModule: TKModuleDelegate | undefined = value.deref();
          if (!moduleMessage.excludeModules || !moduleMessage.excludeModules.includes(key)) {
            if (messageModule) {
              messageModule.onModuleMessage(moduleMessage);
            }
          }
        })
      } else {
        let messageModule: TKModuleDelegate | undefined = this.moduleMap.get(moduleMessage.targetModule!)?.deref();
        if (messageModule) {
          messageModule.onModuleMessage(moduleMessage);
        }
      }
    }
  }

  /**
   *  模块间消息转发处理
   *
   * @param message
   */
  private processDefaultModuleMessage(moduleMessage: TKModuleMessage) {
    let messageModule: TKModuleDelegate | undefined = this.moduleMap.get(moduleMessage.targetModule!)?.deref();
    if (messageModule) {
      messageModule.onModuleMessage(moduleMessage);
    }
  }
}



