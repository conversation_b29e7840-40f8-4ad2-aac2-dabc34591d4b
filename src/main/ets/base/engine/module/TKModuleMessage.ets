import { TKResultVO } from '../../mvc/model/TKResultVO';

/**
 *  插件回调函数
 *
 * @param result
 */
export type TKModuleMessageCallBackFunc = (resultVO: TKResultVO) => void;

/**
 *  模块交互行为
 */
export enum TKModuleMessageAction {
  /**
   *  打开模块
   */
  Open = 0,
  /**
   *  打开模块，兼容逻辑
   */
  Push = 1,
  /**
   *  关闭模块
   */
  Close = 2,
  /**
   *  切换模块
   */
  Change = 3,
  /**
   *  通知模块
   */
  NSNotify = 4
}

/**
 * 模块间消息对象
 */
export class TKModuleMessage {
  /**
   *  消息功能号
   */
  public funcNo?: string;
  /**
   * 消息来源的模块名称
   */
  public sourceModule?: string;
  /**
   *  接收消息的模块名称
   */
  public targetModule?: string;
  /**
   * 接收消息的排除模块名称集合，一般用于群发排除
   */
  public excludeModules?: Array<string>;
  /**
   * 操作动作
   */
  public action?: TKModuleMessageAction;
  /**
   * 业务参数
   */
  public param?: Record<string, Object> = {};
  /**
   * 扩展对象
   */
  public userInfo?: Object;
  /**
   * 回调函数
   */
  public callBackFunc?: TKModuleMessageCallBackFunc;
  /**
   * 是否绿色通道,绿色通道可以绕过拦截器，默认是NO
   */
  public isGreen?: boolean = false;
  /**
   * 当前Nav子组件对象或者堆栈对象
   */
  navComponentOrPathStack?: CustomComponent | NavPathStack;
}

