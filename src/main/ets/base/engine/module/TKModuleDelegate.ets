import { TKModuleMessage } from './TKModuleMessage';

/**
 * 模块接口定义
 */
export interface TKModuleDelegate {

  /**
   * 获取模块名称
   * @returns
   */
  getModuleName(): string;

  /**
   *  处理模块消息引擎消息
   *
   * @param message 消息
   */
  onModuleMessage(message: TKModuleMessage): void;
}

/**
 *  模块消息拦截定义
 */
export interface TKModuleMessageFilterDelegate {
  /**
   *  拦截模块消息
   *
   * @param message 消息
   * @return YES表示继续，NO就会被中断
   */
  onFilterModuleMessage: (message: TKModuleMessage) => boolean;
}

/**
 *  模块消息引擎代理
 */
export interface TKModuleMessageEngineDelegate {

  /**
   *  注册模块拦截器
   *
   * @param messageFilter   模块消息拦截器
   */
  onRegisterModuleMessageFilter(messageFilter: TKModuleMessageFilterDelegate): void;

  /**
   *  卸载模块拦截器
   *
   * @param messageFilter   模块消息拦截器
   */
  unRegisterModuleMessageFilter(messageFilter: TKModuleMessageFilterDelegate): void;

  /**
   * 注册消息模块
   *
   * @param messageModule   消息模块
   */
  onRegisterMessageModule(messageModule: TKModuleDelegate): void;

  /**
   * 卸载消息模块
   * @param messageModuleName
   */
  unRegisterMessageModule(messageModuleName: string): void;

  /**
   *  发送模块交互消息，进入模块消息分发引擎
   *
   * @param message    模块间消息
   */
  sendModuleMessage(message: TKModuleMessage): void;
}
