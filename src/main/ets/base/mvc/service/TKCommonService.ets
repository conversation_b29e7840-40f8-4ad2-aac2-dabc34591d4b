import { T<PERSON><PERSON>Helper } from '../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKCharEncoding, TKContentType, TKDaoType, TKDataType, TKEncryMode, TKReqParamVO } from '../model/TKReqParamVO';
import { TKBaseService } from './TKBaseService';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKDataHelper } from '../../../util/data/TKDataHelper';
import { TKCacheManager } from '../../../util/cache/TKCacheManager';
import { TKBase64Helper } from '../../../util/crypto/TKBase64Helper';
import { TKServer } from './dao/socket/client/gateway/TKServer';
import { TKGatewayManager } from './dao/socket/client/gateway/TKGatewayManager';
import { TKHttpRoomServerListener } from './dao/http/client/gateway/TKHttpRoomServerListener';
import { TKNetHelper } from '../../../util/net/TKNetHelper';
import { TKJSProxyControllerManager } from '../../../components/webview/TKJSProxyControllerManager';
import { TKJSProxyController } from '../../../components/webview/TKJSProxyController';
import { TKLog } from '../../../util/logger/TKLog';
import { TKServiceCallBackFunc } from './protocol/TKServiceDelegate';
import { TKResultVO } from '../model/TKResultVO';

/**
 *  通用基础Service
 */
export class TKCommonService extends TKBaseService {
  //设置调用js的全局代理拦截器
  private static requestFlowNo: number = 0;

  /**
   产生请求流水号
   */
  public static generateRequestFlowNo(): string {
    let flowNo: number = TKCommonService.requestFlowNo;
    TKCommonService.requestFlowNo =
      (TKCommonService.requestFlowNo < Number.MAX_VALUE) ? TKCommonService.requestFlowNo + 1 : 0;
    return `${flowNo}`;
  }

  /**
   *  获取默认请求对象
   *
   * @return 请求对象
   */
  public createReqParamVO(): TKReqParamVO {
    let request: TKReqParamVO = new TKReqParamVO();
    request.flowNo = TKCommonService.generateRequestFlowNo();
    request.isFilterRepeatRequest = true;
    request.isAutoAddSysComParam = true;
    request.isGlobRequest = false;
    request.beginTime = new Date().getTime();
    request.isPost = true;
    request.contentType = TKContentType.NONE;
    request.protocol = TKDaoType.Http;
    request.isShowWait = false;
    request.isReturnList = true;
    request.isCache = false;
    request.isUpload = false;
    request.isKeepOriginalParam = false;
    request.isRestFull = TKSystemHelper.getConfig("networkRequest.isRequestURLRestFull") == "1";
    request.isURLEncode = TKSystemHelper.getConfig("networkRequest.isRequestURLEncode") != "0";
    request.isURLSign = TKSystemHelper.getConfig("networkRequest.isRequestURLSign") == "1";
    request.signKey = TKSystemHelper.getConfig("networkRequest.requestSignKey");
    request.signAppId = TKSystemHelper.getConfig("networkRequest.requestSignAppId");
    request.isURLEncry = TKSystemHelper.getConfig("networkRequest.isRequestURLEncrypt") == "1";
    request.isURLResponseEncry = TKSystemHelper.getConfig("networkRequest.isResponseURLEncrypt") == "1";
    request.encryMode =
      (TKSystemHelper.getConfig("networkRequest.requestEncryptMode") == "aes") ? TKEncryMode.Aes : TKEncryMode.Des;
    request.encryKey = TKSystemHelper.getConfig("networkRequest.requestEncryptKey");
    request.channelId = TKSystemHelper.getConfig("networkRequest.requestChannelId");
    request.charEncoding = TKCharEncoding.DEFAULT;
    request.isValidatesSSLCertificate = TKSystemHelper.getConfig("networkRequest.isValidatesSSLCertificate") != "0";
    return request;
  }


  /**
   *  构建初始化请求对象
   * @param url
   http/https格式：http://地址:端口/servlet/json?key=value&key=value
   socket格式：    socket://busconfig.xml中的serverID?companyId=THINKIVE&systemId=MALL&key=value
   server.xml格式：server://server.xml中的serverID?key=value&key=value
   * @param reqMode
   请求加密模式(0:正常，1:Http加签，2：Http加密，3：Http加密加签，4:Socket加密，5：Socket压缩，6：Socket压缩加密，7：Http加密加签并加密响应包，8：微服务正常，9：微服务加签，10：微服务加密加签，11：微服务加密加签并加密响应包)
   */
  public createReqParamByReqMode(url: string, reqMode: string) {
    let mode: number = 0; //0:http 1:socket 2:server.xml
    if (!TKStringHelper.startsWith(url, "http://") && !TKStringHelper.startsWith(url, "https")) {
      if (TKStringHelper.startsWith(url, "socket://")) {
        mode = 1;
      } else if (TKStringHelper.startsWith(url, "server://")) {
        mode = 2;
      } else {
        mode = 3;
      }
    }
    let paramConfig: Record<string, Object | undefined> = {};
    let temp: Array<string> = TKStringHelper.split(url, "?");
    url = temp[0];
    if (temp.length > 1) {
      let paramStr: string = temp[1];
      TKMapHelper.merge(paramConfig, TKDataHelper.urlToFormatMap(paramStr));
    }
    //param: [内存缓存参数]key=@{name} [文件缓存参数] key=#{name}
    Object.entries(paramConfig).forEach((e, i) => {
      let key: string = e[0];
      let value: string = e[1] === undefined ? "" : e[1].toString();
      if (TKStringHelper.startsWith(value, "@{")) {
        value = value.substring(2, value.length - 1);
        let memValue: string | undefined = TKCacheManager.shareInstance().getMemCacheData(value);
        if (memValue !== undefined) {
          paramConfig[key] = memValue;
        }
      } else if (TKStringHelper.startsWith(value, "#{")) {
        value = value.substring(2, value.length - 1);
        let fileValue: string | undefined = TKCacheManager.shareInstance().getFileCacheData(value);
        if (fileValue !== undefined) {
          paramConfig[key] = fileValue;
        }
      }
    });
    let companyId: string = TKMapHelper.getString(paramConfig, "@companyId");
    if (TKStringHelper.isNotBlank(companyId)) {
      paramConfig["@companyId"] = undefined;
    } else {
      companyId = TKMapHelper.getString(paramConfig, "companyId");
      if (TKStringHelper.isNotBlank(companyId)) {
        paramConfig["companyId"] = undefined;
      } else {
        companyId = "THINKIVE";
      }
    }
    let systemId: string = TKMapHelper.getString(paramConfig, "@systemId");
    if (TKStringHelper.isNotBlank(systemId)) {
      paramConfig["@systemId"] = undefined;
    } else {
      systemId = TKMapHelper.getString(paramConfig, "systemId");
      if (TKStringHelper.isNotBlank(systemId)) {
        paramConfig["systemId"] = undefined;
      } else {
        systemId = "THINKIVE";
      }
    }
    if (mode > 0) {
      if (mode == 1) {
        url = TKStringHelper.replace(url, "socket://", "");
        url = TKStringHelper.split(url, "/")[0];
      } else {
        url = TKStringHelper.replace(url, "server://", "");
        let temp: Array<string> = TKStringHelper.split(url, "/");
        url = TKHttpRoomServerListener.shareInstance().getFlastHost(temp[0]);
        if (temp.length > 1) {
          url = TKNetHelper.getSchemeHostPortByURL(url);
          temp.splice(0, 1);
          url = url + "/" + temp.join("/");
        } else {
          if (url.indexOf("/servlet/json") < 0) {
            url = TKNetHelper.getSchemeHostPortByURL(url) + "/servlet/json";
          }
        }
      }
    }
    let reqParamVO: TKReqParamVO = this.createReqParamVO();
    reqParamVO.url = url;
    reqParamVO.protocol = (mode == 1) ? TKDaoType.Socket : TKDaoType.Http;
    if (mode == 1) {
      reqParamVO.companyId = companyId;
      reqParamVO.systemId = systemId;
    }
    //请求加密模式
    this.processRequestMode(reqParamVO, reqMode);
    reqParamVO.reqParam = paramConfig;
    return reqParamVO;
  }

  /**
   *  请求服务
   *
   * @param reqParamVo   请求对象
   */
  public serviceInvokePromise(reqParamVO: TKReqParamVO): Promise<TKResultVO> {
    return new Promise<TKResultVO>((resolve, reject) => {
      super.serviceInvoke(reqParamVO, (resultVO) => {
        resolve(resultVO);
      });
    });
  }

  /**
   *  原生调用js，单发消息
   * @param param              json参数
   * @param webviewName        webviwe名称,空为当前控制器
   */
  public async harmonyCallJSWithParam(param?: Record<string, Object>,
    webviewName?: string): Promise<string | undefined> {
    webviewName = TKStringHelper.isNotBlank(webviewName) ? webviewName : TKJSProxyControllerManager.shareInstance()
      .currentJSProxyControllerName;
    let jsProxyController: TKJSProxyController | undefined = TKJSProxyControllerManager.shareInstance()
      .getJSProxyController(webviewName);
    if (jsProxyController) {
      return await jsProxyController.harmonyCallJSFunction("callMessage", param);
    } else {
      if (TKJSProxyController.harmonyCallJSFunctionFilter && TKJSProxyController.harmonyCallJSFunctionFilter.onFilter) {
        if (!TKJSProxyController.harmonyCallJSFunctionFilter.onFilter(webviewName ?? "", "callMessage", param)) {
          return undefined;
        }
      } else {
        TKLog.error(`浏览器对象[${webviewName}]未找到！`);
      }
      return undefined;
    }
  }

  /**
   *  鸿蒙调用JS，群发调用，排除某个模块WebView当前对象
   *
   * @param param        入参
   * @param webViewNames 排除WebView模块名称
   */
  public harmonyBatchCallJSWithParam(param: Record<string, Object> = {}, excludeNames?: Array<string>) {
    let excludeJSProxyControllers: Array<TKJSProxyController> = new Array<TKJSProxyController>();
    if (excludeNames && excludeNames.length > 0) {
      for (let excludeName of excludeNames) {
        let excludeJSProxyController: TKJSProxyController | undefined = TKJSProxyControllerManager.shareInstance()
          .getJSProxyController(excludeName);
        if (excludeJSProxyController) {
          excludeJSProxyControllers.push(excludeJSProxyController);
        }
      }
    }
    let jsProxyControllers: Array<TKJSProxyController> = TKJSProxyControllerManager.shareInstance()
      .getAllJSProxyController();
    if (jsProxyControllers && jsProxyControllers.length > 0) {
      for (let jsProxyController of jsProxyControllers) {
        if (excludeJSProxyControllers.indexOf(jsProxyController) < 0) {
          jsProxyController.harmonyCallJSFunction("callMessage", param);
        }
      }
    }
  }

  /**
   *  原生调用js函数
   * @param funcName          函数名
   * @param funcParam         参数
   * @param webviewName        webviwe名称,空为当前控制器
   */
  public async harmonyCallJSFunction(funcName: string, funcParam?: Record<string, Object> | Array<Object>,
    webviewName?: string,): Promise<string | undefined> {
    webviewName = TKStringHelper.isNotBlank(webviewName) ? webviewName : TKJSProxyControllerManager.shareInstance()
      .currentJSProxyControllerName;
    let jsProxyController: TKJSProxyController | undefined = TKJSProxyControllerManager.shareInstance()
      .getJSProxyController(webviewName);
    if (jsProxyController) {
      return await jsProxyController.harmonyCallJSFunction(funcName, funcParam);
    } else {
      if (TKJSProxyController.harmonyCallJSFunctionFilter && TKJSProxyController.harmonyCallJSFunctionFilter.onFilter) {
        if (!TKJSProxyController.harmonyCallJSFunctionFilter.onFilter(webviewName ?? "", funcName, funcParam)) {
          return undefined;
        }
      } else {
        TKLog.error(`浏览器对象[${webviewName}]未找到！`);
      }
      return undefined;
    }
  }


  /**
   *  鸿蒙调用JS，群发调用，排除某个模块WebView当前对象
   *
   * @param param        入参
   * @param webViewNames 排除WebView模块名称
   */
  public harmonyBatchCallJSFunction(funcName: string, funcParam?: Record<string, Object> | Array<Object>,
    excludeNames?: Array<string>) {
    let excludeJSProxyControllers: Array<TKJSProxyController> = new Array<TKJSProxyController>();
    if (excludeNames && excludeNames.length > 0) {
      for (let excludeName of excludeNames) {
        let excludeJSProxyController: TKJSProxyController | undefined = TKJSProxyControllerManager.shareInstance()
          .getJSProxyController(excludeName);
        if (excludeJSProxyController) {
          excludeJSProxyControllers.push(excludeJSProxyController);
        }
      }
    }
    let jsProxyControllers: Array<TKJSProxyController> = TKJSProxyControllerManager.shareInstance()
      .getAllJSProxyController();
    if (jsProxyControllers && jsProxyControllers.length > 0) {
      for (let jsProxyController of jsProxyControllers) {
        if (excludeJSProxyControllers.indexOf(jsProxyController) < 0) {
          jsProxyController.harmonyCallJSFunction(funcName, funcParam);
        }
      }
    }
  }

  /**
   *  实现网络请求代理
   *
   * @param moduleName   模块名称
   * @param protocol     网络协议（0：HTTP/HTTPS 1:行情长连接 2:交易长连接 3:资讯长连接 4:天风新版统一接入长连接 5：思迪新版统一接入长连接）
   * @param url         网络地址 (URL地址或站点名称)
   * @param paramMap     网络参数
   * @param headerMap    请求头参数
   * @param isPost       是否post
   * @param timeOut      超时时间(单位秒)
   * @param mode        请求加密模式(0:正常，1:Http加签，2：Http加密，3：Http加密加签，4:Socket加密，5：Socket压缩，6：Socket压缩加密，7：Http加密加签并加密响应包，
   *                              8：微服务正常，9：微服务加签，10：微服务加密加签，11：微服务加密加签并加密响应包)
   * @param isFilterRepeatRequest  是否开启重复请求拦截机制，默认不开启
   * @param filterRepeatRequestTimeOut 重复请求，进行请求拦截时间，单位毫秒
   * @param isEncodeURL            是否编码入参，默认是编码
   * @param isAutoAddSysComParam   是否自动添加系统公共入参，默认是添加
   * @param isGlobRequest          是否全局请求，默认是NO
   * @param netLinkMode           网络链路(0:普通链路，1：国密链路)，默认是0
   * @param callBackFunc 回调函数
   */
  public doProxyNetWorkService(moduleName: string, protocol: string, url: string,
    paramMap: Record<string, Object | undefined>, headerMap: Record<string, Object | undefined>, isPost: boolean,
    timeOut: number, mode: string, isFilterRepeatRequest: boolean, filterRepeatRequestTimeOut: number,
    isEncodeURL: boolean, isAutoAddSysComParam: boolean, isGlobRequest: boolean, netLinkMode: string,
    callBackFunc: TKServiceCallBackFunc): string {
    let reqParamVO: TKReqParamVO = this.createReqParamVO();
    reqParamVO.url = url;
    reqParamVO.protocol = TKDaoType.Http;
    reqParamVO.isFilterRepeatRequest = isFilterRepeatRequest;
    reqParamVO.filterRepeatRequestTimeOut = filterRepeatRequestTimeOut;
    reqParamVO.isAutoAddSysComParam = isAutoAddSysComParam;
    reqParamVO.isGlobRequest = isGlobRequest;
    reqParamVO.isURLEncode = isEncodeURL;
    if (netLinkMode == "1") {
      reqParamVO.daoName = TKSystemHelper.getConfig("networkRequest.gmDaoName");
    }
    if (protocol == "1") {
      reqParamVO.protocol = TKDaoType.Socket;
    } else if (protocol == "2") {
      reqParamVO.protocol = TKDaoType.Socket;
    } else if (protocol == "3") {
      reqParamVO.protocol = TKDaoType.Socket;
    } else if (protocol == "4") {
      reqParamVO.protocol = TKDaoType.Socket;
    } else if (protocol == "5") {
      reqParamVO.protocol = TKDaoType.Socket;
    } else if (protocol == "6") {
      reqParamVO.protocol = TKDaoType.HttpToSocket;
    } else if (protocol == "7") {
      reqParamVO.protocol = TKDaoType.Socket;
    }
    if (reqParamVO.protocol == TKDaoType.Http) {
      let httpUrl: string = reqParamVO.url.toLowerCase();
      if (!TKStringHelper.startsWith(httpUrl, "http://") && !TKStringHelper.startsWith(httpUrl, "https")) {
        reqParamVO.url = TKHttpRoomServerListener.shareInstance().getFlastHost(url);
      }
    }
    reqParamVO.reqParam = paramMap;
    reqParamVO.isPost = isPost;
    reqParamVO.timeOut = timeOut;
    //处理各自请求模式，加密签名等
    this.processRequestMode(reqParamVO, mode);
    if (TKStringHelper.isNotBlank(moduleName)) {
      let signConfig: string = TKSystemHelper.getConfig(`networkRequestSign.${moduleName}`);
      if (TKStringHelper.isNotBlank(signConfig)) {
        let configs: Array<string> = TKStringHelper.split(signConfig, "|");
        reqParamVO.signAppId = configs[0];
        reqParamVO.signKey = configs[1];
        reqParamVO.encryKey = configs[2];
      }
    }
    if (headerMap && Object.entries(headerMap).length > 0) {
      reqParamVO.companyId = TKMapHelper.getString(headerMap, "companyId")
      reqParamVO.systemId = TKMapHelper.getString(headerMap, "systemId");
      reqParamVO.isLoginReq = TKMapHelper.getString(headerMap, "loginFlag") == "1";
      reqParamVO.busServerId = TKMapHelper.getString(headerMap, "busServerId");
      reqParamVO.busFuncNo = TKMapHelper.getString(headerMap, "busFuncNo");
      headerMap["companyId"] = undefined;
      headerMap["systemId"] = undefined;
      headerMap["loginFlag"] = undefined;
      headerMap["busServerId"] = undefined;
      headerMap["busFuncNo"] = undefined;

      //处理文件上传
      let fileUpload: string = TKMapHelper.getString(headerMap, "fileUpload");
      let fileFileds: string = TKMapHelper.getString(headerMap, "fileFileds");
      if (paramMap && fileUpload == "1" && TKStringHelper.isNotBlank(fileFileds)) {
        reqParamVO.isUpload = true;
        let fileds: Array<string> = TKStringHelper.split(fileFileds, "|");
        if (fileds && fileds.length > 0) {
          fileds.forEach((filed, index) => {
            let fileBase64Data: string = TKMapHelper.getString(paramMap, filed);
            if (TKStringHelper.isNotBlank(fileBase64Data)) {
              let fileData: Uint8Array = TKBase64Helper.dataWithBase64Decode(fileBase64Data);
              if (fileData) {
                paramMap[`${filed}@@F`] = fileData;
                paramMap[filed] = undefined;
              }
            }
          });
        }
      }
      headerMap["fileUpload"] = undefined;
      headerMap["fileFileds"] = undefined;
    }
    if (reqParamVO.protocol != TKDaoType.Http) {
      let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(reqParamVO.busServerId);
      if (!server) {
        reqParamVO.busServerId = TKHttpRoomServerListener.shareInstance().getFlastHost(reqParamVO.busServerId);
      }
    }
    reqParamVO.headerFieldDic = headerMap;
    setTimeout(() => {
      this.serviceInvoke(reqParamVO, callBackFunc);
    }, 0);
    return reqParamVO.flowNo;
  }

  /**
   * 处理请求对象的加密签名等各种模式
   */
  private processRequestMode(reqParamVO: TKReqParamVO, reqMode: string) {
    if (TKStringHelper.isBlank(reqMode)) {
      reqMode = "0";
    }
    if (reqMode == "0") {
      reqParamVO.isURLSign = false;
      reqParamVO.isURLEncry = false;
      reqParamVO.isURLResponseEncry = false;
      reqParamVO.isRestFull = false;
      reqParamVO.dataType = TKDataType.Normal;
    } else if (reqMode == "1") {
      reqParamVO.isURLSign = true;
      reqParamVO.isURLEncry = false;
      reqParamVO.isURLResponseEncry = false;
      reqParamVO.isRestFull = false;
    } else if (reqMode == "2") {
      reqParamVO.isURLSign = false;
      reqParamVO.isURLEncry = true;
      reqParamVO.isURLResponseEncry = false;
      reqParamVO.isRestFull = false;
    } else if (reqMode == "3") {
      reqParamVO.isURLSign = true;
      reqParamVO.isURLEncry = true;
      reqParamVO.isURLResponseEncry = false;
      reqParamVO.isRestFull = false;
    } else if (reqMode == "4") {
      reqParamVO.dataType = TKDataType.Encryt;
    } else if (reqMode == "5") {
      reqParamVO.dataType = TKDataType.Compress;
    } else if (reqMode == "6") {
      reqParamVO.dataType = TKDataType.Compress_Encryt;
    } else if (reqMode == "7") {
      reqParamVO.isURLSign = true;
      reqParamVO.isURLEncry = true;
      reqParamVO.isURLResponseEncry = true;
      reqParamVO.isRestFull = false;
    } else if (reqMode == "8") {
      reqParamVO.isURLSign = false;
      reqParamVO.isURLEncry = false;
      reqParamVO.isURLResponseEncry = false;
      reqParamVO.dataType = TKDataType.Normal;
      reqParamVO.isRestFull = true;
    } else if (reqMode == "9") {
      reqParamVO.isURLSign = true;
      reqParamVO.isURLEncry = false;
      reqParamVO.isURLResponseEncry = false;
      reqParamVO.isRestFull = true;
    } else if (reqMode == "10") {
      reqParamVO.isURLSign = true;
      reqParamVO.isURLEncry = true;
      reqParamVO.isURLResponseEncry = false;
      reqParamVO.isRestFull = true;
    } else if (reqMode == "11") {
      reqParamVO.isURLSign = true;
      reqParamVO.isURLEncry = true;
      reqParamVO.isURLResponseEncry = true;
      reqParamVO.isRestFull = true;
    }
  }
}