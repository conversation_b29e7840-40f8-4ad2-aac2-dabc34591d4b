import { TKCacheManager } from '../../../util/cache/TKCacheManager';
import { TKMd5Helper } from '../../../util/crypto/TKMd5Helper';
import { TKUUIDHelper } from '../../../util/crypto/TKUUIDHelper';
import { TKObjectHelper } from '../../../util/data/TKObjectHelper';
import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKLog } from '../../../util/logger/TKLog';
import { TKNetHelper, TKNetworkType } from '../../../util/net/TKNetHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKDaoType, TKReqParamVO } from '../model/TKReqParamVO';
import { TKResultVO } from '../model/TKResultVO';
import { TKDaoFactory } from './dao/TKDaoFactory';
import { TKServiceDaoDelegate } from './protocol/TKServiceDaoDelegate';
import { TKServiceCallBackFunc, TKServiceDelegate } from './protocol/TKServiceDelegate';
import { TKServiceFilterDelegate } from './protocol/TKServiceFilterDelegate';
import { TKCacheType } from '../../../util/cache/domain/TKCacheVO';
import { TKLoadInfoVO } from '../model/TKLoadInfoVO';
import { TKComponentContent, TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKLoadingDialogOption } from '../../../components/dialog/TKLoadingDialog';

/**
 * 基础服务层
 */
export class TKBaseService implements TKServiceDelegate, TKServiceFilterDelegate {
  /**
   *  服务器拦截器代理对象
   */
  public filterDelegate: TKServiceFilterDelegate | undefined = undefined;
  /**
   * 请求队列
   */
  private reqMap: Map<string, TKReqParamVO> = new Map<string, TKReqParamVO>();
  /**
   * 回调函数
   */
  private callBackFuncMap: Map<string, TKServiceCallBackFunc> = new Map<string, TKServiceCallBackFunc>();
  /**
   *  缓存使用情况字典
   如果没有使用过，并且启用了缓存策略，那边有缓存就返回缓存，同时延长100毫秒执行请求，更新缓存，如果没有缓存，就直接发请求，同时更新缓存
   如果使用过，就直接发请求，更新缓存即可
   */
  private cacheUseMap: Map<string, boolean> = new Map<string, boolean>();
  /**
   * 类实例的唯一标识
   */
  private classIntanceId: string = TKUUIDHelper.uuid();
  /**
   *  请求本地缓存前缀
   */
  private static readonly CACHE_REQUEST_PREFIX: string = "cache_req";
  /**
   * 重复请求拦截缓存前缀
   */
  private static readonly CACHE_FRR_REQUEST_PREFIX: string = "cache_frr_req";
  /**
   * Loading加载框
   */
  private _loadingDialog: TKComponentContent<Object> | undefined = undefined;

  public get loadingDialog(): TKComponentContent<Object> {
    if (!this._loadingDialog) {
      this._loadingDialog = TKDialogHelper.createLoadingDialog();
    }
    return this._loadingDialog;
  }

  public set loadingDialog(loadingDialog: TKComponentContent<Object> | undefined) {
    this._loadingDialog = loadingDialog;
  }

  /**
   *  处理失败
   *
   * @param resultVo 失败的错误对象
   */
  public onFault(resultVO: TKResultVO) {
    this.onResult(resultVO);
  }

  /**
   *  请求成功
   *
   * @param result 返回的结果对象
   */
  public onResult(resultVO: TKResultVO) {
    try {
      if (resultVO.reqParamVO) {
        let reqParamVO: TKReqParamVO = resultVO.reqParamVO;
        let beginTime: number = reqParamVO.beginTime;
        let endTime: number = new Date().getTime();
        TKLog.debug(`[flowNo:${reqParamVO.flowNo}]请求结束.......花费时间[网络+数据解析](${endTime -
          beginTime}ms),返回结果---->${TKObjectHelper.toJsonStr(resultVO)}`);
        //缓存的增加判断
        if (reqParamVO.isCache) {
          try {
            if (resultVO.errorNo == 0) {
              //拼装请求唯一ID
              let requestUniqueId: string = this.getRequestUniqueId(reqParamVO);
              let requestMD5UniqueId: string =
                this.getRequestMD5UniqueId(requestUniqueId, TKBaseService.CACHE_REQUEST_PREFIX, false);
              let cacheData: TKResultVO = TKObjectHelper.copy(resultVO);
              cacheData.reqParamVO = undefined;
              cacheData.cookies = undefined;
              TKCacheManager.shareInstance()
                .saveCacheData(requestMD5UniqueId, cacheData, reqParamVO.cacheType, reqParamVO.cacheTime);
              this.cacheUseMap.set(requestMD5UniqueId, true);
            }
          } catch (error) {
            TKLog.error(`网络通信服务层请求返回数据缓存异常:${error.message}`)
          }
        }
        this.onCacheResult(resultVO);
      }
    } catch (error) {
      TKLog.error(`网络通信服务层请求返回异常:${error.message}`)
    }
  }

  /**
   * 获取请求Dao
   */
  private getRequestDao(reqParamVO: TKReqParamVO): TKServiceDaoDelegate {
    let dao: TKServiceDaoDelegate | undefined = undefined;
    if (TKStringHelper.isBlank(reqParamVO.daoName)) {
      let daoType: TKDaoType = reqParamVO.protocol;
      dao = TKDaoFactory.getDao(daoType);
    } else {
      dao = TKDaoFactory.getDao(reqParamVO.daoName);
    }
    return dao as TKServiceDaoDelegate;
  }

  /**
   *  请求服务
   *
   * @param reqParamVo   请求对象
   * @param callBackFunc 回调函数
   */
  public serviceInvoke(reqParamVO: TKReqParamVO, callBackFunc?: TKServiceCallBackFunc) {
    try {
      TKLog.debug(`网络通信服务层发送请求[flowNo:${reqParamVO.flowNo}]`);
      reqParamVO.isHasCallBackFunc = callBackFunc ? true : false;
      let isPass: boolean = this.requestFilter(reqParamVO);
      if (isPass) {
        if (this.filterDelegate && this.filterDelegate != this && this.filterDelegate.requestFilter) {
          isPass = this.filterDelegate.requestFilter(reqParamVO);
        }
      } else {
        TKLog.warn(`[flowNo:${reqParamVO.flowNo}]重复请求，不予处理。`);
        let resultVO: TKResultVO = new TKResultVO();
        resultVO.errorNo = -111;
        resultVO.errorInfo = "重复请求，不予处理。";
        resultVO.reqParamVO = reqParamVO;
        this.onCallBackFunc(callBackFunc, resultVO);
        return;
      }
      if (isPass) {
        //流水号
        let flowNo: string = reqParamVO.flowNo;
        if (callBackFunc) {
          //加入回调函数队列
          this.callBackFuncMap.set(flowNo, callBackFunc);
          //加入请求队列
          this.reqMap.set(flowNo, reqParamVO);
          TKLog.debug(`网络通信服务层当前请求队列${JSON.stringify(Array.from(this.reqMap.keys()))}`);
        }
        //发送请求
        let dao: TKServiceDaoDelegate = this.getRequestDao(reqParamVO);
        if (dao) {
          reqParamVO.serviceDelegate = this as TKServiceDelegate;
          //缓存的增加判断
          if (reqParamVO.isCache) {
            //拼装请求唯一ID
            let requestUniqueId: string = this.getRequestUniqueId(reqParamVO);
            let requestMD5UniqueId: string =
              this.getRequestMD5UniqueId(requestUniqueId, TKBaseService.CACHE_REQUEST_PREFIX, false);
            let isFirstUse: boolean = !this.cacheUseMap.get(requestMD5UniqueId);
            if (isFirstUse) {
              let resultVO: TKResultVO | undefined = TKCacheManager.shareInstance()
                .getCacheData(requestMD5UniqueId, reqParamVO.cacheType) as TKResultVO | undefined;
              if (resultVO) {
                resultVO = TKObjectHelper.clone(resultVO);
                resultVO.reqParamVO = reqParamVO;
                TKLog.debug(`[flowNo:${reqParamVO.flowNo}]------>缓存命中---->入参:${requestUniqueId}`);
                resultVO.isCacheData = true;
                this.onCacheResult(resultVO);
                this.cacheUseMap.set(requestMD5UniqueId, true);
                if (reqParamVO.cacheType == TKCacheType.Mem_AutoUpdate ||
                  reqParamVO.cacheType == TKCacheType.File_AutoUpdate ||
                  reqParamVO.cacheType == TKCacheType.DB_AutoUpdate) {
                  let This = this;
                  setTimeout(() => {
                    //流水号
                    if (callBackFunc) {
                      //加入回调函数队列
                      This.callBackFuncMap.set(flowNo, callBackFunc);
                      //加入请求队列
                      This.reqMap.set(flowNo, reqParamVO);
                    }
                    dao.invoke(reqParamVO);
                  }, 100);
                }
              } else {
                dao.invoke(reqParamVO);
              }
            } else {
              dao.invoke(reqParamVO);
            }
          } else {
            dao.invoke(reqParamVO);
          }
        } else {
          TKLog.error("请求通信Dao不存在！");
        }
      } else {
        TKLog.warn(`[flowNo:${reqParamVO.flowNo}]请求被拦截，不予处理。`);
        let resultVO: TKResultVO = new TKResultVO();
        resultVO.errorNo = -111;
        resultVO.errorInfo = "请求被拦截，不予处理。";
        resultVO.reqParamVO = reqParamVO;
        this.onCallBackFunc(callBackFunc, resultVO);
      }
    } catch (error) {
      TKLog.error(`网络通信服务层请求返回异常:${error.message}`)
    }
  }

  /**
   *  取消请求
   *
   * @param flowNo  流水号
   */
  public cancelRequest(flowNo: string) {
    try {
      TKLog.debug(`网络通信服务层取消请求[flowNo:${flowNo}]`);
      if (this.reqMap && this.reqMap.size > 0) {
        let reqParamVO: TKReqParamVO | undefined = this.reqMap.get(flowNo);
        if (reqParamVO) {
          let dao: TKServiceDaoDelegate = this.getRequestDao(reqParamVO);
          dao.clearRequest(flowNo);
          let resultVO: TKResultVO = new TKResultVO();
          resultVO.errorNo = -112;
          resultVO.errorInfo = "请求被取消";
          resultVO.reqParamVO = reqParamVO;
          this.onCacheResult(resultVO);
          this.onClose(reqParamVO);
        }
      }
    } catch (error) {
      TKLog.error(`网络通信服务层取消请求异常:${error.message}`)
    }
  }

  /**
   *  清除请求
   *
   * @param flowNo  流水号
   */
  public clearRequest(flowNo: string) {
    try {
      TKLog.debug(`网络通信服务层清理请求[flowNo:${flowNo}]`);
      if (this.reqMap && this.reqMap.size > 0) {
        let reqParamVO: TKReqParamVO | undefined = this.reqMap.get(flowNo);
        if (reqParamVO) {
          let dao: TKServiceDaoDelegate = this.getRequestDao(reqParamVO);
          dao.clearRequest(flowNo);
          //清除请求队列
          this.reqMap.delete(flowNo);
          //清除回调函数
          this.callBackFuncMap.delete(flowNo);
          this.onClose(reqParamVO);
        }
      }
    } catch (error) {
      TKLog.error(`网络通信服务层清理请求异常:${error.message}`)
    }
  }

  /**
   *  根据流水号获取请求
   *
   * @param flowNo 流水号
   *
   * @return 请求对象
   */
  public getRequestWithFlowNo(flowNo: string): TKReqParamVO | undefined {
    return this.reqMap.get(flowNo);
  }

  /**
   *  请求开始
   */
  public onOpen(reqParamVO: TKReqParamVO) {
    try {
      TKLog.debug(`[flowNo:${reqParamVO.flowNo}]请求开始.......`);
      if (reqParamVO.isShowWait) {
        this.loadingDialog.reuse({ tip: reqParamVO.waitTip, angle: reqParamVO.waitAngle } as TKLoadingDialogOption)
        this.loadingDialog.open();
      }
    } catch (error) {
      TKLog.error(`网络通信服务层请求开始异常:${error.message}`)
    }
  }

  /**
   *  请求中
   * @param result 数据对象
   */
  public onProgress(reqParamVO: TKReqParamVO, loadInfo: TKLoadInfoVO) {
    try {
      TKLog.debug(`[flowNo:${reqParamVO.flowNo}]上传数据(${loadInfo.bytesTotal}:${loadInfo.bytesLoaded})`);
    } catch (error) {
      TKLog.error(`网络通信服务层文件上传请求过程异常:${error.message}`)
    }
  }

  /**
   *  请求关闭
   */
  public onClose(reqParamVO: TKReqParamVO) {
    try {
      if (reqParamVO) {
        let beginTime: number = reqParamVO.beginTime;
        let endTime: number = new Date().getTime();
        TKLog.debug(`[flowNo:${reqParamVO.flowNo}]请求结束.......总花费时间[网络+数据解析+业务处理](${endTime -
          beginTime})ms`);
      }
      if (reqParamVO.isShowWait) {
        this.loadingDialog.close();
        this.loadingDialog = undefined;
      }
    } catch (error) {
      TKLog.error(`网络通信服务层文件上传请求关闭异常:${error.message}`)
    }
  }

  /**
   *  取消所有请求
   */
  public cancelAllRequest() {
    try {
      TKLog.debug(`网络通信服务层取消所有请求=>${JSON.stringify(Array.from(this.reqMap.keys()))}`);
      if (this.reqMap && this.reqMap.size > 0) {
        Array.from(this.reqMap.keys()).forEach((flowNo, index) => {
          let reqParamVO: TKReqParamVO | undefined = this.reqMap.get(flowNo);
          if (reqParamVO && !reqParamVO.isGlobRequest) {
            this.cancelRequest(flowNo);
          }
        });
      }
    } catch (error) {
      TKLog.error(`网络通信服务层取消所有待发请求异常:${error.message}`)
    }
  }

  /**
   *  清理所有请求
   */
  public clearAllRequest() {
    try {
      TKLog.debug(`网络通信服务层清理所有请求=>${JSON.stringify(Array.from(this.reqMap.keys()))}`);
      if (this.reqMap && this.reqMap.size > 0) {
        Array.from(this.reqMap.keys()).forEach((flowNo, index) => {
          let reqParamVO: TKReqParamVO | undefined = this.reqMap.get(flowNo);
          if (reqParamVO && !reqParamVO.isGlobRequest) {
            this.clearRequest(flowNo);
          }
        });
      }
    } catch (error) {
      TKLog.error(`网络通信服务层清理所有待发请求异常:${error.message}`)
    }
  }

  /**
   *
   *  获取当前请求队列长度
   */
  public getRequestQueueLength(): number {
    return this.reqMap.size;
  }

  /**
   *  请求拦截器
   *
   * @param reqParamVo 请求对象
   *
   * @return
   */
  public requestFilter(reqParamVO: TKReqParamVO): boolean {
    if (!reqParamVO.reqParam) {
      reqParamVO.reqParam = {};
    }
    let reqParam: Record<string, Object | undefined> = reqParamVO.reqParam;
    if (!reqParamVO.isKeepOriginalParam) {
      Object.entries(reqParam).forEach((e, i) => {
        let k: string = e[0] as string;
        let v: Object = e[1] as Object;
        if (TKObjectHelper.nonNull(v)) {
          if (typeof v === "object" && !(v instanceof Uint8Array) && !(v instanceof ArrayBuffer)) {
            v = TKObjectHelper.toJsonStr(v);
          }
          reqParam[k] = v;
        }
      });
    }

    //校验是否为重复请求
    if (reqParamVO.isFilterRepeatRequest) {
      //拼装请求唯一ID
      let requestUniqueId: string = this.getRequestUniqueId(reqParamVO);
      let requestMD5UniqueId: string =
        this.getRequestMD5UniqueId(requestUniqueId, TKBaseService.CACHE_FRR_REQUEST_PREFIX, true);
      if (this.reqMap && this.reqMap.size > 0) {
        for (let key of Array.from(this.reqMap.keys())) {
          let tempReqParamVo: TKReqParamVO = this.reqMap.get(key) as TKReqParamVO;
          ;
          let tempRequestUniqueId: string = this.getRequestUniqueId(tempReqParamVo);
          let tempRequestMD5UniqueId: string =
            this.getRequestMD5UniqueId(tempRequestUniqueId, TKBaseService.CACHE_FRR_REQUEST_PREFIX, true);
          if (requestMD5UniqueId == tempRequestMD5UniqueId) {
            TKLog.error(`[flowNo:${reqParamVO.flowNo}<-->flowNo:${tempReqParamVo.flowNo}]重复请求，不予处理。`);
            return false;
          }
        }
      }
      if (reqParamVO.filterRepeatRequestTimeOut > 0) {
        let filterRepeatRequestKey: string = requestMD5UniqueId;
        let filterRepeatRequestTime: number = TKCacheManager.shareInstance()
          .getMemCacheData(filterRepeatRequestKey) ?? 0;
        let currentRequestTime: number = new Date().getTime();
        if (filterRepeatRequestTime > 0) {
          let filterRepeatRequestTimeInterval: number = currentRequestTime - filterRepeatRequestTime;
          if (filterRepeatRequestTimeInterval <= reqParamVO.filterRepeatRequestTimeOut) {
            TKLog.error(`[flowNo:${reqParamVO.flowNo}]重复请求,间隔小于${reqParamVO.filterRepeatRequestTimeOut}毫秒，不予处理。`);
            return false;
          }
        } else {
          let filterRepeatRequestTimeOut: number = reqParamVO.filterRepeatRequestTimeOut / 1000.0;
          TKCacheManager.shareInstance()
            .saveMemeCacheData(filterRepeatRequestKey, currentRequestTime, filterRepeatRequestTimeOut);
        }
      }
    }
    //判断是否同意隐私协议
    if (TKSystemHelper.getConfig("system.isUserAgreeRight") != "0") {
      if (reqParamVO.isAutoAddSysComParam && !reqParamVO.isKeepOriginalParam) {
        //时间戳
        const operTime: number = new Date().getTime();
        //应用类型1：安卓，2：IOS  3：H5 5：鸿蒙
        const channel: string = "5";
        //应用包名
        const soft_no: string = TKSystemHelper.getAppIdentifier();
        //版本名称
        const version_name: string = TKSystemHelper.getVersion();
        //版本序号
        const version_code: string = TKSystemHelper.getVersionCode();
        //设备名称
        const device_name: string = TKDeviceHelper.getDeviceName();
        //设备UUID
        const device_id: string = TKDeviceHelper.getDeviceUUID();
        //设备MAC
        const device_mac: string = TKDeviceHelper.getDeviceMac();
        //操作系统版本
        const device_os_version: string = TKDeviceHelper.getDeviceSysVersion();
        //ip地址
        const ip: string = TKNetHelper.getIP();
        //内网ip地址
        const lip: string = TKNetHelper.getLocalIP();
        //运营商
        const device_network_operator: string = TKNetHelper.getPhoneOperator();
        //网络制式
        const device_network_type: TKNetworkType = TKNetHelper.getNetworkType();
        //请求UUID
        const request_id: string = TKMd5Helper.stringWithMd5Sync(TKUUIDHelper.uuid()).toLowerCase();
        //拼接请求的唯一字符串(设备ID|请求ID)
        reqParam["@@_ID"] = request_id;
        //拼接操作站点信息：应用类型(1:安卓,2:IOS)|应用包名|升级版本名称|升级版本序号|设备UUID|设备MAC地址|设备操作系统版本|IP地址|网络运营商(0:中国移动,1:中国联通,2:中国电信,3:中国铁通)|网络制式(1:2G,2:3G,3:4G,4:wifi)|时间戳|流水号|模块编号|内网IP地址|设备名称
        reqParam["@@_OP"] =
          `${channel}|${soft_no}|${version_name}|${version_code}|${device_id}|${device_mac}|${device_os_version}|${ip}|${device_network_operator}|${device_network_type}|${operTime}|${reqParamVO.flowNo}|${reqParamVO.reqModule}|${lip}|${device_name}`;
      }
    }
    //禁止断线重连自动登录
    if (TKSystemHelper.getConfig("networkRequest.isAutoLoginForReConnected") == "0") {
      reqParamVO.isLoginReq = false;
    }
    return true;
  }

  /**
   *  结果拦截
   *
   * @param resultVo 结果对象
   *
   * @return
   */
  public resultFilter(resultVO: TKResultVO): boolean {
    return true;
  }

  /**
   *  命中缓存成功回调
   *
   * @param result 返回的结果对象
   */
  private onCacheResult(resultVO: TKResultVO) {
    try {
      if (resultVO.reqParamVO) {
        let flowNo: string = resultVO.reqParamVO.flowNo;
        TKLog.debug(`网络通信服务层[flowNo:${flowNo}]进行回调，当前请求队列${JSON.stringify(Array.from(this.reqMap.keys()))}`);
        //清除请求队列
        this.reqMap.delete(flowNo);
        //执行回调函数
        let callBackFunc = this.callBackFuncMap.get(flowNo);
        this.callBackFuncMap.delete(flowNo);
        this.onCallBackFunc(callBackFunc, resultVO);
      }
    } catch (error) {
      TKLog.error(`网络通信服务层请求返回异常:${error.message}`)
    }
  }

  /**
   *  执行回调函数
   *
   * @param callBackFunc 回调函数
   * @param resultVo     结果集
   */
  private onCallBackFunc(callBackFunc?: TKServiceCallBackFunc, resultVO?: TKResultVO) {
    if (resultVO && resultVO.reqParamVO) {
      let flowNo: string = resultVO.reqParamVO.flowNo;
      let isPass: boolean = this.resultFilter(resultVO);
      if (isPass && this.filterDelegate && this.filterDelegate !== this && this.filterDelegate.resultFilter) {
        isPass = this.filterDelegate.resultFilter(resultVO);
      }
      if (isPass) {
        if (callBackFunc) {
          callBackFunc(resultVO);
        } else {
          TKLog.warn(`流水号（${flowNo}）没有找到对应的回调函数！`);
        }
      } else {
        TKLog.error(`流水号（${flowNo}）回调函数被拦截执行！`);
      }
    }
  }

  /**
   *  获取请求的唯一id
   *
   * @param reqParamVO 请求对象
   *
   * @return 唯一id
   */
  private getRequestUniqueId(reqParamVO: TKReqParamVO | undefined): string {
    if (reqParamVO) {
      //拼装请求唯一ID
      let url: string = reqParamVO.url;
      let requestID: string = `url:${url}|`;
      let reqParam: Record<string, Object | undefined> = reqParamVO.reqParam;
      if (reqParam) {
        let keys: Array<string> = Object.keys(reqParam);
        keys.sort((a, b) => a.localeCompare(b));
        keys.forEach((key) => {
          if (key !== "@@_ID" && key !== "@@_OP" && !key.endsWith("@@F")) {
            let value: Object = reqParam[key] as Object;
            if (TKObjectHelper.nonNull(value)) {
              if (typeof value === "object" && !(value instanceof Uint8Array) && !(value instanceof ArrayBuffer)) {
                value = TKObjectHelper.toJsonStr(value);
              }
              requestID += `${key}:${value}|`;
            }
          }
        })
      }
      return requestID;
    }
    return "";
  }

  /**
   *
   *  获取请求的唯一id
   *
   * @param requestUniqueId     请求唯一ID
   * @param prefix              请求ID前缀
   * @param isContainSelfObject 是否在自己对象范围内
   *
   * @return 唯一id
   */
  private getRequestMD5UniqueId(requestUniqueId: string, prefix: string, isContainSelfObject: boolean): string {
    let requestMD5UniqueId: string = requestUniqueId ?? "";
    if (isContainSelfObject) {
      requestMD5UniqueId = `${this.classIntanceId}_${requestMD5UniqueId}`;
    }
    requestMD5UniqueId = TKMd5Helper.stringWithMd5Sync(requestMD5UniqueId);
    if (TKStringHelper.isNotBlank(prefix)) {
      requestMD5UniqueId = `${prefix}_${requestMD5UniqueId}`;
    }
    return requestMD5UniqueId;
  }
}