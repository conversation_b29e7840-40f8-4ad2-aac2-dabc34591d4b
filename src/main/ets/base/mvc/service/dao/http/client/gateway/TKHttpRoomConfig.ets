/**
 * 多机房配置解析
 */
import { TKCacheManager } from '../../../../../../../util/cache/TKCacheManager';
import { TKFileHelper } from '../../../../../../../util/file/TKFileHelper';
import { TKXMLHelper } from '../../../../../../../util/file/xml/TKXMLHelper';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../../../../../util/system/TKSystemHelper';
import { TKReqParamVO } from '../../../../../model/TKReqParamVO';
import { TKCommonService } from '../../../../TKCommonService';

export class TKHttpRoomConfig {
  /**
   * Java接入的配置
   */
  public static CACHE_SERVER_CONFIG: string = "TKServerConfig";
  /**
   *  更新地址
   */
  public updateUrl: string = "";
  /**
   *  测速地址
   */
  public speedUrl: string = "";
  /**
   *  测速时间
   */
  public speedTime: string = "";
  /**
   *  请求模式(0:正常，1:Http加签，2：Http加密，3：Http加密加签，4:Socket加密，5：Socket压缩，6：Socket压缩加密，7：Http加密加签并加密响应包，8：微服务正常，9：微服务加签，10：微服务加密加签，11：微服务加密加签并加密响应包)
   */
  public reqMode: string = "";
  /**
   *  机房模式（0：随机，1：最快测速)
   */
  public roomMode: string = "";
  /**
   *  服务器配置
   */
  private roomConfig: Map<string, Object> = new Map<string, Object>();
  private httpService: TKCommonService = new TKCommonService();
  private static instance: TKHttpRoomConfig | undefined = undefined;

  public constructor() {
    this.loadConfig();
  }

  /**
   *  单例
   */
  public static shareInstance(): TKHttpRoomConfig {
    if (!TKHttpRoomConfig.instance) {
      TKHttpRoomConfig.instance = new TKHttpRoomConfig();
    }
    return TKHttpRoomConfig.instance;
  }

  /**
   *  加载配置文件
   */
  private loadConfig() {
    this.roomConfig = TKCacheManager.shareInstance()
      .getFileCacheData(TKHttpRoomConfig.CACHE_SERVER_CONFIG, TKHttpRoomConfig.CACHE_SERVER_CONFIG) ??
    new Map<string, Object>();
    let serverPath: string = `thinkive/config/${TKSystemHelper.getEnvironment()}/http/Server.xml`;
    if (TKFileHelper.readFile(serverPath).length <= 0) {
      serverPath = `thinkive/config/default/http/Server.xml`
    }
    //读取配置文件
    let roomsElem: Map<string, Object> = TKXMLHelper.readConfigXml(serverPath)
    //获得根节点
    if (roomsElem && roomsElem.size > 0) {
      this.speedUrl = TKMapHelper.getString(roomsElem, "speedUrl");
      this.updateUrl = TKMapHelper.getString(roomsElem, "updateUrl");
      this.reqMode = TKMapHelper.getString(roomsElem, "reqMode");
      this.roomMode = TKMapHelper.getString(roomsElem, "roomMode");
      this.speedTime = TKMapHelper.getString(roomsElem, "speedTime");
      if (TKStringHelper.isBlank(this.speedTime)) {
        this.speedTime = "1.5";
      }
      if (TKStringHelper.isBlank(this.roomMode)) {
        this.roomMode = "1";
      }
      if (this.roomConfig.size == 0) {
        //解析Room子节点
        let roomElements: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(roomsElem, "children")
        //循环遍历Room节点
        if (roomElements && roomElements.length > 0) {
          for (let roomElement of roomElements) {
            //机房名称
            let roomName: string = TKMapHelper.getString(roomElement, "name");
            //机房状态
            let roomState: string = TKMapHelper.getString(roomElement, "state");
            //主要站点
            let mainsite: string = TKMapHelper.getString(roomElement, "mainsite");
            //机房描述
            let roomDescription: string = TKMapHelper.getString(roomElement, "description");
            if (TKStringHelper.isNotBlank(roomName)) {
              let roomProp: Map<string, Object> = new Map<string, Object>();
              this.roomConfig.set(roomName, roomProp);
              roomProp.set("name", roomName);
              roomProp.set("state", roomState);
              roomProp.set("mainsite", mainsite);
              roomProp.set("description", roomDescription);
              roomProp.set("speedUrl", this.speedUrl);
              let serverConfig: Map<string, Object> = new Map<string, Object>();
              roomProp.set("server", serverConfig);
              //server节点
              let serverElements: Array<Map<string, Object>> | undefined =
                TKMapHelper.getObject(roomElement, "children");
              if (serverElements && serverElements.length > 0) {
                for (let serverElement of serverElements) {
                  //服务名称
                  let serverName: string = TKMapHelper.getString(serverElement, "name");
                  //服务域名
                  let serverDomain: string = TKMapHelper.getString(serverElement, "domain");
                  //服务IP
                  let serverIP: string = TKMapHelper.getString(serverElement, "ip");
                  //服务器描述
                  let serverDescription: string = TKMapHelper.getString(serverElement, "description");
                  //关联别名
                  let serverRef: string = TKMapHelper.getString(serverElement, "ref");
                  if (TKStringHelper.isNotBlank(serverName)) {
                    let serverProp: Map<string, Object> = new Map<string, Object>();
                    serverProp.set("room", roomName);
                    serverProp.set("speedUrl", this.speedUrl);
                    serverProp.set("domain", serverDomain);
                    serverProp.set("ip", serverIP);
                    serverProp.set("description", serverDescription);
                    serverProp.set("ref", serverRef);
                    serverConfig.set(serverName, serverProp);
                  }
                }
              }
            }
          }
        }
        TKCacheManager.shareInstance()
          .saveFileCacheData(TKHttpRoomConfig.CACHE_SERVER_CONFIG, this.roomConfig, 0, false,
            TKHttpRoomConfig.CACHE_SERVER_CONFIG);
      }
    }
    //读取更新地址
    if (TKStringHelper.isNotBlank(this.updateUrl)) {
      setTimeout(() => {
        this.checkUpdate();
      }, 2000);
    }
  }

  /**
   *  得到所有服务器配置
   */
  public getRoomConfig(): Map<string, Object> {
    return this.roomConfig;
  }

  /**
   * 清除本地缓存
   */
  public static clearCache() {
    TKCacheManager.shareInstance()
      .deleteFileCacheData(TKHttpRoomConfig.CACHE_SERVER_CONFIG, TKHttpRoomConfig.CACHE_SERVER_CONFIG)
  }

  /**
   * 地址更新检测
   */
  private checkUpdate() {
    if (TKStringHelper.isNotBlank(this.updateUrl)) {
      //更新检测地址
      let url: string = this.updateUrl;
      let reqParamVO: TKReqParamVO = this.httpService.createReqParamByReqMode(url, this.reqMode);
      let paramConfig: Record<string, Object | undefined> = reqParamVO.reqParam ?? {};
      if (TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "funcNo"))) {
        paramConfig["funcNo"] = "1108007";
      }
      //应用类型1：安卓，2：IOS  3：H5 5：鸿蒙
      if (TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "channel"))) {
        paramConfig["channel"] = "5";
      }
      if (TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "soft_no"))) {
        paramConfig["soft_no"] = TKSystemHelper.getAppIdentifier();
      }
      paramConfig["random"] = new Date().getTime();
      reqParamVO.reqParam = paramConfig;
      reqParamVO.isPost = false;
      this.httpService.serviceInvoke(reqParamVO, (resultVO) => {
        if (resultVO.errorNo == 0) {
          let results: Array<Record<string, Object>> = resultVO.results as Array<Record<string, Object>>;
          if (results && results.length > 0) {
            let cacheRoomMap: Map<string, Object> = TKCacheManager.shareInstance()
              .getFileCacheData(TKHttpRoomConfig.CACHE_SERVER_CONFIG, TKHttpRoomConfig.CACHE_SERVER_CONFIG) ??
            new Map<string, Object>();
            for (let result of results) {
              let serverRoom: string = TKMapHelper.getString(result, "server_room");
              let serverName: string = TKMapHelper.getString(result, "server_name");
              let serverDomain: string = TKMapHelper.getString(result, "server_domain");
              let serverIP: string = TKMapHelper.getString(result, "server_ip");
              let serverRef: string = TKMapHelper.getString(result, "server_ref");
              let roomState: string = TKMapHelper.getString(result, "room_state");
              let mainSite: string = TKMapHelper.getString(result, "main_site");
              let roomDescription: string = TKMapHelper.getString(result, "room_description");
              let serverDescripion: string = TKMapHelper.getString(result, "description");
              if (TKStringHelper.isNotBlank(serverRoom) && TKStringHelper.isNotBlank(serverName)) {
                let room: Map<string, Object> =
                  TKMapHelper.getObject(cacheRoomMap, serverRoom) ?? new Map<string, Object>();
                room.set("name", serverRoom);
                room.set("state", roomState);
                room.set("mainsite", mainSite);
                room.set("description", roomDescription);
                room.set("speedUrl", this.updateUrl);
                cacheRoomMap.set(serverRoom, room);

                let serverMap: Map<string, Object> = TKMapHelper.getObject(room, "server") ?? new Map<string, Object>();
                room.set("server", serverMap);
                let server: Map<string, Object> =
                  TKMapHelper.getObject(serverMap, serverName) ?? new Map<string, Object>();
                serverMap.set(serverName, server);
                server.set("room", serverRoom);
                server.set("speedUrl", this.speedUrl);
                server.set("domain", serverDomain);
                server.set("ip", serverIP);
                server.set("ref", serverRef);
                server.set("description", serverDescripion);
              }
            }
            TKCacheManager.shareInstance()
              .saveFileCacheData(TKHttpRoomConfig.CACHE_SERVER_CONFIG, cacheRoomMap, 0, false,
                TKHttpRoomConfig.CACHE_SERVER_CONFIG);
          }
        }
      });
    }
  }
}