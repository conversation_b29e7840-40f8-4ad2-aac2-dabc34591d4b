/***
 * 基础HttpDao处理逻辑
 */
import { TKCacheManager } from '../../../../../util/cache/TKCacheManager';
import { TKAesHelper } from '../../../../../util/crypto/TKAesHelper';
import { TKBase64Helper } from '../../../../../util/crypto/TKBase64Helper';
import { TKMd5Helper } from '../../../../../util/crypto/TKMd5Helper';
import { TKPasswordGenerator } from '../../../../../util/crypto/TKPasswordGenerator';
import { TKSm3Helper } from '../../../../../util/crypto/TKSm3Helper';
import { TKSm4Helper } from '../../../../../util/crypto/TKSm4Helper';
import { TKUUIDHelper } from '../../../../../util/crypto/TKUUIDHelper';
import { TKZlibHelper } from '../../../../../util/crypto/TKZlibHelper';
import { TKDataHelper } from '../../../../../util/data/TKDataHelper';
import { TKObjectHelper } from '../../../../../util/data/TKObjectHelper';
import { TKLog } from '../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../util/map/TKMapHelper';
import { TKNetHelper } from '../../../../../util/net/TKNetHelper';
import { TKStringHelper } from '../../../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../../../util/system/TKSystemHelper';
import { TKLoadInfoVO } from '../../../model/TKLoadInfoVO';
import { TKCharEncoding, TKContentType, TKDataType, TKEncryMode, TKReqParamVO } from '../../../model/TKReqParamVO';
import { TKResultErrorType, TKResultVO } from '../../../model/TKResultVO';
import { TKDefaultHttpHandler } from '../../handler/TKDefaultHttpHandler';
import { TKProcessDataDelegate } from '../../protocol/TKProcessDataDelegate';
import { TKBaseDao } from '../TKBaseDao';
import { TKCookieManager } from './client/cookie/TKCookieManager';
import { rcp } from '@kit.RemoteCommunicationKit';
import { BusinessError } from '@kit.BasicServicesKit';

export class TKBaseHttpDao extends TKBaseDao {
  //统一登陆cookie
  private static readonly MEM_OAUTH_CLIENT_INFO: string = "sso_client_info";
  //默认数据处理器
  private defaultHandler: TKProcessDataDelegate = new TKDefaultHttpHandler();

  /**
   * AES加密key
   * @returns
   */
  private aesSystemKey(): string {
    return TKPasswordGenerator.shareInstance().generatorPassword();
  }

  /**
   *  获取加密加签请求入参
   *
   * @ReqParamVo reqParamVO
   *
   * @return
   */
  public async getRequestParam(reqParamVO: TKReqParamVO): Promise<Record<string, Object>> {
    let reqParam: Record<string, Object | undefined> = {};
    if (reqParamVO.isRestFull) {
      reqParamVO.isURLEncode = reqParamVO.isPost ? false : true;
      reqParam = await this.getRestFullRequestParam(reqParamVO);
    } else {
      reqParam = await this.getNormalRequestParam(reqParamVO);
    }
    let parametersDic: Record<string, Object> = {};
    if (TKObjectHelper.nonNull(reqParam)) {
      //设置请求入参
      Object.entries(reqParam).forEach((e, i) => {
        let key: string = e[0] as string;
        let value: Object = e[1] as Object;
        if (reqParamVO.isKeepOriginalParam) {
          parametersDic[key] = value;
        } else {
          if (!TKStringHelper.endsWith(key, "@@F")) {
            value = value ?? "";
            if (reqParamVO.isURLEncode) {
              parametersDic[key] = encodeURIComponent(value.toString())
            } else {
              parametersDic[key] = value.toString();
            }
          }
        }
      });
    }
    return parametersDic;
  }

  /**
   * 处理微服务接口协议
   */
  private async getRestFullRequestParam(reqParamVO: TKReqParamVO): Promise<Record<string, Object | undefined>> {
    //请求入参是否需要进行加签名
    let reqParam: Record<string, Object | undefined> = {};
    if (reqParamVO.isURLSign) {
      let fileParam: Record<string, Object> = {};
      let allParam: Record<string, Object | undefined> = reqParamVO.reqParam;
      if (allParam) {
        Object.entries(allParam).forEach((e, i) => {
          let key: string = e[0] as string;
          let value: Object = e[1] as Object;
          if (TKStringHelper.endsWith(key, "@@F")) {
            fileParam[key] = value;
          }
        });
      }
      let signKey: string = await TKAesHelper.stringWithAesDecrypt(reqParamVO.signKey, this.aesSystemKey());
      if (TKStringHelper.isBlank(signKey)) {
        signKey = reqParamVO.signKey;
      }
      if (reqParamVO.isURLEncry) {
        reqParamVO.encryKey = (await TKSm3Helper.stringWithSm3(signKey)).substring(0, 16);
        let encryParam: string =
          await TKSm4Helper.stringWithSm4Encrypt(TKObjectHelper.toJsonStr(reqParamVO.reqParam), reqParamVO.encryKey);
        reqParam["data"] = encryParam;
        reqParam["request_id"] = TKUUIDHelper.uuid();
        reqParam["encrypted"] = true;
      } else {
        reqParam["data"] = TKObjectHelper.toJsonStr(reqParamVO.reqParam);
        reqParam["request_id"] = TKUUIDHelper.uuid();
        reqParam["encrypted"] = false;
      }
      reqParam = TKObjectHelper.assign(reqParam, fileParam);
      let signature: string =
        await TKSm3Helper.stringWithSm3(`secret=${signKey}data=${reqParam["data"]}request_id=${reqParam["request_id"]}encrypted=${reqParam["encrypted"]}`);
      let headerFieldDic: Record<string, Object | undefined> = {};
      if (reqParamVO.headerFieldDic) {
        headerFieldDic = TKObjectHelper.assign(headerFieldDic, reqParamVO.headerFieldDic);
      }
      headerFieldDic["tk-trans-merchant-key"] = reqParamVO.signAppId;
      headerFieldDic["tk-trans-signature"] = signature;
      headerFieldDic["tk-encrypt-response"] = `${reqParamVO.isURLResponseEncry}`;
      reqParamVO.headerFieldDic = headerFieldDic;
    } else {
      if (reqParamVO.reqParam) {
        reqParam = TKObjectHelper.assign(reqParam, reqParamVO.reqParam);
      }
    }
    if (reqParamVO.isPost && !reqParamVO.isUpload) {
      if (reqParamVO.headerFieldDic && TKMapHelper.getString(reqParamVO.headerFieldDic, "Content-Type")
        .indexOf("application/json") >= 0) {
        reqParamVO.contentType = TKContentType.JSON;
      }
      if (reqParamVO.contentType == TKContentType.NONE || reqParamVO.contentType == TKContentType.JSON) {
        reqParamVO.isURLEncode = false;
        let postParam: Record<string, Object> = {};
        postParam["@@S"] = TKObjectHelper.toJsonStr(reqParam);
        reqParam = postParam;
      }
    }
    return reqParam;
  }

  /**
   * 处理普通接口协议
   */
  private async getNormalRequestParam(reqParamVO: TKReqParamVO): Promise<Record<string, Object | undefined>> {
    let normalReqParam: Record<string, Object | undefined> = TKObjectHelper.assign({}, reqParamVO.reqParam);
    //请求入参是否需要进行加签名
    if (!reqParamVO.isUpload) {
      if (reqParamVO.isURLSign) {
        //转成Json字符串，并进行base64编码
        let reqParam: Record<string, Object | undefined> = reqParamVO.reqParam;
        let parametersJsonStr: string = TKObjectHelper.toJsonStr(reqParam);
        let parametersData: Uint8Array = TKDataHelper.stringToUint8Array(parametersJsonStr);
        if (reqParamVO.dataType == TKDataType.Compress) {
          let compressData: Uint8Array = TKZlibHelper.compress(parametersData);
          if (compressData && compressData.length > 0) {
            parametersData = compressData;
          } else {
            TKLog.error(`提交数据压缩失败!!!(funcNo:${reqParam["funcNo"]},flowNo:${reqParamVO.flowNo})`);
          }
        }
        //是否要加密
        if (reqParamVO.isURLEncry) {
          let aesKey: string = await this.getRequestEncryptKey(reqParamVO);
          parametersData = await TKAesHelper.dataWithAesEncrypt(parametersData, aesKey);
        }
        let jsonStr: string = TKBase64Helper.stringWithBase64Encode(parametersData);
        //发送请求入参
        let param: Record<string, Object> =
          await this.getCommonSignParamMap(reqParam["funcNo"] as string, reqParamVO.isURLEncry, reqParamVO.encryMode,
            reqParamVO.signKey, reqParamVO.signAppId);
        param["data"] = jsonStr;
        param = await this.doSign(param);
        normalReqParam = param;
      } else if (reqParamVO.isURLEncry) {
        let param: Record<string, Object> = {};
        let reqParam: Record<string, Object | undefined> = reqParamVO.reqParam;
        if (reqParam) {
          let aesKey: string = await this.getRequestEncryptKey(reqParamVO);
          let reqParamKeys: Array<string> = Object.keys(reqParam);
          for (let key of reqParamKeys) {
            let value: string = TKMapHelper.getString(reqParam, key);
            if (TKStringHelper.isNotBlank(value)) {
              value = await TKAesHelper.stringWithAesEncrypt(value, aesKey);
            }
            param[key] = value;
          }
        }
        normalReqParam = param;
      }
    }
    if (reqParamVO.isPost && !reqParamVO.isUpload) {
      if (reqParamVO.headerFieldDic && TKMapHelper.getString(reqParamVO.headerFieldDic, "Content-Type")
        .indexOf("application/json") >= 0) {
        reqParamVO.contentType = TKContentType.JSON;
      }
      if (reqParamVO.contentType == TKContentType.JSON) {
        reqParamVO.isURLEncode = false;
        let postParam: Record<string, Object | undefined> = {};
        postParam["@@S"] = TKObjectHelper.toJsonStr(normalReqParam);
        normalReqParam = postParam;
      }
    }
    return normalReqParam;
  }

  /**
   *  获取公共加签名入参
   *
   * @param funcNo   功能号
   * @param dateTime 时间戳
   *
   * @return
   */
  private async getCommonSignParamMap(funcNo: string, isEncry: boolean, encryMode: TKEncryMode, signKey: string,
    appId: string): Promise<Record<string, Object>> {
    let decryptSignKey: string = await TKAesHelper.stringWithAesDecrypt(signKey, this.aesSystemKey());
    if (TKStringHelper.isNotBlank(decryptSignKey)) {
      signKey = decryptSignKey;
    }
    let param: Record<string, Object> = {};
    param["bizcode"] = funcNo;
    param["sign_type"] = "md5";
    param["charset"] = "utf8";
    param["format"] = "json";
    param["merchant_id"] = appId;
    param["data"] = "";
    param["timestamp"] = `${new Date().getTime()}`;
    param["signKey"] = signKey;
    param["request_id"] = TKUUIDHelper.uuid();
    //是否要加密
    if (isEncry) {
      param["encry_mode"] = "aes";
    }
    return param;
  }

  /**
   *  生成签名串
   *
   * @param param
   *
   * @return
   */
  private async doSign(param: Record<string, Object>): Promise<Record<string, Object>> {
    let signKey: string = TKMapHelper.getString(param, "signKey");
    let timestamp: string = TKMapHelper.getString(param, "timestamp");
    let args: Array<string> = new Array<string>();
    Object.entries(param).forEach((e, i) => {
      let key: string = e[0] as string;
      let value: Object = e[1] as Object;
      if (key !== "signKey" && key !== "timestamp") {
        if (TKObjectHelper.nonNull(value)) {
          args.push(key);
        }
      }
    });
    args.sort((a, b) => a.localeCompare(b));
    let sign: string = "";
    for (let arg of args) {
      sign += `${arg}=${param[arg]}&`;
    }
    //后面两个key的位置是固定放在最后的，因为服务器也是拿本地的key按照这个规则拼接的
    sign += `signKey=${signKey}&`;
    sign += `timestamp=${timestamp}`;
    param["sign"] = await TKMd5Helper.stringWithMd5(sign);
    param["timestamp"] = timestamp;
    return param;
  }

  /**
   *  获取加密加签请求加密Key
   *
   * @ReqParamVo reqParamVo
   *
   * @return
   */
  private async getRequestEncryptKey(reqParamVO: TKReqParamVO): Promise<string> {
    let encryptKey: string = "";
    //是否要加密
    if (reqParamVO.isURLEncry) {
      encryptKey = reqParamVO.encryKey;
      if (TKStringHelper.isBlank(encryptKey)) {
        encryptKey = reqParamVO.signKey;
      }
      let decryptEncryptKey: string = await TKAesHelper.stringWithAesDecrypt(encryptKey, this.aesSystemKey());
      if (TKStringHelper.isNotBlank(decryptEncryptKey)) {
        encryptKey = decryptEncryptKey;
      }
      if (encryptKey && encryptKey.length > 16) {
        encryptKey = encryptKey.substring(0, 16);
      }
    }
    return encryptKey;
  }

  /**
   *  获取请求的头
   *
   * @ReqParamVo reqParamVo
   *
   * @return
   */
  public getRequestHeader(reqParamVO: TKReqParamVO): Record<string, Object> {
    let url: string = reqParamVO.url;
    let headerFieldDic: Record<string, Object> = {};
    if (reqParamVO.headerFieldDic) {
      TKObjectHelper.assign(headerFieldDic, reqParamVO.headerFieldDic);
    }
    if (reqParamVO.isURLResponseEncry) {
      headerFieldDic["tk-encrypt-response"] = "true";
    }
    //处理URL上面的;jsessionid
    let jsessionidKey: string = this.getJessionIdKeyByUrl(url);
    let jsessionid: string = this.getJessionIdByUrl(url);
    let domain: string = TKNetHelper.getSchemeHostPortByURL(url);
    if (TKStringHelper.isNotBlank(jsessionid)) {
      TKCookieManager.shareInstance().setCookieItem(domain, jsessionidKey, jsessionid);
    }
    let cookie: string = TKCookieManager.shareInstance().getCookieStr(domain);
    if (TKStringHelper.isNotBlank(cookie)) {
      headerFieldDic["Cookie"] = cookie;
    }
    let oauth_client_info_key: string = `${domain}:${TKBaseHttpDao.MEM_OAUTH_CLIENT_INFO}`;
    let oauth_client_info: string = TKCacheManager.shareInstance().getMemCacheData(oauth_client_info_key) as string;
    if (TKStringHelper.isNotBlank(oauth_client_info)) {
      headerFieldDic[TKBaseHttpDao.MEM_OAUTH_CLIENT_INFO] = oauth_client_info;
    }
    if (reqParamVO.dataType == TKDataType.Compress) {
      headerFieldDic["tk_data_type"] = "1";
    }
    return headerFieldDic;
  }

  /**
   * 根据url获取jsessionid
   */
  private getJessionIdByUrl(url: string): string {
    let jsessionid: string = "";
    let jsessionidAry: Array<string> = TKStringHelper.split(url, ";jsessionid=");
    if (jsessionidAry && jsessionidAry.length == 2) {
      jsessionid = TKStringHelper.split(jsessionidAry[1], "?")[0];
    }
    return jsessionid;
  }

  /**
   * 根据url获取jsessionidKey
   */
  private getJessionIdKeyByUrl(url: string): string {
    let jsessionidKey: string = "";
    let urlParam: Map<string, Object> = TKDataHelper.urlToFormatMap(url);
    if (urlParam && urlParam.size > 0) {
      jsessionidKey = TKMapHelper.getString(urlParam, "TKJsessionIdKey");
    }
    if (TKStringHelper.isBlank(jsessionidKey)) {
      jsessionidKey = "JSESSIONID";
    }
    return jsessionidKey;
  }

  /**
   *  详细信息调试
   *
   * @param url   请求url
   * @param param 参数
   */
  public debugRequestParam(flowNo: string, url: string, header: Record<string, Object>, param: Record<string, Object>) {
    TKLog.debug(`[flowNo:${flowNo}]调试请求头:${TKObjectHelper.toJsonStr(header)}`);
    TKLog.debug(`[flowNo:${flowNo}]调试请求串:${this.buildGetRequestURL(url, param)}`);
  }

  /**
   *  获取Get请求入参
   *
   * @ReqParamVo reqParamVO
   *
   * @return
   */
  public buildGetRequestURL(url: string, param: Record<string, Object>) {
    let queryStr: string = TKDataHelper.mapToFormatUrl(param);
    if (TKStringHelper.isNotBlank(queryStr)) {
      url = url.includes("?") ? `${url}&${queryStr}` : `${url}?${queryStr}`;
    }
    return url;
  }

  /**
   * 获取响应包编码
   */
  private getResponseStringEncoding(response: rcp.Response): TKCharEncoding {
    let contentType: string | Array<string> = response.headers?.['content-type'] ?? "";
    if (Array.isArray(contentType)) {
      let temp: Array<string> = contentType as Array<string>;
      contentType = temp.length > 0 ? temp[0] : "";
    }
    if (TKStringHelper.isNotBlank(contentType)) {
      if (contentType.indexOf("charset=") < 0) {
        return TKCharEncoding.DEFAULT;
      } else if (contentType.indexOf("charset=UTF-8") >= 0 || contentType.indexOf("charset=utf-8") >= 0) {
        return TKCharEncoding.UTF_8;
      } else {
        return TKCharEncoding.GBK;
      }
    }
    return TKCharEncoding.DEFAULT;
  }

  /**
   *  请求关闭
   */
  public close(reqParamVO: TKReqParamVO) {
    if (reqParamVO.serviceDelegate) {
      reqParamVO.serviceDelegate.onClose(reqParamVO);
    }
  }

  /**
   *  请求失败
   *
   * @param request 请求对象
   */
  public async requestFailed(reqParamVO: TKReqParamVO, response: rcp.Response | undefined,
    error: BusinessError<rcp.Response>) {
    try {
      if (reqParamVO) {
        let flowNo: string = reqParamVO.flowNo;
        let resultVO: TKResultVO = new TKResultVO();
        resultVO.errorNo = error.code;
        resultVO.errorInfo = error.message;
        if (error.code == 1007900055 || error.code == 1007900056) {
          let errorInfo: string = TKSystemHelper.getConfig("networkErrorInfo.-100000");
          resultVO.errorInfo = TKStringHelper.isNotBlank(errorInfo) ? errorInfo : "亲，网络很不给力哦~";
        } else if (error.code == 1007900028) {
          let errorInfo: string = TKSystemHelper.getConfig("networkErrorInfo.-100002");
          resultVO.errorInfo = TKStringHelper.isNotBlank(errorInfo) ? errorInfo : "亲，请求超时，请稍后重试~";
        } else if (error.code == 1007900992 || error.code == 1007900993) {
          let errorInfo: string = TKSystemHelper.getConfig("networkErrorInfo.-100003");
          resultVO.errorInfo = TKStringHelper.isNotBlank(errorInfo) ? errorInfo : "亲，请求已中断，请稍后重试~";
        } else if (error.code == 1007900001 || error.code == 1007900003 || error.code == 1007900005 ||
          error.code == 1007900006 || error.code == 1007900007 || error.code == 1007900078) {
          let errorInfo: string = TKSystemHelper.getConfig("networkErrorInfo.-100004");
          resultVO.errorInfo = TKStringHelper.isNotBlank(errorInfo) ? errorInfo : "亲，请求地址不能正常访问~";
        } else {
          let errorInfo: string = TKSystemHelper.getConfig("networkErrorInfo.-100001");
          resultVO.errorInfo = TKStringHelper.isNotBlank(errorInfo) ? errorInfo : "亲，服务器不能正常访问~";
        }
        let responseData: ArrayBuffer | string | undefined = response?.body;
        if (responseData) {
          let resultStr: string = "";
          if (typeof responseData === "string") {
            resultStr = responseData as string;
          } else {
            let charEncoding: TKCharEncoding = this.getResponseStringEncoding(response!);
            reqParamVO.charEncoding = (charEncoding != TKCharEncoding.DEFAULT) ? charEncoding : reqParamVO.charEncoding;
            if (reqParamVO.charEncoding == TKCharEncoding.DEFAULT) {
              reqParamVO.charEncoding = TKCharEncoding.UTF_8;
            }
            if (reqParamVO.charEncoding == TKCharEncoding.GBK) {
              resultStr = TKDataHelper.uint8ArrayToString(responseData, "gb18030");
            } else {
              resultStr = TKDataHelper.uint8ArrayToString(responseData, "utf-8");
            }
            if (TKStringHelper.isBlank(resultStr)) {
              if (reqParamVO.charEncoding == TKCharEncoding.GBK) {
                resultStr = TKDataHelper.uint8ArrayToString(responseData, "utf-8");
              } else {
                resultStr = TKDataHelper.uint8ArrayToString(responseData, "gb18030");
              }
            }
          }
          TKLog.debug(`[flowNo:${reqParamVO.flowNo}]返回结果---->${TKObjectHelper.toJsonStr(resultStr)}`);
          if (TKStringHelper.isNotBlank(resultStr)) {
            let result: Record<string, Object> | undefined =
              TKObjectHelper.deserialize(resultStr) as Record<string, Object> | undefined;
            if (TKObjectHelper.isNull(result) || typeof result !== 'object') {
              result = {};
              result["error_no"] = resultVO.errorNo;
              result["error_info"] = resultVO.errorInfo;
              result["results"] = resultStr;
            }
            resultVO = this.defaultHandler.processResultData(result as Object);
          }
        }
        resultVO.errorType = TKResultErrorType.Network;
        resultVO.reqParamVO = reqParamVO;
        if (reqParamVO.serviceDelegate) {
          reqParamVO.serviceDelegate.onFault(resultVO);
        }
        //清理请求关闭连接
        this.clearRequest(flowNo);
        if (TKStringHelper.isEmpty(reqParamVO.group)) {
          this.close(reqParamVO);
        }
      }
    } catch (error) {
      TKLog.error(`Http请求失败异常:${error.message}`)
    }
  }

  /**
   *  请求成功
   *
   * @param request 请求对象
   */
  public async requestFinished(reqParamVO: TKReqParamVO, response: rcp.Response) {
    try {
      if (response.cookies && response.cookies.length > 0) {
        let domain: string = TKNetHelper.getSchemeHostPortByURL(reqParamVO.url);
        let cookies: Array<string> = new Array<string>();
        response.cookies.forEach((cookie, index) => {
          cookies.push(`${cookie.name}=${cookie.value}`);
        })
        TKCookieManager.shareInstance().setCookie(domain, cookies);
        let oauth_client_info: string = TKCookieManager.shareInstance().getCookieItemValue(domain, "oauth_client_info");
        if (TKStringHelper.isNotBlank(oauth_client_info)) {
          let oauth_client_info_key: string = `${domain}:${TKBaseHttpDao.MEM_OAUTH_CLIENT_INFO}`;
          TKCacheManager.shareInstance().saveMemeCacheData(oauth_client_info_key, oauth_client_info);
        }
      }
      let flowNo: string = reqParamVO.flowNo;
      let responseData: ArrayBuffer | string | undefined = response.body;
      let resultStr: string = "";
      if (typeof responseData === "string") {
        resultStr = responseData as string;
        let isEncryptResponse: string = TKMapHelper.getString(response.headers, "tk-encrypt-response");
        if (isEncryptResponse == "true") {
          if (reqParamVO.isRestFull) {
            resultStr = await TKSm4Helper.stringWithSm4Decrypt(resultStr, reqParamVO.encryKey);
          } else {
            let aesKey: string = await this.getRequestEncryptKey(reqParamVO);
            resultStr = await TKAesHelper.stringWithAesDecrypt(resultStr, aesKey);
          }
        }
      } else {
        let contentType: string | Array<string> = response.headers?.['content-type'] ?? "";
        if (Array.isArray(contentType)) {
          let temp: Array<string> = contentType as Array<string>;
          contentType = temp.length > 0 ? temp[0] : "";
        }
        if (reqParamVO.headerFieldDic["fileDownload"] == "1" || contentType.startsWith("image/")) {
          resultStr = TKBase64Helper.stringWithBase64Encode(new Uint8Array(responseData as ArrayBuffer));
        } else {
          let charEncoding: TKCharEncoding = this.getResponseStringEncoding(response);
          reqParamVO.charEncoding = (charEncoding != TKCharEncoding.DEFAULT) ? charEncoding : reqParamVO.charEncoding;
          if (reqParamVO.charEncoding == TKCharEncoding.DEFAULT) {
            reqParamVO.charEncoding = TKCharEncoding.UTF_8;
          }
          if (reqParamVO.charEncoding == TKCharEncoding.GBK) {
            resultStr = TKDataHelper.uint8ArrayToString(responseData, "gb18030");
          } else {
            resultStr = TKDataHelper.uint8ArrayToString(responseData, "utf-8");
          }
          if (TKStringHelper.isBlank(resultStr)) {
            if (reqParamVO.charEncoding == TKCharEncoding.GBK) {
              resultStr = TKDataHelper.uint8ArrayToString(responseData, "utf-8");
            } else {
              resultStr = TKDataHelper.uint8ArrayToString(responseData, "gb18030");
            }
          }
          let isEncryptResponse: string = TKMapHelper.getString(response.headers, "tk-encrypt-response");
          if (isEncryptResponse == "true") {
            if (reqParamVO.isRestFull) {
              resultStr = await TKSm4Helper.stringWithSm4Decrypt(resultStr, reqParamVO.encryKey);
            } else {
              let aesKey: string = await this.getRequestEncryptKey(reqParamVO);
              resultStr = await TKAesHelper.stringWithAesDecrypt(resultStr, aesKey);
            }
          }
        }
      }
      TKLog.debug(`[flowNo:${reqParamVO.flowNo}]返回结果---->${TKObjectHelper.toJsonStr(resultStr)}`);
      let result: Record<string, Object> | undefined = undefined;
      if (TKStringHelper.isNotBlank(resultStr)) {
        result = TKObjectHelper.deserialize(resultStr) as Record<string, Object> | undefined;
        if (TKObjectHelper.isNull(result) || typeof result !== 'object') {
          result = {};
          result["error_no"] = 0;
          result["error_info"] = "";
          result["results"] = resultStr;
        }
      } else {
        result = {};
        result["error_type"] = "1";
        result["error_no"] = "-999";
        result["error_info"] = "服务器返回的数据格式错误！";
      }
      let resultVO: TKResultVO = this.defaultHandler.processResultData(result as Object);
      resultVO.errorType = TKResultErrorType.Business;
      resultVO.reqParamVO = reqParamVO;
      //通过响应包中的请求获取request，看看是否可以获取到对应的属性
      let request: Record<string, Object> = (response["config"] ?? {}) as Record<string, Object>;
      if (!reqParamVO.headerFieldDic) {
        reqParamVO.headerFieldDic = request["headers"] as Record<string, Object>;
      } else {
        reqParamVO.headerFieldDic =
          TKObjectHelper.assign(reqParamVO.headerFieldDic, request["headers"] as Record<string, Object>);
      }
      resultVO.cookies = response.cookies;
      resultVO.respHeaderFieldDic = response.headers;
      if (reqParamVO.serviceDelegate) {
        reqParamVO.serviceDelegate.onResult(resultVO);
      }
      //清理请求关闭连接
      this.clearRequest(flowNo);
      if (TKStringHelper.isEmpty(reqParamVO.group)) {
        this.close(reqParamVO);
      }
    } catch (error) {
      TKLog.error(`Http请求失败异常:${error.message}`)
    }
  }

  /**
   *  请求开始
   *
   * @param request 请求对象
   */
  public async requestStarted(reqParamVO: TKReqParamVO) {
    try {
      if (reqParamVO.serviceDelegate) {
        reqParamVO.serviceDelegate.onOpen(reqParamVO);
      }
    } catch (error) {
      TKLog.error(`Http请求开始异常:${error.message}`)
    }
  }


  /**
   *  接收数据
   *
   * @param request 请求对象
   * @param bytes    数据
   */
  public async requestProgress(reqParamVO: TKReqParamVO, sendDataLength: number, totalDataLength: number) {
    try {
      if (reqParamVO.serviceDelegate) {
        let loadInfo: TKLoadInfoVO = new TKLoadInfoVO();
        loadInfo.bytesTotal = totalDataLength;
        loadInfo.bytesLoaded = sendDataLength;
        reqParamVO.serviceDelegate.onProgress(reqParamVO, loadInfo);
      }
      if (reqParamVO.uploadBlock) {
        let loadInfo: TKLoadInfoVO = new TKLoadInfoVO();
        loadInfo.bytesTotal = totalDataLength;
        loadInfo.bytesLoaded = sendDataLength;
        reqParamVO.uploadBlock(loadInfo);
      }
    } catch (error) {
      TKLog.error(`Http请求上传文件过程异常:${error.message}`)
    }
  }

  /**
   *  清理请求
   *
   * @param flowNo 流水号
   */
  public clearRequest(flowNo: string) {
  }

  /**
   *  清除组请求对象
   *
   * @param groupNo 组号
   */
  public clearGroup(groupNo: string) {
  }
}