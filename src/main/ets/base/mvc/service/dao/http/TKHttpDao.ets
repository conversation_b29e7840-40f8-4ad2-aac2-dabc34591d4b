import { TKReqParamVO } from '../../../model/TKReqParamVO';
import { TKBaseHttpDao } from './TKBaseHttpDao';
import { TKStringHelper } from '../../../../../util/string/TKStringHelper';
import { TKMapHelper } from '../../../../../util/map/TKMapHelper';
import { TKLog } from '../../../../../util/logger/TKLog';
import { buffer } from '@kit.ArkTS';
import { rcp } from '@kit.RemoteCommunicationKit';
import { BusinessError } from '@kit.BasicServicesKit';

interface TKRequest {
  reqParamVO: TKReqParamVO;
  reqSource: rcp.Request;
  reqSession: rcp.Session
}

/**
 *  Http请求
 */
export class TKHttpDao extends TKBaseHttpDao {
  /**
   *  请求对象集合
   */
  private requestMap: Map<string, TKRequest> = new Map<string, TKRequest>();
  /**
   *  请求对象分组
   */
  private groupMap: Map<string, Map<string, TKRequest>> = new Map<string, Map<string, TKRequest>>();
  /**
   * 类名
   */
  private className: string = "";
  /**
   * 默认超时时间
   */
  private defaultRequesTimeOutSeconds: number = 60;
  /**
   * 实例对象
   */
  private static instance: TKHttpDao | undefined = undefined;

  private constructor() {
    super();
    this.className = this.constructor.name;
  }

  public static shareInstance(): TKHttpDao {
    if (!TKHttpDao.instance) {
      TKHttpDao.instance = new TKHttpDao();
    }
    return TKHttpDao.instance;
  }

  /**
   * @param reqParamVo 处理请求
   */
  public invoke(reqParamVO: TKReqParamVO) {
    super.invoke(reqParamVO);
    this.invokeAsyncHttp(reqParamVO);
  }

  /**
   * @param reqParamVo 处理请求
   */
  private async invokeAsyncHttp(reqParamVO: TKReqParamVO) {
    try {
      //请求参数
      let url: string = reqParamVO.isURLEncode ? encodeURI(reqParamVO.url) : reqParamVO.url;
      let parameters: Record<string, Object> = await this.getRequestParam(reqParamVO);
      let headers: Record<string, Object> = this.getRequestHeader(reqParamVO);
      let method: string = TKStringHelper.isNotBlank(reqParamVO.httpMethod) ? reqParamVO.httpMethod.toUpperCase() :
        (reqParamVO.isPost ? "POST" : "GET");
      let configuration: rcp.Configuration = {
        transfer: {
          timeout: {
            connectMs: reqParamVO.timeOut > 0 ? reqParamVO.timeOut * 1000 :
              (reqParamVO.isUpload ? Number.MAX_VALUE : this.defaultRequesTimeOutSeconds * 1000),
            transferMs: reqParamVO.timeOut > 0 ? reqParamVO.timeOut * 1000 :
              (reqParamVO.isUpload ? Number.MAX_VALUE : this.defaultRequesTimeOutSeconds * 1000)
          }
        },
        tracing: {
          httpEventsHandler: {
            onUploadProgress: (totalSize: number, transferredSize: number) => {
              this.requestProgress(reqParamVO, transferredSize, totalSize);
            }
          }
        }
      } as rcp.Configuration;
      let content: rcp.RequestContent | undefined = undefined;
      if (method == "GET") {
        url = this.buildGetRequestURL(url, parameters);
      } else {
        if (reqParamVO.isUpload) {
          if (TKStringHelper.isBlank(TKMapHelper.getString(headers, "Content-Type"))) {
            headers["Content-Type"] = "multipart/form-data";
          }
          let formData: rcp.MultipartFormFields = {};
          Object.entries(parameters).forEach((e, i) => {
            let key: string = e[0] as string;
            let value: Object = e[1] as Object;
            formData[key] = value as rcp.MultipartFormFieldValue;
          });
          //上传文件不走加密签名
          let reqParam: Record<string, Object | undefined> = reqParamVO.reqParam;
          Object.entries(reqParam).forEach((e, i) => {
            //设置请求入参
            let key: string = e[0] as string;
            let value: Object = e[1] as Object;
            //文件上传处理
            if (TKStringHelper.endsWith(key, "@@F")) {
              let file_extension: string | undefined = TKMapHelper.getString(reqParam, "file_extension");
              if (TKStringHelper.isBlank(file_extension)) {
                file_extension = "jpeg";
              }
              let key1: string = key.substring(0, key.length - 3);
              let fileName: string = `${key1}.${file_extension}`;
              if (Array.isArray(value)) {
                let fileDataArray: Array<Object> = value as Array<Object>;
                let formFiles: Array<rcp.FormFieldFileValue> = new Array<rcp.FormFieldFileValue>();
                for (let i = 0; i < fileDataArray.length; i++) {
                  let fileData: Object = fileDataArray[i];
                  fileName = `${key1}${i}.${file_extension}`;
                  if (typeof fileData === "string") {
                    let filePath: string = fileData;
                    filePath = filePath.replace("file:///", "/");
                    formFiles.push({
                      contentOrPath: filePath
                    });
                  } else {
                    if (fileData && (fileData instanceof Uint8Array || fileData instanceof ArrayBuffer) &&
                      fileData.byteLength > 0) {
                      formFiles.push({
                        contentType: "multipart/form-data",
                        remoteFileName: fileName,
                        contentOrPath: {
                          content: buffer.from(fileData).buffer
                        }
                      });
                    }
                  }
                }
                formData[key1] = formFiles;
              } else {
                let fileData: Object = value;
                if (typeof fileData === "string") {
                  let filePath: string = fileData;
                  filePath = filePath.replace("file:///", "/");
                  formData[key1] = {
                    contentOrPath: filePath
                  } as rcp.FormFieldFileValue;
                } else {
                  if (fileData && (fileData instanceof Uint8Array || fileData instanceof ArrayBuffer) &&
                    fileData.byteLength > 0) {
                    formData[key1] = {
                      contentType: "multipart/form-data",
                      remoteFileName: fileName,
                      contentOrPath: {
                        content: buffer.from(fileData).buffer
                      }
                    } as rcp.FormFieldFileValue;
                  }
                }
              }
            }
          });
          content = new rcp.MultipartForm(formData);
        } else {
          let sendJsonStr: string | undefined = TKMapHelper.getString(parameters, "@@S");
          if (TKStringHelper.isNotBlank(sendJsonStr) && Object.entries(parameters).length == 1) {
            if (TKStringHelper.isBlank(TKMapHelper.getString(headers, "Content-Type"))) {
              headers["Content-Type"] = "application/json";
            }
            content = sendJsonStr;
          } else {
            if (TKStringHelper.isBlank(TKMapHelper.getString(headers, "Content-Type"))) {
              headers["Content-Type"] = "application/x-www-form-urlencoded";
            }
            content = new rcp.Form(parameters as rcp.FormFields);
          }
        }
      }
      let reqSource: rcp.Request =
        new rcp.Request(url, method, headers as rcp.RequestHeaders, content, undefined, undefined, configuration);
      const reqSession = rcp.createSession();
      reqSession.fetch(reqSource).then((response) => {
        this.requestFinished(reqParamVO, response);
      }).catch((error: BusinessError<rcp.Response>) => {
        this.requestFailed(reqParamVO, error.data as rcp.Response, error);
      });
      this.requestMap.set(reqParamVO.flowNo, { reqParamVO, reqSource, reqSession } as TKRequest);
      if (TKStringHelper.isNotBlank(reqParamVO.group)) {
        let groupReqMap: Map<string, TKRequest> = this.groupMap.get(reqParamVO.group) ?? new Map<string, TKRequest>();
        groupReqMap.set(reqParamVO.flowNo, { reqParamVO } as TKRequest);
        this.groupMap.set(reqParamVO.group, groupReqMap);
      }
      this.debugRequestParam(reqParamVO.flowNo, reqParamVO.url, headers, parameters);
      this.requestStarted(reqParamVO);
    } catch (error) {
      TKLog.error(`${this.className}请求异常: error code: ${error.code}, message is: ${error.message}`);
    }
  }

  /**
   * 清理组请求
   */
  private clearGroupRequest(reqParamVO: TKReqParamVO): void {
    try {
      if (TKStringHelper.isNotBlank(reqParamVO.group)) {
        let groupRequestMap: Map<string, TKRequest> | undefined = this.groupMap.get(reqParamVO.group);
        if (groupRequestMap) {
          groupRequestMap.delete(reqParamVO.flowNo);
          if (groupRequestMap.size == 0) {
            this.clearGroup(reqParamVO.group);
            this.close(reqParamVO);
          }
        }
      }
    } catch (error) {
      TKLog.error(`${this.className}分组请求清理异常: error code: ${error.code}, message is: ${error.message}`);
    }
  }

  /**
   *  清理请求
   *
   * @param flowNo 流水号
   */
  public clearRequest(flowNo: string): void {
    try {
      if (TKStringHelper.isNotBlank(flowNo)) {
        let request: TKRequest | undefined = this.requestMap.get(flowNo);
        this.requestMap.delete(flowNo);
        if (request) {
          request.reqSession.cancel(request.reqSource);
          request.reqSession.close();
          this.clearGroupRequest(request.reqParamVO);
        }
      }
    } catch (error) {
      TKLog.error(`${this.className}请求取消异常: error code: ${error.code}, message is: ${error.message}`);
    }
  }

  /**
   *  清除组请求对象
   *
   * @param groupNo 组号
   */
  public clearGroup(groupNo: string): void {
    try {
      if (TKStringHelper.isNotBlank(groupNo)) {
        this.groupMap.delete(groupNo);
      }
    } catch (error) {
      TKLog.error(`${this.className}请求组取消异常: error code: ${error.code}, message is: ${error.message}`);
    }
  }
}