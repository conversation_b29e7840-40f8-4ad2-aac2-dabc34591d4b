/**
 *  网络地址
 */
export class TKHttpAddress {
  /**
   * 机房ID
   */
  public roomName: string = "";
  /**
   *  服务ID
   */
  public serverName: string = "";
  /**
   是否域名
   */
  public isDomain: boolean = false;
  /**
   是否IPV6
   */
  public isIPV6: boolean = false;
  /**
   *  url地址
   */
  public url: string = "";
  /**
   * 测速链接
   */
  public speedUrl: string = "";
  /**
   地址描述
   */
  public desc: string = "";
  /**
   *  是否存活
   */
  public isAlive: boolean = false;
  /**
   *  测速的综合得分,分值越低表示越快
   */
  public speed: number = 0;
  /**
   *  开始测试时间
   */
  public btime: number = 0;
  /**
   *  测试花费时间
   */
  public time: number = 0;
}