/**
 *  网络监听代理
 */
import { TKCacheManager } from '../../../../../../../util/cache/TKCacheManager';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKNetHelper } from '../../../../../../../util/net/TKNetHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKNetworkListener } from '../../../../../../network/listener/TKNetworkListener';
import { TKReqParamVO } from '../../../../../model/TKReqParamVO';
import { TKCommonService } from '../../../../TKCommonService';
import { TKHttpAddress } from './TKHttpAddress';
import { TKHttpServer, TKHttpTestSpeedMode } from './TKHttpServer';

export interface TKHttpSpeedCheckerDelegate {
  /**
   *  检测结果
   *
   * @param address
   */
  httpSpeedCheckResult: (httpSpeedChecker: TKHttpSpeedChecker) => void;
}

/**
 * 测速工具类
 */
export class TKHttpSpeedChecker {
  private static TKFastHttpDic: Map<string, Object> = new Map<string, Object>();
  /**
   *  网络测速代理
   */
  public delegate: TKHttpSpeedCheckerDelegate | undefined = undefined;
  /**
   *  服务器名称
   */
  public netAddress: TKHttpAddress = new TKHttpAddress();
  /**
   * 当前类名
   */
  public className: string = "";
  /**
   *  初始化启动测速标示
   */
  public testSpeedMode: TKHttpTestSpeedMode = TKHttpTestSpeedMode.Room;
  /**
   *  网络是否ok
   */
  private isNetWorkOK: boolean = true;
  /**
   * 服务类
   */
  private httpService: TKCommonService = new TKCommonService();

  /**
   *  初始化
   *
   * @param netAddress 地址
   * @param serverName 服务器名称
   *
   * @return
   */
  public constructor(netAddress: TKHttpAddress) {
    this.className = this.constructor.name;
    this.isNetWorkOK = TKNetworkListener.shareInstance().isNetAvailable();
    this.netAddress = netAddress;
    this.netAddress.speed = Number.MAX_VALUE;
    this.netAddress.time = Number.MAX_VALUE;
  }

  /**
   * 站点测速
   */
  public startTest() {
    this.isNetWorkOK = TKNetworkListener.shareInstance().isNetAvailable();
    if (this.isNetWorkOK) {
      this.check();
    } else {
      this.netAddress.isAlive = false;
      this.netAddress.time = Number.MAX_VALUE;
      this.netAddress.speed = this.netAddress.time;
      if (this.delegate) {
        this.delegate.httpSpeedCheckResult(this);
      }
    }
  }

  /**
   * 取消测速
   */
  public stopTest() {
    this.httpService.clearAllRequest();
  }

  /**
   *  心跳检测
   */
  private check() {
    this.netAddress.btime = new Date().getTime();
    if (TKStringHelper.isNotBlank(this.netAddress.speedUrl)) {
      //开始测速
      let serverSpeedUrl: string = TKNetHelper.getSchemeHostPortByURL(this.netAddress.url) + this.netAddress.speedUrl;
      let reqParamVO: TKReqParamVO = this.httpService.createReqParamVO();
      reqParamVO.isPost = false;
      reqParamVO.url = serverSpeedUrl;
      reqParamVO.timeOut = 2;
      reqParamVO.reqParam = {
        "random": new Date().getTime()
      };
      this.httpService.serviceInvoke(reqParamVO, (resultVO) => {
        this.netAddress.isAlive = (resultVO.errorNo == 0);
        this.netAddress.time = new Date().getTime() - this.netAddress.btime;
        this.netAddress.speed = this.netAddress.time;
        if (this.netAddress.isAlive) {
          this.saveFastSocket();
        }
        if (this.delegate) {
          this.delegate.httpSpeedCheckResult(this);
        }
        this.httpService.clearAllRequest();
        TKLog.info(`${this.className}[${this.netAddress.roomName}][${this.netAddress.serverName}](${this.netAddress.url})是否存活=${this.netAddress.isAlive}---->TestSpeedData(耗时${this.netAddress.time}毫秒)`);
      });
    } else {
      this.netAddress.isAlive = true;
      this.netAddress.time = new Date().getTime() - this.netAddress.btime;
      this.netAddress.speed = this.netAddress.time;
      if (this.netAddress.isAlive) {
        this.saveFastSocket();
      }
      if (this.delegate) {
        this.delegate.httpSpeedCheckResult(this);
      }
      TKLog.info(`${this.className}[${this.netAddress.roomName}][${this.netAddress.serverName}](${this.netAddress.url})是否存活=${this.netAddress.isAlive}---->TestSpeedData(耗时${this.netAddress.time}毫秒)`);
    }
  }

  //缓存测速的最快地址
  private saveFastSocket() {
    let isSave: boolean = false;
    let fastCacheNetAddressKey: string =
      `${TKHttpServer.CACHE_FAST_HTTP_NETADDRESS}_${this.netAddress.roomName}_${this.netAddress.serverName}`;
    let fastCacheNetAddress: Map<string, Object> | undefined =
      TKMapHelper.getObject(TKHttpSpeedChecker.TKFastHttpDic, fastCacheNetAddressKey);
    if (!fastCacheNetAddress) {
      fastCacheNetAddress = new Map<string, Object>();
      isSave = true;
    } else {
      let speed: number = TKMapHelper.getNumber(fastCacheNetAddress, "speed");
      if (speed > this.netAddress.speed) {
        isSave = true;
      }
    }
    if (isSave) {
      fastCacheNetAddress.set("host", this.netAddress.url);
      fastCacheNetAddress.set("speed", this.netAddress.speed);
      TKHttpSpeedChecker.TKFastHttpDic.set(fastCacheNetAddressKey, fastCacheNetAddress);
      TKCacheManager.shareInstance().saveFileCacheData(fastCacheNetAddressKey, fastCacheNetAddress);
    }
  }
}