import { TKCacheManager } from '../../../../../../../util/cache/TKCacheManager';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKNetHelper } from '../../../../../../../util/net/TKNetHelper';
import { TKNumberHelper } from '../../../../../../../util/number/TKNumberHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKHttpAddress } from './TKHttpAddress';
import { TKHttpRoomServerManager } from './TKHttpRoomServerManager';
import { TKHttpSpeedChecker, TKHttpSpeedCheckerDelegate } from './TKHttpSpeedChecker';

export enum TKHttpTestSpeedMode {
  //测试机房
  Room,
  //初始化测速
  Init,
  //检测测速
  Test,
  //界面测速
  View
}

/**
 *  网络监听代理
 */
export interface TKHttpSpeedServerDelegate {

  /**
   *  初始化机房测速结束
   * @param address
   */
  roomCheckSpeedFinished: (roomName: string, serverName: string, result: boolean) => void;

  /**
   *  测速结果
   * @param address
   */
  testSpeed: (address: TKHttpAddress, testSpeedMode: TKHttpTestSpeedMode) => void;

  /**
   *  测速结束
   * @param address
   */
  testSpeedFinished: (serverName: string, testSpeedMode: TKHttpTestSpeedMode) => void;
}

/**
 *  服务地址测速结果对象
 */
export class TKHttpCheckServerResult {
  /**
   * 测速返回成功次数
   */
  public successNum: number = 0;
  /**
   * 测速返回失败次数
   */
  public failedNum: number = 0;
  /**
   * 总共需要测速的次数
   */
  public totalNum: number = 0;

  public addFailedNum() {
    this.failedNum++;
  }

  public addSuccessNum() {
    this.successNum++;
  }

  public resetResult() {
    this.successNum = 0;
    this.failedNum = 0;
  }

  public isFinished(): boolean {
    return (this.successNum + this.failedNum) == this.totalNum;
  }
}

/**
 * Http服务站点对象
 */
export class TKHttpServer implements TKHttpSpeedCheckerDelegate {
  /**
   *本地缓存的最快地址
   */
  public static readonly CACHE_FAST_HTTP_NETADDRESS: string = "cache_fast_http_netaddress";
  /**
   *  网络测速代理
   */
  public delegate: TKHttpSpeedServerDelegate | undefined = undefined;
  /**
   * 机房ID
   */
  public roomName: string = "";
  /**
   *  服务名称
   */
  public serverName: string = "";
  /**
   *  域名地址列表
   */
  public netDomainArr: Array<TKHttpAddress> = new Array<TKHttpAddress>();
  /**
   * IP地址列表
   */
  public netAddressArr: Array<TKHttpAddress> = new Array<TKHttpAddress>();
  /**
   *  用户选择设置的地址
   */
  public useNetAddress: TKHttpAddress | undefined = undefined;
  /**
   *  当前正在使用的地址
   */
  public curNetAddress: TKHttpAddress | undefined = undefined;
  /**
   * 测速链接
   */
  public speedUrl: string = "";
  /**
   *  用户信息
   */
  public userInfo: Object | undefined = undefined;
  /**
   *  检测线程
   */
  private checkThreads: Array<TKHttpSpeedChecker> = new Array<TKHttpSpeedChecker>();
  /**
   *  检测结果字典
   */
  private checkResult: TKHttpCheckServerResult = new TKHttpCheckServerResult();

  /**
   *  初始化
   *
   * @param name      名称
   * @param configMap 配置文件
   *
   * @return
   */
  public constructor(name: string, configMap: Map<string, Object>) {
    //初始化属性
    this.serverName = name;
    if (configMap) {
      this.roomName = TKMapHelper.getString(configMap, "room");
      this.speedUrl = TKMapHelper.getString(configMap, "speedUrl");
      //域名服务器列表
      let domainList: string = TKMapHelper.getString(configMap, "domain");
      this.netDomainArr = this.processServerWithAddresses(domainList, "", true);
      //IP服务器列表
      let serverList: string = TKMapHelper.getString(configMap, "ip");
      let serverNameList: string = TKMapHelper.getString(configMap, "description");
      this.netAddressArr = this.processServerWithAddresses(serverList, serverNameList, false);
      // 域名检测线程
      for (let netAddress of this.netDomainArr) {
        let httpSpeedChecker: TKHttpSpeedChecker = new TKHttpSpeedChecker(netAddress);
        this.checkThreads.push(httpSpeedChecker);
      }
      //IP检测线程
      for (let netAddress of this.netAddressArr) {
        let httpSpeedChecker: TKHttpSpeedChecker = new TKHttpSpeedChecker(netAddress);
        this.checkThreads.push(httpSpeedChecker);
      }
      this.checkResult.totalNum = this.checkThreads.length;
    }
  }

  /**
   * 处理解析地址
   *
   * @param addresses    地址列表字符串，多个是用|分割
   * @param addressNames 地址名称列表字符串，多个是用|分割
   * @param isDomainFlag 是否是域名地址标志
   *
   */
  private processServerWithAddresses(addresses: string, addressNames: string,
    isDomainFlag: boolean): Array<TKHttpAddress> {
    let netAddressResults: Array<TKHttpAddress> = new Array<TKHttpAddress>();
    if (TKStringHelper.isNotBlank(addresses)) {
      //服务器列表
      let addressesAry: Array<string> = TKStringHelper.split(addresses, "|");
      //服务器列表名称
      let addressesNameAry: Array<string> = TKStringHelper.split(addressNames, "|");
      if (addressesAry && addressesAry.length > 0) {
        for (let i = 0; i < addressesAry.length; i++) {
          let address: string = addressesAry[i];
          if (TKStringHelper.startsWith(address, "http://") || TKStringHelper.startsWith(address, "https://")) {
            //服务器地址
            let netAddress: TKHttpAddress = new TKHttpAddress();
            netAddress.roomName = this.roomName;
            netAddress.isDomain = isDomainFlag;
            netAddress.serverName = this.serverName;
            netAddress.url = address;
            netAddress.speedUrl = this.speedUrl;
            netAddress.isAlive = false;
            netAddress.isIPV6 = TKNetHelper.getHostByURL(address).indexOf(":") > -1;
            netAddress.desc = (addressesNameAry && addressesNameAry.length > i) ? addressesNameAry[i] : "";
            netAddressResults.push(netAddress);
          }
        }
      }
    }
    return netAddressResults;
  }

  /**
   *  获取配置所有的服务器地址（IP+域名），也就是属性netAddressArr + netDomainArr的集合
   *
   * @return
   */
  public getAllNetworkAddresses(): Array<TKHttpAddress> {
    let addresses: Array<TKHttpAddress> = new Array<TKHttpAddress>();
    if (this.netDomainArr && this.netDomainArr.length > 0) {
      addresses.push(...this.netDomainArr);
    }
    if (this.netAddressArr && this.netAddressArr.length > 0) {
      addresses.push(...this.netAddressArr);
    }
    return addresses;
  }

  /**
   *  获取使用的服务器地址
   *
   * @return
   */
  public getRmdServer(): TKHttpAddress | undefined {
    let canUseAddress: TKHttpAddress | undefined = undefined;
    //当前使用的站点
    if (!canUseAddress) {
      canUseAddress = this.getCurNetAddress();
    }
    //当前手工选择的站点
    if (!canUseAddress) {
      canUseAddress = this.getUseNetAddress();
    }
    //判断是否手动缓存站点模式
    if (!canUseAddress) {
      canUseAddress = this.getBestCacheCanUseServer();
    }
    //不是就再获取域名
    if (!canUseAddress) {
      canUseAddress = this.getCanUseFastServer(this.netDomainArr);
    }
    //域名没有或者挂了再获取IP
    if (!canUseAddress) {
      canUseAddress = this.getCanUseFastServer(this.netAddressArr);
    }
    //执行最终服务器默认策略
    if (!canUseAddress) {
      canUseAddress = this.getRmdServerForLast();
    }
    //此时没有，就是配置文件出问题了
    if (!canUseAddress) {
      TKLog.error(`没有配置${this.serverName}网关服务器列表，请查看BusConfig.xml配置文件！`);
    }
    this.curNetAddress = canUseAddress;
    return canUseAddress;
  }

  /**
   * 获取当前正在使用的地址
   */
  private getCurNetAddress(): TKHttpAddress | undefined {
    let canUseAddress: TKHttpAddress | undefined = undefined;
    if (this.curNetAddress && this.curNetAddress.isAlive) {
      //获取当前用户正在使用的站点
      canUseAddress = this.curNetAddress;
    }
    return canUseAddress;
  }

  /**
   * 获取当前正在用户选择使用的地址
   */
  private getUseNetAddress(): TKHttpAddress | undefined {
    let canUseAddress: TKHttpAddress | undefined = undefined;
    if (this.useNetAddress && this.useNetAddress.isAlive) {
      //获取当前用户手工设置选择的站点
      canUseAddress = this.useNetAddress;
    }
    return canUseAddress;
  }

  /**
   * 获取手动站点缓存策略下可用的地址
   */
  private getBestCacheCanUseServer(): TKHttpAddress | undefined {
    let canUseAddress: TKHttpAddress | undefined = undefined;
    if (TKHttpRoomServerManager.shareInstance().isRoomServerUseAddressManualMode(this.roomName, this.serverName)) {
      //先判断手工缓存的最优地址，如果有，然后判断是否存活，如果存活就用，不然就继续地址筛选逻辑
      let bestCacheAddressStr: string = TKHttpRoomServerManager.shareInstance()
        .getRoomCacheNoExpireServerUseAddress(this.roomName, this.serverName);
      let bestCacheAddress: TKHttpAddress | undefined = this.getServerAddress(bestCacheAddressStr);
      if (bestCacheAddress && bestCacheAddress.isAlive) {
        canUseAddress = bestCacheAddress;
      }
    }
    return canUseAddress;
  }

  /**
   * 获取可用地址
   */
  private getCanUseFastServer(netAddresses: Array<TKHttpAddress>): TKHttpAddress | undefined {
    let tempList: Array<TKHttpAddress> = new Array<TKHttpAddress>();
    if (netAddresses && netAddresses.length > 0) {
      for (let netAddress of netAddresses) {
        if (netAddress.isAlive) {
          tempList.push(netAddress);
        }
      }
    }
    if (tempList.length > 0) {
      let i: number = 0;
      if (tempList.length > 1) {
        i = this.getRandomIndexForArrayList(tempList);
        let fastSpeedAddress: TKHttpAddress = tempList[i];
        for (let index = 0; index < tempList.length; index++) {
          let netAddress: TKHttpAddress = tempList[index];
          if (fastSpeedAddress.speed > netAddress.speed && netAddress.speed > 0) {
            fastSpeedAddress = netAddress;
            i = index;
          }
        }
      }
      return tempList[i];
    }
    return undefined;
  }

  /**
   * 获取最终可用地址，根据策略LMode
   */
  private getRmdServerForLast(): TKHttpAddress | undefined {
    let canUseAddress: TKHttpAddress | undefined = undefined;
    //先判断域名是否配置
    if (!canUseAddress) {
      canUseAddress = this.getCanUseServerForLast(this.netDomainArr);
    }
    //没有配置域名就获取配置的IP
    if (!canUseAddress) {
      canUseAddress = this.getCanUseServerForLast(this.netAddressArr);
    }
    return canUseAddress;
  }

  /**
   * 获取最终可用地址，根据策略LMode
   */
  private getCanUseServerForLast(netAddresses: Array<TKHttpAddress>): TKHttpAddress | undefined {
    let tempList: Array<TKHttpAddress> = new Array<TKHttpAddress>();
    if (netAddresses && netAddresses.length > 0) {
      for (let address of netAddresses) {
        if (!address.isIPV6) {
          tempList.push(address);
        }
      }
    }
    if (tempList.length > 0) {
      //都没有就获取本地缓存的上次使用的地址
      let canUseAddress: TKHttpAddress | undefined = this.getLastFastCacheCanUseServer();
      if (!canUseAddress) {
        canUseAddress = this.curNetAddress;
      }
      if (!canUseAddress) {
        canUseAddress = this.useNetAddress;
      }
      //再没有，就随机获取一个地址
      if (!canUseAddress) {
        let i: number = this.getRandomIndexForArrayList(tempList);
        canUseAddress = tempList[i];
      }
      return canUseAddress;
    }
    return undefined;
  }

  /**
   * 获取上次缓存的最快地址
   */
  private getLastFastCacheCanUseServer(): TKHttpAddress | undefined {
    let canUseAddress: TKHttpAddress | undefined = undefined;
    let fastCacheNetAddressKey: string =
      `${TKHttpServer.CACHE_FAST_HTTP_NETADDRESS}_${this.roomName}_${this.serverName}`;
    let fastCacheNetAddress: Map<string, Object> | undefined = TKCacheManager.shareInstance()
      .getFileCacheData(fastCacheNetAddressKey);
    if (fastCacheNetAddress) {
      let cacheFastHost: string = TKMapHelper.getString(fastCacheNetAddress, "host");
      let tempServerAddress: TKHttpAddress | undefined = this.getServerAddress(cacheFastHost);
      if (tempServerAddress) {
        canUseAddress = tempServerAddress;
      }
    }
    return canUseAddress;
  }

  /**
   * 获取数组的随机索引
   */
  private getRandomIndexForArrayList(arrayList: Array<TKHttpAddress>): number {
    let index: number = 0;
    if (arrayList && arrayList.length > 0) {
      index = TKNumberHelper.getRandomNum(arrayList.length);
    }
    return index;
  }

  /**
   *
   *  获取指定地址和端口的服务器地址
   * @param address 地址，格式为http://ip:port的字符串
   *
   * @return
   */
  public getServerAddress(address: string): TKHttpAddress | undefined {
    if (TKStringHelper.isNotBlank(address)) {
      let destAddressStr: string = TKNetHelper.getSchemeHostPortByURL(address);
      let networkAddresses: Array<TKHttpAddress> = this.getAllNetworkAddresses();
      if (networkAddresses && networkAddresses.length > 0) {
        for (let netAddress of networkAddresses) {
          let tempAddressStr: string = TKNetHelper.getSchemeHostPortByURL(netAddress.url);
          if (tempAddressStr == destAddressStr) {
            return netAddress;
          }
        }
      }
    }
    return undefined;
  }

  /**
   * 站点测速
   */
  public startTest(testSpeedMode: TKHttpTestSpeedMode) {
    if (this.checkThreads && this.checkThreads.length > 0) {
      this.checkResult.resetResult();
      for (let httpSpeedChecker of this.checkThreads) {
        httpSpeedChecker.testSpeedMode = testSpeedMode;
        httpSpeedChecker.delegate = this;
        httpSpeedChecker.startTest();
      }
    }
  }

  /**
   * 取消测速
   */
  public stopTest() {
    if (this.checkThreads && this.checkThreads.length > 0) {
      for (let httpSpeedChecker of this.checkThreads) {
        httpSpeedChecker.stopTest();
      }
      this.checkResult.resetResult();
    }
  }

  /**
   *  检测结果
   *
   * @param address
   */
  public httpSpeedCheckResult(httpSpeedChecker: TKHttpSpeedChecker) {
    if (httpSpeedChecker.testSpeedMode == TKHttpTestSpeedMode.Room) {
      if (httpSpeedChecker.netAddress.isAlive) {
        this.checkResult.addSuccessNum();
        this.stopTest();
        if (this.delegate) {
          this.delegate.roomCheckSpeedFinished(httpSpeedChecker.netAddress.roomName,
            httpSpeedChecker.netAddress.serverName, true);
        }
      } else {
        this.checkResult.addFailedNum();
        if (this.checkResult.isFinished()) {
          this.stopTest();
          if (this.delegate) {
            this.delegate.roomCheckSpeedFinished(httpSpeedChecker.netAddress.roomName,
              httpSpeedChecker.netAddress.serverName, false);
          }
        }
      }
    } else {
      if (this.delegate) {
        this.delegate.testSpeed(httpSpeedChecker.netAddress, httpSpeedChecker.testSpeedMode);
      }
      if (httpSpeedChecker.netAddress.isAlive) {
        this.checkResult.addSuccessNum();
      } else {
        this.checkResult.addFailedNum();
      }
      if (this.checkResult.isFinished()) {
        this.stopTest();
        if (this.delegate) {
          this.delegate.testSpeedFinished(httpSpeedChecker.netAddress.serverName, httpSpeedChecker.testSpeedMode);
        }
      }
    }
  }
}