/**
 *  Http服务管理器
 */
import { TKCacheManager } from '../../../../../../../util/cache/TKCacheManager';
import { TKDataHelper } from '../../../../../../../util/data/TKDataHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKHttpAddress } from './TKHttpAddress';
import { TKHttpRoom } from './TKHttpRoom';
import { TKHttpRoomConfig } from './TKHttpRoomConfig';
import { TKHttpServer } from './TKHttpServer';

export class TKHttpRoomServerManager {
  public static readonly TK_CACHE_HTTPSERVER_USE_ADDRESS_MODE: string = "TKCacheHttpServerUseAddressMode";
  public static readonly TK_CACHE_HTTPSERVER_USE_ADDRESS: string = "TKCacheHttpServerUseAddress";
  private static instance: TKHttpRoomServerManager | undefined = undefined;
  private roomMap: Map<string, TKHttpRoom> = new Map<string, TKHttpRoom>();

  public constructor() {
    let roomConfig: Map<string, Object> = TKHttpRoomConfig.shareInstance().getRoomConfig();
    if (roomConfig && roomConfig.size > 0) {
      roomConfig.forEach((value, key) => {
        let roomProp: Map<string, Object> = value as Map<string, Object>;
        let room: TKHttpRoom = new TKHttpRoom(key, roomProp);
        this.roomMap.set(key, room);
      });
    }
  }

  /**
   *  单例模式
   */
  public static shareInstance(): TKHttpRoomServerManager {
    if (!TKHttpRoomServerManager.instance) {
      TKHttpRoomServerManager.instance = new TKHttpRoomServerManager();
    }
    return TKHttpRoomServerManager.instance;
  }

  /**
   *  获取所有机房列表
   */
  public getRooms(): Map<string, TKHttpRoom> {
    return this.roomMap;
  }

  /**
   *  获取机房
   *
   * @param roomName 机房名称
   *
   * @return
   */
  public getRoom(roomName: string): TKHttpRoom | undefined {
    return this.roomMap.get(roomName);
  }

  /**
   *  获取所有服务网关列表
   *
   * @param roomName 机房名称
   *
   * @return
   */
  public getRoomServers(roomName: string): Map<string, TKHttpServer> {
    let room: TKHttpRoom | undefined = this.roomMap.get(roomName);
    if (room) {
      return room.getServers();
    }
    return new Map<string, TKHttpServer>();
  }

  /**
   *  获取服务器
   *
   * @param roomName 机房名称
   * @param serverName 服务器名称
   *
   * @return
   */
  public getRoomServer(roomName: string, serverName: string): TKHttpServer | undefined {
    let room: TKHttpRoom | undefined = this.roomMap.get(roomName);
    if (room) {
      return room.getServer(serverName);
    }
    return undefined;
  }

  /**
   *  设置服务器需要使用的站点地址
   *
   * @param roomName 机房名称
   * @param serverName 服务名称
   * @param address    站点地址
   */
  public setRoomServerUseAddress(roomName: string, serverName: string, address: TKHttpAddress) {
    let roomServer: TKHttpServer | undefined = this.getRoomServer(roomName, serverName);
    if (roomServer) {
      roomServer.useNetAddress = address;
      roomServer.curNetAddress = undefined;
      if (this.isRoomServerUseAddressManualMode(roomName, serverName)) {
        let bestCacheServer: string = address.url;
        TKCacheManager.shareInstance()
          .saveFileCacheData(`${TKHttpRoomServerManager.TK_CACHE_HTTPSERVER_USE_ADDRESS}_${roomServer.roomName}_${roomServer.serverName}`,
            bestCacheServer);
      }
    }
  }

  /*
   *  设置服务器是否开启手动保存模式,默认是NO，代表不开启
   *
   *  @param roomName 机房名称
   *  @param serverName      服务名称
   *  @param isManualMode    是否手动模式
   */
  public setRoomServerUseAddressMode(roomName: string, serverName: string, isManualMode: boolean) {
    let server: TKHttpServer | undefined = this.getRoomServer(roomName, serverName);
    if (server) {
      TKCacheManager.shareInstance()
        .saveFileCacheData(`${TKHttpRoomServerManager.TK_CACHE_HTTPSERVER_USE_ADDRESS_MODE}_${server.roomName}_${server.serverName}`,
          isManualMode);
      if (isManualMode) {
        let bestCacheServer: string = "";
        if (server.curNetAddress) {
          //首先获取当前正在用的地址
          bestCacheServer = server.curNetAddress.url;
        } else if (server.useNetAddress) {
          //没有的话，就取当前是否是用户选择的地址
          bestCacheServer = server.useNetAddress.url;
        } else {
          //没有的话，就取上次本地缓存的固定站点
          bestCacheServer = this.getRoomCacheNoExpireServerUseAddress(server.roomName, server.serverName);
        }
        //获取到站点进行保存，变成固定站点，下次启动继续使用
        if (TKStringHelper.isNotBlank(bestCacheServer)) {
          TKCacheManager.shareInstance()
            .saveFileCacheData(`${TKHttpRoomServerManager.TK_CACHE_HTTPSERVER_USE_ADDRESS}_${server.roomName}_${server.serverName}`,
              bestCacheServer);
        }
      }
    }
  }

  /**
   *  获取服务器是否开启手动保存模式
   *
   * @param roomName 机房名称
   * @param serverName      服务名称
   */
  public isRoomServerUseAddressManualMode(roomName: string, serverName: string): boolean {
    let server: TKHttpServer | undefined = this.getRoomServer(roomName, serverName);
    if (server) {
      return TKDataHelper.getBoolean(TKCacheManager.shareInstance()
        .getFileCacheData(`${TKHttpRoomServerManager.TK_CACHE_HTTPSERVER_USE_ADDRESS_MODE}_${server.roomName}_${server.serverName}`));
    }
    return false;
  }

  /**
   *  获取手动模式下缓存的未过期的站点地址
   *
   * @param roomName 机房名称
   * @param serverName      服务名称
   */
  public getRoomCacheNoExpireServerUseAddress(roomName: string, serverName: string): string {
    let server: TKHttpServer | undefined = this.getRoomServer(roomName, serverName);
    if (server) {
      return TKCacheManager.shareInstance()
        .getFileCacheData(`${TKHttpRoomServerManager.TK_CACHE_HTTPSERVER_USE_ADDRESS}_${server.roomName}_${server.serverName}`) ??
        "";
    }
    return "";
  }
}