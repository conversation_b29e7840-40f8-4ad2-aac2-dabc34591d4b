import { TKArrayHelper } from '../../../../../../../util/array/TKArrayHelper';
import { TKCacheManager } from '../../../../../../../util/cache/TKCacheManager';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKNumberHelper } from '../../../../../../../util/number/TKNumberHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKNetworkListener } from '../../../../../../network/listener/TKNetworkListener';
import { TKHttpAddress } from './TKHttpAddress';
import { TKHttpRoom } from './TKHttpRoom';
import { TKHttpRoomConfig } from './TKHttpRoomConfig';
import { TKHttpRoomServerManager } from './TKHttpRoomServerManager';
import { TKHttpServer, TKHttpSpeedServerDelegate, TKHttpTestSpeedMode } from './TKHttpServer';

/**
 *  网络监听代理
 */
export interface TKHttpServerTestSpeedDelegate {

  /**
   *  测速结果
   *
   * @param address
   */
  testSpeed?: (address: TKHttpAddress) => void;

  /**
   *  测速结束
   *
   * @param address
   */
  testSpeedFinished?: (serverName: string) => void;
}

/**
 * 启动监听回调
 */
export type TKHttpServerListenerStartFinishCallBack = () => void;

/**
 *  网络智能测速管理模块
 */
export class TKHttpRoomServerListener implements TKHttpSpeedServerDelegate {
  /**
   *  缓存上次选择的房间
   */
  public static readonly CACHE_SERVERROOM: string = "cache_server_room";
  private static instance: TKHttpRoomServerListener | undefined = undefined;
  /**
   * 启动监听回调
   */
  private httpServerListenerStartFinishCallBack: TKHttpServerListenerStartFinishCallBack | undefined = undefined;
  /**
   * 确认挂掉的机房
   */
  private deadRoomNameSet: Set<string> = new Set<string>();
  /**
   * 确认存活的机房
   */
  private liveRoomNameSet: Set<string> = new Set<string>();
  /**
   * 当前选择的机房
   */
  private selectedRoomName: string = "";
  /**
   *  检测结束的站点集合
   */
  private checkFinishedSet: Set<string> = new Set<string>();
  /**
   *  网络测速代理
   */
  public delegate: TKHttpServerTestSpeedDelegate | undefined = undefined;

  /**
   *  单例模式
   */
  public static shareInstance(): TKHttpRoomServerListener {
    if (!TKHttpRoomServerListener.instance) {
      TKHttpRoomServerListener.instance = new TKHttpRoomServerListener();
    }
    return TKHttpRoomServerListener.instance;
  }

  private excuteHttpServerListenerStartFinishCallBack() {
    if (this.httpServerListenerStartFinishCallBack) {
      this.httpServerListenerStartFinishCallBack();
    }
  }

  /**
   *  开始测速
   */
  public start(httpServerListenerStartFinishCallBack: TKHttpServerListenerStartFinishCallBack | undefined) {
    this.httpServerListenerStartFinishCallBack = httpServerListenerStartFinishCallBack;
    if (!TKNetworkListener.shareInstance().isNetAvailable()) {
      this.excuteHttpServerListenerStartFinishCallBack();
      return;
    }
    let roomMap: Map<string, TKHttpRoom> = TKHttpRoomServerManager.shareInstance().getRooms();
    //本次选择的机房测速
    if (roomMap && roomMap.size > 0) {
      this.selectedRoomName = "";
      if (roomMap.size > 1) {
        this.startTestAllRooms();
      } else {
        this.selectedRoomName = roomMap.keys().next().value;
        this.startTestCurrentRoomServers();
      }
    }
    if (Number(TKHttpRoomConfig.shareInstance().speedUrl) > 0) {
      setTimeout(() => {
        this.excuteHttpServerListenerStartFinishCallBack();
      }, Number(TKHttpRoomConfig.shareInstance().speedUrl) * 1000);
    } else {
      this.excuteHttpServerListenerStartFinishCallBack();
    }
  }

  /**
   * 测试机房
   */
  private startTestAllRooms() {
    this.deadRoomNameSet.clear();
    this.liveRoomNameSet.clear();
    let roomMap: Map<string, TKHttpRoom> = TKHttpRoomServerManager.shareInstance().getRooms();
    if (roomMap && roomMap.size > 0) {
      roomMap.forEach((value, key) => {
        let roomKey: string = key;
        let httpRoom: TKHttpRoom = value;
        if (TKStringHelper.isNotBlank(httpRoom.mainSite)) {
          let mainSite: TKHttpServer | undefined = httpRoom.getServer(httpRoom.mainSite);
          if (mainSite) {
            mainSite.delegate = this;
            mainSite.startTest(TKHttpTestSpeedMode.Room);
          }
        }
      });
    }
  }

  /**
   *  初始化机房选择测速结束
   *
   * @param address
   */
  public roomCheckSpeedFinished(roomName: string, serverName: string, result: boolean) {
    if (TKStringHelper.isBlank(this.selectedRoomName)) {
      if (result) {
        this.liveRoomNameSet.add(roomName);
        TKHttpRoomServerManager.shareInstance().getRoom(roomName)!.roomState = "1";
      } else {
        this.deadRoomNameSet.add(roomName);
        TKHttpRoomServerManager.shareInstance().getRoom(roomName)!.roomState = "0";
      }
      //随机模式
      if (TKHttpRoomConfig.shareInstance().roomMode == "0") {
        if (this.liveRoomNameSet.size + this.deadRoomNameSet.size == TKHttpRoomServerManager.shareInstance()
          .getRooms()
          .size) {
          this.clearRoomCheckThread();
          this.buildInitRoom();
          this.startTestCurrentRoomServers();
        }
      } else {
        // 最优模式
        if (result) {
          this.selectedRoomName = roomName;
          TKCacheManager.shareInstance()
            .saveFileCacheData(TKHttpRoomServerListener.CACHE_SERVERROOM, this.selectedRoomName);
          TKLog.info("成功选择最优机房，机房编号为:" + this.selectedRoomName);
          this.clearRoomCheckThread();
          this.buildInitRoom();
          this.startTestCurrentRoomServers();
        } else {
          if (this.liveRoomNameSet.size + this.deadRoomNameSet.size == TKHttpRoomServerManager.shareInstance()
            .getRooms()
            .size) {
            this.clearRoomCheckThread();
            this.buildInitRoom();
            this.startTestCurrentRoomServers();
          }
        }
      }
    }
  }

  /**
   * 初始化机房选择
   */
  private buildInitRoom() {
    if (TKStringHelper.isBlank(this.selectedRoomName)) {
      //最优选择机房模式
      if (TKHttpRoomConfig.shareInstance().roomMode != "0") {
        this.selectedRoomName = TKCacheManager.shareInstance()
          .getFileCacheData(TKHttpRoomServerListener.CACHE_SERVERROOM) ?? "";
        if (TKStringHelper.isNotBlank(this.selectedRoomName)) {
          let roomMap: Map<string, TKHttpRoom> = TKHttpRoomServerManager.shareInstance().getRooms();
          let httpRoom: TKHttpRoom | undefined = roomMap.get(this.selectedRoomName);
          if (httpRoom) {
            TKLog.info("选择上次缓存的机房，机房编号为:" + this.selectedRoomName);
          } else {
            TKCacheManager.shareInstance().deleteFileCacheData(TKHttpRoomServerListener.CACHE_SERVERROOM);
            this.selectedRoomName = "";
          }
        }
      }
      if (TKStringHelper.isBlank(this.selectedRoomName)) {
        //获取当前确定存活的机房列表
        let roomKeys: Array<string> = Array.from(this.liveRoomNameSet);
        //如果没有明确的机房列表，则进行进一步赛选
        if (roomKeys.length == 0) {
          let roomMap: Map<string, TKHttpRoom> = TKHttpRoomServerManager.shareInstance().getRooms();
          //首先获取所有待选的机房列表
          let allRoomKeys: Array<string> = Array.from(roomMap.keys());
          let unknownRoomKeys: Array<string> = Array.from(allRoomKeys);
          //排除已知挂了的机房列表
          if (this.deadRoomNameSet && this.deadRoomNameSet.size > 0) {
            TKArrayHelper.removeObjectsInArray(unknownRoomKeys, this.deadRoomNameSet);
          }
          //从剩下的列表中再排除可能已经挂了的机房（也就是状态标记为已经不可用的机房）
          roomKeys = Array.from(unknownRoomKeys);
          if (roomKeys.length > 0) {
            let rkeys: Array<string> = Array.from(roomKeys);
            for (let rkey of rkeys) {
              let room: TKHttpRoom = roomMap.get(rkey) as TKHttpRoom;
              if (room.roomState == "0") {
                TKArrayHelper.removeObjectInArray(roomKeys, rkey);
              }
            }
            //如果没有了列表，就取unknownRoomKeys
            if (roomKeys.length == 0) {
              roomKeys = unknownRoomKeys;
            }
          } else {
            //如果机房都挂了，就从所有机房列表中随机选一个
            roomKeys = allRoomKeys;
          }
        }
        if (roomKeys && roomKeys.length > 0) {
          let index: number = TKNumberHelper.getRandomNum(roomKeys.length);
          this.selectedRoomName = roomKeys[index];
        }
        TKLog.info("随机选择机房，机房编号为:" + this.selectedRoomName);
      }
    }
  }

  /**
   * 清理机房选择请求
   */
  private clearRoomCheckThread() {
    let roomMap: Map<string, TKHttpRoom> = TKHttpRoomServerManager.shareInstance().getRooms();
    roomMap.forEach((value, key) => {
      let httpRoom: TKHttpRoom = value;
      if (TKStringHelper.isNotBlank(httpRoom.mainSite)) {
        let mainSite: TKHttpServer | undefined = httpRoom.getServer(httpRoom.mainSite);
        if (mainSite) {
          mainSite.stopTest();
        }
      }
    });
  }

  /**
   * 开始进行机房内各个站点的测速
   */
  private startTestCurrentRoomServers() {
    this.checkFinishedSet.clear();
    let roomServerMap: Map<string, TKHttpServer> = TKHttpRoomServerManager.shareInstance()
      .getRoomServers(this.selectedRoomName);
    if (roomServerMap && roomServerMap.size > 0) {
      roomServerMap.forEach((value, key) => {
        let roomServerKey: string = key;
        let httpServer: TKHttpServer = value;
        if (httpServer.getAllNetworkAddresses().length <= 1) {
          this.checkFinishedSet.add(httpServer.serverName);
          if (this.checkFinishedSet.size == roomServerMap.size) {
            this.excuteHttpServerListenerStartFinishCallBack();
          }
        } else {
          httpServer.delegate = this;
          httpServer.startTest(TKHttpTestSpeedMode.Init);
        }
      });
    }
  }

  /**
   *  测速结果
   * @param address
   */
  public testSpeed(address: TKHttpAddress, testSpeedMode: TKHttpTestSpeedMode) {
    if (testSpeedMode == TKHttpTestSpeedMode.View) {
      if (this.delegate && this.delegate.testSpeed) {
        this.delegate.testSpeed(address);
      }
    }
  }

  /**
   *  测速结束
   *
   * @param address
   */
  public testSpeedFinished(serverName: string, testSpeedMode: TKHttpTestSpeedMode) {
    if (testSpeedMode == TKHttpTestSpeedMode.Init) {
      let roomServerMap: Map<string, TKHttpServer> = TKHttpRoomServerManager.shareInstance()
        .getRoomServers(this.selectedRoomName);
      this.checkFinishedSet.add(serverName);
      if (this.checkFinishedSet.size == roomServerMap.size) {
        this.excuteHttpServerListenerStartFinishCallBack();
      }
    } else if (testSpeedMode == TKHttpTestSpeedMode.View) {
      if (this.delegate && this.delegate.testSpeedFinished) {
        this.delegate.testSpeedFinished(serverName);
      }
    }
  }

  /**
   *  检查某个站点下面的各个线路的速度
   *
   * @param serverName
   */
  public startCheck(serverName: string) {
    this.buildInitRoom();
    let httpServer: TKHttpServer | undefined = TKHttpRoomServerManager.shareInstance()
      .getRoomServer(this.selectedRoomName, serverName);
    if (httpServer) {
      httpServer.delegate = this;
      httpServer.startTest(TKHttpTestSpeedMode.Test);
    }
  }

  /**
   *  测速某个站点下面的各个线路的速度，会执行回调用于渲染界面
   *
   * @param serverName
   */
  public startTest(serverName: string) {
    this.buildInitRoom();
    let httpServer: TKHttpServer | undefined = TKHttpRoomServerManager.shareInstance()
      .getRoomServer(this.selectedRoomName, serverName);
    if (httpServer) {
      httpServer.delegate = this;
      httpServer.startTest(TKHttpTestSpeedMode.View);
    }
  }

  /**
   *  获取最快的网络环境
   *
   * @param serverName 服务类别
   *
   * @return 当前类别下最快的服务器地址
   */
  public getFlastHost(serverName: string): string {
    this.buildInitRoom();
    let httpServer: TKHttpServer | undefined = TKHttpRoomServerManager.shareInstance()
      .getRoomServer(this.selectedRoomName, serverName);
    if (httpServer) {
      return httpServer.getRmdServer()!.url;
    }
    return "";
  }

  /**
   *  设置服务器需要使用的站点地址
   *
   * @param serverName 服务名称
   * @param address    站点地址
   */
  public setServerUseAddress(serverName: string, address: TKHttpAddress) {
    this.buildInitRoom();
    TKHttpRoomServerManager.shareInstance().setRoomServerUseAddress(this.selectedRoomName, serverName, address);
  }

  /**
   *  获取当前正在使用的站点地址
   *
   * @param serverName 服务名称
   */
  public getServerUseAddress(serverName: string): TKHttpAddress | undefined {
    this.buildInitRoom();
    let httpServer: TKHttpServer | undefined = TKHttpRoomServerManager.shareInstance()
      .getRoomServer(this.selectedRoomName, serverName);
    if (httpServer) {
      return httpServer.getRmdServer();
    }
    return undefined;
  }

  /**
   *  获取指定服务的站点地址
   *
   * @param serverName 服务名称
   */
  public getServerAddresses(serverName: string): Array<TKHttpAddress> {
    this.buildInitRoom();
    let httpServer: TKHttpServer | undefined = TKHttpRoomServerManager.shareInstance()
      .getRoomServer(this.selectedRoomName, serverName);
    if (httpServer) {
      return httpServer.netAddressArr;
    }
    return new Array<TKHttpAddress>();
  }

  /*
   *  设置服务器是否开启手动保存模式,默认是NO，代表不开启
   *
   *  @param serverName      服务名称
   *  @param isManualMode    是否手动模式
   */
  public setServerUseAddressMode(serverName: string, isManualMode: boolean) {
    this.buildInitRoom();
    TKHttpRoomServerManager.shareInstance()
      .setRoomServerUseAddressMode(this.selectedRoomName, serverName, isManualMode);
  }

  /**
   *  获取服务器是否开启手动保存模式
   *
   * @param roomName 机房名称
   * @param serverName      服务名称
   */
  public isServerUseAddressManualMode(serverName: string): boolean {
    return TKHttpRoomServerManager.shareInstance().isRoomServerUseAddressManualMode(this.selectedRoomName, serverName);
  }

  /**
   * 清除本地缓存
   */
  public static clearCache() {
    TKHttpRoomConfig.clearCache();
  }
}