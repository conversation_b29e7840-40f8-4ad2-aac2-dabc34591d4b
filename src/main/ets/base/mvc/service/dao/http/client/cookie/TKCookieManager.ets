import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import web_webview from '@ohos.web.webview';
import { TKNetHelper } from '../../../../../../../util/net/TKNetHelper';

/**
 * Cookie存储管理器
 */
export class TKCookieManager {
  //单例对象
  private static instance: TKCookieManager | undefined = undefined;
  //cookie存储Map
  private domainCookieMap: Map<string, Map<string, string>> = new Map<string, Map<string, string>>();

  private constructor() {
  }

  public static shareInstance(): TKCookieManager {
    if (!TKCookieManager.instance) {
      TKCookieManager.instance = new TKCookieManager();
    }
    return TKCookieManager.instance;
  }

  /**
   * 获取cookie
   * @param domain
   * @returns
   */
  public getCookieStr(domain: string): string {
    return this.getCookie(domain).join("; ");
  }

  /**
   * 获取cookie
   * @param domain
   * @returns
   */
  public getCookie(domain: string): Array<string> {
    let cookieMap: Map<string, string> | undefined = this.domainCookieMap.get(domain);
    if (cookieMap) {
      return Array.from(cookieMap.values());
    }
    return new Array<string>();
  }

  /**
   * 设置cookie
   * @param domain
   * @param cookies
   * @param isSyncWebView
   */
  public setCookie(domain: string, cookies: Array<string>, isSyncWebView: boolean = true) {
    if (TKStringHelper.isNotBlank(domain) && cookies.length > 0) {
      let cookieNames: Array<string> = new Array<string>();
      cookies.forEach((cookie) => {
        let temp: Array<string> = cookie.split(";")[0].split("=")
        let cookieName: string = temp[0].trim();
        let cookieValue: string = temp.length > 1 ? temp[1].trim() : "";
        if (!cookieNames.includes(cookieName) && TKStringHelper.isNotBlank(cookieValue)) {
          cookieNames.push(cookieName);
          this.setCookieItem(domain, cookieName, cookieValue, isSyncWebView);
        }
      });
    }
  }

  /**
   * 删除cookie
   * @param domain
   */
  public deleteCookie(domain: string) {
    if (TKStringHelper.isNotBlank(domain)) {
      this.domainCookieMap.delete(domain);
    }
  }

  /**
   * 设置cookieItem
   * @param domain
   * @param cookieName
   * @param cookieValue
   */
  public setCookieItem(domain: string, cookieName: string, cookieValue: string, isSyncWebView: boolean = true) {
    if (TKStringHelper.isNotBlank(domain) && TKStringHelper.isNotBlank(cookieName)) {
      cookieName = (cookieName ?? "").trim();
      cookieValue = (cookieValue ?? "").trim()
      let cookieMap: Map<string, string> = this.domainCookieMap.get(domain) ?? new Map<string, string>();
      this.domainCookieMap.set(domain, cookieMap);
      if (TKStringHelper.isNotBlank(cookieValue)) {
        let cookie: string = `${cookieName}=${cookieValue}`;
        cookieMap.set(cookieName, cookie);
        if (isSyncWebView) {
          web_webview.WebCookieManager.configCookieSync(domain, cookie);
        }
      } else {
        cookieMap.delete(cookieName);
      }
    }
  }

  /**
   * 获取cookieItem值
   * @param domain
   * @param cookieName
   */
  public getCookieItemValue(domain: string, cookieName: string): string {
    if (TKStringHelper.isNotBlank(domain)) {
      let cookieMap: Map<string, string> | undefined = this.domainCookieMap.get(domain);
      if (cookieMap) {
        let cookie = cookieMap.get((cookieName ?? "").trim()) ?? "";
        let temp: Array<string> = cookie.split("=");
        if (temp.length == 2) {
          return temp[1];
        }
      }
    }
    return "";
  }

  /**
   * 清理cookieItem
   * @param domain
   * @param cookieName
   */
  public deleteCookieItem(domain: string, cookieName: string) {
    if (TKStringHelper.isNotBlank(domain)) {
      let cookieMap: Map<string, string> | undefined = this.domainCookieMap.get(domain);
      if (cookieMap) {
        if (TKStringHelper.isNotBlank(cookieName)) {
          cookieMap.delete((cookieName ?? "").trim());
        } else {
          this.domainCookieMap.delete(domain);
        }
      }
    }
  }

  /**
   * 同步所有原生cookie到webview
   * @param domain
   */
  public syncAllNativeCookieToWebview() {
    this.domainCookieMap.forEach((value, key) => {
      this.syncNativeCookieToWebview(key);
    });
  }

  /**
   * 同步原生cookie到webview
   * @param domain
   */
  public syncNativeCookieToWebview(domain: string) {
    if (TKStringHelper.isNotBlank(domain)) {
      let cookies: Array<string> = this.getCookie(domain);
      if (cookies && cookies.length > 0) {
        cookies.forEach((cookie, index) => {
          web_webview.WebCookieManager.configCookieSync(domain, cookie);
        })
      }
    }
  }

  /**
   * 同步webview cookie到原生
   * @param url
   */
  public syncWebviewCookieToNative(url: string) {
    if (TKStringHelper.isNotBlank(url)) {
      let domain: string = TKNetHelper.getSchemeHostPortByURL(url);
      let cookieStr: string = web_webview.WebCookieManager.fetchCookieSync(domain);
      if (TKStringHelper.isNotBlank(cookieStr)) {
        let cookies: Array<string> = TKStringHelper.split(cookieStr, ";");
        this.setCookie(domain, cookies, false);
      }
    }
  }

  /**
   * 同步cookie头到url，拼接jsessionid
   * @param url
   * @returns
   */
  public syncCookieToNativeURL(url: string): string {
    let cookieUrl: string = url;
    if ((url.startsWith("https://") || url.startsWith("http://")) && !url.includes(";jsessionid=")) {
      let urlPath: string = url;
      let urlQuery: string = "";
      let queryIndex: number = url.indexOf("?");
      if (queryIndex > 0) {
        urlPath = url.substring(0, queryIndex);
        urlQuery = url.substring(queryIndex, url.length);
      }
      let domain: string = TKNetHelper.getSchemeHostPortByURL(url);
      let cookies: Array<string> = this.getCookie(domain);
      if (cookies && cookies.length > 0) {
        for (let i = 0; i < cookies.length; i++) {
          let cookie: string = cookies[i];
          let temp: Array<string> = cookie.split("=");
          let cookieName: string = temp[0];
          let cookieValue: string = temp.length > 1 ? temp[1] : "";
          if (cookieName.toUpperCase().includes("JSESSIONID")) {
            if (TKStringHelper.isNotBlank(cookieValue)) {
              cookieUrl = `${urlPath};jsessionid=${cookieValue}${urlQuery}`;
              break;
            }
          }
        }
      }
    }
    return cookieUrl;
  }
}