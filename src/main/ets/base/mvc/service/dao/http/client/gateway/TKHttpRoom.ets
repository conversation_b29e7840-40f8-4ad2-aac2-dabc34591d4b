/**
 * Http多机房对象
 */
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKHttpServer } from './TKHttpServer';

export class TKHttpRoom {
  /**
   *  机房名称
   */
  public roomName: string = "";
  /**
   *  机房状态
   */
  public roomState: string = "";
  /**
   *  主要站点
   */
  public mainSite: string = "";
  /**
   *  机房描述
   */
  public desc: string = "";
  /**
   * 测速链接
   */
  public speedUrl: string = "";
  /**
   *  服务器配置
   */
  private serverMap: Map<string, TKHttpServer> = new Map<string, TKHttpServer>();
  /**
   *  转义服务器配置
   */
  private refServerNameMap: Map<string, string> = new Map<string, string>();

  /**
   *  初始化
   *
   * @param name      名称
   * @param configMap 配置文件
   *
   * @return
   */
  public constructor(name: string, configMap: Map<string, Object>) {
    //初始化属性
    this.roomName = name;
    if (configMap) {
      //主站点
      this.mainSite = TKMapHelper.getString(configMap, "mainsite");
      //机房状态
      this.roomState = TKMapHelper.getString(configMap, "state");
      //机房描述
      this.desc = TKMapHelper.getString(configMap, "description");
      //测速地址
      this.speedUrl = TKMapHelper.getString(configMap, "speedUrl");

      let serverConfig: Map<string, Object> | undefined = TKMapHelper.getObject(configMap, "server");
      if (serverConfig && serverConfig.size > 0) {
        serverConfig.forEach((value, key) => {
          let serverProp: Map<string, Object> = value as Map<string, Object>;
          let server: TKHttpServer = new TKHttpServer(key, serverProp);
          let ref: string = TKMapHelper.getString(serverProp, "ref");
          if (TKStringHelper.isNotBlank(ref)) {
            this.refServerNameMap.set(key, ref);
          } else {
            if (server.getAllNetworkAddresses().length > 0) {
              this.serverMap.set(key, server);
            }
          }
        });
      }
    }
  }

  /**
   *  获取所有服务网关列表
   *
   * @return
   */
  public getServers(): Map<string, TKHttpServer> {
    return this.serverMap;
  }

  /**
   *  获取服务器
   *
   * @param serverName 服务器名称
   *
   * @return
   */
  public getServer(serverName: string): TKHttpServer | undefined {
    let ref: string = TKMapHelper.getString(this.refServerNameMap, serverName);
    if (TKStringHelper.isNotBlank(ref)) {
      return this.serverMap.get(ref);
    } else {
      return this.serverMap.get(serverName);
    }
  }
}