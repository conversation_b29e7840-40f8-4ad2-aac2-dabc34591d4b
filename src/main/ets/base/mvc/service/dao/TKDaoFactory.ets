import { TKDaoType } from '../../model/TKReqParamVO';
import { TKServiceDaoDelegate } from '../protocol/TKServiceDaoDelegate';
import { TKHttpDao } from './http/TKHttpDao';
import { TKBusDao } from './socket/TKBusDao';
import { TKBaseDao } from './TKBaseDao';

/**
 *  Dao工厂
 */
export namespace TKDaoFactory {

  /**
   * Dao类定义Map
   */
  let daoClassMap: Map<string, FunctionConstructor> = new Map<string, FunctionConstructor>();

  /**
   * Dao类Map
   */
  let daoMap: Map<string, TKServiceDaoDelegate> = new Map<string, TKServiceDaoDelegate>();

  /**
   *  获取通信Dao
   *
   * @param type 类型
   *
   * @return 通信Dao
   */
  export function getDao(daoType: TKDaoType | string): TKServiceDaoDelegate | undefined {
    let dao: TKServiceDaoDelegate | undefined = undefined;
    if (typeof daoType === "string") {
      dao = daoMap.get(daoType);
      if (!dao) {
        let daoClass: FunctionConstructor | undefined = daoClassMap.get(daoType);
        if (daoClass) {
          let instance = new daoClass();
          if (instance instanceof TKBaseDao) {
            dao = instance as TKServiceDaoDelegate;
          }
          daoMap.set(daoType, dao!);
        }
      }
    } else {
      switch (daoType) {
        case TKDaoType.Http: {
          dao = TKHttpDao.shareInstance();
          break;
        }
        default:
          dao = TKBusDao.shareInstance();
          break;
      }
    }
    return dao;
  }

  /**
   * 注册插件定义
   * @param plugins
   */
  export function registerDao(daoClass: Function) {
    daoClassMap.set(daoClass.name, daoClass as FunctionConstructor);
  }
}
