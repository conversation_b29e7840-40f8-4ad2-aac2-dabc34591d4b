/**
 *  行情域定义
 */
import { TKXMLHelper } from '../../../../../../../util/file/xml/TKXMLHelper';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKQuoteFieldVO } from './TKQuoteFieldVO';
import { TKFunctionFieldType, TKQuoteFunctionVO } from './TKQuoteFunctionVO';
import { TKQuoteInputVO } from './TKQuoteInputVO';
import { TKQuoteOutputVO } from './TKQuoteOutputVO';
import { TKQuoteOutsetVO } from './TKQuoteOutsetVO';

export class TKQuoteDomainVO {
  /**
   *  功能号映射Map
   */
  private jsonFunctionMap: Map<string, TKQuoteFunctionVO> = new Map<string, TKQuoteFunctionVO>();
  /**
   *  功能号映射Map
   */
  private byteFunctionMap: Map<string, TKQuoteFunctionVO> = new Map<string, TKQuoteFunctionVO>();
  /**
   *  字段映射，根据索引当Key
   */
  private fieldSnMap: Map<string, Map<number, TKQuoteFieldVO>> = new Map<string, Map<number, TKQuoteFieldVO>>();
  /**
   *  字段映射，根据Key当Key
   */
  private fieldKeyMap: Map<string, Map<string, TKQuoteFieldVO>> = new Map<string, Map<string, TKQuoteFieldVO>>();

  /**
   *  加载配置文件
   */
  public loadFuncConfig(configFileName: string) {
    //读取配置文件
    let functionsElem: Map<string, Object> = TKXMLHelper.readConfigXml(configFileName);
    if (functionsElem.size > 0) {
      let childElems: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(functionsElem, "children");
      if (childElems) {
        childElems.forEach(childElem => {
          let childElemTag: string = TKMapHelper.getString(childElem, "$tag");
          if (childElemTag == "fields") {
            let fieldsElem = childElem;
            let fieldType: string = TKMapHelper.getString(fieldsElem, "name");
            if (TKStringHelper.isBlank(fieldType)) {
              fieldType = "field";
            }
            let fieldVOSnMap: Map<number, TKQuoteFieldVO> = new Map<number, TKQuoteFieldVO>();
            let fieldVOKeyMap: Map<string, TKQuoteFieldVO> = new Map<string, TKQuoteFieldVO>();
            this.fieldSnMap.set(fieldType, fieldVOSnMap);
            this.fieldKeyMap.set(fieldType, fieldVOKeyMap);
            let fieldELems: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(fieldsElem, "children");
            if (fieldELems) {
              fieldELems.forEach(fieldELem => {
                let fieldVO: TKQuoteFieldVO = new TKQuoteFieldVO();
                //字段名称
                fieldVO.name = TKMapHelper.getString(fieldELem, "name");
                //字段别名
                fieldVO.aliasName = TKMapHelper.getString(fieldELem, "aliasName", fieldVO.name);
                //字段序号
                fieldVO.serno = TKMapHelper.getNumber(fieldELem, "serno");
                //字段类型名称
                fieldVO.typeName = TKMapHelper.getString(fieldELem, "type");
                //字段类型
                fieldVO.type = this.getTypeByName(fieldVO.typeName);
                //字段长度
                fieldVO.length = TKMapHelper.getNumber(fieldELem, "length");
                //字段描述
                fieldVO.desc = TKMapHelper.getString(fieldELem, "description");
                fieldVOSnMap.set(fieldVO.serno, fieldVO);
                fieldVOKeyMap.set(fieldVO.name, fieldVO);
              })
            }
          } else if (childElemTag == "function") {
            let functionElem = childElem;
            let functionVO: TKQuoteFunctionVO = new TKQuoteFunctionVO();
            //功能号的模式
            functionVO.mode = TKMapHelper.getNumber(functionElem, "mode");
            //二进制功能号
            functionVO.byteFuncNo = TKMapHelper.getString(functionElem, "byteFuncNo");
            //JSON功能号
            functionVO.jsonFuncNo = TKMapHelper.getString(functionElem, "jsonFuncNo");
            //重复行数
            functionVO.repeatRowNum = TKMapHelper.getNumber(functionElem, "repeatRowNum", 1);
            functionVO.repeatRowNum = functionVO.repeatRowNum > 0 ? functionVO.repeatRowNum : 1;
            //通知名称
            functionVO.notiName = TKMapHelper.getString(functionElem, "notiName");
            //是否指数
            functionVO.isStockIndex = TKMapHelper.getString(functionElem, "isStockIndex") == "true";

            let funcChildElems: Array<Map<string, Object>> | undefined =
              TKMapHelper.getObject(functionElem, "children");
            if (funcChildElems) {
              funcChildElems.forEach(funcChildElem => {
                let funcChildElemTag: string = TKMapHelper.getString(funcChildElem, "$tag");
                if (funcChildElemTag == "inputs") {
                  //解析function里面的入参对象
                  let inputsElem = funcChildElem;
                  let inputElems: Array<Map<string, Object>> | undefined =
                    TKMapHelper.getObject(inputsElem, "children");
                  if (inputElems) {
                    let inputVOs: Array<TKQuoteInputVO> = new Array<TKQuoteInputVO>();
                    functionVO.inputs = inputVOs;
                    inputElems.forEach(inputElem => {
                      let inputVO: TKQuoteInputVO = new TKQuoteInputVO();
                      //二进制字段名
                      inputVO.byteName = TKMapHelper.getString(inputElem, "byteName");
                      //JSON字段名称
                      inputVO.jsonName = TKMapHelper.getString(inputElem, "jsonName").toLowerCase();
                      //JSON字段别名
                      inputVO.aliasName = TKMapHelper.getString(inputElem, "aliasName", inputVO.jsonName).toLowerCase();
                      //字段类型名称
                      inputVO.typeName = TKMapHelper.getString(inputElem, "type");
                      //字段类型
                      inputVO.type = this.getTypeByName(inputVO.typeName);
                      //字段长度
                      inputVO.length = TKMapHelper.getNumber(inputElem, "length");
                      //字段默认值
                      inputVO.defaultValue = TKMapHelper.getString(inputElem, "defaultValue");
                      //是否排除空字段
                      inputVO.excludedNull = TKMapHelper.getString(inputElem, "excludedNull") == "1";
                      inputVOs.push(inputVO);
                    });
                  }
                } else if (funcChildElemTag == "outsets") {
                  //解析function里面的出参设置对象
                  let outsetsElem = funcChildElem;
                  let outsetElems: Array<Map<string, Object>> | undefined =
                    TKMapHelper.getObject(outsetsElem, "children");
                  if (outsetElems) {
                    let outesetVOs: Array<TKQuoteOutsetVO> = new Array<TKQuoteOutsetVO>();
                    functionVO.outsets = outesetVOs;
                    outsetElems.forEach(outsetElem => {
                      let outsetVO: TKQuoteOutsetVO = new TKQuoteOutsetVO();
                      outsetVO.name = TKMapHelper.getString(outsetElem, "name");
                      outsetVO.typeName = TKMapHelper.getString(outsetElem, "type");
                      outsetVO.type = this.getTypeByName(outsetVO.typeName);
                      outsetVO.length = TKMapHelper.getNumber(outsetElem, "length");
                      outesetVOs.push(outsetVO);
                    });
                  }
                } else if (funcChildElemTag == "outputs") {
                  //解析function里面的出参对象
                  let outputsElem = funcChildElem;
                  let outputElems: Array<Map<string, Object>> | undefined =
                    TKMapHelper.getObject(outputsElem, "children");
                  if (outputElems) {
                    let outputVOs: Array<TKQuoteOutputVO> = new Array<TKQuoteOutputVO>();
                    functionVO.outputs = outputVOs;
                    outputElems.forEach(outputElem => {
                      let funcOutputElemTag: string = TKMapHelper.getString(outputElem, "$tag");
                      if (funcOutputElemTag == "output") {
                        let outputVO: TKQuoteOutputVO = new TKQuoteOutputVO();
                        //二进制字段名
                        outputVO.byteName = TKMapHelper.getString(outputElem, "byteName");
                        //JSON字段名称
                        outputVO.jsonName = TKMapHelper.getString(outputElem, "jsonName");
                        //JSON字段别名
                        outputVO.aliasName = TKMapHelper.getString(outputElem, "aliasName", outputVO.jsonName);
                        //字段类型名称
                        outputVO.typeName = TKMapHelper.getString(outputElem, "type");
                        //字段类型
                        outputVO.type = this.getTypeByName(outputVO.typeName);
                        //字段长度
                        outputVO.length = TKMapHelper.getNumber(outputElem, "length");
                        outputVOs.push(outputVO);
                      }
                      if (funcOutputElemTag == "outputs") {
                        //解析function里面的二维出参对象
                        let extoutputsElem = outputElem;
                        let extoutputElems: Array<Map<string, Object>> | undefined =
                          TKMapHelper.getObject(extoutputsElem, "children");
                        if (extoutputElems) {
                          let extoutputVOs: Array<TKQuoteOutputVO> = new Array<TKQuoteOutputVO>();
                          functionVO.extoutputs = extoutputVOs;
                          extoutputElems.forEach(extoutputElem => {
                            let extoutputVO: TKQuoteOutputVO = new TKQuoteOutputVO();
                            //二进制字段名
                            extoutputVO.byteName = TKMapHelper.getString(extoutputElem, "byteName");
                            //JSON字段名称
                            extoutputVO.jsonName = TKMapHelper.getString(extoutputElem, "jsonName");
                            //JSON字段别名
                            extoutputVO.aliasName =
                              TKMapHelper.getString(extoutputElem, "aliasName", extoutputVO.jsonName);
                            //字段类型名称
                            extoutputVO.typeName = TKMapHelper.getString(extoutputElem, "type");
                            //字段类型
                            extoutputVO.type = this.getTypeByName(extoutputVO.typeName);
                            //字段长度
                            extoutputVO.length = TKMapHelper.getNumber(extoutputElem, "length");
                            extoutputVOs.push(extoutputVO);
                          });
                        }
                      }
                    });
                  }
                }
              });
            }
            this.jsonFunctionMap.set(functionVO.jsonFuncNo, functionVO);
            this.byteFunctionMap.set(functionVO.byteFuncNo, functionVO);
          }
        });
      }
    }
  }

  /**
   *  根据json功能号获取函数对象
   *
   * @param jsonFuncNo
   *
   * @return
   */
  public getFunctionByJsonFuncNo(jsonFuncNo: string): TKQuoteFunctionVO | undefined {
    return this.jsonFunctionMap.get(jsonFuncNo)
  }

  /**
   *  根据2进制功能号获取函数对象
   *
   * @param byteFuncNo
   *
   * @return
   */
  public getFunctionByByteFuncNo(byteFuncNo: string): TKQuoteFunctionVO | undefined {
    return this.byteFunctionMap.get(byteFuncNo);
  }

  /**
   *  根据序号获取返回字段对象,默认类型是field
   *
   * @param serno 序号
   * @param name  类型 比如field,sfield,bfield,hfield
   *
   * @return
   */
  public getFieldBySerno(serno: number, name: string = "field"): TKQuoteFieldVO | undefined {
    if (this.fieldSnMap) {
      if (TKStringHelper.isBlank(name)) {
        name = "field";
      }
      let fieldVOSnMap: Map<number, TKQuoteFieldVO> | undefined = this.fieldSnMap.get(name);
      if (fieldVOSnMap) {
        return fieldVOSnMap.get(serno);
      }
    }
    return undefined;
  }

  /**
   *  根据序号获取返回字段对象
   *
   * @param key   field的name关键字
   * @param name  类型 比如field,sfield,bfield,hfield
   *
   * @return
   */
  public getFieldByNameKey(key: string, name: string = "field"): TKQuoteFieldVO | undefined {
    if (this.fieldKeyMap) {
      if (TKStringHelper.isBlank(name)) {
        name = "field";
      }
      let fieldVOKeyMap: Map<string, TKQuoteFieldVO> | undefined = this.fieldKeyMap.get(key);
      if (fieldVOKeyMap) {
        return fieldVOKeyMap.get(key);
      }
    }
    return undefined;
  }

  /**
   *  根据名称获取类型
   *
   * @param typeName 名称
   *
   * @return
   */
  private getTypeByName(typeName: string): TKFunctionFieldType {
    //整形
    if (typeName == "int") {
      return TKFunctionFieldType.Int;
    }
    //无符号整形
    if (typeName == "uint") {
      return TKFunctionFieldType.UInt;
    }
    // 短整形
    if (typeName == "short") {
      return TKFunctionFieldType.Short;
    }
    //无符号短整形
    if (typeName == "ushort") {
      return TKFunctionFieldType.UShort;
    }
    //长整形
    if (typeName == "long") {
      return TKFunctionFieldType.Long;
    }
    //无符号长整形
    if (typeName == "ulong") {
      return TKFunctionFieldType.ULong;
    }
    //长长整形
    if (typeName == "longlong") {
      return TKFunctionFieldType.LongLong;
    }
    //无符号长长整形
    if (typeName == "ulonglong") {
      return TKFunctionFieldType.ULongLong;
    }
    //浮点型
    if (typeName == "float") {
      return TKFunctionFieldType.Float;
    }
    //双精度浮点型
    if (typeName == "double") {
      return TKFunctionFieldType.Double;
    }
    //单个字节
    if (typeName == "byte") {
      return TKFunctionFieldType.Byte;
    }
    //字符串
    if (typeName == "char") {
      return TKFunctionFieldType.Char;
    }
    //布尔类型
    if (typeName == "bool") {
      return TKFunctionFieldType.Bool;
    }
    //输入股票类别
    if (typeName == "type") {
      return TKFunctionFieldType.Type;
    }
    //订阅推送功能类别
    if (typeName == "ptype") {
      return TKFunctionFieldType.PType;
    }
    //输入结果字段
    if (this.fieldSnMap.get(typeName) !== undefined) {
      return TKFunctionFieldType.Field;
    }
    //股票字段
    if (typeName == "stock") {
      return TKFunctionFieldType.Stock;
    }
    //股票集合字段
    if (typeName == "stocks") {
      return TKFunctionFieldType.Stocks;
    }
    //K线类型
    if (typeName == "kline") {
      return TKFunctionFieldType.Kline;
    }
    //CFLAG
    if (typeName == "cflag") {
      return TKFunctionFieldType.CFlag;
    }
    //返回结果的数目
    if (typeName == "count") {
      return TKFunctionFieldType.Count;
    }
    //时间类型
    if (typeName == "time") {
      return TKFunctionFieldType.Time;
    }
    //动态二维数据结果Key
    if (typeName == "fieldData") {
      return TKFunctionFieldType.FieldData;
    }
    return TKFunctionFieldType.Float;
  }
}