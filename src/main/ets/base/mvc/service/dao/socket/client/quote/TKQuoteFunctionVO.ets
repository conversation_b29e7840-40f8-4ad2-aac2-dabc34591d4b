/**
 类型定义
 */
import { TKQuoteInputVO } from './TKQuoteInputVO';
import { TKQuoteOutputVO } from './TKQuoteOutputVO';
import { TKQuoteOutsetVO } from './TKQuoteOutsetVO';

export enum TKFunctionFieldType {
  /**
   *  整形
   */
  Int,
  /**
   *  无符号整形
   */
  UInt,
  /**
   *  短整形
   */
  Short,
  /**
   *  无符号短整形
   */
  UShort,
  /**
   *  长整形
   */
  Long,
  /**
   *  无符号长整形
   */
  ULong,
  /**
   *  长长整型
   */
  LongLong,
  /**
   *  无符号长长整型
   */
  ULongLong,
  /**
   *  浮点型
   */
  Float,
  /**
   *  双精度浮点型
   */
  Double,
  /**
   *  字节
   */
  Byte,
  /**
   *  字符串
   */
  Char,
  /**
   *  布尔类型
   */
  Bool,
  /**
   *  输入股票类别
   */
  Type,
  /**
   *  输入结果字段
   */
  Field,
  /**
   *  订阅推送类别
   */
  PType,
  /**
   *  股票字段
   */
  Stock,
  /**
   *股票字段集合
   */
  Stocks,
  /**
   *K线类型
   */
  Kline,
  /**
   * 标示
   */
  CFlag,
  /**
   *返回结果的数目
   */
  Count,
  /**
   *  时间类型
   */
  Time,
  /**
   *  动态二维数据结果Key
   */
  FieldData
}

/**
 *  函数对象
 */
export class TKQuoteFunctionVO {
  /**
   *  模式(0:简单格式-值数组，1:标准格式-数据字典)
   */
  public mode: number = 0;
  /**
   *  2进制功能号
   */
  public byteFuncNo: string = "";
  /**
   *  json功能号
   */
  public jsonFuncNo: string = "";
  /**
   *  每行数据重复的次数
   */
  public repeatRowNum: number = 1;
  /**
   *  通知名称
   */
  public notiName: string = "";
  /**
   *  是否指数的接口，对于港股来说指数的股票代码是11位，普通是5位
   */
  public isStockIndex: boolean = false;
  /**
   *  输入对象
   */
  public inputs: Array<TKQuoteInputVO> = new Array<TKQuoteInputVO>();
  /**
   *  输出设置对象
   */
  public outsets: Array<TKQuoteOutsetVO> = new Array<TKQuoteOutsetVO>();
  /**
   *  输出对象
   */
  public outputs: Array<TKQuoteOutputVO> = new Array<TKQuoteOutputVO>();
  /**
   *  输出二维对象
   */
  public extoutputs: Array<TKQuoteOutputVO> = new Array<TKQuoteOutputVO>();

  public getFunctionMode(functionMode: number | undefined): number {
    if (functionMode === undefined) {
      return this.mode;
    }
    return functionMode as number;
  }
}
