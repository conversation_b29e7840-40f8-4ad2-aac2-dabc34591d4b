/**
 *  网络地址
 */
export class TKNetAddress {
  /**
   *  是否参与站点分配
   */
  public isJoinConnectSite: boolean = true;
  /**
   *  是否国密站点
   */
  public isGMSite: boolean = false;
  /**
   *  分组ID
   */
  public groupId: string = "";
  /**
   *  网关名称ID
   */
  public gateWayName: string = "";
  /**
   是否主域名
   */
  public isDomain: boolean = false;
  /**
   是否IPV6
   */
  public isIPV6: boolean = false;
  /**
   *  ip地址
   */
  public ip: string = "";
  /**
   *  端口
   */
  public port: number = 0;
  /**
   地址描述
   */
  public desc: string = "";
  /**
   *  是否存活
   */
  public isAlive: boolean = false;
  /**
   *  是否是最优测速模式
   */
  public isNeedTestSpeed: boolean = false;
  /**
   *  测速的综合得分,分值越低表示越快
   */
  public speed: number = 0;
  /**
   *  开始测试时间
   */
  public btime: number = 0;
  /**
   *  测试花费时间
   */
  public time: number = 0;
  /**
   *  服务器负载得分
   */
  public serverScore: number = 0;
}