import { TKCacheManager } from '../../../../../util/cache/TKCacheManager';
import { TKObjectHelper } from '../../../../../util/data/TKObjectHelper';
import { TKMapHelper } from '../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../util/string/TKStringHelper';
import { TKTimer } from '../../../../../util/timer/TKTimer';
import { TKNotificationCenter } from '../../../../notification/TKNotificationCenter';
import { TKDaoType, TKReqParamVO } from '../../../model/TKReqParamVO';
import { TKDefaultHttpHandler } from '../../handler/TKDefaultHttpHandler';
import { TKBaseDao } from '../TKBaseDao';
import { TKBusClientManager, TKBusClientManagerNotificationDelegate } from './client/common/TKBusClientManager';
import { TKComBusClient } from './client/common/TKComBusClient';
import { TKGatewayManager } from './client/gateway/TKGatewayManager';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKDataVO } from './client/common/TKDataVO';
import { TKLog } from '../../../../../util/logger/TKLog';
import { TKNotification } from '../../../../notification/TKNotification';
import { TKSystemHelper } from '../../../../../util/system/TKSystemHelper';
import { TKResultErrorType, TKResultVO } from '../../../model/TKResultVO';
import { TKProcessDataDelegate } from '../../protocol/TKProcessDataDelegate';
import { TKCommonService } from '../../TKCommonService';
import { TKDataHelper } from '../../../../../util/data/TKDataHelper';
import { TKServer } from './client/gateway/TKServer';

/**
 *  socket协议

 =============请求错误定义======================
 -990 服务器连接认证失败
 -991 服务器获取连接异常
 -992 服务器建立连接中断
 -993 服务器建立连接超时
 -994 服务器建立连接拒绝
 -995 服务器建立连接网络异常
 -996 客户端数据请求超时
 -999 服务器返回的数据格式错误
 -900 请求功能号不能为空

 =============行情错误定义======================
 0   正常
 -1   找不到股票代码
 -2   包体长度有误
 -100 服务器正在初始化
 -101 行情服务器返回数据包与客户端配置文件不匹配

 */
export class TKBusDao extends TKBaseDao implements TKBusClientManagerNotificationDelegate {
  /**
   *  请求对象集合
   */
  private requestMap: Map<string, Object> = new Map<string, Object>();
  /**
   *  需要重发一次的请求集合
   */
  private needReSendReqMap: Map<string, Array<TKReqParamVO>> = new Map<string, Array<TKReqParamVO>>();
  /**
   *  默认的Http解析器
   */
  private defaultHandler: TKProcessDataDelegate = new TKDefaultHttpHandler();
  /**
   *  类名
   */
  private className: string = "";
  /**
   *  定时任务
   */
  private timer: TKTimer | undefined = undefined;
  /**
   * 实例对象
   */
  private static instance: TKBusDao | undefined = undefined;

  public constructor() {
    super();
    this.className = this.constructor.name;
    //发送心跳包
    this.timer = new TKTimer(1000, this, this.checkTimeout);
    let notes: Array<string> = this.listNotification();
    if (notes && notes.length > 0) {
      for (let noteName of notes) {
        TKNotificationCenter.defaultCenter.addObserver(this, this.handleNotification, noteName);
      }
    }
  }

  public static shareInstance(): TKBusDao {
    if (!TKBusDao.instance) {
      TKBusDao.instance = new TKBusDao();
    }
    return TKBusDao.instance;
  }

  /**
   *  监听的通知
   * @return
   */
  private listNotification(): Array<string> {
    let notes: Array<string> = new Array<string>();
    notes.push(TKBusClientManager.NOTE_BUSCLIENT_LOGOUT);
    return notes;
  }

  /**
   *  处理请求
   *
   * @param reqParamVO 请求对象
   */
  public invoke(reqParamVO: TKReqParamVO) {
    super.invoke(reqParamVO);
    this.invokeAsyncSocket(reqParamVO);
    this.timer!.start();
  }

  /**
   *  异步Post请求
   *
   * @param reqParamVo 请求对象
   */
  private invokeAsyncSocket(reqParamVO: TKReqParamVO) {
    let busClient: TKComBusClient | undefined = undefined;
    try {
      if (!this.filterReqParamVO(reqParamVO)) {
        //流水号
        let flowNo: string = reqParamVO.flowNo;
        //错误信息
        let errorInfo: string = "请求参数功能号不能为空！";
        //错误对象
        let userInfo: Record<string, Object> = {
          "flowNo": flowNo,
          "errorType": "1",
          "errorNo": "-900",
          "errorInfo": errorInfo,
          "reqParamVO": reqParamVO
        };
        this.onNotifyBusClientNotification(busClient!, TKBusClientManager.NOTE_BUSCLIENT_ERROR, userInfo);
        return;
      }
      //开始发送请求
      if (reqParamVO.serviceDelegate) {
        reqParamVO.serviceDelegate.onOpen(reqParamVO);
      }
      //请求参数
      let reqParam: Record<string, Object | undefined> = reqParamVO.reqParam;
      //流水号
      let flowNo: string = reqParamVO.flowNo;
      //请求服务器的名称
      let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(reqParamVO.busServerId);
      if (server === undefined) {
        let networkException: BusinessError = {
          message: `获取连接失败，失败原因：服务器【${reqParamVO.busServerId}】不存在!`
        } as BusinessError;
        this.handleException(networkException, busClient!, reqParamVO);
        return;
      }
      if (reqParamVO.timeOut == 0) {
        reqParamVO.timeOut = server!.recTimeout;
      }
      if (reqParamVO.timeOut <= 0) {
        reqParamVO.timeOut = 30;
      }
      let daoType: TKDaoType = reqParamVO.protocol;
      if (daoType == TKDaoType.HttpToSocket) {
        let headerFieldDic: Record<string, Object | undefined> = reqParamVO.headerFieldDic ?? {};
        let httpDomain: string = TKMapHelper.getString(headerFieldDic, "httpDomain");
        let cookie: string = TKMapHelper.getString(headerFieldDic, "Cookie");
        if (TKStringHelper.isBlank(cookie)) {
          cookie = TKCacheManager.shareInstance().getMemCacheData(`cookie:${httpDomain}`) as string;
        }
        if (TKStringHelper.isNotBlank(cookie)) {
          headerFieldDic["Cookie"] = cookie;
        }
        reqParamVO.headerFieldDic = headerFieldDic;
        daoType = TKDaoType.Socket;
        let busFuncNo: string = reqParamVO.busFuncNo;
        let url: string = reqParamVO.url;
        let method: string = reqParamVO.httpMethod;
        let reqHeaderJsonStr: string = TKObjectHelper.toJsonStr(headerFieldDic);
        let reqParamJsonStr: string = TKObjectHelper.toJsonStr(reqParam);
        reqParam = {};
        if (TKStringHelper.isNotBlank(busFuncNo)) {
          reqParam["funcNo"] = busFuncNo;
        }
        if (TKStringHelper.isNotBlank(url)) {
          reqParam["url"] = url;
        }
        if (TKStringHelper.isNotBlank(method)) {
          reqParam["method"] = method;
        }
        if (TKStringHelper.isNotBlank(reqHeaderJsonStr)) {
          reqParam["header"] = reqHeaderJsonStr;
        }
        if (TKStringHelper.isNotBlank(reqParamJsonStr)) {
          reqParam["param"] = reqParamJsonStr;
        }
      }
      TKBusClientManager.shareInstance().delegate = this;
      busClient = TKBusClientManager.shareInstance()
        .getTKBusClient(server!.gateWayName, TKDataHelper.getBoolean(reqParamVO.daoMode)) as TKComBusClient;
      reqParamVO.busClientUUID = busClient.uuid;
      busClient.charSet = Number(reqParamVO.charEncoding);
      //添加到集合
      if (busClient.isLongConnect) {
        this.requestMap.set(flowNo, reqParamVO);
      } else {
        busClient.userInfo = reqParamVO;
        this.requestMap.set(flowNo, busClient);
      }
      if (!TKBusClientManager.shareInstance().isNetWorkOK) {
        let networkException: BusinessError = {
          message: `获取连接失败，失败原因：服务器【${server!.gateWayName}】网络连接异常!`
        } as BusinessError;
        this.handleException(networkException, busClient, reqParamVO);
        return;
      }
      if (reqParam) {
        Object.entries(reqParam).forEach((e, i) => {
          let key: string = e[0];
          reqParam[key] = TKMapHelper.getString(reqParam, key);
        });
      }
      let dataVO: TKDataVO = busClient.getSendData(flowNo, reqParam, {
        "channelId": reqParamVO.channelId,
        "companyId": reqParamVO.companyId,
        "systemId": reqParamVO.systemId,
        "dataType": reqParamVO.dataType,
        "busServerId": reqParamVO.busServerId,
        "functionMode": reqParamVO.quoteFunctionMode
      })
      busClient.sendData(dataVO);
    } catch (error) {
      this.handleException(error, busClient!, reqParamVO);
    }
  }

  private handleException(exception: Error, busClient: TKComBusClient, reqParamVO: TKReqParamVO) {
    TKLog.error(`${this.className}请求失败!${exception.message}`);
    //流水号
    let flowNo: string = reqParamVO.flowNo;
    //错误信息
    let errorInfo: string = exception.message ?? "";
    //错误对象
    let userInfo: Record<string, Object> = {
      "flowNo": flowNo,
      "errorType": "1",
      "errorInfo": errorInfo,
      "reqParamVO": reqParamVO
    };
    if (!TKBusClientManager.shareInstance().isNetWorkOK) {
      userInfo["errorNo"] = "-995";
    } else if (errorInfo.toLowerCase().indexOf("timed out") > 0) {
      userInfo["errorNo"] = "-993";
    } else if (errorInfo.toLowerCase().indexOf("refused") > 0) {
      userInfo["errorNo"] = "-994";
    } else {
      userInfo["errorNo"] = "-991";
    }
    this.onNotifyBusClientDisConnectNotification(busClient, userInfo);
  }

  /**
   *  过滤非法请求
   * @param reqParamVO
   * @return
   */
  private filterReqParamVO(reqParamVO: TKReqParamVO): boolean {
    if (!reqParamVO || TKStringHelper.isBlank(reqParamVO.busFuncNo)) {
      return false;
    }
    return true;
  }

  /****************************代理实现类********************************/


  /**
   * @param note
   */
  private handleNotification(note: TKNotification) {
    let noteName: string = note.name;
    let userInfo: Record<string, Object> = note.userInfo ?? {};
    let obj: Object | undefined = note.obj;
    let busClient: TKComBusClient | undefined = undefined;
    if (obj) {
      if (obj instanceof TKComBusClient) {
        busClient = obj as TKComBusClient;
      } else {
        busClient = TKBusClientManager.shareInstance().getCacheTKBusLongClient(obj.toString());
      }
    }
    this.onNotifyBusClientNotification(busClient!, noteName, userInfo);
  }

  /**
   *  BusClient通知处理代理协议
   */
  public onNotifyBusClientNotification(busClient: TKComBusClient, noteName: string, userInfo?: Record<string, Object>) {
    if (noteName == TKBusClientManager.NOTE_BUSCLIENT_CONNECT) {
      //连接成功
      this.onNotifyBusClientConnectNotification(busClient);
    } else if (noteName == TKBusClientManager.NOTE_BUSCLIENT_DISCONNECT) {
      //连接断开
      this.onNotifyBusClientDisConnectNotification(busClient, userInfo ?? {});
    } else if (noteName == TKBusClientManager.NOTE_BUSCLIENT_ERROR) {
      //连接错误
      this.onNotifyBusClientErrorNotification(busClient, userInfo ?? {});
    } else if (noteName == TKBusClientManager.NOTE_BUSCLIENT_RESULT) {
      //返回结果
      this.onNotifyBusClientResultNotification(busClient, userInfo ?? {});
    } else if (noteName == TKBusClientManager.NOTE_BUSCLIENT_LOGOUT) {
      this.onNotifyBusClientLogoutNotification(busClient, userInfo ?? {});
    }
  }

  /**
   *  处理连接成功通知
   */
  private onNotifyBusClientConnectNotification(busClient: TKComBusClient) {
    this.reSendLoginRequest(busClient);
    TKNotificationCenter.defaultCenter.postNotificationName(TKBusClientManager.NOTE_BUSCLIENT_CONNECT, busClient);
  }

  /**
   *  处理连接异常中断通知
   */
  private onNotifyBusClientDisConnectNotification(busClient: TKComBusClient, userInfo: Record<string, Object>) {
    this.onNotifyBusClientErrorNotification(busClient, userInfo);
    TKNotificationCenter.defaultCenter.postNotificationName(TKBusClientManager.NOTE_BUSCLIENT_DISCONNECT, busClient,
      userInfo);
  }

  /**
   *  处理请求错误通知
   */
  private onNotifyBusClientErrorNotification(busClient: TKComBusClient, userInfo: Record<string, Object>) {
    //流水号
    let flowNo: string = TKMapHelper.getString(userInfo, "flowNo");
    //错误类型
    let errorType: string = TKMapHelper.getString(userInfo, "errorType");
    //错误号
    let errorNo: number = TKMapHelper.getNumber(userInfo, "errorNo");
    //错误信息
    let errorInfo: string = TKMapHelper.getString(userInfo, "errorInfo");
    let error_info: string = "";
    if (errorNo == -995) {
      error_info = TKSystemHelper.getConfig("networkErrorInfo.-100000"); //"亲，网络很不给力哦~";
    } else if (errorNo == -991) {
      error_info = TKSystemHelper.getConfig("networkErrorInfo.-100001"); //"亲，服务器不能正常访问~"
    } else if (errorNo == -993 || errorNo == -996) {
      error_info = TKSystemHelper.getConfig("networkErrorInfo.-100002"); //"亲，请求超时，请稍后重试~"
    } else if (errorNo == -992) {
      error_info = TKSystemHelper.getConfig("networkErrorInfo.-100003"); //"亲，请求已中断，请稍后重试~"
    } else if (errorNo == -994) {
      error_info = TKSystemHelper.getConfig("networkErrorInfo.-100004"); //"亲，请求地址不能正常访问~"
    }
    if (TKStringHelper.isNotBlank(error_info)) {
      errorInfo = error_info;
    }
    let reqParamVO: TKReqParamVO = TKMapHelper.getObject(userInfo, "reqParamVO") as TKReqParamVO;
    if (TKStringHelper.isNotBlank(flowNo)) {
      let requestObj: Object | undefined = TKMapHelper.getObject(this.requestMap, flowNo);
      if (requestObj) {
        if (requestObj instanceof TKReqParamVO) {
          reqParamVO = requestObj as TKReqParamVO;
        } else {
          reqParamVO = (requestObj as TKComBusClient).userInfo as TKReqParamVO;
        }
      }
      if (reqParamVO) {
        if (reqParamVO.serviceDelegate) {
          let resultVO: TKResultVO = new TKResultVO();
          resultVO.errorType = Number(errorType);
          resultVO.errorNo = errorNo;
          if (TKSystemHelper.getConfig("networkRequest.isShowErrorPrefix") == "0") {
            resultVO.errorInfo = errorInfo;
          } else {
            if (busClient) {
              resultVO.errorInfo = `[${busClient.serverName}]${errorInfo}`;
            } else {
              resultVO.errorInfo = errorInfo;
            }
          }
          resultVO.reqParamVO = reqParamVO;
          TKLog.debug(`[flowNo:${flowNo}]请求结果---->${TKObjectHelper.toJsonStr(resultVO.errorInfo)}`);
          reqParamVO.serviceDelegate.onFault(resultVO);
        }
        //清理对象
        this.clearRequest(flowNo);
        //请求结束
        this.close(reqParamVO);
      }
    } else {
      this.clearServerAllRequestAndReturnErrorNo(errorNo, errorInfo, busClient.uuid);
    }
  }

  /**
   *  处理请求结果数据
   */
  private onNotifyBusClientResultNotification(busClient: TKComBusClient, userInfo: Record<string, Object>) {
    //流水号
    let flowNo: string = TKMapHelper.getString(userInfo, "flowNo");
    //结果
    let result: Record<string, Object> | undefined = TKMapHelper.getObject(userInfo, "result");
    if (!result || Object.entries(result).length == 0) {
      result = {
        "error_type": "1",
        "error_no": "-999",
        "error_info": "服务器返回的数据格式错误！"
      };
    }
    //数据推送
    if (flowNo == "-1") {
      if (result) {
        TKLog.debug(`[flowNo:${flowNo}]返回结果---->${TKObjectHelper.toJsonStr(result)}`);
        TKNotificationCenter.defaultCenter.postNotificationName(TKBusClientManager.NOTE_BUSCLIENT_PUSH_DATA, result);
      }
    } else {
      let reqParamVO: TKReqParamVO | undefined = undefined;
      let requestObj: Object | undefined = TKMapHelper.getObject(this.requestMap, flowNo);
      if (requestObj) {
        if (requestObj instanceof TKReqParamVO) {
          reqParamVO = requestObj as TKReqParamVO;
        } else {
          reqParamVO = (requestObj as TKComBusClient).userInfo as TKReqParamVO;
        }
      }
      if (reqParamVO) {
        TKLog.debug(`[flowNo:${flowNo}]返回结果---->${TKObjectHelper.toJsonStr(result)}`);
        let resultVO: TKResultVO = this.defaultHandler.processResultData(result);
        if (resultVO) {
          resultVO.reqParamVO = reqParamVO;
        }
        if (reqParamVO.protocol == TKDaoType.HttpToSocket) {
          let result: Object = resultVO.results;
          if (result && Array.isArray(result) && (result as Array<Object>).length > 0) {
            result = (result as Array<Object>)[0];
          }
          if (result) {
            resultVO.isStandardResult = false;
            let headerJsonStr: string = TKMapHelper.getString(result as Record<string, Object>, "header");
            let dataJsonStr: string = TKMapHelper.getString(result as Record<string, Object>, "data");
            if (TKStringHelper.isNotBlank(headerJsonStr)) {
              let respHeaderFieldDic: Record<string, Object> = JSON.parse(headerJsonStr);
              if (respHeaderFieldDic) {
                let setCookie: string = TKMapHelper.getString(respHeaderFieldDic, "set-cookie");
                if (TKStringHelper.isBlank(setCookie)) {
                  setCookie = TKMapHelper.getString(respHeaderFieldDic, "Set-Cookie");
                }
                respHeaderFieldDic["set-cookie"] = setCookie;
                respHeaderFieldDic["Set-Cookie"] = setCookie;
                resultVO.respHeaderFieldDic = respHeaderFieldDic;
                if (reqParamVO.headerFieldDic) {
                  if (TKStringHelper.isNotBlank(setCookie)) {
                    let httpDomain: string = TKMapHelper.getString(reqParamVO.headerFieldDic, "httpDomain");
                    TKCacheManager.shareInstance().saveMemeCacheData(`cookie:${httpDomain}`, setCookie);
                  }
                }
              }
            }
            if (TKStringHelper.isNotBlank(dataJsonStr)) {
              resultVO.results = JSON.parse(dataJsonStr);
            }
          }
        }
        if (reqParamVO.isLoginReq && busClient && busClient.isLongConnect) {
          busClient.isLogining = false;
          if (resultVO.errorNo == 0) {
            let loginReqParamVO: TKReqParamVO = TKObjectHelper.clone(reqParamVO);
            busClient.userInfo = loginReqParamVO;
            TKGatewayManager.shareInstance().getServer(busClient.serverName)!.userInfo = loginReqParamVO;
            //登录成功群发通知，reconnectLoginFlag代表是否断线重连引发
            let loginUserInfo: Record<string, Object | undefined> = resultVO.toJson();
            if (reqParamVO.reqParam) {
              let reconnectLoginFlag: string = TKMapHelper.getString(reqParamVO.reqParam, "reconnectLoginFlag");
              loginUserInfo["reconnectLoginFlag"] = reconnectLoginFlag;
            }
            TKNotificationCenter.defaultCenter.postNotificationName(TKBusClientManager.NOTE_BUSCLIENT_LOGIN,
              loginUserInfo);
          }
          this.reSendCacheNeedLoginRequest(busClient.serverName, resultVO);
        }
        if (busClient && busClient.isLongConnect) {
          //未登录
          if (resultVO.errorNo == -4003 && busClient.userInfo) {
            let reqAry: Array<TKReqParamVO> | undefined =
              TKMapHelper.getObject(this.needReSendReqMap, busClient.serverName);
            if (!reqAry) {
              reqAry = new Array<TKReqParamVO>();
              this.needReSendReqMap.set(busClient.serverName, reqAry);
            }
            reqAry.push(reqParamVO);
            this.reSendLoginRequest(busClient);
            return;
          }
        }
        if (reqParamVO.serviceDelegate) {
          reqParamVO.serviceDelegate.onResult(resultVO);
        }
        //清理对象
        this.clearRequest(flowNo);
        //请求结束
        this.close(reqParamVO);
      }
    }
  }

  /**
   *  处理退出请求通知
   */
  private onNotifyBusClientLogoutNotification(busClient: TKComBusClient, userInfo: Record<string, Object>) {
    if (busClient && busClient.isLongConnect) {
      busClient.userInfo = undefined;
      busClient.isLogining = false;
      TKGatewayManager.shareInstance().getServer(busClient.serverName)!.userInfo = undefined;
      TKBusClientManager.shareInstance().stop(busClient.serverName);
    } else {
      let serverMap: Map<string, TKComBusClient> = TKBusClientManager.shareInstance().getAllCacheTKBusLongClient();
      if (serverMap && serverMap.size > 0) {
        Array.from(serverMap.keys()).forEach((key, i) => {
          let busClient: TKComBusClient = TKMapHelper.getObject(serverMap, key) as TKComBusClient;
          busClient.userInfo = undefined;
          busClient.isLogining = false;
          TKGatewayManager.shareInstance().getServer(busClient.serverName)!.userInfo = undefined;
          TKBusClientManager.shareInstance().stop(busClient.serverName);
        })
      }
    }
  }

  /**
   *  重新登录
   *
   * @param busClient
   */
  private reSendLoginRequest(busClient: TKComBusClient) {
    if (busClient && busClient.isLongConnect && busClient.userInfo && !busClient.isLogining) {
      busClient.isLogining = true;
      let loginReqParamVO: TKReqParamVO = TKObjectHelper.clone(busClient.userInfo) as TKReqParamVO;
      let reqParam: Record<string, Object | undefined> = loginReqParamVO.reqParam;
      reqParam["reconnectLoginFlag"] = "1";
      TKLog.debug("reSendLoginRequest......................................" + TKObjectHelper.toJsonStr(reqParam));
      let flowNo: string = TKCommonService.generateRequestFlowNo();
      loginReqParamVO.flowNo = flowNo;
      loginReqParamVO.beginTime = new Date().getTime(); //时间搓
      loginReqParamVO.reqParam = reqParam;
      this.invokeAsyncSocket(loginReqParamVO);
    }
  }

  /**
   *  重发缓存的需要登录的请求
   */
  private reSendCacheNeedLoginRequest(serverName: string, resultVO: TKResultVO) {
    let reqAry: Array<TKReqParamVO> | undefined = TKMapHelper.getObject(this.needReSendReqMap, serverName);
    TKLog.debug(`reSendCacheNeedLoginRequest......................................${serverName}:${reqAry}:${resultVO.toJsonStr()}`);
    if (reqAry && reqAry.length > 0) {
      let tempReqAry: Array<TKReqParamVO> = new Array<TKReqParamVO>();
      tempReqAry = tempReqAry.concat(reqAry);
      for (let reqParamVO of tempReqAry) {
        if (resultVO.errorNo == 0) {
          let reqParam: Record<string, Object | undefined> = reqParamVO.reqParam ?? {};
          let random: number = Math.random();
          reqParam["_reFlowNo"] = random;
          reqParamVO.reqParam = reqParam;
          this.invokeAsyncSocket(reqParamVO);
        } else {
          resultVO.reqParamVO = reqParamVO;
          if (reqParamVO.serviceDelegate) {
            reqParamVO.serviceDelegate.onResult(resultVO);
          }
          //清理对象
          this.clearRequest(reqParamVO.flowNo);
          //请求结束
          this.close(reqParamVO);
        }
      }
    }
    this.needReSendReqMap.delete(serverName);
  }

  /**
   *  根据流水号清理垃圾缓存需要登录的请求
   *
   * @param flowNo
   */
  private clearCacheNeedLoginRequest(flowNo: string) {
    if (this.needReSendReqMap.size > 0) {
      this.needReSendReqMap.forEach((value, key) => {
        let serverName: string = key;
        let reqAry: Array<TKReqParamVO> = value;
        if (reqAry && reqAry.length > 0) {
          let tempReqAry: Array<TKReqParamVO> = new Array<TKReqParamVO>();
          tempReqAry = tempReqAry.concat(reqAry);
          for (let reqParamVO of tempReqAry) {
            if (reqParamVO.flowNo == flowNo) {
              let index: number = reqAry.indexOf(reqParamVO);
              if (index >= 0) {
                reqAry.splice(index, 1);
              }
            }
          }
        }
      });
    }
  }

  /**
   *  请求关闭
   */
  private close(reqParamVO: TKReqParamVO) {
    if (reqParamVO.serviceDelegate) {
      reqParamVO.serviceDelegate.onClose(reqParamVO);
    }
  }


  /**
   *  清理请求
   *
   * @param flowNo 流水号
   */
  public clearRequest(flowNo: string) {
    if (TKStringHelper.isNotBlank(flowNo) && this.requestMap && this.requestMap.get(flowNo)) {
      let requestObj: Object | undefined = this.requestMap.get(flowNo);
      if (requestObj && requestObj instanceof TKComBusClient) {
        let busClient: TKComBusClient = requestObj as TKComBusClient;
        busClient.disConnect();
        busClient.releaseCTX();
      }
      this.requestMap.delete(flowNo);
    }
  }

  /**
   *  检测超时
   */
  private checkTimeout() {
    if (this.requestMap && this.requestMap.size > 0) {
      let tempRequestKeys: Array<string> = Array.from(this.requestMap.keys());
      for (let flowNo of tempRequestKeys) {
        let requestObj: Object = TKMapHelper.getObject(this.requestMap, flowNo) as Object;
        let reqParamVO: TKReqParamVO | undefined = undefined;
        if (requestObj instanceof TKReqParamVO) {
          reqParamVO = requestObj as TKReqParamVO;
        } else {
          reqParamVO = (requestObj as TKComBusClient).userInfo as TKReqParamVO;
        }
        if (reqParamVO) {
          let beginTime: number = reqParamVO.beginTime;
          let curentTime: number = new Date().getTime();
          if ((curentTime - beginTime) > (reqParamVO.timeOut * 1000)) {
            if (reqParamVO.serviceDelegate) {
              let resultVO: TKResultVO = new TKResultVO();
              resultVO.errorNo = -996;
              let errorInfo: string = TKSystemHelper.getConfig("networkErrorInfo.-100002"); //@"亲，请求超时，请稍后重试~"
              if (TKStringHelper.isBlank(errorInfo)) {
                errorInfo = "亲，请求超时，请稍后重试~";
              }
              resultVO.errorInfo = errorInfo;
              resultVO.reqParamVO = reqParamVO;
              reqParamVO.serviceDelegate.onFault(resultVO);
            }
            //清理对象
            this.clearRequest(reqParamVO.flowNo);
            //请求结束
            this.close(reqParamVO);
            //清除缓存需要重试的请求
            this.clearCacheNeedLoginRequest(reqParamVO.flowNo);
          }
        }
      }
    } else {
      this.timer!.pause();
    }
  }

  /**
   *  请求中断清理所有请求
   * @param error
   */
  private clearServerAllRequestAndReturnErrorNo(errorNo: number, errorInfo: string, busClientUUID: string) {
    if (this.requestMap && this.requestMap.size > 0) {
      let tempRequestKeys: Array<string> = Array.from(this.requestMap.keys());
      for (let flowNo of tempRequestKeys) {
        let requestObj: Object = TKMapHelper.getObject(this.requestMap, flowNo) as Object;
        let reqParamVO: TKReqParamVO | undefined = undefined;
        if (requestObj instanceof TKReqParamVO) {
          reqParamVO = requestObj as TKReqParamVO;
        } else {
          reqParamVO = (requestObj as TKComBusClient).userInfo as TKReqParamVO;
        }
        if (reqParamVO) {
          if (busClientUUID == reqParamVO.busClientUUID) {
            TKLog.debug("clearRequestByFlowNoAndReturnError......................................" + reqParamVO.flowNo);
            if (reqParamVO.serviceDelegate) {
              let resultVO: TKResultVO = new TKResultVO();
              resultVO.errorType = TKResultErrorType.Network;
              resultVO.errorNo = errorNo;
              if (TKStringHelper.isNotBlank(errorInfo)) {
                resultVO.errorInfo = errorInfo;
              }
              resultVO.reqParamVO = reqParamVO;
              reqParamVO.serviceDelegate.onFault(resultVO);
            }
            //清理对象
            this.clearRequest(reqParamVO.flowNo);
            //请求结束
            this.close(reqParamVO);
            //清除缓存需要重试的请求
            this.clearCacheNeedLoginRequest(reqParamVO.flowNo);
          }
        }
      }
    }
  }

  /**
   *  释放内存
   */
  public dealloc() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
    if (this.timer) {
      this.timer.stop();
      this.timer = undefined;
    }
  }
}
