/**
 消息类型
 */
import { TKUUIDHelper } from '../../../../../../../util/crypto/TKUUIDHelper';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKBusClientDelegate, TKBusMsgType } from './TKComBusClient';
import socket from '@ohos.net.socket';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKReadDataView } from './TKReadDataView';
import { TKWriteDataView } from './TKWriteDataView';
import { TKDataHelper } from '../../../../../../../util/data/TKDataHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { buffer } from '@kit.ArkTS';

export enum TKSocketMsgType {
  /**
   *  会话包
   */
  SessionKey = 1,

  /**
   *  连接认证包
   */
  AuthLogin = 2,

  /**
   *  连接认证回报包
   */
  AuthLoginReturn = 3,

  /**
   *  配置请求包
   */
  TestSpeedConfig = 90,

  /**
   *  配置响应包
   */
  TestSpeedConfigReturn = 91,

  /**
   *  测试请求包
   */
  TestSpeed = 98,

  /**
   *  测试响应包
   */
  TestSpeedReturn = 99
}

/**
 * 测速客户端
 */
export class TKComBusTestSpeedClient {
  /**
   * GBK编辑
   */
  protected static readonly CHAR_GBK: string = "gbk";
  /*
 *  包头类型
 */
  private static readonly TAG: string = "TH";
  /*
  *  包头长度
  */
  private static readonly HEADlENGTH: number = 10;
  /**
   *  类名
   */
  protected className: string = "";
  /**
   *  主机UUID
   */
  public readonly uuid: string = TKUUIDHelper.uuid();
  /**
   *  主机名
   */
  public host: string = "";
  /**
   *  端口号
   */
  public port: number = 0;
  /**
   *  最终连接主机IP
   */
  public connectedHost: string = "";
  /**
   *  最终连接主机端口
   */
  public connectedPort: number = 0;
  /**
   *  网络通信代理协议
   */
  public delegate: TKBusClientDelegate | undefined = undefined;
  /**
   *  用户信息
   */
  public userInfo: Object = new Object();
  /**
   *  服务器名称
   */
  public serverName: string = "";
  /**
   * socket的当前客户端的地址
   */
  public clientAddress: string = "";
  /**
   *  是否初始化
   */
  public isInitCTX: boolean = false;
  /**
   *  是否连接成功
   */
  public isConnect: boolean = false;
  /**
   *  是否正在连接
   */
  public isConnecting: boolean = false;
  /**
   *  socket连接
   */
  private socket: socket.TCPSocket | undefined = undefined;
  /**
   *  数据读取缓冲区
   */
  private readBufferData: Array<number> = new Array<number>();

  /**************************生命周期的方法*******************************/

  public constructor() {
    this.className = this.constructor.name;
  }

  /**
   *
   *  初始化上下文
   */
  public initCTX() {
    this.host = "";
    this.port = 0;
    this.connectedHost = "";
    this.connectedPort = 0;
    this.clientAddress = "";
    this.isConnect = false;
    this.isConnecting = true;
    this.isInitCTX = true;

  }

  /**
   *  建立连接，连接成功以后进行服务器认证
   *
   * @param host 服务器地址
   * @param port 服务器端口
   */
  public connect(host: string, port: number): void {
    TKLog.info(`${this.className}(${host}:${port})---->开始连接`);
    this.host = host;
    this.port = port;
    this.connectedHost = host;
    this.connectedPort = port;
    this.socket = socket.constructTCPSocketInstance();
    //绑定socket事件监听
    this.bindSocketEventListener();
    //建立socket连接
    this.socket?.connect({
      address: {
        address: this.host,
        port: this.port
      },
      timeout: 3000
    } as socket.TCPConnectOptions).then(() => {
    }).catch((error: BusinessError) => {
      this.socketDidDisconnect(this.socket as socket.TCPSocket, error);
    });
  }

  /**
   * 监听socket事件
   * @param socket
   */
  private bindSocketEventListener() {
    if (this.socket) {
      this.socket.on('message', async (value: socket.SocketMessageInfo) => {
        this.socketDidReadData(this.socket as socket.TCPSocket, value.message);
      });
      this.socket.on('connect', () => {
        this.socket?.getRemoteAddress(async (error: BusinessError, data: socket.NetAddress) => {
          this.socketDidConnectToHost(this.socket as socket.TCPSocket, (data?.address) ?? this.host,
            (data?.port) ?? this.port);
        })
      });
      this.socket.on('close', async () => {
        this.socketDidDisconnect(this.socket as socket.TCPSocket);
      });
      this.socket.on('error', async (error: BusinessError) => {
        this.socketDidDisconnect(this.socket as socket.TCPSocket, error);
      });
    }
  }

  /**
   * 取消监听socket事件
   * @param socket
   */
  private unbindSocketEventListener() {
    if (this.socket) {
      this.socket.off('message');
      this.socket.off('connect');
      this.socket.off('close');
      this.socket.off('error');
    }
  }


  /**
   *  断开socket连接
   */
  public disConnect(): void {
    if (this.socket && this.isConnect) {
      this.socket.close(() => {
        //this.socketDidDisconnect(this.socket as socket.TCPSocket);
      });
    }
    this.unbindSocketEventListener();
    this.readBufferData = new Array<number>();
  }

  /**
   *  释放上下文
   */
  public releaseCTX(): void {
    this.socket = undefined;
    this.delegate = undefined;
    this.isInitCTX = false;
    this.isConnect = false;
    this.isConnecting = false;
  }

  /*****************发送业务请求数据********************/

  /**
   * 发起测速接口
   */
  public async doSendTestSpeed() {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送测速请求包`);
    let messageType: TKSocketMsgType = TKSocketMsgType.TestSpeed;
    this.sendMessage(messageType, new ArrayBuffer(0));
  }

  /**
   * 拉取配置接口
   */
  public async doSendTestSpeedConfig() {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送拉取配置请求包`);
    let messageType: TKSocketMsgType = TKSocketMsgType.TestSpeedConfig;
    this.sendMessage(messageType, new ArrayBuffer(0));
  }


  /*************************************socket代理服务方法**************************************/
  /**
   *  建立连接
   *
   * @param sock  socket连接
   * @param host  主机
   * @param port  端口
   */
  protected socketDidConnectToHost(socket: socket.TCPSocket, host: string, port: number) {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->连接已建立`);
    this.connectedHost = host;
    this.connectedPort = port;
    this.isConnect = true;
    //回调服务器连接成功
    if (this.delegate) {
      this.delegate.onNotifyMessage(this, TKBusMsgType.Connect);
    }
  }

  /**
   *  写数据完成
   *
   * @param sock socket连接
   * @param tag 标签
   */
  protected socketDidWriteData(socket: socket.TCPSocket) {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送数据完成`);
  }

  /**
   *  socket连接遇到错误将要断开连接
   *
   * @param sock socket
   * @param err  错误
   */
  protected socketDidDisconnect(socket: socket.TCPSocket, error?: BusinessError) {
    if (error && error.code !== 9) {
      error.message = error.message ?? "";
      TKLog.error(`${this.className}(${this.host}:${this.port})---->连接已断开:${error.message}`);
      //回调服务器请求失败动作，这个时候如果是长连接模式，应该断开重新连接认证
      if (this.delegate) {
        let result: Record<string, Object> = {};
        result["error_type"] = "1";
        if (error.code == 2303210 || error.message.indexOf("超时") > 0 || error.message.indexOf("timed out") > 0) {
          result["error_no"] = "-993";
        } else if (error.message.indexOf("断开") > 0 || error.message.indexOf("refused") > 0 ||
          error.message.indexOf("中断") > 0) {
          result["error_no"] = "-994";
        } else {
          result["error_no"] = "-995";
        }
        result["error_info"] = `连接中断，中断原因：${error.message}`;
        this.delegate.onNotifyMessage(this, TKBusMsgType.DisConnect, "", result);
      }
    } else {
      TKLog.info(`${this.className}(${this.host}:${this.port})---->连接已断开`);
    }
  }

  /**
   *  读取数据
   *
   * @param sock socket连接
   * @param data 数据
   * @param tag  请求标签
   */
  protected socketDidReadData(socket: socket.TCPSocket, data: ArrayBuffer) {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->收到服务器端数据`);
    if (data.byteLength > 0) {
      this.readBufferData.push(...new Uint8Array(data));
    }
    this.doReadData();
  }

  /**
   *  读取数据
   */
  private doReadData() {
    while (this.readBufferData && this.readBufferData.length >= TKComBusTestSpeedClient.HEADlENGTH) {
      let readDataView: TKReadDataView = new TKReadDataView(buffer.from(new Uint8Array(this.readBufferData)).buffer);
      //获取包标记
      let hqTag: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 2);
      //获取消息类型
      let messageType: number = readDataView.getInt();
      //获取包体长度
      let bodyLength: number = readDataView.getUInt();
      if (!readDataView.isOutBounds(bodyLength)) {
        //获取包体内容
        let bodyData: ArrayBuffer = readDataView.getBytes(bodyLength);
        //清空缓存
        this.readBufferData.splice(0, TKComBusTestSpeedClient.HEADlENGTH + bodyLength);
        //解析包体
        TKLog.debug(`${this.className}(${this.host}:${this.port})---->响应包头:(业务包类别:${hqTag},业务包功能号:${messageType},业务包数据长度:${TKComBusTestSpeedClient.HEADlENGTH +
          bodyLength},业务包包体长度:${bodyLength})`);
        switch (messageType) {
          case TKSocketMsgType.TestSpeedReturn: {
            this.parseTestSpeedData(bodyData);
            break;
          }
          case TKSocketMsgType.TestSpeedConfigReturn: {
            this.parseTestSpeedConfigData(bodyData);
            break;
          }
          default:
            break;
        }
      } else {
        break;
      }
    }
  }

  /********************************私有方法************************************/

  /**
   *  构造请求数据
   *
   * @param messageType 消息类型
   * @param bodydata    消息体
   *
   * @return 发送的消息数据
   */
  private buildSendMessage(messageType: number, bodyData: ArrayBuffer): ArrayBuffer {
    //请求体
    if (!bodyData) {
      bodyData = new ArrayBuffer(0);
    }
    //计算请求包长度
    let bodySize: number = bodyData.byteLength;
    //构建消息数据对象
    let bBuffer: TKWriteDataView = new TKWriteDataView(TKComBusTestSpeedClient.HEADlENGTH + bodySize);
    //包标记
    bBuffer.putChar(TKWriteDataView.CHAR_GBK, TKComBusTestSpeedClient.TAG, 2);
    //消息类型
    bBuffer.putUInt(messageType);
    //包体长度
    bBuffer.putUInt(bodySize);
    //包体内容
    bBuffer.putBytes(bodyData);
    return bBuffer.buffer;
  }


  /**
   *  发送请求信息
   *
   * @param messageType 消息类型
   * @param bodydata    消息体
   */
  private sendMessage(messageType: number, bodyData: ArrayBuffer) {
    if (this.socket && this.isConnect) {
      this.socket.send({
        data: this.buildSendMessage(messageType, bodyData)
      }).then(() => {
        this.socketDidWriteData(this.socket as socket.TCPSocket);
      }).catch((error: BusinessError) => {
        this.socketDidDisconnect(this.socket as socket.TCPSocket, error);
      });
    }
  }

  /********************************消息解析相关方法************************************/

  /**
   *  解析测试速度数据
   *
   * @param data
   */
  private parseTestSpeedData(data: ArrayBuffer) {
    let speedData: string = TKDataHelper.uint8ArrayToString(new Uint8Array(data), TKDataHelper.CHAR_GBK);
    speedData = TKStringHelper.trim(speedData);
    let result: Record<string, Object> = {
      "data": speedData
    };
    //回调服务器连接成功
    if (this.delegate) {
      this.delegate.onNotifyMessage(this, TKBusMsgType.Result, `${TKSocketMsgType.TestSpeedReturn}`, result);
    }
  }

  /**
   *  解析测试配置数据
   *
   * @param data
   */
  private parseTestSpeedConfigData(data: ArrayBuffer) {
    let speedConfigData: string = TKDataHelper.uint8ArrayToString(new Uint8Array(data), TKDataHelper.CHAR_GBK);
    speedConfigData = TKStringHelper.trim(speedConfigData);
    let result: Record<string, Object> = {
      "data": speedConfigData
    };
    //回调服务器连接成功
    if (this.delegate) {
      this.delegate.onNotifyMessage(this, TKBusMsgType.Result, `${TKSocketMsgType.TestSpeedConfigReturn}`, result);
    }
  }
}