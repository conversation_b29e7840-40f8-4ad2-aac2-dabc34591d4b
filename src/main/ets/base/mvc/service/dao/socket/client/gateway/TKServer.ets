import { TKCacheManager } from '../../../../../../../util/cache/TKCacheManager';
import { TKDataHelper } from '../../../../../../../util/data/TKDataHelper';
import { TKDateHelper } from '../../../../../../../util/date/TKDateHelper';
import { TKFileHelper } from '../../../../../../../util/file/TKFileHelper';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKNetHelper } from '../../../../../../../util/net/TKNetHelper';
import { TKNumberHelper } from '../../../../../../../util/number/TKNumberHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../../../../../util/system/TKSystemHelper';
import { TKNotification } from '../../../../../../notification/TKNotification';
import { TKNotificationCenter } from '../../../../../../notification/TKNotificationCenter';
import { TKComBusClient } from '../common/TKComBusClient';
import { TKGatewayManager } from './TKGatewayManager';
import { TKNetAddress } from './TKNetAddress';
import { TKSocketSpeedChecker, TKSocketSpeedCheckerDelegate } from './TKSocketSpeedChecker';

export enum TKIPMode {
  IPV4,
  IPV6,
  ALL
}

export enum TKSocketTestSpeedMode {
  //初始化串行测试
  InitSerial,
  //初始化并行测速
  InitConCurrent,
  //检测测速
  Test,
  //界面测速
  View
}

/**
 *  网络监听代理
 */
export interface TKSocketSpeedServerDelegate {

  /**
   *  测速结果
   * @param address
   */
  testSpeedWithMode: (address: TKNetAddress, testSpeedMode: TKSocketTestSpeedMode) => void;

  /**
   *  测速结束
   *
   * @param address
   */
  testSpeedFinishedWithMode: (serverName: string, testSpeedMode: TKSocketTestSpeedMode) => void;
}

/**
 轮训模式
 */
export enum LBMODE {
  /**
   *  不测速模式
   */
  NONE = -1,
  /**
   *  随机模式
   */
  RANDOM = 0,
  /**
   *  顺序模式
   */
  LOOP = 1,
  /**
   *  主备模式
   */
  BACKUPS = 2,
  /**
   *  最优模式
   */
  BEST = 3,
  /**
   *  期货最优模式
   */
  QHBEST = 4,
  /**
   *  站点固定模式
   */
  NOCHANGE = 5,
  /**
   *  负载最优模式
   */
  FZBEST = 6
}
;

/**
 代理模式
 */
export enum LBPOLICY {
  /**
   *  普通
   */
  COMMON = 0,
  /**
   *  期货
   */
  FUTURES = 1,
  /**
   *  云模式
   */
  CLOUD = 2
}
;

/**
 *  服务地址测速结果对象
 */
export class TKSocketCheckServerResult {
  /**
   * 测速返回成功次数
   */
  public successNum: number = 0;
  /**
   * 测速返回失败次数
   */
  public failedNum: number = 0;
  /**
   * 总共需要测速的次数
   */
  public totalNum: number = 0;

  public addFailedNum() {
    this.failedNum++;
  }

  public addSuccessNum() {
    this.successNum++;
  }

  public resetResult() {
    this.successNum = 0;
    this.failedNum = 0;
  }

  public isFinished(): boolean {
    return (this.successNum + this.failedNum) == this.totalNum;
  }

  public lastNum(): number {
    return this.totalNum - this.successNum - this.failedNum;
  }
}

/**
 *  服务器模型
 */
export class TKServer implements TKSocketSpeedCheckerDelegate {
  /**
   连接/认证
   */
  private static readonly NOTE_BUSCLIENT_CONNECT: string = "busClientConnect";
  /**
   *  BUS服务器配置缓存
   */
  private static readonly CACHE_BUS_CONFIG: string = "TKBusConfig";
  /**
   *  服务器配置缓存
   */
  private static readonly CACHE_SERVER_CONFIG: string = "TKServerConfig";
  /**
   *  是否开启国密站点
   */
  private _isUseGMSite: boolean = false;
  /**
   *  扫描时间间隔，秒
   */
  public scanInterval: number = 0;
  /**
   *  网关名称
   */
  public gateWayName: string = "";
  /**
   *  关联网关名称，主要用来标示行情服务器和行情推送服务器之间的关联关系
   */
  public refGateWayName: string = "";
  /**
   *  域名地址列表
   */
  public netDomainArr: Array<TKNetAddress> = new Array<TKNetAddress>();
  /**
   * IP地址列表
   */
  public netAddressArr: Array<TKNetAddress> = new Array<TKNetAddress>();
  /**
   *  服务器分组机房轮询模式
   */
  public LBGroupMode: number = 0;
  /**
   *  服务器轮询模式
   */
  public LBMode: number = 0;
  /**
   *  服务器策略
   */
  public LBPolicy: number = 0;
  /**
   *  连接模式
   */
  public mode: number = 0;
  /**
   *  版本号
   */
  public version: string = "";
  /**
   *  超时时间
   */
  public recTimeout: number = 0;
  /**
   *  心跳检测间隔时间，单位秒
   */
  public heartTime: number = 0;
  /**
   *  站点缓存本地保留的时间，单位小时，默认7天，0代表永久缓存
   */
  public saveCacheTime: number = 0;
  /**
   *  数据加密密钥
   */
  public key: string = "";
  /**
   *  用户选择设置的地址
   */
  public useNetAddress: TKNetAddress | undefined = undefined;
  /**
   *  当前正在使用的地址
   */
  public curNetAddress: TKNetAddress | undefined = undefined;
  /**
   *  当前排除使用的地址列表
   */
  public excludesAddress: Array<TKNetAddress> = new Array<TKNetAddress>();
  /**
   *  当前正在使用的地址的索引
   */
  public curNetAddressIndex: number = 0;
  /**
   *  配置文件名称
   */
  public functionConfig: string = "";
  /**
   *  服务端公钥证书的名称
   */
  public serverPublicCer: string = "";
  /**
   *  客户端公钥证书的名称
   */
  public clientPublicCer: string = "";
  /**
   *  客户端私钥证书的名称
   */
  public clientPrivateCer: string = "";
  /**
   *  客户端证书的密码
   */
  public clientPassword: string = "";
  /**
   *  是否加密证书(0:否，1:是)，默认是0
   */
  public isEncryptCer: boolean = false;
  /**
   *  是否发送Base64格式的加密证书到服务器认证(0:否，1:是)，默认是0
   */
  public isSendEncrytBase64Cer: boolean = false;
  /**
   *  是否开启防外挂机制(0:否，1:是)，默认是0
   */
  public isGameGuard: boolean = false;
  /**
   *  服务器版本号
   */
  public serverVersion: string = "";
  /**
   *  服务器TAG
   */
  public serverTag: string = "";
  /**
   *  服务器认证模式（0:不认证，1：密码认证，2：普通证书双向认证，3：普通证书双向认证+国密加密算法，4：国密证书双向认证）
   */
  public vityifyMode: string = "0";
  /**
   * 是否发送测速配置请求包（0:不发送，1:发送）默认是0
   */
  public isGetSpeedConfig: boolean = false;
  /**
   *  是否以期货缓存的最优地址优先选择
   *
   * @return
   */
  public isQHBestCacheAddressFirst: boolean = false;
  /**
   * socket的实现类
   */
  public socketClassName: string = "";
  /**
   * socket的测速实现类
   */
  public socketSpeedClassName: string = "";
  /**
   *  用户信息
   */
  public userInfo: Object | undefined = undefined;
  /**
   *  网络测速代理
   */
  public delegate: TKSocketSpeedServerDelegate | undefined = undefined;
  /**
   本地缓存的最快地址
   */
  private static readonly CACHE_FAST_SOCKET_NETADDRESS: string = "cache_fast_socket_netaddress";
  /**
   *  调用次数
   */
  private invokeCount: number = 0;
  /**
   *  分组调用次数
   */
  private invokeGroupCount: number = 0;
  /**
   *  域名分组
   */
  private netDomainArrGroupMap: Map<string, Array<TKNetAddress>> = new Map<string, Array<TKNetAddress>>();
  /**
   *  IP分组
   */
  private netAddressArrGroupMap: Map<string, Array<TKNetAddress>> = new Map<string, Array<TKNetAddress>>();
  /**
   *  主域名检测线程
   */
  private checkDomainThreads: Array<TKSocketSpeedChecker> = new Array<TKSocketSpeedChecker>();
  /**
   *  IP检测线程
   */
  private checkIPThreads: Array<TKSocketSpeedChecker> = new Array<TKSocketSpeedChecker>();
  /**
   *  主域名检测结果字典
   */
  private checkDomainResult: TKSocketCheckServerResult = new TKSocketCheckServerResult();
  /**
   *  IP检测结果字典
   */
  private checkIPResult: TKSocketCheckServerResult = new TKSocketCheckServerResult();
  /**
   *  测速检测结果字典
   */
  private checkSpeedResult: TKSocketCheckServerResult = new TKSocketCheckServerResult();

  /**
   *  初始化
   *
   * @param name      名称
   * @param configMap 配置文件
   *
   * @return
   */
  public constructor(name: string, configMap: Map<string, Object>) {
    //初始化属性
    this.gateWayName = name;
    if (configMap) {
      //关联服务器id
      this.refGateWayName = TKMapHelper.getString(configMap, "refServer");
      //版本号
      this.version = TKMapHelper.getString(configMap, "version");
      //分组轮训模式
      if (TKStringHelper.isNotBlank(TKMapHelper.getString(configMap, "LBGroupMode"))) {
        this.LBGroupMode = TKMapHelper.getNumber(configMap, "LBGroupMode");
      } else {
        this.LBGroupMode = LBMODE.BEST;
      }
      //轮询模式
      if (TKStringHelper.isNotBlank(TKMapHelper.getString(configMap, "LBMode"))) {
        this.LBMode = TKMapHelper.getNumber(configMap, "LBMode");
      } else {
        this.LBMode = LBMODE.BEST;
      }
      //服务器策略
      this.LBPolicy = TKMapHelper.getNumber(configMap, "LBPolicy");
      if (this.LBMode == LBMODE.QHBEST) {
        this.LBPolicy = LBPOLICY.FUTURES;
        this.LBMode = LBMODE.RANDOM;
      }
      //连接模式
      this.mode = TKMapHelper.getNumber(configMap, "mode");
      //扫描间隔时间
      this.scanInterval = TKMapHelper.getNumber(configMap, "scanInterval");
      //超时时间
      this.recTimeout = TKMapHelper.getNumber(configMap, "recvTimeout");
      if (this.recTimeout == 0) {
        this.recTimeout = -1;
      }
      //心跳检测间隔时间
      this.heartTime = TKMapHelper.getNumber(configMap, "heartTime");
      if (this.heartTime == 0) {
        this.heartTime = 5;
      }
      //手工选择模式下站点缓存本地的时间，单位天，0代表永久缓存，默认永久缓存
      this.saveCacheTime = TKMapHelper.getNumber(configMap, "saveCacheTime");
      if (this.saveCacheTime == 0) {
        this.saveCacheTime = 3650;
      }
      //加密key
      this.key = TKMapHelper.getString(configMap, "key");
      //功能号配置文件
      this.functionConfig = TKMapHelper.getString(configMap, "functionConfig");
      //服务端公钥证书的名称
      this.serverPublicCer = this.getCertPath(TKMapHelper.getString(configMap, "serverPublicCer"));
      //客户端公钥证书的名称
      this.clientPublicCer = this.getCertPath(TKMapHelper.getString(configMap, "clientPublicCer"));
      //客户端私钥证书的名称
      this.clientPrivateCer = this.getCertPath(TKMapHelper.getString(configMap, "clientPrivateCer"));
      //客户端证书的密码
      this.clientPassword = TKMapHelper.getString(configMap, "clientPassword");
      //是否加密证书(0:否，1:是)，默认是0
      this.isEncryptCer = TKMapHelper.getBoolean(configMap, "isEncrytCer");
      //是否发送Base64格式的加密证书到服务器认证(0:否，1:是)，默认是0
      this.isSendEncrytBase64Cer = TKMapHelper.getBoolean(configMap, "isSendEncrytBase64Cer");
      //是否开启防外挂机制(0:否，1:是)，默认是0
      this.isGameGuard = TKMapHelper.getBoolean(configMap, "isGameGuard");
      //服务器版本号
      this.serverVersion = TKMapHelper.getString(configMap, "serverVersion");
      //服务器Tag
      this.serverTag = TKMapHelper.getString(configMap, "serverTag");
      //服务器认证模式（0:不认证，1：密码认证，2：普通证书双向认证，3：普通证书双向认证+国密加密算法，4：国密证书双向认证）
      this.vityifyMode = TKMapHelper.getString(configMap, "vityifyMode");
      //是否发送测速配置请求包（0:不发送，1:发送）默认是0
      this.isGetSpeedConfig = TKMapHelper.getBoolean(configMap, "isGetSpeedConfig");
      //socket实现类
      this.socketClassName = TKMapHelper.getString(configMap, "socketClassName");
      //socket测速实现类
      this.socketSpeedClassName = TKMapHelper.getString(configMap, "socketSpeedClassName");
      //域名服务器列表
      let domainList: string = TKMapHelper.getString(configMap, "domain");
      let domainNameList: string = TKMapHelper.getString(configMap, "domainName");
      this.netDomainArr = this.processServerWithAddresses(domainList, domainNameList, true);
      // 读取本地更新数据，server.xml的updateUrl更新下来的数据，进行替换
      this.processRoomCacheConfigWithDomainFlag(true);
      this.netDomainArrGroupMap = this.processServerGroupMapWithAddresses(this.netDomainArr);

      //IP服务器列表
      let serverList: string = TKMapHelper.getString(configMap, "gateway");
      let serverNameList: string = TKMapHelper.getString(configMap, "gatewayName");
      //读取本地更新数据，BusConfig.xml的updateUrl更新下来的数据，进行替换
      let updateServerDic: Map<string, Map<string, Object>> | undefined = TKCacheManager.shareInstance()
        .getFileCacheData(TKServer.CACHE_BUS_CONFIG, TKServer.CACHE_BUS_CONFIG);
      if (updateServerDic) {
        let serverDic: Map<string, Object> | undefined = TKMapHelper.getObject(updateServerDic, this.gateWayName);
        if (serverDic) {
          let tempServerList: string = TKMapHelper.getString(serverDic, "ip");
          let tempServerNameList: string = TKMapHelper.getString(serverDic, "name");
          if (TKStringHelper.isNotBlank(tempServerList)) {
            serverList = tempServerList;
            serverNameList = tempServerNameList;
          }
        }
      }
      this.netAddressArr = this.processServerWithAddresses(serverList, serverNameList, false);
      // 读取本地更新数据，server.xml的updateUrl更新下来的数据，进行替换
      this.processRoomCacheConfigWithDomainFlag(false);
      this.netAddressArrGroupMap = this.processServerGroupMapWithAddresses(this.netAddressArr);

      //期货缓存的最优地址
      this.isQHBestCacheAddressFirst = (this.LBPolicy == LBPOLICY.FUTURES);
      TKNotificationCenter.defaultCenter.addObserver(this, this.handleNotification, TKServer.NOTE_BUSCLIENT_CONNECT);

      //构建检测线程
      this.buildCheckThreadsWithDomainFlag(true);
      this.buildCheckThreadsWithDomainFlag(false);
    }
  }

  //获取证书路径
  private getCertPath(certPath: string): string {
    if (TKStringHelper.isNotBlank(certPath)) {
      if (TKStringHelper.startsWith(certPath, "@bundle:")) {
        return certPath;
      }
      if (TKStringHelper.startsWith(certPath, "/")) {
        return certPath.substring(1, certPath.length);
      }
      if (!certPath.includes("/")) {
        certPath = `cert/${certPath}`;
      }
      let lastCertPath: string = `thinkive/config/${TKSystemHelper.getEnvironment()}/socket/${certPath}`;
      if (TKFileHelper.readFile(lastCertPath).length <= 0) {
        lastCertPath = `thinkive/config/default/socket/${certPath}`;
      }
      return lastCertPath;
    }
    return certPath;
  }

  //兼容读取多机房配置
  private processRoomCacheConfigWithDomainFlag(isDomainFlag: boolean) {
    let cacheServer: string = "";
    let cacheServerName: string = "";
    //读取本地缓存机房更新的地址
    let roomMap: Map<string, Object> | undefined = TKCacheManager.shareInstance()
      .getFileCacheData(TKServer.CACHE_SERVER_CONFIG, TKServer.CACHE_SERVER_CONFIG);
    if (roomMap) {
      let addressStr: string = "";
      let addressNameStr: string = "";
      roomMap.forEach((value, key) => {
        let room: Map<string, Object> = value as Map<string, Object>;
        let serverMap: Map<string, Object> = TKMapHelper.getObject(room, "server") as Map<string, Object>;
        let server: Map<string, Object> = TKMapHelper.getObject(serverMap, this.gateWayName) as Map<string, Object>;
        let domain: string = TKMapHelper.getString(server, "domain");
        let ip: string = TKMapHelper.getString(server, "ip");
        let description: string = TKMapHelper.getString(server, "description");
        let address: string = isDomainFlag ? domain : ip;
        if (TKStringHelper.isNotBlank(address) && !TKStringHelper.startsWith(address, "http://") &&
          !TKStringHelper.startsWith(address, "https://") &&
          TKNetHelper.formatAddress(address).length >= 2) {
          addressStr += (address + "|");
          addressNameStr += (description + "|");
        }
      });
      cacheServer = addressStr.endsWith("|") ? addressStr.substring(0, addressStr.length - 1) : addressStr;
      cacheServerName =
        addressNameStr.endsWith("|") ? addressNameStr.substring(0, addressNameStr.length - 1) : addressNameStr;
    }
    //更新缓存地址
    if (TKStringHelper.isNotBlank(cacheServer)) {
      let tempHosts: Array<string> = TKStringHelper.split(cacheServer, "|");
      let tempHostNames: Array<string> = TKStringHelper.split(cacheServerName, "|");
      if (tempHosts && tempHosts.length > 0) {
        let hosts: Array<string> = new Array<string>();
        let hostNames: Array<string> = new Array<string>();
        for (let tempHostIndex = 0; tempHostIndex < tempHosts.length; tempHostIndex++) {
          let tempHost: string = tempHosts[tempHostIndex];
          if (hosts.indexOf(tempHost) < 0) {
            hosts.push(tempHost);
            if (tempHosts.length == tempHostNames.length) {
              let tempHostName: string = tempHostNames[tempHostIndex];
              hostNames.push(tempHostName);
            }
          }
        }
        cacheServer = hosts.join("|");
        cacheServerName = hostNames.join("|");
        let cacheNetAddressArr: Array<TKNetAddress> =
          this.processServerWithAddresses(cacheServer, cacheServerName, isDomainFlag);
        if (cacheNetAddressArr && cacheNetAddressArr.length > 0) {
          if (isDomainFlag) {
            this.netDomainArr = cacheNetAddressArr;
          } else {
            this.netAddressArr = cacheNetAddressArr;
          }
        }
      }
    }
  }

  /**
   * 处理解析地址
   *
   * @param addresses    地址列表字符串，多个是用|分割
   * @param addressNames 地址名称列表字符串，多个是用|分割
   * @param isDomainFlag 是否是域名地址标志
   *
   */
  private processServerWithAddresses(addresses: string, addressNames: string,
    isDomainFlag: boolean): Array<TKNetAddress> {
    let netAddressList: Array<TKNetAddress> = new Array<TKNetAddress>();
    if (TKStringHelper.isNotBlank(addresses)) {
      //服务器列表
      let addressesAry: Array<string> = TKStringHelper.split(addresses, "|");
      //服务器列表名称
      let addressesNameAry: Array<string> = TKStringHelper.split(addressNames, "|");
      if (addressesAry && addressesAry.length > 0) {
        for (let i = 0; i < addressesAry.length; i++) {
          let temp: Array<string> = TKNetHelper.formatAddress(addressesAry[i]);
          if (temp.length >= 2) {
            let host: string = temp[0];
            let port: number = Number(temp[1]);
            let groupId: string = temp.length > 2 ? temp[2] : "";
            let isJoinConnectSite: boolean = temp.length > 3 ? temp[3] != "0" : true;
            let isGMSite: boolean = temp.length > 4 ? temp[4] == "1" : false;
            //服务器地址
            let netAddress: TKNetAddress = new TKNetAddress();
            netAddress.isJoinConnectSite = isJoinConnectSite;
            netAddress.isGMSite = isGMSite;
            netAddress.groupId = groupId;
            netAddress.isDomain = isDomainFlag;
            netAddress.isIPV6 = host.indexOf(":") > -1;
            netAddress.gateWayName = this.gateWayName;
            netAddress.ip = host;
            netAddress.port = port;
            netAddress.isAlive = false;
            netAddress.isNeedTestSpeed = true;
            netAddress.desc = (addressesNameAry && addressesNameAry.length > i) ? addressesNameAry[i] : "";
            netAddressList.push(netAddress);
          }
        }
      }
    }
    return netAddressList;
  }

  /**
   *  解析站点分组
   */
  private processServerGroupMapWithAddresses(netAddresses: Array<TKNetAddress>): Map<string, Array<TKNetAddress>> {
    let netAddressesGroupMap: Map<string, Array<TKNetAddress>> = new Map<string, Array<TKNetAddress>>();
    if (netAddresses && netAddresses.length > 0) {
      for (let address of netAddresses) {
        let groupId: string = address.groupId;
        let groupNetAddresses: Array<TKNetAddress> | undefined = TKMapHelper.getObject(netAddressesGroupMap, groupId);
        if (!groupNetAddresses) {
          groupNetAddresses = new Array<TKNetAddress>();
        }
        groupNetAddresses.push(address);
        netAddressesGroupMap.set(groupId, groupNetAddresses);
      }
    }
    return netAddressesGroupMap;
  }

  private buildCheckThreadsWithDomainFlag(isDomainFlag: boolean) {
    if (this.LBMode != LBMODE.NONE) {
      //检测线程
      let netAddressArr: Array<TKNetAddress> = isDomainFlag ? this.netDomainArr : this.netAddressArr;
      if (netAddressArr && netAddressArr.length > 0) {
        let checkServerResult: TKSocketCheckServerResult = isDomainFlag ? this.checkDomainResult : this.checkIPResult;
        checkServerResult.totalNum = netAddressArr.length;
        let checkNetAddressThreads: Array<TKSocketSpeedChecker> =
          isDomainFlag ? this.checkDomainThreads : this.checkIPThreads;
        for (let netAddress of netAddressArr) {
          // Class socketSpeedClas = ([TKStringHelper isNotBlank:_socketSpeedClassName] && netAddress.isGMSite) ? NSClassFromString(_socketSpeedClassName) : [TKSocketSpeedChecker class];
          // if(!socketSpeedClas)
          // {
          //   socketSpeedClas = [TKSocketSpeedChecker class];
          // }
          let heartSpeedChecker: TKSocketSpeedChecker = new TKSocketSpeedChecker(netAddress, this.gateWayName);
          checkNetAddressThreads.push(heartSpeedChecker);
        }
      }
    }
  }

  /**
   * 获取期货缓存的最优地址
   */
  private getQHBestCacheServer(): string {
    let QHBestServer: string = "";
    if (this.LBPolicy == LBPOLICY.FUTURES) {
      let cacheQHBestServer: string | undefined = TKCacheManager.shareInstance()
        .getFileCacheData(`QHBestServer_${this.gateWayName}`);
      if (TKStringHelper.isNotBlank(cacheQHBestServer)) {
        let temp: Array<string> = TKStringHelper.split(cacheQHBestServer, "|");
        if (temp.length == 2) {
          let cacheDate: Date = TKDateHelper.parseDateStr(temp[0]);
          let currentDate: Date = new Date();
          if (currentDate.getTime() < cacheDate.getTime()) {
            QHBestServer = temp[1];
          }
        }
      }
    }
    return QHBestServer;
  }

  /**
   *  获取配置所有的网关服务器地址（IP+域名），也就是属性netAddressArr + netDomainArr的集合
   *
   * @return
   */
  public getAllNetworkAddresses(): Array<TKNetAddress> {
    let addresses: Array<TKNetAddress> = new Array<TKNetAddress>();
    if (this.netDomainArr && this.netDomainArr.length > 0) {
      addresses.push(...this.netDomainArr);
    }
    if (this.netAddressArr && this.netAddressArr.length > 0) {
      addresses.push(...this.netAddressArr);
    }
    return addresses;
  }

  /**
   *  获取指定地址和端口的网关服务器地址
   * @param address 地址，格式为ip:port:group的字符串
   *
   * @return
   */
  public getServerAddress(address: string): TKNetAddress | undefined {
    let destAddressStr: string = address;
    let networkAddresses: Array<TKNetAddress> = this.getAllNetworkAddresses();
    if (networkAddresses && networkAddresses.length > 0) {
      for (let netAddress of networkAddresses) {
        let tempAddressStr: string = `${netAddress.ip}:${netAddress.port}:${netAddress.groupId ?? ''}`;
        if (destAddressStr.indexOf(tempAddressStr) > -1) {
          return netAddress;
        }
      }
    }
    return undefined;
  }

  /**
   *  获取指定地址和端口的网关服务器地址
   *
   * @return
   */
  public getServerAddressWithHostPortGroup(host: string, port: number, group: string): TKNetAddress | undefined {
    let networkAddresses: Array<TKNetAddress> = this.getAllNetworkAddresses();
    if (networkAddresses && networkAddresses.length > 0) {
      let destAddressStr: string = `${host}:${port}:${group ?? ''}`;
      for (let netAddress of networkAddresses) {
        let tempAddressStr: string = `${netAddress.ip}:${netAddress.port}:${netAddress.groupId ?? ''}`;
        if (tempAddressStr == destAddressStr) {
          return netAddress;
        }
      }
    }
    return undefined;
  }

  public set isUseGMSite(isUseGMSite: boolean) {
    if (this._isUseGMSite != isUseGMSite) {
      this.useNetAddress = undefined;
    }
    this._isUseGMSite = isUseGMSite;
    this.getRmdServer();
  }

  public get isUseGMSite(): boolean {
    return this._isUseGMSite;
  }

  /**
   *  获取网关服务器地址
   *
   * @return
   */
  public getRmdServer(): TKNetAddress | undefined {
    //执行站点永久不改变策略机制
    let canUseAddress: TKNetAddress | undefined = this.getRmdServerForNoChange();
    //获取网关服务器地址,执行各种代理模式和轮训负载模式及国密标记判断
    if (!canUseAddress) {
      canUseAddress = this.getRmdServerForLBPolicyMode();
    }
    //执行最终服务器默认策略
    if (!canUseAddress) {
      canUseAddress = this.getRmdServerForLast();
    }
    //获取网关服务器地址,执行各种代理模式和轮训负载模式及国密标记判断,忽略国密标记的差异
    if (!canUseAddress) {
      canUseAddress = this.getRmdServerForLBPolicyModeIgnoreGMFlag();
    }
    //执行最终服务器默认策略,忽略国密标记的差异
    if (!canUseAddress) {
      canUseAddress = this.getRmdServerForLastIgnoreGMFlag();
    }
    //此时没有，就是配置文件出问题了
    if (!canUseAddress) {
      TKLog.error(`没有配置${this.gateWayName}网关服务器列表，请查看BusConfig.xml配置文件！`);
    }
    this.curNetAddress = canUseAddress;
    this.curNetAddressIndex = this.getAllNetworkAddresses().indexOf(this.curNetAddress!);
    return canUseAddress;
  }

  /**
   * 获取网关服务器地址,执行NoChange策略
   */
  private getRmdServerForNoChange(): TKNetAddress | undefined {
    let canUseAddress: TKNetAddress | undefined = undefined;
    if (this.useNetAddress && (this.LBMode == LBMODE.NOCHANGE)) {
      canUseAddress = this.useNetAddress;
    }
    return canUseAddress;
  }

  /**
   * 获取网关服务器地址,执行各种代理模式和轮训负载模式及国密标记判断
   */
  private getRmdServerForLBPolicyMode(): TKNetAddress | undefined {
    let canUseAddress: TKNetAddress | undefined = undefined;
    if (this._isUseGMSite) {
      //执行服务器可用+低负载选择策略+国密站点
      if (!canUseAddress) {
        canUseAddress = this.getRmdServerForLowerLoad(true, "1");
      }
      //执行服务器可用+高负载选择策略+国密站点
      if (!canUseAddress) {
        canUseAddress = this.getRmdServerForLowerLoad(false, "1");
      }
      if (!canUseAddress) {
        //执行服务器可用+低负载选择策略+非国密站点
        if (!canUseAddress) {
          canUseAddress = this.getRmdServerForLowerLoad(true, "0");
        }
        //执行服务器可用+高负载选择策略+非国密站点
        if (!canUseAddress) {
          canUseAddress = this.getRmdServerForLowerLoad(false, "0");
        }
        if (canUseAddress) {
          this._isUseGMSite = false;
        }
      }
    } else {
      //执行服务器可用+低负载选择策略+非国密站点
      if (!canUseAddress) {
        canUseAddress = this.getRmdServerForLowerLoad(true, "0");
      }
      //执行服务器可用+高负载选择策略+非国密站点
      if (!canUseAddress) {
        canUseAddress = this.getRmdServerForLowerLoad(false, "0");
      }
    }
    return canUseAddress;
  }

  /**
   * 获取网关服务器地址,执行各种代理模式和轮训负载模式
   */
  private getRmdServerForLBPolicyModeIgnoreGMFlag(): TKNetAddress | undefined {
    let canUseAddress: TKNetAddress | undefined = undefined;
    //执行服务器可用+低负载选择策略+非国密站点
    if (!canUseAddress) {
      canUseAddress = this.getRmdServerForLowerLoad(true, "");
    }
    //执行服务器可用+高负载选择策略+非国密站点
    if (!canUseAddress) {
      canUseAddress = this.getRmdServerForLowerLoad(false, "");
    }
    return canUseAddress;
  }

  /**
   *  获取网关服务器地址,站点可用时候，根据负载+国密选择
   *
   * @return 网关服务器地址
   */
  private getRmdServerForLowerLoad(isNeedLowerLoad: boolean, GMSiteFlag: string): TKNetAddress | undefined {
    //判断是不是多公司云模式
    let canUseAddress: TKNetAddress | undefined = this.getCanUseServerByCloudPolicy(isNeedLowerLoad, GMSiteFlag);
    //不是就再判断是否期货最优缓存
    if (!canUseAddress) {
      canUseAddress = this.getQHBestCacheCanUseServer(isNeedLowerLoad, GMSiteFlag);
    }
    //不是就再判断是否手动缓存站点模式
    if (!canUseAddress) {
      canUseAddress = this.getTKBestCacheCanUseServer(isNeedLowerLoad, GMSiteFlag);
    }
    //不是就再执行分组策略,获取分组内的可用站点
    if (!canUseAddress) {
      canUseAddress = this.getCanUseServerByGroupPolicy(isNeedLowerLoad, GMSiteFlag);
    }
    //不是就再获取域名
    if (!canUseAddress) {
      canUseAddress = this.getCanUseServer(this.netDomainArr, isNeedLowerLoad, GMSiteFlag);
    }
    //域名没有或者挂了再获取IP
    if (!canUseAddress) {
      canUseAddress = this.getCanUseServer(this.netAddressArr, isNeedLowerLoad, GMSiteFlag);
    }
    return canUseAddress;
  }

  /**
   * 是否可以连接到服务器，满足可用和低负载及国密优先
   */
  private isCanConnectToServer(netAddress: TKNetAddress, isNeedLowerLoad: boolean, GMSiteFlag: string): boolean {
    return this.excludesAddress.indexOf(netAddress) < 0 && netAddress.isAlive &&
      (isNeedLowerLoad ? (netAddress.speed < Number.MAX_VALUE) : true) &&
      (TKStringHelper.isNotBlank(GMSiteFlag) ? (netAddress.isGMSite == TKDataHelper.getBoolean(GMSiteFlag)) : true);
  }

  /**
   * 获取数组的随机索引
   */
  private getRandomIndexForArrayList(arrayList: Array<Object>): number {
    let index: number = 0;
    if (arrayList && arrayList.length > 0) {
      index = TKNumberHelper.getRandomNum(arrayList.length);
    }
    return index;
  }

  /**
   * 获取期货缓存策略下可用的地址
   * @param isNeedLowerLoad 是否需要满足低负载策略
   * @param isNeedGMSite 是否需要满足国密站点
   */
  private getQHBestCacheCanUseServer(isNeedLowerLoad: boolean, GMSiteFlag: string): TKNetAddress | undefined {
    let canUseAddress: TKNetAddress | undefined = undefined;
    if (this.isQHBestCacheAddressFirst) {
      //先判断期货缓存的最优地址，如果有，然后判断是否存活，如果存活就用，不然就继续地址筛选逻辑
      let QHBestCacheAddress: TKNetAddress | undefined = this.getServerAddress(this.getQHBestCacheServer());
      if (QHBestCacheAddress && QHBestCacheAddress.isJoinConnectSite &&
      this.isCanConnectToServer(QHBestCacheAddress, isNeedLowerLoad, GMSiteFlag)) {
        canUseAddress = QHBestCacheAddress;
      } else {
        //获取当前用户手工设置选择的站点
        if (this.useNetAddress && this.isCanConnectToServer(this.useNetAddress, isNeedLowerLoad, GMSiteFlag)) {
          canUseAddress = this.useNetAddress;
        }
      }
    } else {
      if (this.useNetAddress && this.isCanConnectToServer(this.useNetAddress, isNeedLowerLoad, GMSiteFlag)) {
        //获取当前用户手工设置选择的站点
        canUseAddress = this.useNetAddress;
      } else {
        let QHBestCacheAddress: TKNetAddress | undefined = this.getServerAddress(this.getQHBestCacheServer());
        if (QHBestCacheAddress && QHBestCacheAddress.isJoinConnectSite &&
        this.isCanConnectToServer(QHBestCacheAddress, isNeedLowerLoad, GMSiteFlag)) {
          canUseAddress = QHBestCacheAddress;
        }
      }
    }
    return canUseAddress;
  }

  /**
   * 获取手动站点缓存策略下可用的地址
   * @param isNeedLowerLoad 是否需要满足低负载策略
   * @param isNeedGMSite 是否需要满足国密站点
   */
  private getTKBestCacheCanUseServer(isNeedLowerLoad: boolean, GMSiteFlag: string) {
    let canUseAddress: TKNetAddress | undefined = undefined;
    if (TKGatewayManager.shareInstance().isServerUseAddressManualMode(this.gateWayName)) {
      //先判断手工缓存的最优地址，如果有，然后判断是否存活，如果存活就用，不然就继续地址筛选逻辑
      let cacheAddress: string = TKGatewayManager.shareInstance().getCacheNoExpireServerUseAddress(this.gateWayName);
      let TKBestCacheAddress: TKNetAddress | undefined = this.getServerAddress(cacheAddress);
      if (TKBestCacheAddress && TKBestCacheAddress.isJoinConnectSite &&
      this.isCanConnectToServer(TKBestCacheAddress, isNeedLowerLoad, GMSiteFlag)) {
        canUseAddress = TKBestCacheAddress;
      }
    }
    return canUseAddress;
  }

  /**
   * 执行分组策略,获取分组内的可用站点
   */
  private getCanUseServerByGroupPolicy(isNeedLowerLoad: boolean, GMSiteFlag: string): TKNetAddress | undefined {
    let canUseAddress: TKNetAddress | undefined = undefined;
    if (this.netDomainArrGroupMap.size > 1 || this.netAddressArrGroupMap.size > 1) {
      //没有，先获取当前用户选择的站点，然后根据分组找到备用站点，再执行负载策略
      if (!canUseAddress) {
        if (this.useNetAddress) {
          canUseAddress = this.getCanUseServerByGroupId(this.useNetAddress.groupId, isNeedLowerLoad, GMSiteFlag);
        }
      }
      //没有，先获取当前站点，然后根据分组找到备用站点，再执行负载策略
      if (!canUseAddress) {
        if (this.curNetAddress) {
          canUseAddress = this.getCanUseServerByGroupId(this.curNetAddress.groupId, isNeedLowerLoad, GMSiteFlag);
        }
      }
      //没有，先执行获取分组的策略，获取到分组ID以后根据分组找到备用站点，再执行负载策略
      if (!canUseAddress) {
        let canUseServerGroupId: string = this.getCanUseServerGroupId(isNeedLowerLoad, GMSiteFlag);
        if (canUseServerGroupId) {
          canUseAddress = this.getCanUseServerByGroupId(canUseServerGroupId, isNeedLowerLoad, GMSiteFlag);
        }
      }
    }
    return canUseAddress;
  }

  /**
   * 执行分组策略,获取分组ID
   */
  private getCanUseServerGroupId(isNeedLowerLoad: boolean, GMSiteFlag: string): string {
    let canUseServerGroupId: string =
      this.getCanUseServerAddressGroupId(this.netDomainArr, isNeedLowerLoad, GMSiteFlag);
    if (!canUseServerGroupId) {
      canUseServerGroupId = this.getCanUseServerAddressGroupId(this.netAddressArr, isNeedLowerLoad, GMSiteFlag);
    }
    return canUseServerGroupId;
  }

  /**
   * 执行分组策略,获取分组ID
   */
  private getCanUseServerAddressGroupId(netAddresses: Array<TKNetAddress>, isNeedLowerLoad: boolean,
    GMSiteFlag: string): string {
    let canUseServerGroupId: string = "";
    let fastCanUseServerGroupId: string = "";
    let canUseServerGroupIdList: Array<string> = new Array<string>();
    if (netAddresses && netAddresses.length > 0) {
      let i: number = this.getRandomIndexForArrayList(netAddresses);
      let fastSpeedAddress: TKNetAddress = netAddresses[i];
      fastCanUseServerGroupId = fastSpeedAddress.groupId;
      for (let netAddress of netAddresses) {
        if (netAddress.isJoinConnectSite && this.isCanConnectToServer(netAddress, isNeedLowerLoad, GMSiteFlag)) {
          let groupId: string = netAddress.groupId;
          if (canUseServerGroupIdList.indexOf(groupId) < 0) {
            canUseServerGroupIdList.push(groupId);
          }
          if (this.LBGroupMode == LBMODE.BEST) {
            if (fastSpeedAddress.speed > netAddress.speed && netAddress.speed > 0) {
              fastSpeedAddress = netAddress;
              fastCanUseServerGroupId = fastSpeedAddress.groupId;
            }
          } else if (this.LBGroupMode == LBMODE.FZBEST) {
            let fastSpeedAddressServerScore: number =
              (fastSpeedAddress.serverScore > 0 && fastSpeedAddress.serverScore < 1) ? fastSpeedAddress.serverScore :
                1.0;
            let netAddressServerScore: number =
              (netAddress.serverScore > 0 && netAddress.serverScore < 1) ? netAddress.serverScore : 1.0;
            let fastSpeedAddressSpeed: number = fastSpeedAddress.speed * fastSpeedAddressServerScore;
            let netAddressSpeed: number = netAddress.speed * netAddressServerScore;
            if (fastSpeedAddressSpeed > netAddressSpeed && netAddressSpeed > 0) {
              fastSpeedAddress = netAddress;
              fastCanUseServerGroupId = fastSpeedAddress.groupId;
            }
          }
        }
      }
    }
    if (canUseServerGroupIdList.length > 0) {
      if (canUseServerGroupIdList.length > 1) {
        if (this.LBGroupMode == LBMODE.RANDOM) {
          //随机模式
          let i: number = this.getRandomIndexForArrayList(canUseServerGroupIdList);
          canUseServerGroupId = canUseServerGroupIdList[i];
        } else if (this.LBGroupMode == LBMODE.BACKUPS) {
          //主备模式
          canUseServerGroupId = canUseServerGroupIdList[0];
        } else if (this.LBGroupMode == LBMODE.LOOP) {
          //顺序模式
          let i: number = (this.invokeGroupCount % canUseServerGroupIdList.length);
          this.invokeGroupCount = i + 1;
          canUseServerGroupId = canUseServerGroupIdList[i];
        } else if (this.LBGroupMode == LBMODE.BEST || this.LBGroupMode == LBMODE.FZBEST) {
          //最优模式
          canUseServerGroupId = fastCanUseServerGroupId;
        }
      } else {
        canUseServerGroupId = canUseServerGroupIdList[0];
      }
    }
    return canUseServerGroupId;
  }

  /**
   * 执行分组策略,根据分组ID获取分组内的可用站点
   */
  private getCanUseServerByGroupId(groupId: string, isNeedLowerLoad: boolean,
    GMSiteFlag: string): TKNetAddress | undefined {
    let canUseAddress: TKNetAddress | undefined = undefined;
    if (this.netDomainArrGroupMap.size > 1 || this.netAddressArrGroupMap.size > 1) {
      let netAddressByGroupIdDomain: Array<TKNetAddress> | undefined =
        TKMapHelper.getObject(this.netDomainArrGroupMap, groupId);
      let netAddressByGroupIdIP: Array<TKNetAddress> | undefined =
        TKMapHelper.getObject(this.netAddressArrGroupMap, groupId);
      if (!canUseAddress && netAddressByGroupIdDomain) {
        canUseAddress = this.getCanUseServer(netAddressByGroupIdDomain, isNeedLowerLoad, GMSiteFlag);
      }
      if (!canUseAddress && netAddressByGroupIdIP) {
        canUseAddress = this.getCanUseServer(netAddressByGroupIdIP, isNeedLowerLoad, GMSiteFlag);
      }
    }
    return canUseAddress;
  }

  /**
   * 执行云服务器分组策略,获取分组内的可用站点
   */
  private getCanUseServerByCloudPolicy(isNeedLowerLoad: boolean, GMSiteFlag: string): TKNetAddress | undefined {
    let canUseAddress: TKNetAddress | undefined = undefined;
    if (this.LBPolicy == LBPOLICY.CLOUD) {
      if (this.useNetAddress) {
        //获取当前用户手工设置选择的站点
        if (this.isCanConnectToServer(this.useNetAddress, isNeedLowerLoad, GMSiteFlag)) {
          canUseAddress = this.useNetAddress;
        }
        //没有，先获取当前站点，获取到当前站点以后根据分组找到备用站点，再执行负载策略
        if (!canUseAddress) {
          canUseAddress = this.getCanUseServerByGroupId(this.useNetAddress.groupId, isNeedLowerLoad, GMSiteFlag);
        }
        //没有，先获取最快站点，获取到最快站点以后根据分组找到备用站点，再执行负载策略
        if (!canUseAddress) {
          canUseAddress = this.useNetAddress;
        }
      }
    }
    return canUseAddress;
  }

  /**
   * 获取可用地址，根据策略LMode
   */
  private getCanUseServer(netAddresses: Array<TKNetAddress>, isNeedLowerLoad: boolean, GMSiteFlag: string) {
    let tempList: Array<TKNetAddress> = new Array<TKNetAddress>();
    if (netAddresses && netAddresses.length > 0) {
      for (let netAddress of netAddresses) {
        if (netAddress.isJoinConnectSite && this.isCanConnectToServer(netAddress, isNeedLowerLoad, GMSiteFlag)) {
          tempList.push(netAddress);
        }
      }
    }
    if (tempList.length > 0) {
      let i: number = 0;
      if (tempList.length > 1) {
        if (this.LBMode == LBMODE.RANDOM) {
          //随机模式
          i = this.getRandomIndexForArrayList(tempList);
        } else if (this.LBMode == LBMODE.BACKUPS) {
          //主备模式
          i = 0;
        } else if (this.LBMode == LBMODE.LOOP) {
          //顺序模式
          i = (this.invokeCount % tempList.length);
          this.invokeCount = i + 1;
        } else if (this.LBMode == LBMODE.BEST) {
          //最优模式
          i = this.getRandomIndexForArrayList(tempList);
          let fastSpeedAddress: TKNetAddress = tempList[i];
          for (let index = 0; index < tempList.length; index++) {
            let netAddress: TKNetAddress = tempList[index];
            if (fastSpeedAddress.speed > netAddress.speed && netAddress.speed > 0) {
              fastSpeedAddress = netAddress;
              i = index;
            }
          }
        } else if (this.LBMode == LBMODE.FZBEST) {
          //负载最优模式
          i = this.getRandomIndexForArrayList(tempList);
          let fastSpeedAddress: TKNetAddress = tempList[i] as TKNetAddress;
          for (let index = 0; index < tempList.length; index++) {
            let netAddress: TKNetAddress = tempList[index];
            let fastSpeedAddressServerScore: number =
              (fastSpeedAddress.serverScore > 0 && fastSpeedAddress.serverScore < 1) ? fastSpeedAddress.serverScore :
                1.0;
            let netAddressServerScore: number =
              (netAddress.serverScore > 0 && netAddress.serverScore < 1) ? netAddress.serverScore : 1.0;
            let fastSpeedAddressSpeed: number = fastSpeedAddress.speed * fastSpeedAddressServerScore;
            let netAddressSpeed: number = netAddress.speed * netAddressServerScore;
            if (fastSpeedAddressSpeed > netAddressSpeed && netAddressSpeed > 0) {
              fastSpeedAddress = netAddress;
              i = index;
            }
          }
        }
      }
      return tempList[i];
    }
    return undefined;
  }

  /**
   * 获取最终可用地址，根据策略LMode
   */
  private getRmdServerForLast(): TKNetAddress | undefined {
    let canUseAddress: TKNetAddress | undefined = undefined;
    if (this._isUseGMSite) {
      //先判断域名是否配置
      if (!canUseAddress) {
        canUseAddress = this.getCanUseServerForLast(this.netDomainArr, "1");
      }
      //没有配置域名就获取配置的IP
      if (!canUseAddress) {
        canUseAddress = this.getCanUseServerForLast(this.netAddressArr, "1");
      }
    } else {
      //先判断域名是否配置
      if (!canUseAddress) {
        canUseAddress = this.getCanUseServerForLast(this.netDomainArr, "0");
      }
      //没有配置域名就获取配置的IP
      if (!canUseAddress) {
        canUseAddress = this.getCanUseServerForLast(this.netAddressArr, "0");
      }
    }
    return canUseAddress;
  }

  /**
   * 获取最终可用地址，根据策略LMode
   */
  private getRmdServerForLastIgnoreGMFlag(): TKNetAddress | undefined {
    let canUseAddress: TKNetAddress | undefined = undefined;
    //先判断域名是否配置
    if (!canUseAddress) {
      canUseAddress = this.getCanUseServerForLast(this.netDomainArr, "");
    }
    //没有配置域名就获取配置的IP
    if (!canUseAddress) {
      canUseAddress = this.getCanUseServerForLast(this.netAddressArr, "");
    }
    return canUseAddress;
  }

  /**
   * 获取最终可用地址，根据策略LMode
   */
  private getCanUseServerForLast(netAddresses: Array<TKNetAddress>, GMSiteFlag: string): TKNetAddress | undefined {
    let tempList: Array<TKNetAddress> = new Array<TKNetAddress>();
    if (netAddresses && netAddresses.length > 0) {
      for (let address of netAddresses) {
        if (!address.isIPV6 && address.isJoinConnectSite &&
          (TKStringHelper.isNotBlank(GMSiteFlag) ? address.isGMSite == TKDataHelper.getBoolean(GMSiteFlag) : true)) {
          tempList.push(address);
        }
      }
    }
    if (tempList.length > 0) {
      if (this.LBMode == LBMODE.RANDOM) {
        //随机模式
        let i: number = this.getRandomIndexForArrayList(tempList);
        return tempList[i];
      } else if (this.LBMode == LBMODE.BACKUPS) {
        //主备模式
        return tempList[0];
      } else if (this.LBMode == LBMODE.LOOP) {
        //顺序模式
        return tempList[0];
      } else {
        //其他最优测速模式
        //都没有就获取本地缓存的上次使用的地址
        let canUseAddress: TKNetAddress | undefined = this.getLastFastCacheCanUseServer(GMSiteFlag);
        //再没有，就随机获取一个地址
        if (!canUseAddress) {
          let i: number = this.getRandomIndexForArrayList(tempList);
          canUseAddress = tempList[i];
        }
        return canUseAddress;
      }
    }
    return undefined;
  }

  /**
   * 获取上次缓存的最快地址
   */
  private getLastFastCacheCanUseServer(GMSiteFlag: string) {
    let canUseAddress: TKNetAddress | undefined = undefined;
    let fastCacheNetAddressKey: string = `${TKServer.CACHE_FAST_SOCKET_NETADDRESS}_${this.gateWayName}`;
    let fastCacheNetAddress: Map<string, Object> | undefined = TKCacheManager.shareInstance()
      .getFileCacheData(fastCacheNetAddressKey);
    if (fastCacheNetAddress) {
      let cacheFastHost: string = TKMapHelper.getString(fastCacheNetAddress, "host");
      let tempServerAddress: TKNetAddress | undefined = this.getServerAddress(cacheFastHost);
      if (tempServerAddress && tempServerAddress.isJoinConnectSite &&
        (TKStringHelper.isNotBlank(GMSiteFlag) ? tempServerAddress.isGMSite == TKDataHelper.getBoolean(GMSiteFlag) :
          true)) {
        canUseAddress = tempServerAddress;
      }
    }
    return canUseAddress;
  }

  /**
   * 处理连接通知
   */
  private handleNotification(note: TKNotification) {
    let noteName: string = note.name;
    let busClient: TKComBusClient = note.obj as TKComBusClient;
    //连接成功
    if (noteName == TKServer.NOTE_BUSCLIENT_CONNECT) {
      if (busClient && busClient.serverName == this.gateWayName) {
        if (this.LBPolicy == LBPOLICY.FUTURES) {
          let currentDate: Date = new Date();
          let cacheDateTime: string = `${TKDateHelper.formatDate(currentDate)} 20:00:00`;
          let cacheDate: Date = TKDateHelper.parseDateStr(cacheDateTime);
          if (currentDate.getTime() - cacheDate.getTime() >= 0) {
            cacheDate = TKDateHelper.getDateDiffDate(currentDate, -1);
            cacheDateTime = `${TKDateHelper.formatDate(cacheDate)} 20:00:00`;
          }
          let QHBestCacheServer: string = `${cacheDateTime}|${busClient.host}:${busClient.port}:${busClient.groupId}`;
          TKCacheManager.shareInstance().saveFileCacheData(`QHBestServer_${this.gateWayName}`, QHBestCacheServer);
          this.isQHBestCacheAddressFirst = true;
        } else if (TKGatewayManager.shareInstance().isServerUseAddressManualMode(this.gateWayName)) {
          let currentServerUseAddress: string = `${busClient.host}:${busClient.port}:${busClient.groupId}`;
          let cacheServerUseAddress: string = TKGatewayManager.shareInstance()
            .getCacheNoExpireServerUseAddress(this.gateWayName);
          if (TKStringHelper.isNotBlank(cacheServerUseAddress)) {
            if (cacheServerUseAddress != currentServerUseAddress) {
              //切换成自动测试模式
              //[[TKGatewayManager shareInstance]isServerUseAddressManualMode:self.gateWayName isManualMode:NO];
            }
          }
        }
      }
    }
  }

  /**
   * 站点测速
   */
  public startTest(testSpeedMode: TKSocketTestSpeedMode, ipMode: TKIPMode) {
    if (testSpeedMode == TKSocketTestSpeedMode.InitConCurrent) {
      //初始化并行测速
      this.startInitTestSpeed();
    } else if (testSpeedMode == TKSocketTestSpeedMode.InitSerial) {
      //初始化串行测速
      //采用主备模式测速
      if (this.checkDomainThreads && this.checkDomainThreads.length > 0) {
        this.startInitTestDomainSpeed();
      } else {
        this.startInitTestIPSpeed();
      }
    } else {
      // 普通测速请求
      this.startTestSpeed(testSpeedMode, ipMode);
    }
  }

  /**
   * 开始初始化所有地址测速
   */
  private startInitTestSpeed() {
    this.checkDomainResult.resetResult();
    this.checkIPResult.resetResult();
    //采用并发模式测速
    let checkThreads: Array<TKSocketSpeedChecker> = new Array<TKSocketSpeedChecker>();
    if (this.checkDomainThreads && this.checkDomainThreads.length > 0) {
      checkThreads.push(...this.checkDomainThreads);
    }
    if (this.checkIPThreads && this.checkIPThreads.length > 0) {
      checkThreads.push(...this.checkIPThreads);
    }
    if (checkThreads && checkThreads.length > 0) {
      TKLog.debug(`App启动测速[并行测速模式]，站点[${this.gateWayName}]开始测速，共${checkThreads.length}个备选地址(域名共${this.checkDomainThreads.length}个，IP共${this.checkIPThreads.length}个)`);
      for (let socketSpeedChecker of checkThreads) {
        socketSpeedChecker.testSpeedMode = TKSocketTestSpeedMode.InitConCurrent;
        socketSpeedChecker.delegate = this;
        socketSpeedChecker.start();
      }
    }
  }

  /**
   开始初始化对域名进行测速
   @param serverName
   */
  private startInitTestDomainSpeed() {
    this.checkDomainResult.resetResult();
    if (this.checkDomainThreads && this.checkDomainThreads.length > 0) {
      TKLog.debug(`App启动测速[串行测速模式]，站点[${this.gateWayName}]开始域名测速，共${this.checkDomainThreads.length}个备选地址`);
      for (let socketSpeedChecker of this.checkDomainThreads) {
        socketSpeedChecker.testSpeedMode = TKSocketTestSpeedMode.InitSerial;
        socketSpeedChecker.delegate = this;
        socketSpeedChecker.start();
      }
    }
  }

  /**
   开始初始化对IP进行测速
   @param serverName
   */
  private startInitTestIPSpeed() {
    this.checkIPResult.resetResult();
    if (this.checkIPThreads && this.checkIPThreads.length > 0) {
      TKLog.debug(`App启动测速[串行测速模式]，站点[${this.gateWayName}]开始IP测速，共${this.checkDomainThreads.length}个备选地址`);
      for (let socketSpeedChecker of this.checkIPThreads) {
        socketSpeedChecker.testSpeedMode = TKSocketTestSpeedMode.InitSerial;
        socketSpeedChecker.delegate = this;
        socketSpeedChecker.start();
      }
    }
  }

  /**
   * 普通测速模式
   */
  private startTestSpeed(testSpeedMode: TKSocketTestSpeedMode, ipMode: TKIPMode) {
    let checkThreads: Array<TKSocketSpeedChecker> = new Array<TKSocketSpeedChecker>();
    let checkDomainThreadsNum: number = 0;
    let checkIPThreadsNum: number = 0;
    if (this.checkDomainThreads && this.checkDomainThreads.length > 0) {
      for (let domainSocketSpeedChecker of this.checkDomainThreads) {
        if ((ipMode == TKIPMode.IPV6 && domainSocketSpeedChecker.netAddress.isIPV6)
          || (ipMode == TKIPMode.IPV4 && !domainSocketSpeedChecker.netAddress.isIPV6)
          || (ipMode == TKIPMode.ALL)) {
          checkThreads.push(domainSocketSpeedChecker);
          checkDomainThreadsNum++;
        }
      }
    }
    if (this.checkIPThreads && this.checkIPThreads.length > 0) {
      for (let ipSocketSpeedChecker of this.checkIPThreads) {
        if ((ipMode == TKIPMode.IPV6 && ipSocketSpeedChecker.netAddress.isIPV6)
          || (ipMode == TKIPMode.IPV4 && !ipSocketSpeedChecker.netAddress.isIPV6)
          || (ipMode == TKIPMode.ALL)) {
          checkThreads.push(ipSocketSpeedChecker);
          checkIPThreadsNum++;
        }
      }
    }
    if (checkThreads && checkThreads.length > 0) {
      this.checkSpeedResult.totalNum = checkThreads.length;
      this.checkSpeedResult.resetResult();
      TKLog.debug(`App普通测速[模式为${ipMode}]，站点[${this.gateWayName}]开始测速，共${checkThreads.length}个备选地址(域名共${checkDomainThreadsNum}个，IP共${checkIPThreadsNum}个)`);
      for (let socketSpeedChecker of checkThreads) {
        socketSpeedChecker.testSpeedMode = testSpeedMode;
        socketSpeedChecker.delegate = this;
        socketSpeedChecker.start();
      }
    }
  }

  /**
   *  测速结果
   *
   * @param address
   */
  public socketSpeedCheckResult(socketSpeedChecker: TKSocketSpeedChecker) {
    if (socketSpeedChecker.testSpeedMode == TKSocketTestSpeedMode.InitSerial ||
      socketSpeedChecker.testSpeedMode == TKSocketTestSpeedMode.InitConCurrent) {
      //初始化测速
      this.socketSpeedCheckResultForInit(socketSpeedChecker);
    } else {
      // 普通测速
      this.socketSpeedCheckResultForTest(socketSpeedChecker);
    }
  }

  /**
   * 初始化测试处理
   */
  private socketSpeedCheckResultForInit(socketSpeedChecker: TKSocketSpeedChecker) {
    if (socketSpeedChecker.netAddress.isAlive) {
      if (socketSpeedChecker.netAddress.isDomain) {
        this.checkDomainResult.addSuccessNum();
        if (socketSpeedChecker.testSpeedMode == TKSocketTestSpeedMode.InitSerial) {
          TKLog.debug(`App启动测速[串行测速模式]，站点[${this.gateWayName}]域名测速地址(${socketSpeedChecker.netAddress.ip}:${socketSpeedChecker.netAddress.port})成功(共${this.checkDomainResult.totalNum}个备选地址，成功${this.checkDomainResult.successNum}个，失败${this.checkDomainResult.failedNum}个)`);
        } else {
          TKLog.debug(`App启动测速[并行测速模式]，站点[${this.gateWayName}]域名测速地址(${socketSpeedChecker.netAddress.ip}:${socketSpeedChecker.netAddress.port})成功(共${this.checkDomainResult.totalNum}个备选地址，成功${this.checkDomainResult.successNum}个，失败${this.checkDomainResult.failedNum}个)`);
        }
      } else {
        this.checkIPResult.addSuccessNum();
        if (socketSpeedChecker.testSpeedMode == TKSocketTestSpeedMode.InitSerial) {
          TKLog.debug(`App启动测速[串行测速模式]，站点[${this.gateWayName}]IP测速地址(${socketSpeedChecker.netAddress.ip}:${socketSpeedChecker.netAddress.port})成功(共${this.checkIPResult.totalNum}个备选地址，成功${this.checkIPResult.successNum}个，失败${this.checkIPResult.failedNum}个)`);
        } else {
          TKLog.debug(`App启动测速[并行测速模式]，站点[${this.gateWayName}]IP测速地址(${socketSpeedChecker.netAddress.ip}:${socketSpeedChecker.netAddress.port})成功(共${this.checkIPResult.totalNum}个备选地址，成功${this.checkIPResult.successNum}个，失败${this.checkIPResult.failedNum}个)`);
        }
      }
    } else {
      if (socketSpeedChecker.netAddress.isDomain) {
        this.checkDomainResult.addFailedNum();
        if (socketSpeedChecker.testSpeedMode == TKSocketTestSpeedMode.InitSerial) {
          TKLog.debug(`App启动测速[串行测速模式]，站点[${this.gateWayName}]域名测速地址(${socketSpeedChecker.netAddress.ip}:${socketSpeedChecker.netAddress.port})失败(共${this.checkDomainResult.totalNum}个备选地址，成功${this.checkDomainResult.successNum}个，失败${this.checkDomainResult.failedNum}个)`);
        } else {
          TKLog.debug(`App启动测速[并行测速模式]，站点[${this.gateWayName}]域名测速地址(${socketSpeedChecker.netAddress.ip}:${socketSpeedChecker.netAddress.port})失败(共${this.checkDomainResult.totalNum}个备选地址，成功${this.checkDomainResult.successNum}个，失败${this.checkDomainResult.failedNum}个)`);
        }
        if (this.checkDomainResult.failedNum == this.checkDomainResult.totalNum) {
          if (socketSpeedChecker.testSpeedMode == TKSocketTestSpeedMode.InitSerial) {
            //开始测速IP
            this.startInitTestIPSpeed();
          }
        }
      } else {
        this.checkIPResult.addFailedNum();
        if (socketSpeedChecker.testSpeedMode == TKSocketTestSpeedMode.InitSerial) {
          TKLog.debug(`App启动测速[串行测速模式]，站点[${this.gateWayName}]IP测速地址(${socketSpeedChecker.netAddress.ip}:${socketSpeedChecker.netAddress.port})失败(共${this.checkIPResult.totalNum}个备选地址，成功${this.checkIPResult.successNum}个，失败${this.checkIPResult.failedNum}个)`);
        } else {
          TKLog.debug(`App启动测速[并行测速模式]，站点[${this.gateWayName}]IP测速地址(${socketSpeedChecker.netAddress.ip}:${socketSpeedChecker.netAddress.port})失败(共${this.checkIPResult.totalNum}个备选地址，成功${this.checkIPResult.successNum}个，失败${this.checkIPResult.failedNum}个)`);
        }
      }
    }
    //如果域名是通的，并且所有域名的测速包全部回来时候，就代表完成，否则要等域名和IP全部回来才算完成
    let isFinished: boolean = false;
    if (socketSpeedChecker.netAddress.isDomain && socketSpeedChecker.netAddress.isAlive &&
    this.checkDomainResult.isFinished()) {
      isFinished = true;
    } else if (this.checkDomainResult.isFinished() && this.checkIPResult.isFinished()) {
      isFinished = true;
    }
    if (isFinished) {
      if (socketSpeedChecker.testSpeedMode == TKSocketTestSpeedMode.InitSerial) {
        TKLog.debug(`App启动测速[串行测速模式]，站点[${this.gateWayName}]测速完成，备选失败站点(域名共${this.checkDomainResult.failedNum}个，IP共${this.checkIPResult.failedNum}个)`);
      } else {
        TKLog.debug(`App启动测速[并行测速模式]，站点[${this.gateWayName}]测速完成，备选失败站点(域名共${this.checkDomainResult.failedNum}个，IP共${this.checkIPResult.failedNum}个)`);
      }
      this.stopTest();
      if (this.delegate) {
        this.delegate.testSpeedFinishedWithMode(socketSpeedChecker.netAddress.gateWayName,
          socketSpeedChecker.testSpeedMode);
      }
    }
  }

  /**
   * 普通测试处理
   */
  private socketSpeedCheckResultForTest(socketSpeedChecker: TKSocketSpeedChecker) {
    if (socketSpeedChecker.netAddress.isAlive) {
      this.checkSpeedResult.addSuccessNum();
    } else {
      this.checkSpeedResult.addFailedNum();
    }
    if (this.delegate) {
      this.delegate.testSpeedWithMode(socketSpeedChecker.netAddress, socketSpeedChecker.testSpeedMode);
    }
    TKLog.debug(`App普通测速，站点[${socketSpeedChecker.netAddress.gateWayName}]测速地址(${socketSpeedChecker.netAddress.ip}:${socketSpeedChecker.netAddress.port})已经返回，地址是否存活:${socketSpeedChecker.netAddress.isAlive}，地址是否域名:${socketSpeedChecker.netAddress.isDomain}，地址是否IPV6:${socketSpeedChecker.netAddress.isIPV6}，剩余${this.checkSpeedResult.lastNum()}地址尚未返回`);
    if (this.checkSpeedResult.isFinished()) {
      TKLog.debug(`App普通测速，站点[${socketSpeedChecker.netAddress.gateWayName}]测速已经完成，执行上层完成回调动作`);
      this.stopTest();
      if (this.delegate) {
        this.delegate.testSpeedFinishedWithMode(socketSpeedChecker.netAddress.gateWayName,
          socketSpeedChecker.testSpeedMode);
      }
    }
  }

  /**
   * 取消测速
   */
  public stopTest() {
    //采用并发模式测速
    let checkThreads: Array<TKSocketSpeedChecker> = new Array<TKSocketSpeedChecker>();
    if (this.checkDomainThreads && this.checkDomainThreads.length > 0) {
      checkThreads.push(...this.checkDomainThreads);
    }
    if (this.checkIPThreads && this.checkIPThreads.length > 0) {
      checkThreads.push(...this.checkIPThreads);
    }
    if (checkThreads && checkThreads.length > 0) {
      for (let socketSpeedChecker of checkThreads) {
        socketSpeedChecker.stop();
      }
    }
  }

  public dealloc() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }
}
