import { TKCacheManager } from '../../../../../../../util/cache/TKCacheManager';
import { TKDataHelper } from '../../../../../../../util/data/TKDataHelper';
import { TKDateHelper } from '../../../../../../../util/date/TKDateHelper';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKBusClientManager } from '../common/TKBusClientManager';
import { TKComQuotePushManager } from '../../../../../../../../../../Index';
import { TKBusConfig } from './TKBusConfig';
import { TKNetAddress } from './TKNetAddress';
import { TKServer } from './TKServer';

/**
 *  socket网关管理器
 */
export class TKGatewayManager {
  /**
   * 用户使用地址缓存
   */
  private static readonly TK_CACHE_SERVER_USE_ADDRESS_MODE: string = "TKCacheServerUseAddressMode";
  /**
   * 用户使用国密标示
   */
  private static readonly TK_CACHE_SERVER_GLOBAL_GMFLAG: string = "TKCacheServerGlobalGMFlag";
  /**
   *  服务器配置
   */
  private serverMap: Map<string, TKServer> = new Map<string, TKServer>();
  /**
   *  转义服务器配置
   */
  private refServerConfMap: Map<string, TKServer> = new Map<string, TKServer>();
  /**
   *  服务器名称转义映射表
   */
  private refServerNameMap: Map<string, string> = new Map<string, string>();
  /**
   *  推送服务器配置
   */
  private pushServerMap: Map<string, TKServer> = new Map<string, TKServer>();
  /**
   * 单例对象
   */
  private static instance: TKGatewayManager | undefined = undefined;

  private constructor() {
    let config: Map<string, Object> = TKBusConfig.shareInstance().getServerConfig();
    if (config && config.size > 0) {
      config.forEach((value, key) => {
        let serverProp: Map<string, Object> = value as Map<string, Object>;
        let server: TKServer = new TKServer(key, serverProp);
        let ref: string = TKMapHelper.getString(serverProp, "ref");
        if (TKStringHelper.isNotBlank(ref)) {
          this.refServerNameMap.set(key, ref);
          if (TKStringHelper.isNotBlank(server.functionConfig)) {
            this.refServerConfMap.set(key, server);
          }
        } else {
          if (server.getAllNetworkAddresses().length > 0) {
            this.serverMap.set(key, server);
            if (server.version == "20") {
              this.pushServerMap.set(key, server);
            }
          }
        }
      });
    }
  }

  public static shareInstance(): TKGatewayManager {
    if (!TKGatewayManager.instance) {
      TKGatewayManager.instance = new TKGatewayManager();
    }
    return TKGatewayManager.instance;
  }

  /**
   *  获取所有服务网关列表
   */
  public getServers(): Map<string, TKServer> {
    return this.serverMap;
  }

  /**
   *  获取所有的推送服务网关列表
   */
  public getPushServers(): Map<string, TKServer> {
    return this.pushServerMap;
  }

  /**
   *  获取服务器
   *
   * @param serverName 服务器名称
   */
  public getServer(serverName: string): TKServer | undefined {
    let ref: string = TKMapHelper.getString(this.refServerNameMap, serverName);
    if (TKStringHelper.isNotEmpty(ref)) {
      return this.serverMap.get(ref);
    } else {
      return this.serverMap.get(serverName);
    }
  }

  /**
   *  设置服务器需要使用的站点地址
   *
   * @param serverName 服务名称
   * @param address    站点地址
   */
  public setServerUseAddress(serverName: string, address: TKNetAddress | undefined) {
    let server: TKServer | undefined = this.getServer(serverName);
    if (server) {
      if (address) {
        let tempAddress: TKNetAddress | undefined =
          server.getServerAddressWithHostPortGroup(address.ip, address.port, address.groupId);
        if (tempAddress) {
          address = tempAddress;
        }
        address.isAlive = true;
        server.useNetAddress = address;
        server.isQHBestCacheAddressFirst = false;
        if (this.isServerUseAddressManualMode(server.gateWayName)) {
          let bestCacheServer: string = `${address.ip}:${address.port}:${address.groupId}`;
          this.saveCacheServerUseAddress(server.gateWayName, bestCacheServer);
        }
      } else {
        server.useNetAddress = undefined;
      }
      if (server.curNetAddress != server.useNetAddress) {
        if (this.pushServerMap.get(server.gateWayName)) {
          TKComQuotePushManager.shareInstance().restart(server.gateWayName);
        } else {
          TKBusClientManager.shareInstance().restart(server.gateWayName);
        }
      }
    }
  }

  /**
   * 保存手动模式下的站点地址
   */
  private saveCacheServerUseAddress(serverName: string, address: string) {
    let server: TKServer | undefined = this.getServer(serverName);
    if (server) {
      let cacheDate: Date = TKDateHelper.getDateDiffDate(new Date(), -server.saveCacheTime);
      let cacheDateTime: string = TKDateHelper.formatDate(cacheDate, "YYYY-MM-DD HH:mm:ss");
      let cacheAddress: String = `${cacheDateTime}|${address}`;
      TKCacheManager.shareInstance().saveFileCacheData(`TKBestServer_${server.gateWayName}`, cacheAddress);
    }
  }

  /**
   *  获取手动模式下缓存的未过期的站点地址
   */
  public getCacheNoExpireServerUseAddress(serverName: string): string {
    let TKBestServer: string = "";
    let server: TKServer | undefined = this.getServer(serverName);
    if (server) {
      let cacheTKBestServer: string = TKCacheManager.shareInstance()
        .getFileCacheData(`TKBestServer_${server.gateWayName}`) as string;
      if (TKStringHelper.isNotBlank(cacheTKBestServer)) {
        let temp: Array<string> = TKStringHelper.split(cacheTKBestServer, "|");
        if (temp.length == 2) {
          let cacheDate: Date = TKDateHelper.parseDateStr(temp[0]);
          let currentDate: Date = new Date();
          if (currentDate.getTime() < cacheDate.getTime()) {
            TKBestServer = temp[1];
          } else {
            TKCacheManager.shareInstance().deleteFileCacheData(`TKBestServer_${server.gateWayName}`);
            this.setServerUseAddressMode(server.gateWayName, false);
          }
        }
      }
    }
    return TKBestServer;
  }

  /**
   *  获取服务器功能号配置文件，目前是行情类产品在用
   *
   * @param serverName 服务器名称
   *
   * @return
   */
  public getServerFunctionConfig(serverName: string): string {
    let server: TKServer | undefined = this.refServerConfMap.get(serverName);
    if (!server || TKStringHelper.isBlank(server.functionConfig)) {
      server = this.getServer(serverName);
    }
    if (server) {
      return server.functionConfig;
    }
    return "";
  }

  /**
   *  设置服务站点是否开启国密模式,默认是NO，代表不开启
   * @param serverName      服务名称
   * @param isUseGMSite     是否开启国密模式
   */
  public setServerUseAddressGMMode(serverName: string, isUseGMSite: boolean) {
    let server: TKServer | undefined = this.getServer(serverName);
    if (server) {
      let isChangeGMMode: boolean = (server.isUseGMSite != isUseGMSite);
      server.isUseGMSite = isUseGMSite;
      if (isChangeGMMode) {
        if (this.pushServerMap.get(server.gateWayName)) {
          TKComQuotePushManager.shareInstance().delete(serverName);
        } else {
          TKBusClientManager.shareInstance().delete(serverName);
        }
      }
    }
  }

  /**
   * 获取服务站点的是否开启国密模式
   */
  public isServerUseAddressGMMode(serverName: string) {
    let server: TKServer | undefined = this.getServer(serverName);
    if (server) {
      return server.isUseGMSite;
    }
    return false;
  }

  /*
   *  设置服务器是否开启手动保存模式,默认是NO，代表不开启
   *  @param serverName      服务名称
   *  @param isManualMode    是否手动模式
   */
  public setServerUseAddressMode(serverName: string, isManualMode: boolean) {
    let server: TKServer | undefined = this.getServer(serverName);
    if (server) {
      TKCacheManager.shareInstance()
        .saveFileCacheData(`${TKGatewayManager.TK_CACHE_SERVER_USE_ADDRESS_MODE}_${server.gateWayName}`, isManualMode);
      if (isManualMode) {
        let bestCacheServer: string = "";
        if (server.curNetAddress) {
          //首先获取当前正在用的地址
          let curNetAddress: TKNetAddress = server.curNetAddress;
          bestCacheServer = `${curNetAddress.ip}:${curNetAddress.port}:${curNetAddress.groupId}`;
        } else if (server.useNetAddress) {
          //没有的话，就取当前是否是用户选择的地址
          let useNetAddress: TKNetAddress = server.useNetAddress;
          bestCacheServer = `${useNetAddress.ip}:${useNetAddress.port}:${useNetAddress.groupId}`;
        } else {
          //没有的话，就取上次本地缓存的固定站点
          bestCacheServer = this.getCacheNoExpireServerUseAddress(server.gateWayName);
        }
        //获取到站点进行保存，变成固定站点，下次启动继续使用
        if (TKStringHelper.isNotBlank(bestCacheServer)) {
          this.saveCacheServerUseAddress(server.gateWayName, bestCacheServer);
        }
      }
    }
  }

  /**
   *  获取服务器是否开启手动保存模式
   * @param serverName      服务名称
   */
  public isServerUseAddressManualMode(serverName: string): boolean {
    let server: TKServer | undefined = this.getServer(serverName);
    if (server) {
      this.getCacheNoExpireServerUseAddress(server.gateWayName);
      return TKDataHelper.getBoolean(TKCacheManager.shareInstance()
        .getFileCacheData(`${TKGatewayManager.TK_CACHE_SERVER_USE_ADDRESS_MODE}_${server.gateWayName}`));
    }
    return false;
  }

  /**
   * 保存本地国密全局开关
   */
  public saveCacheGlobalGMFlag(GFFlag: string) {
    TKCacheManager.shareInstance().saveFileCacheData(TKGatewayManager.TK_CACHE_SERVER_GLOBAL_GMFLAG, GFFlag);
  }

  /**
   * 获取本地国密全局开关
   */
  public getCacheGlobalGMFlag(): string {
    return TKCacheManager.shareInstance().getFileCacheData(TKGatewayManager.TK_CACHE_SERVER_GLOBAL_GMFLAG) ?? "";
  }
}




