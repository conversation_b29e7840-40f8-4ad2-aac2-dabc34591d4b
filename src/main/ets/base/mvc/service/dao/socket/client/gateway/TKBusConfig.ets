/**
 *  服务器网关配置文件
 */
import { TKFileHelper } from '../../../../../../../util/file/TKFileHelper';
import { TKXMLHelper } from '../../../../../../../util/file/xml/TKXMLHelper';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../../../../../util/system/TKSystemHelper';
import { TKNotificationCenter } from '../../../../../../notification/TKNotificationCenter';

export class TKBusConfig {
  /**
   *  通知更新地址
   */
  private static readonly NOTE_BUSCONFIG_UPDATEURL: string = "note_busConfig_updateUrl";
  /**
   *  更新地址
   */
  private _updateUrl: string = "";
  /**
   *  测速时间
   */
  public speedTime: string = "";
  /**
   *  服务器配置
   */
  private serverConfig: Map<string, Object> = new Map<string, Object>();
  private static instance: TKBusConfig | undefined = undefined;

  private constructor() {
    this.loadConfig();
  }

  public static shareInstance(): TKBusConfig {
    if (!TKBusConfig.instance) {
      TKBusConfig.instance = new TKBusConfig();
    }
    return TKBusConfig.instance;
  }

  /**
   *  加载配置文件
   */
  private loadConfig() {
    //读取配置文件
    let busConfigPath: string = `thinkive/config/${TKSystemHelper.getEnvironment()}/socket/BusConfig.xml`;
    if (TKFileHelper.readFile(busConfigPath).length <= 0) {
      busConfigPath = `thinkive/config/default/socket/BusConfig.xml`;
    }
    let serversElm: Map<string, Object> = TKXMLHelper.readConfigXml(busConfigPath);
    if (serversElm.size > 0) {
      this._updateUrl = TKMapHelper.getString(serversElm, "updateUrl");
      this.speedTime = TKMapHelper.getString(serversElm, "speedTime");
      if (TKStringHelper.isBlank(this.speedTime)) {
        this.speedTime = "1.5";
      }
      let serverElems: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(serversElm, "children");
      if (serverElems) {
        serverElems.forEach(serverElem => {
          let serverId: string = TKMapHelper.getString(serverElem, "id");
          if (TKStringHelper.isNotBlank(serverId)) {
            let ref: string = TKMapHelper.getString(serverElem, "ref");
            let propMap: Map<string, Object> = new Map<string, Object>();
            propMap.set("ref", ref);
            let propElems: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(serverElem, "children");
            if (propElems) {
              propElems.forEach(propElem => {
                let name1: string = TKMapHelper.getString(propElem, "name");
                let value1: string = TKMapHelper.getString(propElem, "value");
                propMap.set(name1, value1);
              });
            }
            this.serverConfig.set(serverId, propMap);
          }
        });
      }
    }
  }

  public set updateUrl(updateUrl: string) {
    this._updateUrl = updateUrl;
    TKNotificationCenter.defaultCenter.postNotificationName(TKBusConfig.NOTE_BUSCONFIG_UPDATEURL, this._updateUrl);
  }

  public get updateUrl() {
    return this._updateUrl;
  }

  /**
   *  获取配置项
   *
   * @param serverName 服务器名称
   * @param key        配置项名称
   * @param defaultValue 默认值
   *
   * @return
   */
  public getStringWithSeverName(serverName: string, key: string, defaultValue: string = ""): string {
    let serverMap: Map<string, Object> | undefined = this.getServerMap(serverName);
    if (serverMap) {
      return TKMapHelper.getString(serverMap, key, defaultValue);
    }
    return defaultValue;
  }

  /**
   *  获取配置项
   *
   * @param serverName 服务器名称
   * @param key        配置项名称
   * @param defaultValue 默认值
   *
   * @return
   */
  public getNumberWithSeverName(serverName: string, key: string, defaultValue: number = 0): number {
    let serverMap: Map<string, Object> | undefined = this.getServerMap(serverName);
    if (serverMap) {
      return TKMapHelper.getNumber(serverMap, key, defaultValue);
    }
    return defaultValue;
  }

  /**
   *  获取服务器的配置
   *
   * @param serverId 服务器标志
   *
   * @return 服务器配置
   */
  public getServerMap(serverId: string): Map<string, Object> | undefined {
    let serverMap: Map<string, Object> | undefined = TKMapHelper.getObject(this.serverConfig, serverId);
    if (serverMap) {
      let ref: string = TKMapHelper.getString(serverMap, "ref");
      if (TKStringHelper.isNotBlank(ref)) {
        serverMap = TKMapHelper.getObject(this.serverConfig, ref);
      }
    }
    return serverMap;
  }

  /**
   *  得到所有服务器配置<serverId:server>
   */
  public getServerConfig(): Map<string, Object> {
    return this.serverConfig;
  }
}