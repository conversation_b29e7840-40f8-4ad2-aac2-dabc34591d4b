/**
 *  输出设置对象
 */
export class TKQuoteOutsetVO {
  /**
   *  名称
   */
  public name: string = "";
  /**
   * json定义的参数别名，默认就是json的参数值
   */
  public aliasName: string = "";
  /**
   *  参数类型
   */
  public typeName: string = "";
  /**
   *  参数类型
   */
  public type: number = 0;
  /**
   *  字段长度
   */
  public length: number = 0;

  public getJsonName(functionMode: number | undefined): string {
    return (functionMode == 2) ? this.aliasName : this.name;
  }
}
