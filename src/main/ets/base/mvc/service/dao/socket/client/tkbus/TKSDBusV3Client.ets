import { TKDataHelper } from '../../../../../../../util/data/TKDataHelper';
import { TKObjectHelper } from '../../../../../../../util/data/TKObjectHelper';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKComBusV3Client } from '../common/TKComBusV3Client';
import { TKDataVO, TKDataVOMode, TKDataVOType } from '../common/TKDataVO';
import { TKAesHelper, TKAesMode } from '../../../../../../../util/crypto/TKAesHelper';
import { TKSm4Helper } from '../../../../../../../util/crypto/TKSm4Helper';
import { TK<PERSON>libHelper } from '../../../../../../../util/crypto/TKZlibHelper';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKWriteDataView } from '../common/TKWriteDataView';
import { TKMd5Helper } from '../../../../../../../util/crypto/TKMd5Helper';
import { TKReadDataView } from '../common/TKReadDataView';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKServer } from '../gateway/TKServer';
import { TKGatewayManager } from '../gateway/TKGatewayManager';
import { TKHexHelper } from '../../../../../../../util/crypto/TKHexHelper';
import { buffer } from '@kit.ArkTS';

/**
 * 思迪版本3.0拉取请求通信层
 */
export class TKSDBusV3Client extends TKComBusV3Client {
  /**
   *  业务请求头长度
   */
  private static readonly TK_REQUEST_HEADLENGTH: number = 86;
  /**
   *  业务响应头长度
   */
  private static readonly TK_RESPONSE_HEADLENGTH: number = 86;
  /**
   *  版本号
   */
  private static readonly TK_VERSION: number = 0x00010000;
  /**
   *  错误码
   */
  private static readonly TK_CODE: number = 0;

  private getTKResponseHeaderLength(): number {
    return TKSDBusV3Client.TK_RESPONSE_HEADLENGTH;
  }

  private getTKRequestHeaderLength(): number {
    return TKSDBusV3Client.TK_REQUEST_HEADLENGTH;
  }

  /**
   *  获取请求对象
   *
   * @param flowNo 流水号
   * @param data   数据
   *
   * @return
   */
  public getSendData(flowNo: string, data: Object, userInfo?: Record<string, Object>): TKDataVO {
    let reqParam: Record<string, Object> = data as Record<string, Object>;
    let reqParamJsonStr: string = TKObjectHelper.toJsonStr(reqParam);
    let bBuffer: Uint8Array = TKDataHelper.stringToUint8Array(reqParamJsonStr, TKDataHelper.CHAR_GBK);
    //构建对象
    let dataVO: TKDataVO = {} as TKDataVO;
    dataVO.flowNo = flowNo;
    dataVO.funcNo = TKMapHelper.getString(reqParam, "funcNo");
    if (userInfo) {
      dataVO.companyId = TKMapHelper.getString(userInfo, "companyId");
      dataVO.systemId = TKMapHelper.getString(userInfo, "systemId");
      let dataType: number = TKMapHelper.getNumber(userInfo, "dataType");
      dataVO.dataType = this.convertToTKBusClientDataVOType(dataType);
      dataVO.channelId = TKMapHelper.getString(userInfo, "channelId");
    }
    dataVO.data = buffer.from(bBuffer).buffer;
    dataVO.dataMode = TKDataVOMode.Business;
    return dataVO;
  }

  /**
   *  处理加工发送数据
   *
   * @param data 数据
   *
   * @return 处理后的结果
   */
  public async processSendData(dataVO: TKDataVO): Promise<ArrayBuffer> {
    if (dataVO.dataMode == TKDataVOMode.Business) {
      let bodyData: Uint8Array = new Uint8Array(dataVO.data);
      let origDataLength: number = bodyData.byteLength;
      switch (dataVO.dataType) {
        case TKDataVOType.AesEncryt: {
          bodyData = await TKAesHelper.dataWithAesEncrypt(bodyData, this.getEncrytKey());
          break;
        }
        case TKDataVOType.SM4Encryt: {
          bodyData = await TKSm4Helper.dataWithSm4Encrypt(bodyData, this.getEncrytKey());
          break;
        }
        case TKDataVOType.Compress: {
          let compressData: Uint8Array = TKZlibHelper.compress(bodyData);
          if (compressData && compressData.length > 0) {
            bodyData = compressData;
          } else {
            TKLog.error(`${this.className}(${this.host}:${this.port})---->压缩失败(业务包流水号:${dataVO.flowNo}),业务包类别:${this.getTag()},业务包数据类型:${dataVO.dataType},业务包功能号:${dataVO.funcNo}`);
            dataVO.dataType = TKDataVOType.Normal;
          }
          break;
        }
        case TKDataVOType.AesEncryt_Compress: {
          bodyData = await TKAesHelper.dataWithAesEncrypt(bodyData, this.getEncrytKey());
          origDataLength = bodyData.byteLength;
          let compressData: Uint8Array = TKZlibHelper.compress(bodyData);
          if (compressData && compressData.length > 0) {
            bodyData = compressData;
          } else {
            TKLog.error(`${this.className}(${this.host}:${this.port})---->压缩失败(业务包流水号:${dataVO.flowNo}),业务包类别:${this.getTag()},业务包数据类型:${dataVO.dataType},业务包功能号:${dataVO.funcNo}`);
            dataVO.dataType = TKDataVOType.AesEncryt;
          }
          break;
        }
        case TKDataVOType.SM4Encryt_Compress: {
          bodyData = await TKSm4Helper.dataWithSm4Encrypt(bodyData, this.getEncrytKey());
          origDataLength = bodyData.byteLength;
          let compressData: Uint8Array = TKZlibHelper.compress(bodyData);
          if (compressData && compressData.length > 0) {
            bodyData = compressData;
          } else {
            TKLog.error(`${this.className}(${this.host}:${this.port})---->压缩失败(业务包流水号:${dataVO.flowNo}),业务包类别:${this.getTag()},业务包数据类型:${dataVO.dataType},业务包功能号:${dataVO.funcNo}`);
            dataVO.dataType = TKDataVOType.SM4Encryt;
          }
          break;
        }
        case TKDataVOType.Compress_AesEncryt: {
          let compressData: Uint8Array = TKZlibHelper.compress(bodyData);
          if (compressData && compressData.length > 0) {
            bodyData = compressData;
          } else {
            TKLog.error(`${this.className}(${this.host}:${this.port})---->压缩失败(业务包流水号:${dataVO.flowNo}),业务包类别:${this.getTag()},业务包数据类型:${dataVO.dataType},业务包功能号:${dataVO.funcNo}`);
            dataVO.dataType = TKDataVOType.AesEncryt;
          }
          bodyData = await TKAesHelper.dataWithAesEncrypt(bodyData, this.getEncrytKey());
          break;
        }
        case TKDataVOType.Compress_SM4Encryt: {
          let compressData: Uint8Array = TKZlibHelper.compress(bodyData);
          if (compressData && compressData.length > 0) {
            bodyData = compressData;
          } else {
            TKLog.error(`${this.className}(${this.host}:${this.port})---->压缩失败(业务包流水号:${dataVO.flowNo}),业务包类别:${this.getTag()},业务包数据类型:${dataVO.dataType},业务包功能号:${dataVO.funcNo}`);
            dataVO.dataType = TKDataVOType.SM4Encryt;
          }
          bodyData = await TKSm4Helper.dataWithSm4Encrypt(bodyData, this.getEncrytKey());
          break;
        }
        default: {
          break;
        }
      }
      //请求体
      if (!bodyData) {
        bodyData = new Uint8Array();
      }
      //计算请求包长度
      let bodySize = bodyData.byteLength;
      //构建消息数据对象
      let bBuffer: TKWriteDataView = new TKWriteDataView(TKSDBusV3Client.TK_REQUEST_HEADLENGTH + bodySize);
      //包标记
      let tag: string = this.getTag();
      bBuffer.putChar(TKWriteDataView.CHAR_GBK, tag, 2);
      //流水号
      let flowNo: number = Number(dataVO.flowNo);
      bBuffer.putInt(flowNo);
      //协议版本号
      let server: TKServer = TKGatewayManager.shareInstance().getServer(this.serverName) as TKServer;
      let serverVersion: string = server.serverVersion;
      let version: number = TKStringHelper.isBlank(serverVersion) ? TKSDBusV3Client.TK_VERSION :
      TKHexHelper.integerFromHexString(serverVersion);
      bBuffer.putInt(version);
      //公司编号
      let companyId: string = dataVO.companyId;
      bBuffer.putChar(TKWriteDataView.CHAR_GBK, companyId, 8);
      //系统编号
      let systemId: string = dataVO.systemId;
      bBuffer.putChar(TKWriteDataView.CHAR_GBK, systemId, 16);
      //数据类型 0表示正常，1表示AES加密，2表示压缩，3表示先AES加密后压缩，4先压缩再AES加密，5表示SM4加密，6表示先SM4加密后压缩，7先压缩再SM4加密
      let dataType: number = dataVO.dataType;
      bBuffer.putByte(dataType);
      //包体长度
      bBuffer.putUInt(bodySize);
      //原始包体长度
      bBuffer.putUInt(origDataLength);
      //分支号
      let branchId: number = Number(dataVO.funcNo.substring(0, 1));
      bBuffer.putShort(branchId);
      //功能号
      let commandId: number = Number(dataVO.funcNo);
      bBuffer.putUInt(commandId);
      //错误号
      let code: number = TKSDBusV3Client.TK_CODE;
      bBuffer.putInt(code);
      //包体数据格式（默认json,1:json、2：二进制）
      let bodyType: number = 1;
      bBuffer.putByte(bodyType);
      //请求包签名
      let encrytData: Uint8Array = TKDataHelper.stringToUint8Array(this.getEncrytKey(), TKDataHelper.CHAR_GBK);
      let signDataDV: TKWriteDataView = new TKWriteDataView(encrytData.byteLength + bodyData.byteLength);
      signDataDV.putBytes(encrytData);
      signDataDV.putBytes(bodyData);
      let signStr: string = await TKMd5Helper.stringWithMd5(new Uint8Array(signDataDV.buffer));
      signStr = signStr.substring(0, 16);
      let signData: Uint8Array = TKDataHelper.stringToUint8Array(signStr, TKDataHelper.CHAR_GBK);
      bBuffer.putBytes(signData);
      //1：安卓，2：IOS  3：H5 5：鸿蒙
      let deviceType: number = 5;
      bBuffer.putByte(deviceType);
      //渠道编号
      let channelId: number = Number(dataVO.channelId);
      bBuffer.putUShort(channelId);
      //保留字段
      let reserved: string = "";
      bBuffer.putChar(TKWriteDataView.CHAR_GBK, reserved, 13);
      //包体内容
      bBuffer.putBytes(bodyData);
      TKLog.info(`${this.className}(${this.host}:${this.port})---->发送数据包(业务包流水号:${flowNo},业务包类别:${this.getTag()},业务包版本号:${TKHexHelper.hexStringFromInteger(version)},业务包数据类型:${dataVO.dataType},业务包体长度:${bodySize},业务包体原始长度:${origDataLength},业务包渠道号:${channelId},业务包公司号:${companyId},业务包系统号:${systemId},业务包分支号:${branchId},业务包功能号:${commandId})`);
      return bBuffer.buffer;
    }
    return dataVO.data;
  }

  /**
   *  从整个结果数据中截取需要的数据包
   *
   * @param data 数据
   *
   * @return
   */
  public getResultData(data: ArrayBuffer): ArrayBuffer | undefined {
    let responseLength: number = this.getResponseHeaderLength();
    if (data && data.byteLength >= responseLength) {
      let readDataView: TKReadDataView = new TKReadDataView(data);
      let tag: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 2);
      //获取消息类型
      let messageType: number = readDataView.getInt();
      if (tag == TKSDBusV3Client.TH_TAG && messageType < 30) {
        responseLength = this.getCertVitifyResponseHeaderLength();
      }
      if (data.byteLength >= responseLength) {
        //头部最后4位是一个整形，用来存放主体的长度
        let bodyLength: number = 0;
        let packegeLength: number = 0;
        if (tag == TKSDBusV3Client.TH_TAG) {
          readDataView.offset = (responseLength - 4);
          bodyLength = readDataView.getUInt();
          packegeLength = responseLength + bodyLength;
        } else if (tag == this.getTag()) {
          if (data.byteLength >= this.getTKResponseHeaderLength()) {
            readDataView.offset = 35;
            bodyLength = readDataView.getUInt();
            packegeLength = this.getTKResponseHeaderLength() + bodyLength;
          } else {
            return undefined;
          }
        } else {
          return undefined;
        }
        TKLog.debug(`${this.className}(${this.host}:${this.port})---->响应包头:(业务包类别:${tag},已收到数据长度:${data.byteLength},业务包数据长度:${packegeLength},业务包包体长度:${bodyLength})`);
        //截取数据包
        if (data.byteLength >= packegeLength) {
          //打印网络响应包时间
          if (tag == this.getTag()) {
            readDataView.offset = 2;
            let flowNo: number = readDataView.getInt();
            TKLog.debug(`${this.className}(${this.host}:${this.port})---->读取数据包完成---->业务包流水号:${flowNo}`);
          }
          return data.slice(0, packegeLength);
        }
      }
    }
    return undefined;
  }

  /*************************************处理响应数据**************************************/

  /**
   *  转换结果数据到最后的resultVo的数据字典对象
   *
   * @param resultData 结果数据
   *
   * @return
   */
  protected async convertTKResultDataToResultDic(resultData: ArrayBuffer): Promise<Record<string, Object> | undefined> {
    let readDataView: TKReadDataView = new TKReadDataView(resultData);
    /*************************解析包头开始****************************/
    //获取包标记
    let tag: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 2);
    //流水号
    let flowNo: number = readDataView.getInt();
    //获取版本号
    let version: number = readDataView.getInt();
    //公司编号
    let companyId: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 8);
    //系统编号
    let systemId: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 16);
    //数据类型 0表示正常，1表示加密，2表示压缩，3表示先加密后压缩, 4表示先压缩后加密
    let dataType: number = readDataView.getByte();
    //包体长度
    let dataLength: number = readDataView.getUInt();
    //原始包体长度
    let origDataLength: number = readDataView.getUInt();
    //分支号
    let branchId: number = readDataView.getShort();
    //功能号
    let comandId: number = readDataView.getUInt();
    //错误代码
    let code: number = readDataView.getInt();
    //包体数据格式（默认json,1:json、2：二进制）
    let bodyType: number = readDataView.getByte();
    //响应包签名
    let signStr: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 16);
    //备用字段
    let reserved: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 16);
    /*************************解析包头结束****************************/

    //获取包体内容
    let bodyData: Uint8Array = dataLength > 0 ? new Uint8Array(readDataView.getBytes(dataLength)) : new Uint8Array();
    //解析包体
    let messageType: number = comandId;
    TKLog.info(`${this.className}(${this.host}:${this.port})---->读取数据包(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},业务包数据类型:${dataType},业务包数据长度:${resultData.byteLength},业务包包体长度:${bodyData.byteLength})`);

    let result: Record<string, Object> | undefined = undefined;
    try {
      if (bodyData && bodyData.length > 0) {
        //进行验签工作
        let localSignData: Uint8Array = TKDataHelper.stringToUint8Array(this.getEncrytKey(), TKDataHelper.CHAR_GBK);
        let localSignDataView: TKWriteDataView = new TKWriteDataView(localSignData.byteLength + bodyData.byteLength);
        localSignDataView.putBytes(localSignData);
        localSignDataView.putBytes(bodyData);
        localSignData = new Uint8Array(localSignDataView.buffer);
        let localSignStr: string = await TKMd5Helper.stringWithMd5(localSignData);
        localSignStr = localSignStr.substring(0, 16);
        if (localSignStr == signStr) {
          switch (dataType) {
            case TKDataVOType.AesEncryt: {
              bodyData =
                await TKAesHelper.dataWithAesDecrypt(bodyData, this.getEncrytKey(), "", TKAesMode.ECB_NOPADDING);
              if (!bodyData || bodyData.length == 0) {
                TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据AES解密失败)`);
              }
              break;
            }
            case TKDataVOType.SM4Encryt: {
              bodyData = await TKSm4Helper.dataWithSm4Decrypt(bodyData, this.getEncrytKey());
              if (!bodyData || bodyData.length == 0) {
                TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据SM4解密失败)`);
              }
              break;
            }
            case TKDataVOType.Compress: {
              bodyData = TKZlibHelper.unCompress(bodyData);
              if (bodyData.byteLength > origDataLength) {
                bodyData = bodyData.subarray(0, origDataLength);
              }
              if (!bodyData || bodyData.length == 0) {
                TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据解压失败)`);
              }
              break;
            }
            case TKDataVOType.AesEncryt_Compress: {
              bodyData = TKZlibHelper.unCompress(bodyData);
              if (bodyData.byteLength > origDataLength) {
                bodyData = bodyData.subarray(0, origDataLength);
              }
              if (!bodyData || bodyData.length == 0) {
                TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据解压失败)`);
              } else {
                bodyData =
                  await TKAesHelper.dataWithAesDecrypt(bodyData, this.getEncrytKey(), "", TKAesMode.ECB_NOPADDING);
                if (!bodyData || bodyData.length == 0) {
                  TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据AES解密失败)`);
                }
              }
              break;
            }
            case TKDataVOType.SM4Encryt_Compress: {
              bodyData = TKZlibHelper.unCompress(bodyData);
              if (bodyData.byteLength > origDataLength) {
                bodyData = bodyData.subarray(0, origDataLength);
              }
              if (!bodyData || bodyData.length == 0) {
                TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据解压失败)`);
              } else {
                bodyData = await TKSm4Helper.dataWithSm4Decrypt(bodyData, this.getEncrytKey());
                if (!bodyData || bodyData.length == 0) {
                  TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据SM4解密失败)`);
                }
              }
              break;
            }
            case TKDataVOType.Compress_AesEncryt: {
              bodyData =
                await TKAesHelper.dataWithAesDecrypt(bodyData, this.getEncrytKey(), "", TKAesMode.ECB_NOPADDING);
              if (!bodyData || bodyData.length == 0) {
                TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据AES解密失败)`);
              } else {
                bodyData = TKZlibHelper.unCompress(bodyData);
                if (bodyData.byteLength > origDataLength) {
                  bodyData = bodyData.subarray(0, origDataLength);
                }
                if (!bodyData || bodyData.length == 0) {
                  TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据解压失败)`);
                }
              }
              break;
            }
            case TKDataVOType.Compress_SM4Encryt: {
              bodyData = await TKSm4Helper.dataWithSm4Decrypt(bodyData, this.getEncrytKey());
              if (!bodyData || bodyData.length == 0) {
                TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据SM4解密失败)`);
              } else {
                bodyData = TKZlibHelper.unCompress(bodyData);
                if (bodyData.byteLength > origDataLength) {
                  bodyData = bodyData.subarray(0, origDataLength);
                }
                if (!bodyData || bodyData.length == 0) {
                  TKLog.error(`${this.className}(${this.host}:${this.port})---->读取数据异常(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},数据解压失败)`);
                }
              }
              break;
            }
            default: {
              break;
            }
          }
          let jsonStr: string = TKDataHelper.uint8ArrayToString(bodyData, TKDataHelper.CHAR_GBK);
          TKLog.debug(`${this.className}(${this.host}:${this.port})---->读取数据结果(业务包流水号:${flowNo},业务包类别:${tag},业务包功能号:${messageType},结果:${jsonStr})`);
          if (TKStringHelper.isBlank(jsonStr)) {
            jsonStr = TKDataHelper.uint8ArrayToString(bodyData);
            jsonStr = TKStringHelper.replace(jsonStr, "\n", "\\n", false);
          }
          jsonStr = this.processOtherChineseChar(jsonStr);
          result = JSON.parse(jsonStr) as Record<string, Object>
        } else {
          result = { "error_type": 1, "error_no": -997, "error_info": "服务器数据验签失败！" };
        }
      }
    } catch (error) {
      result = { "error_type": 1, "error_no": -998, "error_info": error.message };
    }
    let resultDic: Record<string, Object> = {
      "flowNo": flowNo,
      "extend": `${tag}|${messageType}`
    }
    if (!result || Object.entries(result).length == 0) {
      result = { "error_type": 1, "error_no": -999, "error_info": "服务器返回的数据格式错误！" };
    }
    TKObjectHelper.assign(resultDic, result);
    return resultDic;
  }
}