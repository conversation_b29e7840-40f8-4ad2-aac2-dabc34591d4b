import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKNetworkListener } from '../../../../../../network/listener/TKNetworkListener';
import { TKBusConfig } from './TKBusConfig';
import { TKGatewayManager } from './TKGatewayManager';
import { TKNetAddress } from './TKNetAddress';
import { LBMODE, TKIPMode, TKServer, TKSocketSpeedServerDelegate, TKSocketTestSpeedMode } from './TKServer';

/**
 *  网络监听代理
 */
export interface TKSocketServerTestSpeedDelegate {

  /**
   *  测速结果
   *
   * @param address
   */
  testSpeed?: (address: TKNetAddress) => void;

  /**
   *  测速结束
   *
   * @param address
   */
  testSpeedFinished?: (serverName: string) => void;
}

/**
 * 启动监听回调
 */
export type TKGatewayListenerStartFinishCallBack = () => void;

/**
 *  网关监听器
 */
export class TKGatewayListener implements TKSocketSpeedServerDelegate {
  /**
   *  检测结束的站点集合
   */
  private checkFinishedSet: Set<string> = new Set<string>();
  /**
   *  运行状态
   */
  private _isRunning: boolean = false;
  /**
   *  网络测速代理
   */
  public delegate: TKSocketServerTestSpeedDelegate | undefined = undefined;
  /**
   * 启动监听回调
   */
  private gatewayListenerStartFinishCallBack: TKGatewayListenerStartFinishCallBack | undefined = undefined;
  /**
   * 单例对象
   */
  private static instance: TKGatewayListener | undefined = undefined;

  public static shareInstance(): TKGatewayListener {
    if (!TKGatewayListener.instance) {
      TKGatewayListener.instance = new TKGatewayListener();
    }
    return TKGatewayListener.instance;
  }

  private excuteGatewayListenerStartFinishCallBack() {
    if (this.gatewayListenerStartFinishCallBack) {
      this.gatewayListenerStartFinishCallBack();
      this.gatewayListenerStartFinishCallBack = undefined;
    }
  }

  /**
   *  启动监听
   */
  public start(gatewayListenerStartFinishCallBack: TKGatewayListenerStartFinishCallBack) {
    this.gatewayListenerStartFinishCallBack = gatewayListenerStartFinishCallBack;
    if (!this._isRunning) {
      this._isRunning = true;
      if (!TKNetworkListener.shareInstance().isNetAvailable()) {
        this.excuteGatewayListenerStartFinishCallBack();
        return;
      }
      this.startAppStartUpCheckSpeed();
      let speedTime: number = Number(TKBusConfig.shareInstance().speedTime);
      if (speedTime > 0) {
        setTimeout(() => {
          this.excuteGatewayListenerStartFinishCallBack();
        }, speedTime * 1000);
      } else {
        this.excuteGatewayListenerStartFinishCallBack();
      }
    }
  }

  /**
   *  APP初始化启动某个服务的测速监听
   */
  private startAppStartUpCheckSpeed() {
    this.checkFinishedSet.clear();
    let serverMap: Map<string, Object> = TKGatewayManager.shareInstance().getServers();
    if (serverMap && serverMap.size > 0) {
      TKLog.debug("开始App启动测速...");
      serverMap.forEach((value, key) => {
        let server: TKServer = value as TKServer;
        if (server.getAllNetworkAddresses().length == 1 || server.LBMode == LBMODE.NONE) {
          let netAddresses: Array<TKNetAddress> = server.getAllNetworkAddresses();
          for (let address of netAddresses) {
            address.isAlive = true;
          }
          TKLog.debug(`App启动测速，站点[${server.gateWayName}]共1个备选地址无需测速`);
          this.checkFinishedSet.add(key);
        } else {
          server.delegate = this;
          server.startTest(TKSocketTestSpeedMode.InitConCurrent, TKIPMode.ALL);
        }
      });
      if (this.checkFinishedSet.size == serverMap.size) {
        TKLog.debug("App启动测速完成...");
        this.checkFinishedSet.clear();
        this.excuteGatewayListenerStartFinishCallBack();
      }
    }
  }

  /**
   *  启动某个服务的监听
   */
  public startTest(serverName: string, ipMode: TKIPMode = TKIPMode.ALL) {
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(serverName);
    if (server) {
      server.delegate = this;
      server.startTest(TKSocketTestSpeedMode.View, ipMode);
    }
  }

  /**
   *  停止监听
   */
  public stop() {
    if (this._isRunning) {
      let serverMap: Map<string, TKServer> = TKGatewayManager.shareInstance().getServers();
      if (serverMap && serverMap.size > 0) {
        serverMap.forEach((value, key) => {
          this.stopTest(key);
        });
      }
      this._isRunning = false;
    }
  }

  /**
   *  停止某个服务的测速监听
   */
  public stopTest(serverName: string) {
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(serverName);
    if (server) {
      server.stopTest();
    }
  }

  /**
   *  测速结果
   *
   * @param address
   */
  public testSpeedWithMode(address: TKNetAddress, testSpeedMode: TKSocketTestSpeedMode) {
    if (testSpeedMode == TKSocketTestSpeedMode.View) {
      if (this.delegate && this.delegate.testSpeed) {
        this.delegate.testSpeed(address);
      }
    }
  }

  /**
   *  测速结束
   *
   * @param address
   */
  public testSpeedFinishedWithMode(serverName: string, testSpeedMode: TKSocketTestSpeedMode) {
    if (testSpeedMode == TKSocketTestSpeedMode.InitSerial || testSpeedMode == TKSocketTestSpeedMode.InitConCurrent) {
      //APP启动的第一次测速会卡UI，这个操作是为了尽量减少卡UI的时间
      this.checkFinishedSet.add(serverName);
      let serverMap: Map<string, TKServer> = TKGatewayManager.shareInstance().getServers();
      if (this.checkFinishedSet.size == serverMap.size) {
        TKLog.debug("App启动测速完成...");
        this.excuteGatewayListenerStartFinishCallBack();
      }
    } else if (testSpeedMode == TKSocketTestSpeedMode.View) {
      if (this.delegate && this.delegate.testSpeedFinished) {
        this.delegate.testSpeedFinished(serverName);
      }
    }
  }

  /**
   *  是否在运行
   */
  public get isRunning() {
    return this._isRunning;
  }
}


