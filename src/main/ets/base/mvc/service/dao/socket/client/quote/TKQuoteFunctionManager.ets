/**
 * 行情功能号解析管理器
 */
import { TKFileHelper } from '../../../../../../../util/file/TKFileHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../../../../../util/system/TKSystemHelper';
import { TKGatewayManager } from '../gateway/TKGatewayManager';
import { TKQuoteDomainVO } from './TKQuoteDomainVO';

export class TKQuoteFunctionManager {
  /**
   *  函数域映射Map
   */
  private domianMap: Map<string, TKQuoteDomainVO> = new Map<string, TKQuoteDomainVO>();
  /**
   * 单例对象
   */
  private static instance: TKQuoteFunctionManager | undefined = undefined;

  private constructor() {
  }

  public static shareInstance(): TKQuoteFunctionManager {
    if (!TKQuoteFunctionManager.instance) {
      TKQuoteFunctionManager.instance = new TKQuoteFunctionManager();
    }
    return TKQuoteFunctionManager.instance;
  }

  /**
   *  获取domain域
   *
   * @param domainName 功能服务域名
   *
   * @return
   */
  public getQuoteDomainVOByName(domainName: string): TKQuoteDomainVO {
    let fileName: string = TKGatewayManager.shareInstance().getServerFunctionConfig(domainName);
    if (TKStringHelper.isBlank(fileName)) {
      fileName = "QuoteFunction.xml";
    }
    fileName = this.getFunctionPath(fileName);
    let quoteDomain: TKQuoteDomainVO | undefined = this.domianMap.get(fileName);
    if (!quoteDomain) {
      quoteDomain = new TKQuoteDomainVO();
      quoteDomain.loadFuncConfig(fileName);
      this.domianMap.set(fileName, quoteDomain);
    }
    return quoteDomain;
  }

  //获取行情接口文件路径
  private getFunctionPath(funcPath: string): string {
    if (TKStringHelper.startsWith(funcPath, "@bundle:")) {
      return funcPath;
    }
    if (TKStringHelper.startsWith(funcPath, "/")) {
      return funcPath.substring(1, funcPath.length);
    }
    if (!funcPath.includes("/")) {
      funcPath = `quote/${funcPath}`;
    }
    let lastFuncPath: string = `thinkive/config/${TKSystemHelper.getEnvironment()}/socket/${funcPath}`;
    if (TKFileHelper.readFile(lastFuncPath).length <= 0) {
      lastFuncPath = `thinkive/config/default/socket/${funcPath}`;
    }
    return lastFuncPath;
  }
}
