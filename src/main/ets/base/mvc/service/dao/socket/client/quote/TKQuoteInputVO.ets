/**
 *  输入参数对象
 */
export class TKQuoteInputVO {
  /**
   *  2进制定义参数名称
   */
  public byteName: string = "";
  /**
   *  json定义的参数名称
   */
  public jsonName: string = "";
  /**
   * json定义的参数别名，默认就是json的参数值
   */
  public aliasName: string = "";
  /**
   *  参数类型
   */
  public typeName: string = "";
  /**
   *  参数类型
   */
  public type: number = 0;
  /**
   *  字段长度
   */
  public length: number = 0;
  /**
   *  默认值
   */
  public defaultValue: string = "";
  /**
   *  是否为空的时候不算到输入流里面
   */
  public excludedNull: boolean = false;

  public getJsonName(functionMode: number | undefined): string {
    return (functionMode == 2) ? this.aliasName : this.jsonName;
  }
}
