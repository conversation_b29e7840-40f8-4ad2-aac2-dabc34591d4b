/**
 * 写缓存区
 */
import { buffer, util } from '@kit.ArkTS';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';

export class TKWriteDataView {
  public static readonly CHAR_GBK: string = "gbk";
  public static readonly CHAR_UTF8: string = "utf-8";
  private dataView: DataView = new DataView(new ArrayBuffer(0));
  public offset: number = 0;
  private littleEndian: boolean = true;
  private gbkEncoder: util.TextEncoder = new util.TextEncoder(TKWriteDataView.CHAR_GBK);
  private utf8Encoder: util.TextEncoder = new util.TextEncoder(TKWriteDataView.CHAR_UTF8);

  public constructor(bufferSize: number, littleEndian: boolean = true) {
    this.dataView = new DataView(new ArrayBuffer(bufferSize));
    this.littleEndian = littleEndian;
  }

  public getFieldBits(length: number): Array<number> {
    let bits: Array<number> = new Array<number>();
    for (let i = 0; i < length; i++) {
      for (let j = 0; j < 8; j++) {
        bits.push(0);
      }
    }
    return bits;
  }

  public bitsToByte(bits: Array<number>): number {
    let byte: number = 0;
    let count: number = bits.length - 1;
    for (let index = count; index >= 0; index--) {
      let value: number = bits[index];
      byte |= value << count - index;
    }
    return byte;
  }

  public putField(value: string, length: number) {
    if (length > 0) {
      let fieldsHeader: Array<number> = this.getFieldBits(length);
      if (TKStringHelper.isNotBlank(value)) {
        let fields: Array<string> = value.split(":");
        for (let i = 0; i < fields.length; i++) {
          let field: number = parseInt(fields[i]);
          if (field > 0) {
            fieldsHeader[field - 1] = 1;
          }
        }
      }
      for (let i = 0; i < length; i++) {
        let bits: Array<number> = fieldsHeader.slice(i * 8, (i + 1) * 8);
        let byte: number = this.bitsToByte(bits);
        this.putByte(byte);
      }
    }
  }

  public putType(value: string, length: number) {
    if (length > 0) {
      let typesHeader: Array<number> = this.getFieldBits(length);
      if (TKStringHelper.isNotBlank(value)) {
        let types: Array<string> = value.split(":");
        for (let i = 0; i < types.length; i++) {
          let type: number = parseInt(types[i]);
          if (type >= 0) {
            typesHeader[type] = 1;
          }
        }
      }
      for (let i = 0; i < length; i++) {
        let bits: Array<number> = typesHeader.slice(i * 8, (i + 1) * 8);
        let byte: number = this.bitsToByte(bits);
        this.putByte(byte);
      }
    }
  }

  public putCFlag(value: string, length: number) {
    let flags: Array<number> = new Array<number>();
    for (let i = 0; i < length; i++) {
      flags.push(0);
    }
    if (TKStringHelper.isNotBlank(value)) {
      let temp: Array<string> = value.split(":");
      for (let j = 0; j < temp.length; j++) {
        let index: number = parseInt(temp[j]) - 1;
        if (index >= 0 && index < length) {
          flags[index] = 1;
        }
      }
    }
    let cFlag: string = flags.join("");
    this.putChar(TKWriteDataView.CHAR_GBK, cFlag, length);
  }

  public putKline(value: string) {
    if (TKStringHelper.isBlank(value)) {
      value = "day";
    }
    let kline: number = 1;
    if (value == "day") {
      kline = 1;
    } else if (value == "week") {
      kline = 2;
    } else if (value == "month") {
      kline = 3;
    }
    this.putInt(kline);
  }

  public putStock(value: string, length: number) {
    let inputs: Array<string> = value.split("|");
    for (let input of inputs) {
      let stock: string = TKStringHelper.replace(input, ":", "");
      this.putChar(TKWriteDataView.CHAR_GBK, stock, length);
    }
  }

  public putStocks(value: string, length: number) {
    if (TKStringHelper.isNotBlank(value)) {
      let inputs: Array<string> = value.split("|");
      for (let input of inputs) {
        let stock: string = TKStringHelper.replace(input, ":", "");
        this.putChar(TKWriteDataView.CHAR_GBK, stock, length);
      }
    }
  }

  public putFloat(value: number) {
    this.dataView.setFloat32(this.offset, value, this.littleEndian);
    this.offset += 4;
  }

  public putDouble(value: number) {
    this.dataView.setFloat64(this.offset, value, this.littleEndian);
    this.offset += 8;
  }

  public putInt(value: number) {
    this.dataView.setInt32(this.offset, value, this.littleEndian);
    this.offset += 4;
  }

  public putUInt(value: number) {
    this.dataView.setUint32(this.offset, value, this.littleEndian);
    this.offset += 4;
  };

  public putChar(charset: string, value: string, length: number) {
    if (length > 0) {
      let bytes: Uint8Array = new Uint8Array();
      if (charset == TKWriteDataView.CHAR_GBK) {
        bytes = this.gbkEncoder.encodeInto(value)
      } else {
        bytes = this.utf8Encoder.encodeInto(value);
      }
      bytes = bytes ?? new Uint8Array();
      let bytesLength: number = bytes.length < length ? bytes.length : length;
      for (let i = 0; i < bytesLength; i++) {
        let byte: number = bytes[i];
        this.putByte(byte);
      }
      while (bytesLength < length) {
        this.putByte(0);
        bytesLength += 1;
      }
    }
  };

  public putBool(value: boolean) {
    this.putByte(value ? 1 : 0);
  };

  public putLong(value: number) {
    let high: number = parseInt(String(value / 4294967296));
    let low: number = (value % 4294967296);
    this.putInt(low);
    this.putInt(high);
  };

  public putULong(value: number) {
    let high: number = parseInt(String(value / 4294967296));
    let low: number = (value % 4294967296);
    this.putUInt(low);
    this.putUInt(high);
  };

  public putShort(value: number) {
    this.dataView.setInt16(this.offset, value, this.littleEndian);
    this.offset += 2;
  };

  public putUShort(value: number) {
    this.dataView.setUint16(this.offset, value, this.littleEndian);
    this.offset += 2;
  };

  public putByte(value: number) {
    this.dataView.setUint8(this.offset, value);
    this.offset += 1;
  };

  public putBytes(value: ArrayBuffer | Uint8Array) {
    if (value && value.byteLength > 0) {
      let dv: DataView = new DataView(buffer.from(value).buffer);
      for (let i = 0; i < dv.byteLength; i++) {
        let byte: number = dv.getUint8(i);
        this.putByte(byte);
      }
    }
  };

  public get buffer(): ArrayBuffer {
    return this.dataView.buffer;
  }

  public get byteLength(): number {
    return this.dataView.byteLength;
  };

  public addOffset(length: number) {
    this.offset += length;
  };

  public isOutBounds(length: number): boolean {
    return ((this.offset + length) > this.dataView.byteLength);
  }

  public get isValidate(): boolean {
    return (this.dataView.byteLength > this.offset);
  };

  public get validateLength(): number {
    return (this.dataView.byteLength - this.offset);
  };
}