/**
 *  网络监听代理
 */
import { TKCacheManager } from '../../../../../../../util/cache/TKCacheManager';
import { TKDataHelper } from '../../../../../../../util/data/TKDataHelper';
import { TKObjectHelper } from '../../../../../../../util/data/TKObjectHelper';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKNumberHelper } from '../../../../../../../util/number/TKNumberHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKNetworkListener } from '../../../../../../network/listener/TKNetworkListener';
import { TKNotification } from '../../../../../../notification/TKNotification';
import { TKNotificationCenter } from '../../../../../../notification/TKNotificationCenter';
import { TKBusClientDelegate, TKBusMsgType } from '../common/TKComBusClient';
import { TKComBusTestSpeedClient, TKSocketMsgType } from '../common/TKComBusTestSpeedClient';
import { TKGatewayManager } from './TKGatewayManager';
import { TKNetAddress } from './TKNetAddress';
import { LBMODE, LBPOLICY, TKSocketTestSpeedMode } from './TKServer';

export interface TKSocketSpeedCheckerDelegate {

  /**
   *  检测结果
   * @param address
   */
  socketSpeedCheckResult: (socketSpeedChecker: TKSocketSpeedChecker) => void;

}

/**
 *心跳检测
 */
export class TKSocketSpeedChecker implements TKBusClientDelegate {
  private static TKFastSocketDic: Map<string, Map<string, Object>> = new Map<string, Map<string, Object>>();
  /**
   本地缓存的最快地址
   */
  private static readonly CACHE_FAST_SOCKET_NETADDRESS: string = "cache_fast_socket_netaddress";
  /**
   *  网络测速代理
   */
  public delegate: TKSocketSpeedCheckerDelegate | undefined = undefined;
  /**
   *  服务器名称
   */
  public gateWayName: string = "";
  /**
   *  服务器地址
   */
  public netAddress: TKNetAddress = new TKNetAddress();
  /**
   * 当前类名
   */
  public className: string = "";
  /**
   *  初始化启动测速标示
   */
  public testSpeedMode: TKSocketTestSpeedMode = TKSocketTestSpeedMode.InitSerial;
  /**
   *  网络是否ok
   */
  private isNetWorkOK: boolean = true;
  /**
   * 测试对象集合
   */
  private testSpeedClients: Array<TKComBusTestSpeedClient> = new Array<TKComBusTestSpeedClient>();

  /**
   *  初始化
   *
   * @param netAddress 地址
   * @param gateWayName 服务器名称
   *
   * @return
   */
  public constructor(netAddress: TKNetAddress, gateWayName: string) {
    this.className = "TKSocketSpeedChecker"
    this.isNetWorkOK = TKNetworkListener.shareInstance().isNetAvailable();
    this.netAddress = netAddress;
    this.gateWayName = gateWayName;
    this.netAddress.speed = Number.MAX_VALUE;
    this.netAddress.time = Number.MAX_VALUE;
    this.netAddress.serverScore = Number.MAX_VALUE;
    TKNotificationCenter.defaultCenter.addObserver(this, this.doNetWorkChange, TKNetworkListener.NOTE_NETWORK_CHANGE);
  }

  /**
   * 初始化Socket
   */
  public initInnerSocket() {
    if (!this.testSpeedClients) {
      this.testSpeedClients = new Array<TKComBusTestSpeedClient>();
    }
  }

  /**
   * 释放Socket
   */
  public releaseInnerSocket() {
    if (this.testSpeedClients) {
      for (let testSpeedClient of this.testSpeedClients) {
        testSpeedClient.disConnect();
        testSpeedClient.releaseCTX();
      }
      this.testSpeedClients.splice(0, this.testSpeedClients.length);
    }
  }

  /**
   *  网络变化
   *
   * @param note
   */
  private doNetWorkChange(note: TKNotification) {
    this.isNetWorkOK = TKDataHelper.getBoolean(note.obj);
    if (!this.isNetWorkOK) {
      this.netAddress.isAlive = false;
    }
    TKLog.info(`${this.className}网关服务器${this.gateWayName}(${this.netAddress.ip}:${this.netAddress.port})重新检测存活状态`);
    this.start();
  }

  /**
   *  开始线程心跳检测
   */
  public start() {
    this.initInnerSocket();
    this.isNetWorkOK = TKNetworkListener.shareInstance().isNetAvailable();
    if (this.isNetWorkOK) {
      if (TKGatewayManager.shareInstance().getCacheGlobalGMFlag() == "0" && this.netAddress.isGMSite) {
        this.netAddress.isAlive = true;
        this.netAddress.time = 0;
        this.netAddress.speed = this.netAddress.time;
        this.releaseInnerSocket();
        this.callbackSocketSpeedCheckResult();
      } else {
        this.check();
      }
    } else {
      this.netAddress.isAlive = false;
      this.netAddress.time = Number.MAX_VALUE;
      this.netAddress.speed = this.netAddress.time;
      this.releaseInnerSocket();
      this.callbackSocketSpeedCheckResult();
    }
  }

  /**
   *  停止线程心跳检测
   */
  public stop() {
    this.releaseInnerSocket();
  }

  /**
   * 进行线程心跳检测逻辑
   */
  private check() {
    this.netAddress.btime = new Date().getTime();
    let testSpeedClient: TKComBusTestSpeedClient = new TKComBusTestSpeedClient();
    this.testSpeedClients.push(testSpeedClient);
    //初始化环境
    testSpeedClient.initCTX();
    //服务名称
    testSpeedClient.serverName = this.gateWayName;
    //请求代理
    testSpeedClient.delegate = this;
    //建立连接
    testSpeedClient.connect(this.netAddress.ip, this.netAddress.port);
  }

  /***************************** socket代理服务方法*****************************/

  /**
   *  处理代理
   *
   * @param msgType
   * @param wparam
   * @param lparam
   */
  public onNotifyMessage(obj: Object, msgType: TKBusMsgType, wparam?: Object, lparam?: Object) {
    let busClient: TKComBusTestSpeedClient = obj as TKComBusTestSpeedClient;
    if (this.testSpeedClients.indexOf(busClient) >= 0) {
      switch (msgType) {
        case TKBusMsgType.Connect: {
          this.onNotifyConnectMessage(busClient);
          break;
        }
        case TKBusMsgType.DisConnect: {
          //流水号
          let flowNo: string = String(wparam ?? "")
          //错误对象
          let error: Record<string, Object> | undefined = lparam as Record<string, Object>;
          this.onNotifyDisConnectMessage(busClient, flowNo, error);
          break;
        }
        case TKBusMsgType.Result: {
          //流水号
          let flowNo: string = String(wparam ?? "")
          //结果集
          let result: Record<string, Object> = (lparam ?? {}) as Record<string, Object>;
          this.onNotifyResultMessage(busClient, flowNo, result);
          break;
        }
        default:
          break;
      }
    }
  }

  /**
   * 处理连接成功的消息
   */
  private onNotifyConnectMessage(busClient: TKComBusTestSpeedClient) {
    TKLog.info(`${this.className}网关服务器${this.gateWayName}(${this.netAddress.ip}:${this.netAddress.port})连接成功`);
    if (this.netAddress.isNeedTestSpeed) {
      //进行测速
      busClient.doSendTestSpeed();
    } else {
      this.netAddress.isAlive = true;
      this.netAddress.time = new Date().getTime() - this.netAddress.btime;
      this.netAddress.speed = this.netAddress.time;
      this.saveFastSocket();
      this.releaseInnerSocket();
      this.callbackSocketSpeedCheckResult();
    }
  }

  /**
   * 处理连接异常断开的消息
   */
  private onNotifyDisConnectMessage(busClient: TKComBusTestSpeedClient, flowNo: string,
    error?: Record<string, Object>) {
    if (error) {
      TKLog.info(`${this.className}网关服务器${this.gateWayName}(${this.netAddress.ip}:${this.netAddress.port})连接异常断开:${TKObjectHelper.toJsonStr(error)}`);
      this.netAddress.isAlive = false;
      if (!this.netAddress.isAlive) {
        TKLog.error(`${this.className}网关服务器${this.gateWayName}(${this.netAddress.ip}:${this.netAddress.port})是否存活=false`);
      }
      this.netAddress.time = new Date().getTime() - this.netAddress.btime;
      this.netAddress.speed = this.netAddress.time;
      this.releaseInnerSocket();
      this.callbackSocketSpeedCheckResult();
    } else {
      TKLog.info(`${this.className}网关服务器${this.gateWayName}(${this.netAddress.ip}:${this.netAddress.port})断开连接`);
    }
  }

  /**
   * 处理请求返回结果
   */
  private onNotifyResultMessage(busClient: TKComBusTestSpeedClient, flowNo: string, result: Record<string, Object>) {
    let data: string = TKMapHelper.getString(result, "data");
    if (Number(flowNo) == TKSocketMsgType.TestSpeedReturn) {
      this.parseTestSpeedData(busClient, data);
    } else if (Number(flowNo) == TKSocketMsgType.TestSpeedConfigReturn) {
      this.parseTestSpeedConfigData(busClient, data);
    }
  }

  /********************消息解析相关方法****************************/

  /**
   *  解析测试速度数据
   *
   * @param data
   */
  private parseTestSpeedData(busClient: TKComBusTestSpeedClient, speedData: string) {
    if (TKStringHelper.isNotBlank(speedData)) {
      this.netAddress.isAlive = true;
      this.netAddress.time = new Date().getTime() - this.netAddress.btime;
      if (speedData == "1" || speedData == "0" || speedData == "-1") {
        this.netAddress.serverScore = Number(speedData);
      } else {
        this.netAddress.serverScore = TKNumberHelper.parsePerNumberStr(speedData);
      }
      if (TKGatewayManager.shareInstance().getServer(this.gateWayName)!.LBPolicy == LBPOLICY.FUTURES) {
        if (this.netAddress.serverScore == 1) {
          this.netAddress.speed = this.netAddress.time;
        } else {
          this.netAddress.speed = Number.MAX_VALUE + this.netAddress.time;
        }
      } else {
        if (TKGatewayManager.shareInstance().getServer(this.gateWayName)!.LBMode == LBMODE.BEST) {
          if (this.netAddress.serverScore == -1) {
            this.netAddress.speed = Number.MAX_VALUE + this.netAddress.time;
          } else {
            this.netAddress.speed = this.netAddress.time;
          }
        } else {
          this.netAddress.speed = this.netAddress.time;
        }
      }
      this.saveFastSocket();
      if (TKGatewayManager.shareInstance().getServer(this.gateWayName)!.isGetSpeedConfig) {
        busClient.doSendTestSpeedConfig();
        return;
      }
    }
    TKLog.info(`${this.className}(${this.netAddress.ip}:${this.netAddress.port})---->测速完成(耗时${this.netAddress.time}毫秒)---->${speedData}`);
    this.releaseInnerSocket();
    this.callbackSocketSpeedCheckResult();
  }

  /**
   * <AUTHOR> 2016-01-12 02:01:15
   *
   *  解析测试配置数据
   *
   * @param data
   */
  private parseTestSpeedConfigData(busClient: TKComBusTestSpeedClient, speedConfigData: string) {
    TKLog.info(`${this.className}(${this.netAddress.ip}:${this.netAddress.port})---->获取测速配置---->${speedConfigData}`);
    if (TKStringHelper.isNotBlank(speedConfigData)) {
      let config: Array<string> = TKStringHelper.split(speedConfigData, "|");
      if (config && config.length > 0) {
        let speedAddConfig: string = config[0];
        if (TKStringHelper.isNotBlank(speedAddConfig)) {
          this.netAddress.speed += Number(speedAddConfig);
        }
      }
    }
    this.releaseInnerSocket();
    this.callbackSocketSpeedCheckResult();
  }

  /**
   * 保存最快站点到缓存
   */
  private saveFastSocket() {
    let isSave: boolean = false;
    let fastCacheNetAddressKey: string = `${TKSocketSpeedChecker.CACHE_FAST_SOCKET_NETADDRESS}_${this.gateWayName}`;
    let fastCacheNetAddress: Map<string, Object> | undefined =
      TKMapHelper.getObject(TKSocketSpeedChecker.TKFastSocketDic, fastCacheNetAddressKey);
    if (!fastCacheNetAddress) {
      fastCacheNetAddress = new Map<string, Object>();
      isSave = true;
    } else {
      let speed: number = TKMapHelper.getNumber(fastCacheNetAddress, "speed");
      if (speed > this.netAddress.speed) {
        isSave = true;
      }
    }
    if (isSave) {
      fastCacheNetAddress.set("host", `${this.netAddress.ip}:${this.netAddress.port}:${this.netAddress.groupId}`);
      fastCacheNetAddress.set("speed", this.netAddress.speed);
      fastCacheNetAddress.set("serverScore", this.netAddress.serverScore);
      TKSocketSpeedChecker.TKFastSocketDic.set(fastCacheNetAddressKey, fastCacheNetAddress);
      TKCacheManager.shareInstance().saveFileCacheData(fastCacheNetAddressKey, fastCacheNetAddress);
    }
  }

  /**
   * 回调测速结果
   */
  private callbackSocketSpeedCheckResult() {
    if (this.delegate) {
      this.delegate.socketSpeedCheckResult(this);
    }
  }

  public dealloc() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }
}