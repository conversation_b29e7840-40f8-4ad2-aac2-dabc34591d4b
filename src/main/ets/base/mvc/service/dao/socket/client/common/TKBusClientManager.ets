import { T<PERSON><PERSON>ata<PERSON>elper } from '../../../../../../../util/data/TKDataHelper';
import { TKObjectHelper } from '../../../../../../../util/data/TKObjectHelper';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKNetworkListener } from '../../../../../../network/listener/TKNetworkListener';
import { TKNotification } from '../../../../../../notification/TKNotification';
import { TKNotificationCenter } from '../../../../../../notification/TKNotificationCenter';
import { TKGatewayListener } from '../gateway/TKGatewayListener';
import { TKGatewayManager } from '../gateway/TKGatewayManager';
import { TKNetAddress } from '../gateway/TKNetAddress';
import { LBMODE, TKServer } from '../gateway/TKServer';
import { TKComQuotePushASClient } from '../quote/push/TKComQuotePushASClient';
import { TKQuoteV3Client } from '../quote/TKQuoteV3Client';
import { TKSDBusV3Client } from '../tkbus/TKSDBusV3Client';
import { TKBusClientDelegate, TKBusClientVersion, TKBusMsgType, TKComBusClient } from './TKComBusClient';

/**
 * BusClient通知处理代理协议
 */
export interface TKBusClientManagerNotificationDelegate {
  /**
   * 处理通知消息
   * @param busClient 长连接对象
   * @param notName   通知名称
   * @param userInfo  携带数据
   */
  onNotifyBusClientNotification(busClient: TKComBusClient, noteName: string,
    userInfo?: Record<string, Object>): void;
}

/**
 *  BusClient管理中心，管理长/短连接
 */
export class TKBusClientManager implements TKBusClientDelegate {
  /**
   失败
   */
  public static readonly NOTE_BUSCLIENT_ERROR: string = "busClientError";
  /**
   成功
   */
  public static readonly NOTE_BUSCLIENT_RESULT: string = "busClientResult";
  /**
   连接/认证
   */
  public static readonly NOTE_BUSCLIENT_CONNECT: string = "busClientConnect";
  /**
   断开
   */
  public static readonly NOTE_BUSCLIENT_DISCONNECT: string = "busClientDisConnect";
  /**
   *  登录成功
   */
  public static readonly NOTE_BUSCLIENT_LOGIN: string = "busClientLogin";
  /**
   *  退出登录
   */
  public static readonly NOTE_BUSCLIENT_LOGOUT: string = "busClientLogout";
  /**
   *  数据推送协议通知
   */
  public static readonly NOTE_BUSCLIENT_PUSH_DATA: string = "note_bus_push_data";
  /**
   *  BusClient通知处理代理协议
   */
  public delegate: TKBusClientManagerNotificationDelegate | undefined = undefined;
  /**
   *  是否网络ok
   */
  public isNetWorkOK: boolean = true;
  /**
   *  服务长连接
   */
  private serverMap: Map<string, TKComBusClient> = new Map<string, TKComBusClient>();
  /**
   *  是否在启动
   */
  private isStartingMap: Map<string, boolean> = new Map<string, boolean>();
  /**
   * 类名称
   */
  protected className: string = "";
  /**
   * 需要延迟释放队列
   */
  private delayCloseBusClientList: Array<TKComBusClient> = new Array<TKComBusClient>();
  /**
   * 单例对象
   */
  private static instance: TKBusClientManager | undefined = undefined;

  public constructor() {
    this.className = this.constructor.name;
    this.isNetWorkOK = TKNetworkListener.shareInstance().isNetAvailable();
    TKNotificationCenter.defaultCenter.addObserver(this, this.doNetWorkChange, TKNetworkListener.NOTE_NETWORK_CHANGE);
  }

  public static shareInstance(): TKBusClientManager {
    if (!TKBusClientManager.instance) {
      TKBusClientManager.instance = new TKBusClientManager();
    }
    return TKBusClientManager.instance;
  }

  /**
   *  网络变化
   *
   * @param note
   */
  private doNetWorkChange(note: TKNotification) {
    this.isNetWorkOK = TKDataHelper.getBoolean(note.obj);
    if (this.isNetWorkOK) {
      if (this.serverMap) {
        this.serverMap.forEach((value, key) => {
          let busClient: TKComBusClient = value as TKComBusClient;
          setTimeout(() => {
            this.start(busClient.serverName);
          }, 1500);
        });
      }
    }
  }

  /**
   * @brief 启动服务
   *
   * @param serverName 服务名称
   */
  public start(serverName: string) {
    try {
      let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(serverName);
      if (server) {
        serverName = server.gateWayName;
        this.isNetWorkOK = TKNetworkListener.shareInstance().isNetAvailable();
        if (this.serverMap.get(serverName) && this.isNetWorkOK) {
          let busClient: TKComBusClient | undefined = this.getTKBusClient(serverName);
          TKLog.info(`${this.className}[${serverName}]服务器,启动断线重连检测机制`);
          if (busClient && !busClient.isConnect && (this.serverMap.get(serverName) == busClient) && this.isNetWorkOK) {
            this.connectServer(busClient, serverName, busClient.isLongConnect);
          }
        }
      }
    } catch (error) {
      TKLog.error(`${this.className}[${serverName}]服务器连接失败:${error.message}`);
    }
  }

  /**
   *  连接服务器
   *
   * @param busClient
   * @param serverName
   */
  private connectServer(busClient: TKComBusClient, serverName: string, isLongConnect: boolean) {
    if (!busClient.isConnecting) {
      //获取服务器配置
      let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(serverName);
      if (server) {
        serverName = server.gateWayName;
        //获取服务器配置
        let address: TKNetAddress | undefined = server.getRmdServer();
        if (address) {
          //初始化环境
          busClient.initCTX();
          //超时时间
          busClient.timeOut = -1;
          //心跳检测间隔时间
          busClient.heartTime = server.heartTime;
          //是否长连接
          busClient.isLongConnect = isLongConnect;
          //服务名称
          busClient.serverName = serverName;
          //请求代理
          busClient.delegate = this;
          //组备注
          busClient.groupId = address.groupId;
          //用户信息
          busClient.userInfo = server.userInfo;
          //实现的socket类
          // busClient.socketImpl = server.socketClassName;
          //是否使用国密的模式
          // busClient.isUseSocketClassName = address.isGMSite;
          //建立连接
          busClient.connect(address.ip, address.port);
        } else {
          TKLog.error(`连接失败:没有配置${serverName}网关服务器列表，请查看BusConfig.xml配置文件！`);
        }
      }
    }
  }

  /**
   *  重启服务
   * @param serverName 服务名称
   */
  public restart(serverName: string) {
    this.stop(serverName);
    this.start(serverName);
  }

  /**
   *  停止服务
   * @param serverName 服务名称
   */
  public stop(serverName: string) {
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(serverName);
    if (server) {
      serverName = server.gateWayName;
      let busClient: TKComBusClient | undefined = this.serverMap.get(serverName);
      if (busClient && busClient.isInitCTX) {
        busClient.disConnect();
        busClient.releaseCTX();
      }
    }
  }


  /**
   *  删除服务
   *
   * @param serverName 服务名称
   */
  public delete(serverName: string) {
    this.delayRemove(serverName);
  }

  /**
   *  延迟删除服务
   *
   * @param serverName 服务名称
   */
  private delayRemove(serverName: string) {
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(serverName);
    if (server) {
      serverName = server.gateWayName;
      let busClient: TKComBusClient | undefined = this.serverMap.get(serverName);
      if (busClient) {
        if (this.delayCloseBusClientList.indexOf(busClient) < 0) {
          this.delayCloseBusClientList.push(busClient);
        }
        this.serverMap.delete(serverName);
        this.clearDelayCloseBusClient(busClient);
      }
    }
  }

  /**
   * 清理延迟关闭的连接对象
   */
  private clearDelayCloseBusClient(busClient: TKComBusClient) {
    if (busClient && busClient.isInitCTX && busClient.reqDataQueue.length == 0 &&
      busClient.writeBufferData.length == 0 && this.delayCloseBusClientList.indexOf(busClient) >= 0) {
      TKLog.info(`${busClient.className}(${busClient.host}:${busClient.port})---->延迟释放连接`);
      busClient.disConnect();
      busClient.releaseCTX();
      let index: number = this.delayCloseBusClientList.indexOf(busClient);
      if (index >= 0) {
        this.delayCloseBusClientList.splice(index, 1);
      }
    }
  }

  /**
   *  获取服务长连接
   *
   * @param serverName 服务名
   * @param version 版本号
   *
   * @return 服务长连接
   */
  public getTKBusClient(serverName: string, isLongConnect?: boolean | undefined): TKComBusClient | undefined {
    //获取服务器配置
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(serverName) as TKServer;
    if (isLongConnect === undefined) {
      isLongConnect = TKDataHelper.getBoolean(server.mode);
    }
    if (server) {
      serverName = server.gateWayName;
      let busClient: TKComBusClient | undefined = this.serverMap.get(serverName);
      if (busClient && isLongConnect) {
        busClient.isLongConnect = isLongConnect;
        if (!busClient.isConnecting) {
          this.connectServer(busClient, serverName, isLongConnect);
        }
        return busClient;
      } else {
        //初始化BusClient
        let version: TKBusClientVersion = TKBusClientVersion.SDBusV3;
        if (server.version == "3") {
          version = TKBusClientVersion.QuoteV3;
        } else if (server.version == "20") {
          version = TKBusClientVersion.QuotePushV3;
        }
        busClient = this.getBusClient(version);
        if (busClient) {
          this.connectServer(busClient, serverName, isLongConnect);
          //存储连接
          if (isLongConnect) {
            this.serverMap.set(serverName, busClient);
          }
        }
        return busClient as TKComBusClient;
      }
    }
    return undefined;
  }

  /**
   *  获取指定名字缓存的长连接对象
   *
   * @param serverName
   *
   * @return
   */
  public getCacheTKBusLongClient(serverName: string): TKComBusClient | undefined {
    let busClient: TKComBusClient | undefined = undefined;
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(serverName);
    if (server) {
      serverName = server.gateWayName;
      busClient = this.serverMap.get(serverName);
      if (!busClient) {
        busClient = this.getTKBusClient(serverName);
      }
    }
    return busClient;
  }


  /**
   *  获取所有缓存的长连接对象
   *
   * @param serverName
   *
   * @return
   */
  public getAllCacheTKBusLongClient(): Map<string, TKComBusClient> {
    return this.serverMap;
  }

  /**
   *  获取连接
   *
   * @param version
   *
   * @return
   */
  private getBusClient(version: TKBusClientVersion): TKComBusClient | undefined {
    let busClient: TKComBusClient | undefined = undefined;
    switch (version) {
      case TKBusClientVersion.QuoteV3:
        busClient = new TKQuoteV3Client();
        break;
      case TKBusClientVersion.SDBusV3:
        busClient = new TKSDBusV3Client();
        break;
      case TKBusClientVersion.QuotePushV3:
        busClient = new TKComQuotePushASClient();
        break;
      default:
        break;
    }
    if (busClient) {
      //版本号
      busClient.version = version;
    }
    return busClient;
  }

  /**
   *  处理代理
   *
   * @param msgType
   * @param wparam
   * @param lparam
   */
  public onNotifyMessage(obj: Object, msgType: TKBusMsgType, wparam?: Object, lparam?: Object) {
    let busClient: TKComBusClient = obj as TKComBusClient;
    switch (msgType) {
      case TKBusMsgType.Connect: {
        this.onNotifyConnectMessage(busClient);
        break;
      }
      case TKBusMsgType.DisConnect: {
        //流水号
        let flowNo: string = String(wparam ?? "")
        //错误对象
        let error: Record<string, Object> | undefined = lparam as Record<string, Object>;
        this.onNotifyDisConnectMessage(busClient, flowNo, error);
        break;
      }
      case TKBusMsgType.Result: {
        //流水号
        let flowNo: string = String(wparam ?? "")
        //结果集
        let result: Record<string, Object> = (lparam ?? {}) as Record<string, Object>;
        this.onNotifyResultMessage(busClient, flowNo, result);
        break;
      }
      case TKBusMsgType.Error: {
        //流水号
        let flowNo: string = String(wparam ?? "")
        //错误对象
        let error: Record<string, Object> | undefined = lparam as Record<string, Object>;
        this.onNotifyErrorMessage(busClient, flowNo, error);
        break;
      }
      default:
        break;
    }
    //清理延迟关闭
    this.clearDelayCloseBusClient(busClient);
  }

  /**
   * 处理连接成功的消息
   */
  protected onNotifyConnectMessage(busClient: TKComBusClient) {
    TKLog.info(`${this.className}(${busClient.host}:${busClient.port})建立连接成功!`);
    //开始心跳
    if (busClient && busClient.isLongConnect) {
      busClient.startHeart();
    }
    //执行代理响应
    if (this.delegate) {
      this.delegate.onNotifyBusClientNotification(busClient, TKBusClientManager.NOTE_BUSCLIENT_CONNECT);
    }
    //联动切换行情推送服务器
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(busClient.serverName);
    if (server) {
      if (TKStringHelper.isNotBlank(server.refGateWayName)) {
        let refServer: TKServer | undefined = TKGatewayManager.shareInstance().getServer(server.refGateWayName);
        if (refServer) {
          let refAddresses: Array<TKNetAddress> = refServer.getAllNetworkAddresses();
          if (refAddresses.length > server.curNetAddressIndex) {
            let refAddress: TKNetAddress = refAddresses[server.curNetAddressIndex];
            TKGatewayManager.shareInstance().setServerUseAddress(refServer.gateWayName, refAddress);
          }
        }
      }
    }
  }

  /**
   * 处理连接异常断开的消息
   */
  protected onNotifyDisConnectMessage(busClient: TKComBusClient, flowNo: string, error: Record<string, Object>) {
    TKLog.error(`${this.className}(${busClient.host}:${busClient.port})连接异常中断:${TKObjectHelper.toJsonStr(error)}`);
    let userInfo: Record<string, Object> = {
      "flowNo": flowNo,
      "errorType": TKMapHelper.getString(error, "error_type"),
      "errorNo": TKMapHelper.getString(error, "error_no"),
      "errorInfo": TKMapHelper.getString(error, "error_info")
    };
    //执行代理响应
    if (this.delegate) {
      this.delegate.onNotifyBusClientNotification(busClient, TKBusClientManager.NOTE_BUSCLIENT_DISCONNECT, userInfo);
    }
    if (busClient && busClient.isLongConnect) {
      this.delete(busClient.serverName);
      let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(busClient.serverName);
      if (server) {
        if (server.getAllNetworkAddresses().length > 1 && server.LBMode != LBMODE.NONE) {
          TKGatewayListener.shareInstance().startTest(busClient.serverName);
        }
        setTimeout(() => {
          this.start(busClient.serverName);
        }, 1500);
      }
    }
  }

  /**
   * 处理请求错误的消息
   */
  protected onNotifyErrorMessage(busClient: TKComBusClient, flowNo: string, error: Record<string, Object>) {
    let userInfo: Record<string, Object> = {
      "flowNo": flowNo,
      "errorType": TKMapHelper.getString(error, "error_type"),
      "errorNo": TKMapHelper.getString(error, "error_no"),
      "errorInfo": TKMapHelper.getString(error, "error_info")
    };
    //执行代理响应
    if (this.delegate) {
      this.delegate.onNotifyBusClientNotification(busClient, TKBusClientManager.NOTE_BUSCLIENT_ERROR, userInfo);
    }
  }

  /**
   * 处理请求返回结果
   */
  protected onNotifyResultMessage(busClient: TKComBusClient, flowNo: string, result: Record<string, Object>) {
    let userInfo: Record<string, Object> = {
      "flowNo": flowNo,
      "result": result
    } as Record<string, Object>;
    //执行代理响应
    if (this.delegate) {
      this.delegate.onNotifyBusClientNotification(busClient, TKBusClientManager.NOTE_BUSCLIENT_RESULT, userInfo);
    }
  }

  public dealloc() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }
}