/**
 *  思迪3.0拉取请求通信层
 */
import { T<PERSON>es<PERSON>elper, TKAesMode } from '../../../../../../../util/crypto/TKAesHelper';
import { TKBase64Helper } from '../../../../../../../util/crypto/TKBase64Helper';
import { TKMd5Helper } from '../../../../../../../util/crypto/TKMd5Helper';
import { TKPasswordGenerator } from '../../../../../../../util/crypto/TKPasswordGenerator';
import { TKRsaHelper } from '../../../../../../../util/crypto/TKRsaHelper';
import { TKSm4Helper } from '../../../../../../../util/crypto/TKSm4Helper';
import { TKDataHelper } from '../../../../../../../util/data/TKDataHelper';
import { TKDeviceHelper } from '../../../../../../../util/dev/TKDeviceHelper';
import { TKFileHelper } from '../../../../../../../util/file/TKFileHelper';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKNetHelper, TKNetworkType } from '../../../../../../../util/net/TKNetHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../../../../../util/system/TKSystemHelper';
import { TKGatewayManager } from '../gateway/TKGatewayManager';
import { TKServer } from '../gateway/TKServer';
import { TKComBusClient } from './TKComBusClient';
import { TKDataVO, TKDataVOMode, TKDataVOType } from './TKDataVO';
import { TKReadDataView } from './TKReadDataView';
import { TKWriteDataView } from './TKWriteDataView';
import { buffer } from '@kit.ArkTS';

/**
 基础请求功能号
 */
export enum TKComBusV3MsgType {
  /**
   *  心跳包
   */
  Heart = 0,

  /**
   *  会话包
   */
  SessionKey = 1,

  /**
   *  连接认证包
   */
  AuthLogin = 2,

  /**
   *  连接认证返回包
   */
  AuthLogin_Return = 3,

  /**
   *  验证客户端证书
   */
  VitifyClientCer = 4,

  /**
   *  验证客户端证书响应包
   */
  VitifyClientCer_Return = 5,

  /**
   *  验证AES加密密钥
   */
  VitifyAesKey = 6,

  /**
   *  验证AES加密密钥响应包
   */
  VitifyAesKey_Return = 7,

  /**
   *  验证SM4加密密钥
   */
  VitifySM4Key = 8,

  /**
   *  验证SM4加密密钥响应包
   */
  VitifySM4Key_Return = 9,

  /**
   *  验证国密客户端证书
   */
  VitifyClientSMCer = 10,

  /**
   *  验证国密客户端证书响应包
   */
  VitifyClientSMCer_Return = 11,

  /**
   * 申请服务器挑战码请求包
   */
  ApplyGameGuardChallenge = 20,

  /**
   *  申请服务器挑战码响应包
   */
  ApplyGameGuardChallenge_Return = 21,

  /**
   *  验证服务器挑战吗请求包
   */
  VitifyGameGuardChallenge = 22
}

export class TKComBusV3Client extends TKComBusClient {
  /**
   包头类型
   */
  protected static readonly TH_TAG: string = "TH";
  /**
   *  认证用户名
   */
  private static readonly TH_USERNAME: string = "thinkive";
  /**
   *  认证密码
   */
  private static readonly TH_PASSWORD: string = "thinkive";
  /**
   *  证书双向认证请求头长度
   */
  private static readonly TH_CERTVITIFY_REQUEST_HEADLENGTH: number = 14;
  /**
   *  证书双向认证响应头长度
   */
  private static readonly TH_CERTVITIFY_RESPONSE_HEADLENGTH: number = 14;
  /**
   *  约定的加密秘钥
   */
  private encrytKey: string = "";
  /**
   *  客户端随机数
   */
  private clientRandomKey: string = "";
  /**
   *  服务器端随机数
   */
  private serverRandomKey: string = "";
  /**
   *  服务端公钥证书路径
   */
  private serverPublicCerFile: string = "";
  /**
   *  客户端公钥证书路径
   */
  private clientPublicCerFile: string = "";
  /**
   *  客户端私钥证书路径
   */
  private clientPrivateCerFile: string = "";
  /**
   *  客户端证书密码
   */
  private clientPassword: string = "";
  /**
   *  是否是加密的CER证书
   */
  private isEncryptCer: boolean = false;
  /**
   *  服务器认证模式（0:不认证，1：密码认证，2：普通证书双向认证，3：普通证书双向认证+国密加密算法，4：国密证书双向认证）
   */
  private vitifyMode: number = 0;

  /*************************************受保护私有方法**************************************/

  /**
   * AES加密key
   */
  protected aesSystemKey(): string {
    return TKPasswordGenerator.shareInstance().generatorPassword();
  }

  /**
   *  过滤生僻字,工具类
   *
   * @param content    字符串
   *
   * @return
   */
  protected processOtherChineseChar(content: string): string {
    content = content.split("碶\\").join("碶");
    content = content.split("昞\\").join("昞");
    content = content.split("錦\\").join("錦");
    return content;
  }

  /**
   * 转换数据类型
   */
  protected convertToTKBusClientDataVOType(dataType: number) {
    let server: TKServer = TKGatewayManager.shareInstance().getServer(this.serverName) as TKServer;
    //服务器认证模式（0:不认证，1：密码认证，2：普通证书双向认证，3：普通证书双向认证+国密加密算法，4：国密证书双向认证）
    this.vitifyMode = TKStringHelper.isNotBlank(server.vityifyMode) ? Number(server.vityifyMode) : 1;
    let dataVOType: TKDataVOType = TKDataVOType.Normal;
    switch (dataType) {
    //正常数据
      case 0: {
        dataVOType = TKDataVOType.Normal;
        break;
      }
    //加密数据
      case 1: {
        dataVOType = (this.vitifyMode == 2) ? TKDataVOType.AesEncryt : TKDataVOType.SM4Encryt;
        break;
      }
    //压缩
      case 2: {
        dataVOType = TKDataVOType.Compress;
        break;
      }
    //先加密后压缩
      case 3: {
        dataVOType = (this.vitifyMode == 2) ? TKDataVOType.AesEncryt_Compress : TKDataVOType.SM4Encryt_Compress;
        break;
      }
    //先压缩后加密
      case 4: {
        dataVOType = (this.vitifyMode == 2) ? TKDataVOType.Compress_AesEncryt : TKDataVOType.Compress_SM4Encryt;
        break;
      }
      default: {
        break;
      }
    }
    return dataVOType;
  }

  /*************************************pragma mark 发送请求**************************************/

  /**
   *  获取服务器业务标示
   *
   * @return
   */
  protected getTag(): string {
    let serverTag: string = "TK";
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(this.serverName);
    if (server && TKStringHelper.isNotBlank(server.serverTag)) {
      serverTag = server.serverTag;
    }
    return serverTag;
  }

  /**
   *  获取AES加密Key
   *
   * @return
   */
  protected getEncrytKey(): string {
    return this.encrytKey;
  }

  /**
   *  构造请求数据
   *
   * @param messageType 消息类型
   * @param bodydata    消息体
   * @param origDataLength    消息体原始长度
   *
   * @return 发送的消息数据
   */
  protected buildTHSendMessage(messageType: string, flowNo: string, bodyData: ArrayBuffer,
    origDataLength: number): ArrayBuffer {
    //请求体
    if (!bodyData) {
      bodyData = new ArrayBuffer(0);
    }
    //计算请求包长度
    let bodySize: number = bodyData.byteLength;
    //构建消息数据对象
    let bBuffer: TKWriteDataView = new TKWriteDataView(this.getCertVitifyResponseHeaderLength() + bodySize);
    //包标记
    bBuffer.putChar(TKWriteDataView.CHAR_GBK, TKComBusV3Client.TH_TAG, 2);
    //消息类型
    bBuffer.putUInt(Number(messageType));
    if (this.vitifyMode >= 2 && Number(messageType) < 30) {
      //原始数据长度
      bBuffer.putUInt(origDataLength);
    }
    //包体长度
    bBuffer.putUInt(bodySize);
    //包体内容
    bBuffer.putBytes(bodyData);
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送数据包(业务包流水号:${flowNo},业务包类别:${TKComBusV3Client.TH_TAG},业务包体长度:${bodySize},业务包功能号:${messageType})`);
    return bBuffer.buffer;
  }

  /**
   *  发送请求信息
   *
   * @param messageType 消息类型
   * @param bodydata    消息体
   * @param origDataLength    消息体原始长度
   */
  protected sendTHMessage(messageType: string, flowNo: string, bodyData: ArrayBuffer, origDataLength: number) {
    let bBuffer: ArrayBuffer = this.buildTHSendMessage(messageType, flowNo, bodyData, origDataLength);
    let dataVO: TKDataVO = {
      flowNo: flowNo,
      funcNo: messageType,
      data: bBuffer,
      dataMode: TKDataVOMode.Connect
    } as TKDataVO;
    this.sendData(dataVO);
  }

  /**
   *  构造请求数据
   *
   * @param messageType 消息类型
   * @param bodydata    消息体
   * @param origDataLength    消息体原始长度
   *
   * @return 发送的消息数据
   */
  protected buildTKSendMessage(messageType: string, flowNo: string, bodyData: ArrayBuffer,
    origDataLength: number): ArrayBuffer {
    return new ArrayBuffer(0);
  }

  /**
   *  发送请求信息
   *
   * @param messageType 消息类型
   * @param bodydata    消息体
   */
  protected sendTKMessage(messageType: string, flowNo: string, bodyData: ArrayBuffer, origDataLength: number) {
    let bBuffer: ArrayBuffer = this.buildTKSendMessage(messageType, flowNo, bodyData, origDataLength);
    let dataVO: TKDataVO = {
      flowNo: flowNo,
      funcNo: messageType,
      data: bBuffer,
      dataMode: TKDataVOMode.Business
    } as TKDataVO;
    this.sendData(dataVO);
  }

  /*************************************重写父类方法**************************************/

  /**
   *  初始化上下文
   */
  public initCTX(): void {
    super.initCTX();
    this.encrytKey = "";
    this.clientRandomKey = `${this.getTag()}${TKStringHelper.randomStr(30)}`;
    this.serverRandomKey = "";
    this.serverPublicCerFile = "";
    this.clientPublicCerFile = "";
    this.clientPrivateCerFile = "";
    this.clientPassword = "";
    this.isEncryptCer = false;
    this.vitifyMode = 1;
  }

  /**
   *  建立连接成功，尚未认证的状态
   */
  private connectTKCertReady(): void {
    super.connectReady();
    let server: TKServer = TKGatewayManager.shareInstance().getServer(this.serverName) as TKServer;
    //服务器公钥证书路径
    this.serverPublicCerFile = server.serverPublicCer;
    //客户端公钥证书路径
    this.clientPublicCerFile = server.clientPublicCer;
    //客户端私钥证书路径
    this.clientPrivateCerFile = server.clientPrivateCer;
    //客户端证书密码
    this.clientPassword = server.clientPassword;
    //是否是加密证书
    this.isEncryptCer = server.isEncryptCer;
    let decryptClientPassword: string = TKAesHelper.stringWithAesDecryptSync(this.clientPassword, this.aesSystemKey());
    if (TKStringHelper.isNotBlank(decryptClientPassword)) {
      this.clientPassword = decryptClientPassword;
    }
  }

  /**
   *  建立连接并且认证成功
   */
  protected connectOk(): void {
    let server: TKServer = TKGatewayManager.shareInstance().getServer(this.serverName) as TKServer;
    //服务器认证模式（0:不认证，1：密码认证，2：普通证书双向认证，3：普通证书双向认证+国密加密算法，4：国密证书双向认证）
    this.vitifyMode = TKStringHelper.isNotBlank(server.vityifyMode) ? Number(server.vityifyMode) : 1;
    switch (this.vitifyMode) {
      case 0: {
        //直接认为服务器认证成功返回
        this.doServerVitifySuccess();
        break;
      }
      case 1: {
        //服务器密码认证
        super.connectReady();
        this.doComBusV3TcpConnnect();
        break;
      }
      case 2: {
        //普通证书双向认证
        this.connectTKCertReady();
        this.doVitifyClientCer();
        break;
      }
      case 3: {
        //普通证书双向认证+国密加密算法
        this.connectTKCertReady();
        this.doVitifyClientCer();
        break;
      }
      default: {
        break;
      }
    }
  }

  /*************************************辅助业务方法**************************************/

  /**
   *  发送心跳检测
   */
  protected async sendHeart() {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送心跳请求包`);
    let messageType: string = String(TKComBusV3MsgType.Heart);
    this.sendTHMessage(messageType, messageType, new ArrayBuffer(0), 0);
  }

  /**
   *  建立TCP连接
   */
  private async doComBusV3TcpConnnect() {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送TH登陆会话Key请求包`);
    let messageType: string = String(TKComBusV3MsgType.SessionKey);
    this.sendTHMessage(messageType, messageType, new ArrayBuffer(0), 0);
  }

  /**
   *  登陆认证
   *
   * @param sessionKey
   */
  private async doComBusV3ClientAuthConnect(sessionKey: string) {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送TH登陆请求包`);
    let server: TKServer = TKGatewayManager.shareInstance().getServer(this.serverName) as TKServer;
    let serverKey: string = server.key;
    let decryptServerKey: string = await TKAesHelper.stringWithAesDecrypt(serverKey, this.aesSystemKey());
    if (TKStringHelper.isNotBlank(decryptServerKey)) {
      serverKey = decryptServerKey;
    }
    let userName: string = TKStringHelper.isBlank(serverKey) ? TKComBusV3Client.TH_USERNAME : serverKey;
    let password: string = TKStringHelper.isBlank(serverKey) ? TKComBusV3Client.TH_PASSWORD : serverKey;
    password = `${sessionKey}${userName}${password}`;
    password = await TKMd5Helper.stringWithMd5(password);
    password = `${password.substring(16, 32)}${password.substring(0, 16)}`;
    password = await TKMd5Helper.stringWithMd5(password);
    password = password.substring(0, 8);
    //服务器网络认证
    let bodyData: TKWriteDataView = new TKWriteDataView(28);
    bodyData.putChar(TKWriteDataView.CHAR_GBK, userName, 20);
    bodyData.putChar(TKWriteDataView.CHAR_GBK, password, 8);
    let messageType: string = String(TKComBusV3MsgType.AuthLogin);
    let origDataLength: number = bodyData.byteLength;
    this.sendTHMessage(messageType, messageType, bodyData.buffer, origDataLength);
  }

  /**
   *  校验客户端证书
   */
  private async doVitifyClientCer() {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送校验客户端证书请求包`);
    //客户端公钥证书二进制
    let clientPublicCerData: Uint8Array = TKFileHelper.readFile(this.clientPublicCerFile);
    if (!clientPublicCerData || clientPublicCerData.length == 0) {
      TKLog.error(`${this.className}(${this.host}:${this.port})---->本地客户端公钥证书不存在！${this.clientPublicCerFile}`);
      return;
    }
    if (this.isEncryptCer) {
      clientPublicCerData = TKBase64Helper.dataWithBase64Decode(clientPublicCerData);
      if (clientPublicCerData && clientPublicCerData.length > 0) {
        clientPublicCerData = await TKAesHelper.dataWithAesDecrypt(clientPublicCerData, this.aesSystemKey());
      }
      if (!clientPublicCerData || clientPublicCerData.length == 0) {
        TKLog.error(`${this.className}(${this.host}:${this.port})---->本地客户端公钥证书不合法！${this.clientPublicCerFile}`);
        return;
      }
    }
    let clientRandomKeyData: Uint8Array = TKDataHelper.stringToUint8Array(this.clientRandomKey, TKDataHelper.CHAR_GBK);
    let clientPublicCerDV: TKWriteDataView =
      new TKWriteDataView(clientPublicCerData.byteLength + clientRandomKeyData.byteLength);
    clientPublicCerDV.putBytes(clientPublicCerData);
    clientPublicCerDV.putBytes(clientRandomKeyData);
    clientPublicCerData = new Uint8Array(clientPublicCerDV.buffer);
    let origDataLength: number = clientPublicCerDV.byteLength;
    let encryptedData: Uint8Array =
      await TKRsaHelper.dataWithRsaPublicCertEncrypt(clientPublicCerData, this.serverPublicCerFile, this.isEncryptCer);
    if (!encryptedData || encryptedData.length == 0) {
      TKLog.error(`${this.className}(${this.host}:${this.port})---->本地服务端公钥证书不存在！${this.serverPublicCerFile}`);
    }
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(this.serverName);
    if (server && server.isSendEncrytBase64Cer) {
      let base64Prex: Uint8Array = TKDataHelper.stringToUint8Array("base64:", TKDataHelper.CHAR_GBK);
      let base64Data: Uint8Array = TKBase64Helper.dataWithBase64Encode(encryptedData);
      let writeDataView: TKWriteDataView = new TKWriteDataView(base64Prex.length + base64Data.length);
      writeDataView.putBytes(base64Prex);
      writeDataView.putBytes(base64Data);
      encryptedData = new Uint8Array(writeDataView.buffer);
    }
    let messageType: string = String(TKComBusV3MsgType.VitifyClientCer);
    this.sendTHMessage(messageType, messageType, buffer.from(encryptedData).buffer, origDataLength);
  }

  /**
   *  校验生成的AES秘钥用来加密服务器端随机数进行验证
   *
   * @param data
   */
  private async doVitifyAesKeyAndServerRandStr() {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送AES校验服务器随机数请求包----->${this.serverRandomKey}`);
    let randData: Uint8Array = TKDataHelper.stringToUint8Array(this.serverRandomKey, TKDataHelper.CHAR_GBK);
    let origDataLength: number = randData.byteLength;
    let encryptedData: Uint8Array = await TKAesHelper.dataWithAesEncrypt(randData, this.encrytKey);
    let messageType: string = String(TKComBusV3MsgType.VitifyAesKey);
    this.sendTHMessage(messageType, messageType, buffer.from(encryptedData).buffer, origDataLength);
  }

  /**
   *  校验生成的SM4秘钥用来加密服务器端随机数进行验证
   *
   * @param data
   */
  private async doVitifySM4KeyAndServerRandStr() {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送SM4校验服务器随机数请求包----->${this.serverRandomKey}`);
    let randData: Uint8Array = TKDataHelper.stringToUint8Array(this.serverRandomKey, TKDataHelper.CHAR_GBK);
    let origDataLength: number = randData.byteLength;
    let encryptedData: Uint8Array = await TKSm4Helper.dataWithSm4Encrypt(randData, this.encrytKey);
    let messageType: string = String(TKComBusV3MsgType.VitifySM4Key);
    this.sendTHMessage(messageType, messageType, buffer.from(encryptedData).buffer, origDataLength);
  }

  /**
   * 申请服务器挑战码请求包
   */
  private async doApplyGameGuardChallenge() {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->申请服务器挑战码请求包`);
    let messageType: string = String(TKComBusV3MsgType.ApplyGameGuardChallenge);
    this.sendTHMessage(messageType, messageType, new ArrayBuffer(0), 0);
  }

  /**
   * 验证服务器挑战码请求包
   */
  private async doVitifyGameGuardChallenge(challenge: string) {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->验证服务器挑战码请求包---->${challenge}`);
    //拼接操作站点信息：应用类型(1:安卓,2:IOS)|应用包名|升级版本名称|升级版本序号|设备UUID|设备MAC地址|设备操作系统版本|IP地址|网络运营商(0:中国移动,1:中国联通,2:中国电信,3:中国铁通)|网络制式(1:2G,2:3G,3:4G,4:wifi)|时间戳|流水号|模块编号
    //应用类型1：安卓，2：IOS
    let channel: string = "2";
    //应用包名
    let soft_no: string = TKSystemHelper.getAppIdentifier();
    //版本名称
    let version_name: string = TKSystemHelper.getVersion();
    //版本序号
    let version_code: string = TKSystemHelper.getVersionCode();
    //设备UUID
    let device_id: string = TKDeviceHelper.getDeviceUUID();
    //设备MAC
    let device_mac: string = TKDeviceHelper.getDeviceMac();
    //操作系统版本
    let device_os_version: string = TKDeviceHelper.getDeviceSysVersion();
    //ip地址
    let ip: string = TKNetHelper.getIP();
    //运营商
    let device_network_operator: string = TKNetHelper.getPhoneOperator();
    //网络制式
    let device_network_type: TKNetworkType = TKNetHelper.getNetworkType();
    let opstation: string =
      `${channel}|${soft_no}|${version_name}|${version_code}|${device_id}|${device_mac}|${device_os_version}|${ip}|${device_network_operator}|${device_network_type}`;
    let sign: string = `${this.encrytKey}${challenge}${opstation}`;
    sign = await TKMd5Helper.stringWithMd5(sign);
    let signData: Uint8Array = TKDataHelper.stringToUint8Array(sign, TKDataHelper.CHAR_GBK);
    let bodyData: TKWriteDataView = new TKWriteDataView(1024 + signData.byteLength);
    bodyData.putChar(TKWriteDataView.CHAR_GBK, opstation, 1024);
    bodyData.putBytes(signData);
    let messageType: string = String(TKComBusV3MsgType.VitifyGameGuardChallenge);
    this.sendTHMessage(messageType, messageType, bodyData.buffer, bodyData.byteLength);
  }

  /*************************************TKBusClientDelegate**************************************/

  /**
   *  获取支持证书认证模式的响应头长度
   *
   * @return
   */
  protected getCertVitifyResponseHeaderLength(): number {
    if (this.vitifyMode >= 2) {
      return TKComBusV3Client.TH_CERTVITIFY_RESPONSE_HEADLENGTH;
    }
    return TKComBusV3Client.TH_RESPONSE_HEADLENGTH;
  }

  /*************************************处理响应数据**************************************/

  /**
   *  转换结果数据到最后的resultVo的数据字典对象
   *
   * @param resultData 结果数据
   *
   * @return
   */
  public async convertResultDataToResultDic(resultData: ArrayBuffer): Promise<Record<string, Object> | undefined> {
    if (resultData && resultData.byteLength > 2) {
      //获取包标记
      let readDataView: TKReadDataView = new TKReadDataView(resultData);
      let tag: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 2);
      if (tag == TKComBusV3Client.TH_TAG) {
        return await this.convertTHResultDataToResultDic(resultData);
      } else {
        return await this.convertTKResultDataToResultDic(resultData);
      }
    }
    return undefined;
  }


  /**
   *  转换结果数据到最后的resultVo的数据字典对象
   *
   * @param resultData 结果数据
   *
   * @return
   */
  protected async convertTHResultDataToResultDic(resultData: ArrayBuffer): Promise<Record<string, Object> | undefined> {
    /*************************解析包头开始****************************/
    let readDataView: TKReadDataView = new TKReadDataView(resultData);
    //获取包标记
    let tag: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 2);
    //获取消息类型
    let messageType: number = readDataView.getInt();
    //获取包体原始长度
    let origDataLen: number = 0;
    if (this.vitifyMode >= 2 && messageType < 30) {
      origDataLen = readDataView.getUInt();
    }
    //获取包体长度
    let bodyLength: number = readDataView.getUInt();
    if (origDataLen == 0) {
      origDataLen = bodyLength;
    }
    /*************************解析包头结束****************************/

    //解析包体
    let bodyData: ArrayBuffer = new ArrayBuffer(0);
    if (bodyLength > 0) {
      bodyData = readDataView.getBytes(bodyLength);
    }
    this.parseTHResultData(messageType, bodyData, origDataLen);
    return undefined;
  }

  /**
   *
   *  解析认证包体
   *
   * @param bodyData 认证包体
   */
  protected parseTHResultData(messageType: number, bodyData: ArrayBuffer, origDataLength: number) {
    if (messageType == TKComBusV3MsgType.ApplyGameGuardChallenge_Return) {
      //申请服务器挑战码返回，返回以后客户端计算挑战吗，然后发给服务器验证
      this.parseApplyGameGuardChallengeReturnData(bodyData, origDataLength);
    } else if (messageType == TKComBusV3MsgType.VitifyClientCer_Return) {
      //验证客户端证书返回，返回以后根据服务器返回的服务器证书进行客户端的解密校验
      this.parseVitifyClientCerReturnData(bodyData, origDataLength);
    } else if (messageType == TKComBusV3MsgType.VitifyClientSMCer_Return) {
      //验证国密客户端证书返回，返回以后根据服务器返回的服务器证书进行客户端的解密校验
    } else if (messageType == TKComBusV3MsgType.VitifyAesKey_Return) {
      //验证服务端随机数返回，返回以后根据服务器返回的客户端的随机数进行客户端Aes解密校验
      this.parseVitifyAesKeyAndServerRandDataReturnData(bodyData, origDataLength);
    } else if (messageType == TKComBusV3MsgType.VitifySM4Key_Return) {
      //验证服务端随机数返回，返回以后根据服务器返回的客户端的随机数进行客户端SM4解密校验
      this.parseVitifySM4KeyAndServerRandDataReturnData(bodyData, origDataLength);
    } else if (messageType == TKComBusV3MsgType.SessionKey) {
      //解析会话包
      if (this.vitifyMode == 1) {
        this.parseSessionKeyData(bodyData);
      }
    } else if (messageType == TKComBusV3MsgType.AuthLogin_Return) {
      //解析登陆包
      this.parseAuthLoginReturnData(bodyData);
    } else if (messageType == TKComBusV3MsgType.Heart) {
      //解析心跳包
    }
  }

  /**
   *  转换结果数据到最后的resultVo的数据字典对象
   *
   * @param resultData 结果数据
   *
   * @return
   */
  protected async convertTKResultDataToResultDic(resultData: ArrayBuffer): Promise<Record<string, Object> | undefined> {
    return undefined;
  }

  /*************************************消息解析相关方法**************************************/

  /**
   * <AUTHOR> 2015-05-11 15:05:53
   *
   *  解析验证客户端证书返回的数据，返回以后根据服务器返回的服务器证书进行客户端的解密校验
   *
   * @param data           数据
   * @param origDataLength 原始长度
   */
  private async parseVitifyClientCerReturnData(data: ArrayBuffer, origDataLength: number) {
    if (data && data.byteLength > 0) {
      let decryptedData: Uint8Array =
        await TKRsaHelper.dataWithRsaPrivateCertDecrypt(new Uint8Array(data), this.clientPrivateCerFile,
          this.clientPassword);
      if (decryptedData && decryptedData.byteLength > 0) {
        TKLog.info(`${this.className}(${this.host}:${this.port})---->解析校验服务端证书---->加密包长度:${data.byteLength},原始长度:${origDataLength},解密后长度:${decryptedData.byteLength}`);
        if (decryptedData.byteLength != origDataLength) {
          decryptedData = decryptedData.slice(0, origDataLength);
        }
        if (decryptedData && decryptedData.byteLength > 32) {
          //服务端证书二进制
          let serverCerData: Uint8Array = decryptedData.slice(0, decryptedData.byteLength - 32);
          //服务器随机数
          let randData: Uint8Array = decryptedData.slice(serverCerData.byteLength, decryptedData.byteLength);
          //本地服务器端证书二进制
          let localServerCerData: Uint8Array = TKFileHelper.readFile(this.serverPublicCerFile);
          if (localServerCerData && localServerCerData.length > 0) {
            if (this.isEncryptCer) {
              localServerCerData = TKBase64Helper.dataWithBase64Decode(localServerCerData);
              if (localServerCerData && localServerCerData.length > 0) {
                localServerCerData = await TKAesHelper.dataWithAesDecrypt(localServerCerData, this.aesSystemKey());
              }
              if (!localServerCerData || localServerCerData.length == 0) {
                super.connectError("本地服务端证书不合法!");
                return;
              }
            }
            //服务端证书二进制Md5值
            let serverCerDataMd5: string = await TKMd5Helper.stringWithMd5(serverCerData);
            //本地服务器端证书二进制Md5值
            let localServerCerDataMd5: string = await TKMd5Helper.stringWithMd5(localServerCerData);
            if (TKStringHelper.isNotBlank(serverCerDataMd5) && TKStringHelper.isNotBlank(localServerCerDataMd5) &&
              serverCerDataMd5 == localServerCerDataMd5) {
              this.serverRandomKey = TKDataHelper.uint8ArrayToString(randData, TKDataHelper.CHAR_GBK);
              let key: string = `${this.clientRandomKey}${this.serverRandomKey}1111`;
              key = await TKMd5Helper.stringWithMd5(key);
              key = key.substring(1, key.length - 1);
              this.encrytKey = `${this.getTag()}${key}`;
              TKLog.info(`${this.className}(${this.host}:${this.port})---->生成的加密秘钥为---->${this.encrytKey}`)
              if (this.vitifyMode == 2) {
                this.doVitifyAesKeyAndServerRandStr();
              } else {
                this.doVitifySM4KeyAndServerRandStr();
              }
            } else {
              super.connectError("校验服务端证书失败！");
            }
          } else {
            super.connectError("本地服务端证书不存在！");
          }
        } else {
          super.connectError("校验服务端证书失败！");
        }
      } else {
        super.connectError("校验服务端证书失败！");
      }
    } else {
      super.connectError("校验服务端证书失败！");
    }
  }

  /**
   *  解析验证服务端随机数返回数据，返回以后根据服务器返回的客户端的随机数进行客户端Aes解密校验
   *
   * @param data
   */
  private async parseVitifyAesKeyAndServerRandDataReturnData(data: ArrayBuffer, origDataLength: number) {
    if (data && data.byteLength > 0) {
      let decryptedData: Uint8Array =
        await TKAesHelper.dataWithAesDecrypt(new Uint8Array(data), this.encrytKey, "", TKAesMode.ECB_NOPADDING);
      if (decryptedData && decryptedData.byteLength > 0) {
        TKLog.info(`${this.className}(${this.host}:${this.port})---->AES解析校验客户端随机数---->加密包长度:${data.byteLength},原始长度:${origDataLength},AES解密后长度:${decryptedData.byteLength}`);
        if (decryptedData.byteLength != origDataLength) {
          decryptedData = decryptedData.slice(0, origDataLength);
        }
        if (decryptedData.byteLength >= 32) {
          //服务器返回的客户端加密随机串
          let clientRandStr: string = TKDataHelper.uint8ArrayToString(decryptedData);
          clientRandStr = clientRandStr.substring(0, 32);
          if (TKStringHelper.isNotBlank(clientRandStr) && TKStringHelper.isNotBlank(this.clientRandomKey) &&
            clientRandStr == this.clientRandomKey) {
            this.doServerVitifySuccess();
          } else {
            super.connectError("AES校验客户端随机数失败！");
          }
        } else {
          super.connectError("AES校验客户端随机数失败！");
        }
      } else {
        super.connectError("AES校验客户端随机数失败！");
      }
    } else {
      super.connectError("AES校验客户端随机数失败！");
    }
  }

  /**
   *  解析验证服务端随机数返回数据，返回以后根据服务器返回的客户端的随机数进行客户端SM4解密校验
   *
   * @param data
   */
  private async parseVitifySM4KeyAndServerRandDataReturnData(data: ArrayBuffer, origDataLength: number) {
    if (data && data.byteLength > 0) {
      let decryptedData: Uint8Array = await TKSm4Helper.dataWithSm4Decrypt(new Uint8Array(data), this.encrytKey);
      if (decryptedData && decryptedData.byteLength > 0) {
        TKLog.info(`${this.className}(${this.host}:${this.port})---->SM4解析校验客户端随机数---->加密包长度:${data.byteLength},原始长度:${origDataLength},SM4解密后长度:${decryptedData.byteLength}`);
        if (decryptedData.byteLength != origDataLength) {
          decryptedData = decryptedData.slice(0, origDataLength);
        }
        if (decryptedData.byteLength >= 32) {
          //服务器返回的客户端加密随机串
          let clientRandStr: string = TKDataHelper.uint8ArrayToString(decryptedData);
          clientRandStr = clientRandStr.substring(0, 32);
          if (TKStringHelper.isNotBlank(clientRandStr) && TKStringHelper.isNotBlank(this.clientRandomKey) &&
            clientRandStr == this.clientRandomKey) {
            this.doServerVitifySuccess();
          } else {
            super.connectError("SM4校验客户端随机数失败！");
          }
        } else {
          super.connectError("SM4校验客户端随机数失败！");
        }
      } else {
        super.connectError("SM4校验客户端随机数失败！");
      }
    } else {
      super.connectError("SM4校验客户端随机数失败！");
    }
  }

  /**
   *  获取会话
   *
   * @param data
   */
  private async parseSessionKeyData(data: ArrayBuffer) {
    let sessionKey: string = TKDataHelper.uint8ArrayToString(data, TKDataHelper.CHAR_GBK);
    TKLog.info(`${this.className}(${this.host}:${this.port})---->解析TH登陆会话Key---->${sessionKey}`);
    if (TKStringHelper.isNotBlank(sessionKey)) {
      //认证网络服务连接
      this.doComBusV3ClientAuthConnect(sessionKey);
    } else {
      super.connectError("获取会话失败");
    }
  }


  /**
   *  解析服务器认证数据
   *
   * @param data
   */
  private async parseAuthLoginReturnData(data: ArrayBuffer) {
    let index: number = 0;
    let result: number = 0;
    let readDataView: TKReadDataView = new TKReadDataView(data);
    result = readDataView.getInt();
    TKLog.info(`${this.className}(${this.host}:${this.port})---->解析TH登陆返回结果---->${result}`);
    if (result == 0) {
      this.doServerVitifySuccess(data.slice(4, data.byteLength))
    } else {
      super.connectError("登陆验证失败");
    }
  }

  /**
   *  申请服务器挑战码返回，返回以后客户端计算挑战吗，然后发给服务器验证
   *
   * @param data
   */
  private async parseApplyGameGuardChallengeReturnData(data: ArrayBuffer, origDataLength: number) {
    if (data && data.byteLength > 0) {
      let decryptedData: Uint8Array =
        await TKAesHelper.dataWithAesDecrypt(new Uint8Array(data), this.encrytKey, "", TKAesMode.ECB_NOPADDING);
      if (decryptedData && decryptedData.byteLength > 0) {
        TKLog.info(`${this.className}(${this.host}:${this.port})---->解析服务器返回挑战码---->加密包长度:${data.byteLength},原始长度:${origDataLength},AES解密后长度:${decryptedData.byteLength}`);
        if (decryptedData.byteLength != origDataLength) {
          decryptedData = decryptedData.slice(0, origDataLength);
        }
        if (decryptedData.byteLength >= 34) {
          //服务器返回的挑战码
          let challenge: string = TKDataHelper.uint8ArrayToString(decryptedData);
          challenge = challenge.substring(2, 34);
          this.doVitifyGameGuardChallenge(challenge);
        }
      }
    }
  }

  /**
   *  服务器认证成功以后的数据处理
   *
   * @param data
   */
  protected doServerVitifySuccess(data?: ArrayBuffer) {
    super.connectOk();
    //防外挂逻辑
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(this.serverName);
    if (server && server.isGameGuard) {
      this.doApplyGameGuardChallenge();
    }
  }
}