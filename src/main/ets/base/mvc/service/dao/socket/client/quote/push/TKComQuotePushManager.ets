import { TKObjectHelper } from '../../../../../../../../util/data/TKObjectHelper';
import { TKLog } from '../../../../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../../../../util/map/TKMapHelper';
import { TKNotificationCenter } from '../../../../../../../notification/TKNotificationCenter';
import { TKBusClientManager } from '../../common/TKBusClientManager';
import { TKBusMsgType } from '../../common/TKComBusClient';
import { TKGatewayListener } from '../../gateway/TKGatewayListener';
import { TKGatewayManager } from '../../gateway/TKGatewayManager';
import { LBMODE, TKServer } from '../../gateway/TKServer';
import { TKComQuotePushASClient } from './TKComQuotePushASClient';

/**
 *  推送相关连接管理器
 */
export class TKComQuotePushManager extends TKBusClientManager {
  /**
   *  行情推送网络连接成功
   */
  public static readonly NOTE_COMQUOTEPUSH_CONNECT: string = "ComQuotePush_Connect";
  /**
   *  行情推送网络连接断开
   */
  public static readonly NOTE_COMQUOTEPUSH_DISCONNECT: string = "ComQuotePush_DisConnect";
  /**
   *  行情推送网络连接错误
   */
  public static readonly NOTE_COMQUOTEPUSH_ERROR: string = "QuotePush_ConnectError";
  /**
   * 单例对象
   */
  private static instance1: TKComQuotePushManager | undefined = undefined;

  public static shareInstance(): TKComQuotePushManager {
    if (!TKComQuotePushManager.instance1) {
      TKComQuotePushManager.instance1 = new TKComQuotePushManager();
    }
    return TKComQuotePushManager.instance1;
  }

  /**
   *  启动服务
   *
   * @param serverName 服务名称
   */
  public startQuotePush(serverName: string) {
    this.start(serverName);
  }

  /**
   *  重启服务
   * @param serverName 服务名称
   */
  public restartQuotePush(serverName: string) {
    this.restart(serverName);
  }

  /**
   *  关闭服务
   * @param serverName 服务名称
   */
  public stopQuotePush(serverName: string) {
    this.stop(serverName);
  }

  /**
   *  获取指定名字缓存的行情推送对象
   *
   * @param serverName
   *
   * @return
   */
  public getQuotePushASClient(serverName: string): TKComQuotePushASClient | undefined {
    return this.getTKBusClient(serverName, true) as TKComQuotePushASClient | undefined;
  }

  /**
   *  处理代理
   *
   * @param msgType
   * @param wparam
   * @param lparam
   */
  public onNotifyMessage(obj: Object, msgType: TKBusMsgType, wparam?: Object, lparam?: Object) {
    let busClient: TKComQuotePushASClient = obj as TKComQuotePushASClient;
    switch (msgType) {
      case TKBusMsgType.Connect: {
        this.onNotifyConnectMessage(busClient);
        break;
      }
      case TKBusMsgType.DisConnect: {
        //流水号
        let flowNo: string = String(wparam ?? "")
        //错误对象
        let error: Record<string, Object> | undefined = lparam as Record<string, Object>;
        this.onNotifyDisConnectMessage(busClient, flowNo, error);
        break;
      }
      case TKBusMsgType.Error: {
        //流水号
        let flowNo: string = String(wparam ?? "")
        //错误对象
        let error: Record<string, Object> | undefined = lparam as Record<string, Object>;
        this.onNotifyErrorMessage(busClient, flowNo, error);
        break;
      }
      default:
        break;
    }
  }

  /**
   * 处理连接成功的消息
   */
  protected onNotifyConnectMessage(busClient: TKComQuotePushASClient) {
    TKLog.info(`${this.className}(${busClient.host}:${busClient.port})建立连接成功!`);
    if (busClient && busClient.isLongConnect) {
      busClient.startHeart();
    }
    //订阅股票列表
    busClient.rePublishStocks();
    TKNotificationCenter.defaultCenter.postNotificationName(TKComQuotePushManager.NOTE_COMQUOTEPUSH_CONNECT, busClient);
  }

  /**
   * 处理连接异常断开的消息
   */
  protected onNotifyDisConnectMessage(busClient: TKComQuotePushASClient, flowNo: string,
    error: Record<string, Object>) {
    TKLog.error(`${this.className}(${busClient.host}:${busClient.port})连接异常中断:${TKObjectHelper.toJsonStr(error)}`);
    let userInfo: Record<string, Object> = {
      "flowNo": flowNo,
      "errorType": TKMapHelper.getString(error, "error_type"),
      "errorNo": TKMapHelper.getString(error, "error_no"),
      "errorInfo": TKMapHelper.getString(error, "error_info")
    };
    TKNotificationCenter.defaultCenter.postNotificationName(TKComQuotePushManager.NOTE_COMQUOTEPUSH_DISCONNECT,
      busClient, userInfo);
    if (busClient && busClient.isLongConnect) {
      this.stop(busClient.serverName);
      let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(busClient.serverName);
      if (server) {
        if (server.getAllNetworkAddresses().length > 1 && server.LBMode != LBMODE.NONE) {
          TKGatewayListener.shareInstance().startTest(busClient.serverName);
          setTimeout(() => {
            this.start(busClient.serverName);
          }, 1500);
        } else {
          this.start(busClient.serverName);
        }
      }
    }
  }

  /**
   * 处理请求错误的消息
   */
  protected onNotifyErrorMessage(busClient: TKComQuotePushASClient, flowNo: string, error: Record<string, Object>) {
    let userInfo: Record<string, Object> = {
      "flowNo": flowNo,
      "errorType": TKMapHelper.getString(error, "error_type"),
      "errorNo": TKMapHelper.getString(error, "error_no"),
      "errorInfo": TKMapHelper.getString(error, "error_info")
    };
    TKNotificationCenter.defaultCenter.postNotificationName(TKComQuotePushManager.NOTE_COMQUOTEPUSH_ERROR, busClient,
      userInfo);
  }
}