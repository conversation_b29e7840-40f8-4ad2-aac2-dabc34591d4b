export enum TKDataVOMode {
  /**
   *  连接
   */
  Connect,

  /**
   *  业务
   */
  Business
}

export enum TKDataVOType {
  /**
   *  正常数据
   */
  Normal,
  /**
   *  AES加密
   */
  AesEncryt,
  /**
   *  压缩
   */
  Compress,
  /**
   *  先加密后压缩
   */
  AesEncryt_Compress,
  /**
   *  先压缩后加密
   */
  Compress_AesEncryt,
  /**
   *  SM4加密
   */
  SM4Encryt,
  /**
   *  先SM4加密后压缩
   */
  SM4Encryt_Compress,
  /**
   *  先压缩后SM4加密
   */
  Compress_SM4Encryt
}

/**
 *  数据对象
 */
export interface TKDataVO {

  /**
   *  流水号
   */
  flowNo: string;

  /**
   *  功能号
   */
  funcNo: string;

  /**
   *  数据
   */
  data: ArrayBuffer;

  /**
   * 渠道ID
   */
  channelId: string;

  /**
   *  请求公司编号
   */
  companyId: string;

  /**
   *  系统编号
   */
  systemId: string;

  /**
   *  数据类型协议
   */
  dataType: TKDataVOType;

  /**
   *  数据类型
   */
  dataMode: TKDataVOMode;
}
