/**
 *  返回字段
 */
export class TKQuoteFieldVO {
  /**
   *  名称
   */
  public name: string = "";
  /**
   * json定义的参数别名，默认就是json的参数值
   */
  public aliasName: string = "";
  /**
   *  流水号
   */
  public serno: number = 0;
  /**
   *  参数类型
   */
  public typeName: string = "";
  /**
   *  参数类型
   */
  public type: number = 0;
  /**
   *  字段长度
   */
  public length: number = 0;
  /**
   * 字段描述
   */
  public desc: string = "";

  public getJsonName(functionMode: number | undefined): string {
    return (functionMode == 2) ? this.aliasName : this.name;
  }
}
