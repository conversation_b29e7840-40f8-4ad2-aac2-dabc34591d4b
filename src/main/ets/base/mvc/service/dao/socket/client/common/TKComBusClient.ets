/**
 bus版本
 */
import { TK<PERSON>ataVO, TKDataVOMode } from './TKDataVO';
import socket from '@ohos.net.socket';
import { TKUUIDHelper } from '../../../../../../../util/crypto/TKUUIDHelper';
import { TKTimer } from '../../../../../../../util/timer/TKTimer';
import { TKNotification } from '../../../../../../notification/TKNotification';
import { TKNotificationCenter } from '../../../../../../notification/TKNotificationCenter';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKApplicationStateChangeListener } from '../../../../../../../util/system/TKContextHelper';
import { buffer } from '@kit.ArkTS';

export enum TKBusClientVersion {
  QuoteV3 = 5,
  SDBusV3 = 9,
  QuotePushV3 = 20
}

/**
 消息类型
 */
export enum TKBusMsgType {
  /**
   *  服务器连接认证成功消息
   */
  Connect = 10001,

  /**
   *  连接断开消息
   */
  DisConnect = 10002,

  /**
   *  请求完成消息
   */
  Result = 10003,

  /**
   *  请求出错信息
   */
  Error = 10004

}
;

/**
 网络通信层代理协议
 */
export interface TKBusClientDelegate {
  /**
   *
   *  接收各种状态通知消息

   msgType为TKBusV1MsgType_Connect：（服务器连接状态消息）
   wparam：流水号
   lparam：备用
   说明：当客户端连接服务器时被触发

   msgType为TKBusV1MsgType_DisConnect：（连接断开消息）
   wparam：流水号
   lparam：表示连接断开的原因，NSString类型；

   *
   * @param msgType 消息类型
   * @param wparam  参数1
   * @param lparam  参数2
   */
  onNotifyMessage(obj: Object, msgType: TKBusMsgType, wparam?: Object, lparam?: Object): void;
}

/**
 数据解析函数代理
 */
export interface TKBusClientDataDelegate {
  /**
   *  获取响应头长度
   *
   * @return
   */
  getResponseHeaderLength(): number;

  /**
   *  获取请求对象
   *
   * @param flowNo   流水号
   * @param data     数据
   * @param userInfo 扩展字段
   *
   * @return
   */
  getSendData(flowNo: string, data: Object, userInfo?: Record<string, Object>): TKDataVO;

  /**
   *  处理加工发送数据
   *
   * @param data 数据
   *
   * @return 处理后的结果
   */
  processSendData(dataVO: TKDataVO): Promise<ArrayBuffer>;

  /**
   *  从整个结果数据中截取需要的数据包
   *
   * @param data 数据
   *
   * @return
   */
  getResultData(data: ArrayBuffer): ArrayBuffer | undefined;

  /**
   *  转换结果数据到最后的resultVo的数据字典对象
   *
   * @param resultData 结果数据
   *
   * @return
   */
  convertResultDataToResultDic(resultData: ArrayBuffer): Promise<Object | undefined>;
}

/**
 编码格式
 */
export enum TKCharSet {
  DEFAULT,
  UTF_8,
  GBK
}


/**
 *  BUS网络通信层 ，用法按照以下步骤：
 //初始化上下文
 1、initCTX;
 //连接服务器
 2、connect:(NSString *)host :(int)port;
 //发送消息
 3、sendData
 //读取消息
 4、readData
 //断开连接
 5、disConnect;
 //释放上下文
 6、releaseCTX;
 */
export abstract class TKComBusClient implements TKBusClientDataDelegate {
  /**
   *  请求头长度
   */
  protected static readonly TH_REQUEST_HEADLENGTH: number = 10;
  /*
  *  响应头长度
  */
  protected static readonly TH_RESPONSE_HEADLENGTH: number = 10;
  /**
   *  类名
   */
  public className: string = "";
  /**
   *  主机UUID
   */
  public readonly uuid: string = TKUUIDHelper.uuid();
  /**
   *  主机名
   */
  public host: string = "";
  /**
   *  端口号
   */
  public port: number = 0;
  /**
   *  分组ID
   */
  public groupId: string = "";
  /**
   *  最终连接主机IP
   */
  public connectedHost: string = "";
  /**
   *  最终连接主机端口
   */
  public connectedPort: number = 0;
  /**
   *  超时时间，单位秒
   */
  public timeOut: number = 60;
  /**
   *  心跳间隔时间,单位秒
   */
  public heartTime: number = 0;
  /**
   *  网络通信代理协议
   */
  public delegate: TKBusClientDelegate | undefined = undefined;
  /**
   *  是否长连接
   */
  public isLongConnect: boolean = false;
  /**
   *  用户信息
   */
  public userInfo: Object | undefined = undefined;
  /**
   *  是否正在登陆中,防止重复登录
   */
  public isLogining: boolean = false;
  /**
   *  服务器名称
   */
  public serverName: string = "";
  /**
   * socket的实现类
   */
  public socketImpl: socket.TCPSocket | undefined = undefined;
  /**
   * 是否使用自定义实现类
   */
  public isUseSocketImpl: boolean = false;
  /**
   * socket的当前客户端的地址
   */
  public clientAddress: string = "";
  /**
   *  bus版本号
   */
  public version: TKBusClientVersion = TKBusClientVersion.SDBusV3;
  /**
   *  是否初始化
   */
  public isInitCTX: boolean = false;
  /**
   *  是否连接成功
   */
  public isConnect: boolean = false;
  /**
   *  是否正在连接
   */
  public isConnecting: boolean = false;
  /**
   *  是否认证成功
   */
  public isVitify: boolean = false;
  /**
   *  返回字符编码集
   */
  public charSet: TKCharSet = TKCharSet.GBK;
  /**
   *  收到响应包的最后时间搓，可以用来检测客户端是否超过一定时间未收到响应包，然后根据需要主动断开长连接进行重连
   */
  public respDataTimeStamp: number = 0;
  /**
   *  当前数据请求队列
   */
  public readonly reqDataQueue: Array<string> = new Array<string>();
  /**
   *  当前数据数据写队列
   */
  public readonly writeBufferData: Array<TKDataVO> = new Array<TKDataVO>();
  /**
   *  socket连接
   */
  private socket: socket.TCPSocket | undefined = undefined;
  /**
   *  数据读取缓冲区
   */
  private readBufferData: Array<number> = new Array<number>();
  /**
   *  心跳定时任务
   */
  private heartTimer: TKTimer | undefined = undefined;
  /**
   *  连接定时任务
   */
  private connectTimer: TKTimer | undefined = undefined;
  /**
   *  重试连接次数
   */
  private tryReConnectNum: number = 0;

  /*************************************生命周期的方法**************************************/

  public constructor() {
    this.className = this.constructor.name;
    //实现应用前后台监听
    TKNotificationCenter.defaultCenter.addObserver(this, this.applicationDidBecomeActive,
      TKApplicationStateChangeListener.NOTE_TKAPPLICATION_FOREGROUND);
    TKNotificationCenter.defaultCenter.addObserver(this, this.applicationDidEnterBackground,
      TKApplicationStateChangeListener.NOTE_TKAPPLICATION_BACKGROUND);
  }

  /**
   *  处理应用进入前台
   *
   * @param notification
   */
  private applicationDidBecomeActive(notification: TKNotification) {
    if (this.connectTimer) {
      this.respDataTimeStamp = new Date().getTime();
      this.connectTimer.resume();
    }
  }

  /**
   *  处理应用进入后台
   *
   * @param notification
   */
  private applicationDidEnterBackground(notification: TKNotification) {
    if (this.connectTimer) {
      this.connectTimer.pause();
    }
  }

  /**
   *  初始化上下文
   */
  public initCTX(): void {
    this.host = "";
    this.port = 0;
    this.connectedHost = "";
    this.connectedPort = 0;
    this.clientAddress = "";
    this.groupId = "";
    this.timeOut = -1;
    this.heartTime = 5;
    this.isConnect = false;
    this.isVitify = false;
    this.isConnecting = true;
    this.isLogining = false;
    this.isLongConnect = false;
    this.isInitCTX = true;
    this.respDataTimeStamp = new Date().getTime();
  }

  /**
   *  建立连接，连接成功以后进行服务器认证
   *
   * @param host 服务器地址
   * @param port 服务器端口
   */
  public connect(host: string, port: number): void {
    TKLog.info(`${this.className}(${host}:${port})---->开始连接`);
    this.host = host;
    this.port = port;
    this.connectedHost = host;
    this.connectedPort = port;
    this.socket = this.socketImpl ? this.socketImpl : socket.constructTCPSocketInstance();
    //绑定socket事件监听
    this.bindSocketEventListener();
    //建立socket连接
    this.socket?.connect({
      address: {
        address: this.host,
        port: this.port,
        family: this.host.indexOf(":") >= 0 ? 2 : 1
      },
      timeout: 5000
    } as socket.TCPConnectOptions).then(() => {
    }).catch(async (error: BusinessError) => {
      this.socketDidDisconnect(this.socket as socket.TCPSocket, error);
    });
  }

  /**
   * 监听socket事件
   * @param socket
   */
  private bindSocketEventListener() {
    if (this.socket) {
      this.socket.on('connect', async () => {
        this.socket?.getRemoteAddress((error: BusinessError, data: socket.NetAddress) => {
          this.socketDidConnectToHost(this.socket as socket.TCPSocket, (data?.address) ?? this.host,
            (data?.port) ?? this.port);
        })
      });
      this.socket.on('message', async (value: socket.SocketMessageInfo) => {
        this.socketDidReadData(this.socket as socket.TCPSocket, value.message);
      });
      this.socket.on('close', async () => {
        this.socketDidDisconnect(this.socket as socket.TCPSocket);
      });
      this.socket.on('error', async (error: BusinessError) => {
        this.socketDidDisconnect(this.socket as socket.TCPSocket, error);
      });
    }
  }

  /**
   * 取消监听socket事件
   * @param socket
   */
  private unbindSocketEventListener() {
    if (this.socket) {
      this.socket.off('message');
      this.socket.off('connect');
      this.socket.off('close');
      this.socket.off('error');
    }
  }

  /**
   *  建立连接成功，尚未认证的状态
   */
  protected connectReady(): void {
    this.isConnect = true;
  }

  /**
   *  建立连接并且认证成功
   */
  protected connectOk(): void {
    this.isConnect = true;
    this.isVitify = true;
    this.tryReConnectNum = 0;
    //回调服务器连接成功
    if (this.delegate) {
      this.delegate.onNotifyMessage(this, TKBusMsgType.Connect);
    }
    // 发送业务数据包
    this.sendQueneKeepData();
  }

  /**
   *  连接认证失败
   *
   * @param errorInfo 错误信息
   */
  protected connectError(errorInfo: string): void {
    //回调服务器连接成功
    if (this.writeBufferData && this.writeBufferData.length > 0) {
      for (let dataVO of this.writeBufferData) {
        if (dataVO.dataMode == TKDataVOMode.Business) {
          //回调服务器认证失败动作，这个时候如果是长连接模式，应该断开重新连接认证
          if (this.delegate) {
            let result: Record<string, Object> = {
              "error_type": "1",
              "error_no": "-990",
              "error_info": `连接认证失败，失败原因：${errorInfo}`
            };
            this.delegate.onNotifyMessage(this, TKBusMsgType.Error, dataVO.flowNo, result);
          }
        }
      }
      if (this.writeBufferData.length > 0) {
        this.writeBufferData.splice(0, this.writeBufferData.length);
      }
    }
    if (this.reqDataQueue.length > 0) {
      this.reqDataQueue.splice(0, this.reqDataQueue.length);
    }
    if (this.delegate) {
      let result: Record<string, Object> = {
        "error_type": "1",
        "error_no": "-990",
        "error_info": `连接中断，中断原因：${errorInfo}`
      };
      this.delegate.onNotifyMessage(this, TKBusMsgType.DisConnect, "", result);
    }
  }

  /**
   *  发送心跳检测
   */
  public startHeart(): void {
    if (this.isLongConnect) {
      //发送心跳包
      if (!this.heartTimer) {
        this.heartTimer = new TKTimer(this.heartTime * 1000, this, this.sendHeart);
      }
      this.heartTimer!.start();
    }
  }

  /**
   *  发送心跳检测
   */
  protected async sendHeart() {
  }

  /**
   *  停止心跳检测
   */
  public stopHeart(): void {
    if (this.heartTimer) {
      this.heartTimer.stop();
      this.heartTimer = undefined;
    }
  }

  /**
   开启连接检测
   */
  private startCheckConnect() {
    if (this.isLongConnect) {
      this.respDataTimeStamp = new Date().getTime();
      //连接检测
      if (!this.connectTimer) {
        this.connectTimer = new TKTimer(this.heartTime * 1000, this, this.sendHeart)
      }
      this.connectTimer.start();
    }
  }

  /**
   连接检测是否正常
   */
  private async checkConnect() {
    if (this.socket && this.isConnect) {
      let currentTime: number = new Date().getTime();
      let responseTimeOut: number = this.isVitify ? (this.heartTime * 2 * 1000) : 1000;
      if ((currentTime - this.respDataTimeStamp) > responseTimeOut) {
        let isNeedDisConnectWithError: boolean = true;
        //未认证成功的情况下，允许2次重连尝试，为了解决偶发的服务器在认证过程中没有回包的问题，一旦发现没有回包，就永远回不来了，此时必须通过断线重连的机制来解决
        if (!this.isVitify) {
          //尝试两次重连重发数据
          if (this.tryReConnectNum < 3) {
            //计数加1
            this.tryReConnectNum++;
            //并不进行异常回调
            isNeedDisConnectWithError = false;
            //停止此次的心跳，按道理此处不会有心跳开始，不过写一下也没有问题。
            this.stopHeart();
            //停止此次虚链路响应超时检测，下次连接上去会重新开启检测
            this.stopCheckConnect();
            //端口长连接，并重置初始化状态为：未连接，未认证
            if (this.socket) {
              try {
                let socketState: socket.SocketStateBase = await this.socket.getState();
                if (socketState.isConnected) {
                  await this.socket.close();
                }
              } catch (error) {
                TKLog.error("disConnect===>" + error.message)
              }
              this.socket = undefined;
            }
            this.isConnect = false;
            this.isVitify = false;
            //开始重连逻辑，此处是否要延迟执行？？？？，目前暂时是立即尝试重连，逻辑上应该立即重连可以解决的。
            this.connect(this.host, this.port);
          }
        }
        if (isNeedDisConnectWithError) {
          this.tryReConnectNum = 0;
          this.reqDataQueue.splice(0, this.reqDataQueue.length);
          if (this.delegate) {
            let result: Record<string, Object> = {
              "error_type": "1",
              "error_no": "-992",
              "error_info": "连接中断，中断原因:网络链路异常,收不到服务器的响应包!"
            };
            this.delegate.onNotifyMessage(this, TKBusMsgType.DisConnect, "", result);
          }
        }
      }
    }
  }

  /**
   关闭连接检测
   */
  private stopCheckConnect() {
    if (this.connectTimer) {
      this.connectTimer.stop();
      this.connectTimer = undefined;
    }
  }

  /**
   *  发送队列保存的请求
   */
  private async sendQueneKeepData() {
    let writeBufferBusData: Array<TKDataVO> = new Array<TKDataVO>();
    while (this.writeBufferData && this.writeBufferData.length > 0) {
      let dataVO: TKDataVO = this.writeBufferData[0];
      this.writeBufferData.splice(0, 1);
      if (dataVO.dataMode == TKDataVOMode.Business) {
        if (this.isConnect && this.isVitify) {
          if (!(await this.doWriteTKDataVO(dataVO))) {
            break;
          }
        } else {
          writeBufferBusData.push(dataVO);
        }
      } else {
        if (!(await this.doWriteTKDataVO(dataVO))) {
          break;
        }
      }
    }
    this.writeBufferData.push(...writeBufferBusData);
  }

  /**
   *  发送数据
   *
   * @param dataVo
   */
  public async sendData(dataVO: TKDataVO) {
    if (dataVO.dataMode == TKDataVOMode.Business) {
      this.reqDataQueue.push(dataVO.flowNo);
      if (this.isConnect && this.isVitify) {
        await this.doWriteTKDataVO(dataVO);
      } else {
        this.writeBufferData.push(dataVO);
      }
    } else {
      await this.doWriteTKDataVO(dataVO);
    }
  }

  /**
   *  发送数据
   *
   * @param dataVo 数据
   *
   * @return 是否成功
   */
  private async doWriteTKDataVO(dataVO: TKDataVO): Promise<boolean> {
    if (this.socket && this.isConnect) {
      this.socket.send({
        data: await this.processSendData(dataVO)
      }).then(() => {
        this.socketDidWriteData(this.socket as socket.TCPSocket);
      }).catch((error: BusinessError) => {
        this.socketDidDisconnect(this.socket as socket.TCPSocket, error);
      });
    } else {
      this.reqDataQueue.splice(0, this.reqDataQueue.length);
      //回调服务器认证失败动作，这个时候如果是长连接模式，应该断开重新连接认证
      if (this.delegate) {
        let result: Record<string, Object> = {
          "error_type": "1",
          "error_no": "-991",
          "error_info": `连接中断，中断原因：服务器【${this.serverName}】连接异常!`
        };
        this.delegate.onNotifyMessage(this, TKBusMsgType.DisConnect, `${dataVO.flowNo}`, result);
      }
      return false;
    }
    return true;
  }

  /**
   *  断开socket连接
   */
  public disConnect(): void {
    if (this.socket && this.isConnect) {
      this.socket.close(() => {
        //this.socketDidDisconnect(this.socket as socket.TCPSocket);
      });
    }
    this.unbindSocketEventListener();
    this.stopHeart();
    this.stopCheckConnect();
    this.clearAllRequest();
  }

  /**
   *  清理所有未发送的请求
   */
  private clearAllRequest() {
    if (this.writeBufferData && this.writeBufferData.length > 0) {
      for (let dataVO of this.writeBufferData) {
        if (dataVO.dataMode == TKDataVOMode.Business) {
          //回调服务器认证失败动作，这个时候如果是长连接模式，应该断开重新连接认证
          if (this.delegate) {
            let result: Record<string, Object> = {
              "error_type": "1",
              "error_no": "-992",
              "error_info": `请求失败，失败原因：服务器【${this.serverName}】连接中断!`
            };
            this.delegate.onNotifyMessage(this, TKBusMsgType.Error, `${dataVO.flowNo}`, result);
          }
        }
      }
      this.writeBufferData.splice(0, this.writeBufferData.length);
    }
    if (this.readBufferData) {
      this.readBufferData = new Array<number>();
    }
    if (this.reqDataQueue) {
      this.reqDataQueue.splice(0, this.reqDataQueue.length);
    }
  }

  /**
   *  释放上下文
   */
  public releaseCTX(): void {
    this.socket = undefined;
    this.delegate = undefined;
    this.isInitCTX = false;
    this.isConnect = false;
    this.isVitify = false;
    this.isConnecting = false;
    this.isLogining = false;
  }

  /*************************************socket代理服务方法**************************************/
  /**
   *  建立连接
   *
   * @param sock  socket连接
   * @param host  主机
   * @param port  端口
   */
  protected socketDidConnectToHost(socket: socket.TCPSocket, host: string, port: number) {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->TCP连接已建立`);
    this.connectedHost = host;
    this.connectedPort = port;
    this.connectOk();
    this.startCheckConnect();
  }

  /**
   *  写数据完成
   *
   * @param sock socket连接
   * @param tag 标签
   */
  protected socketDidWriteData(socket: socket.TCPSocket) {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送数据完成`);
  }

  /**
   *  socket连接遇到错误将要断开连接
   *
   * @param sock socket
   * @param err  错误
   */
  protected socketDidDisconnect(socket: socket.TCPSocket, error?: BusinessError) {
    if (error && error.code !== 9) {
      TKLog.error(`${this.className}(${this.host}:${this.port})---->连接已断开:${error.message}`);
      this.tryReConnectNum = 0;
      this.reqDataQueue.splice(0, this.reqDataQueue.length);
      //回调服务器请求失败动作，这个时候如果是长连接模式，应该断开重新连接认证
      if (this.delegate) {
        let result: Record<string, Object> = {};
        result["error_type"] = "1";
        if (error.code == 2303210 || error.message.indexOf("超时") > 0 || error.message.indexOf("timed out") > 0) {
          result["error_no"] = "-993";
        } else if (error.message.indexOf("断开") > 0 || error.message.indexOf("refused") > 0 ||
          error.message.indexOf("中断") > 0) {
          result["error_no"] = "-994";
        } else {
          result["error_no"] = "-995";
        }
        result["error_info"] = `连接中断，中断原因：${error.message}`;
        this.delegate.onNotifyMessage(this, TKBusMsgType.DisConnect, "", result);
      }
    } else {
      TKLog.info(`${this.className}(${this.host}:${this.port})---->连接已断开`);
    }
  }

  /**
   *  读取数据
   *
   * @param sock socket连接
   * @param data 数据
   * @param tag  请求标签
   */
  protected socketDidReadData(socket: socket.TCPSocket, data: ArrayBuffer) {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->收到服务器端数据`);
    this.respDataTimeStamp = new Date().getTime();
    if (data.byteLength > 0) {
      this.readBufferData.push(...new Uint8Array(data));
    }
    this.readData();
  }

  /**
   * <AUTHOR> 2015-06-30 14:06:27
   *
   *  读取数据
   */
  private async readData() {
    while (this.readBufferData && this.readBufferData.length >= this.getResponseHeaderLength()) {
      let resultData: ArrayBuffer | undefined =
        this.getResultData(buffer.from(new Uint8Array(this.readBufferData)).buffer);
      if (resultData && resultData.byteLength > 0) {
        //删除缓存数据
        this.readBufferData.splice(0, resultData.byteLength);
        await this.processResultData(resultData);
      } else {
        break;
      }
    }
  }

  /**
   *  解析数据
   */
  private async processResultData(resultData: ArrayBuffer) {
    try {
      let timeInterval: number = new Date().getTime();
      if (this.socket) {
        let state: socket.SocketStateBase = await this.socket.getState();
        if (state.isConnected) {
          let resultDic: Record<string, Object> | undefined = await this.convertResultDataToResultDic(resultData);
          if (resultDic !== undefined) {
            let flowNo: string = TKMapHelper.getString(resultDic, "flowNo", "0");
            resultDic["timeInterval"] = timeInterval;
            let index: number = this.reqDataQueue.indexOf(flowNo);
            if (index >= 0) {
              this.reqDataQueue.splice(index, 1);
            }
            //回调服务器连接成功
            if (this.delegate) {
              this.delegate.onNotifyMessage(this, TKBusMsgType.Result, flowNo, resultDic);
            }
          }
        }
      }
    } catch (error) {
      this.socketDidDisconnect(this.socket as socket.TCPSocket, error);
    }
  }

  /**
   *  获取响应头长度
   *
   * @return
   */
  public getResponseHeaderLength(): number {
    return TKComBusClient.TH_RESPONSE_HEADLENGTH;
  }

  /**
   *  获取请求对象
   *
   * @param flowNo 流水号
   * @param data   数据
   *
   * @return
   */
  public getSendData(flowNo: string, data: Object, userInfo?: Record<string, Object>): TKDataVO {
    return { "flowNo": flowNo } as TKDataVO;
  }

  /**
   *  处理加工发送数据
   *
   * @param data 数据
   *
   * @return 处理后的结果
   */
  public async processSendData(dataVO: TKDataVO): Promise<ArrayBuffer> {
    return dataVO.data;
  }

  /**
   *  从整个结果数据中截取需要的数据包
   *
   * @param data 数据
   *
   * @return
   */
  public getResultData(data: ArrayBuffer): ArrayBuffer | undefined {
    return undefined;
  }

  /**
   *  转换结果数据到最后的resultVo的数据字典对象
   *
   * @param resultData 结果数据
   *
   * @return
   */
  public async convertResultDataToResultDic(resultData: ArrayBuffer): Promise<Record<string, Object> | undefined> {
    return undefined;
  }

  public dealloc() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }
}
