import { buffer, util } from '@kit.ArkTS';

/**
 * 读缓存区
 */
export class TKReadDataView {
  public static readonly CHAR_GBK: string = "gbk";
  public static readonly CHAR_UTF8: string = "utf-8";
  private dataView: DataView = new DataView(new ArrayBuffer(0));
  public offset: number = 0;
  private littleEndian: boolean = true;
  private gbkDecoder: util.TextDecoder = util.TextDecoder.create(TKReadDataView.CHAR_GBK);
  private utf8Decoder: util.TextDecoder = util.TextDecoder.create(TKReadDataView.CHAR_UTF8);

  public constructor(bytes: ArrayBuffer | Uint8Array, littleEndian: boolean = true) {
    this.dataView = new DataView(buffer.from(bytes).buffer);
    this.littleEndian = littleEndian;
  }

  public byteToBits(byte: number): Array<number> {
    let bits: Array<number> = new Array<number>();
    for (let i = 7; i >= 0; i--) {
      if (byte & (1 << i)) {
        bits.push(1);
      } else {
        bits.push(0);
      }
    }
    return bits;
  };

  public getField(length: number): Array<number> {
    let fields: Array<number> = new Array<number>();
    if (length > 0) {
      for (let i = 0; i < length; i++) {
        let byte: number = this.getByte();
        let bits: Array<number> = this.byteToBits(byte);
        fields.push(...bits);
      }
    }
    return fields;
  };

  public getFloat(): number {
    let value: number = this.dataView.getFloat32(this.offset, this.littleEndian);
    this.offset += 4;
    return value;
  };

  public getDouble(): number {
    let value: number = this.dataView.getFloat64(this.offset, this.littleEndian);
    this.offset += 8;
    return value;
  };

  public getInt(): number {
    let value: number = this.dataView.getInt32(this.offset, this.littleEndian);
    this.offset += 4;
    return value;
  };

  public getUInt(): number {
    let value: number = this.dataView.getUint32(this.offset, this.littleEndian);
    this.offset += 4;
    return value;
  };

  public getChar(charset: string, length: number): string {
    let result: string = "";
    if (length > 0) {
      let bytes: ArrayBuffer = this.dataView.buffer.slice(this.offset, this.offset + length);
      this.offset += length;
      if (charset == TKReadDataView.CHAR_GBK) {
        result = this.gbkDecoder.decodeWithStream(new Uint8Array(bytes));
      } else {
        result = this.utf8Decoder.decodeWithStream(new Uint8Array(bytes));
      }
    }
    return result.trim();
  };

  public getBool(): boolean {
    let value: number = this.getByte();
    return (value == 1);
  }

  public getLong(): number {
    let low: number = this.getInt();
    let high: number = this.getInt();
    return high * 4294967296 + (low >>> 0);
  };

  public getULong(): number {
    let low: number = this.getInt();
    let high: number = this.getInt();
    return ((high >>> 0) * 4294967296) + (low >>> 0);
  };

  public getShort(): number {
    let value: number = this.dataView.getInt16(this.offset, this.littleEndian);
    this.offset += 2;
    return value;
  };

  public getUShort(): number {
    let value: number = this.dataView.getUint16(this.offset, this.littleEndian);
    this.offset += 2;
    return value;
  };

  public getByte(): number {
    let value: number = this.dataView.getUint8(this.offset);
    this.offset += 1;
    return value;
  };

  public getBytes(length: number): ArrayBuffer {
    let value: ArrayBuffer = this.dataView.buffer.slice(this.offset, this.offset + length);
    this.offset += length;
    return value;
  };

  public get buffer(): ArrayBuffer {
    return this.dataView.buffer;
  };

  public get byteLength(): number {
    return this.dataView.byteLength;
  }

  public addOffset(length: number) {
    this.offset += length;
  };

  public isOutBounds(length: number): boolean {
    return ((this.offset + length) > this.dataView.byteLength);
  }

  public get isValidate(): boolean {
    return (this.dataView.byteLength > this.offset);
  };

  public get validateLength(): number {
    return (this.dataView.byteLength - this.offset);
  };
}