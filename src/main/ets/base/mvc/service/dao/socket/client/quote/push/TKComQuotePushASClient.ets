/**
 消息类型
 */
import { TKObjectHelper } from '../../../../../../../../util/data/TKObjectHelper';
import { TKLog } from '../../../../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../../../../util/string/TKStringHelper';
import { TKNotificationCenter } from '../../../../../../../notification/TKNotificationCenter';
import { TKComBusV3Client } from '../../common/TKComBusV3Client';
import { TKDataVO, TKDataVOMode } from '../../common/TKDataVO';
import { TKReadDataView } from '../../common/TKReadDataView';
import { TKWriteDataView } from '../../common/TKWriteDataView';
import { TKQuoteDomainVO } from '../TKQuoteDomainVO';
import { TKQuoteFieldVO } from '../TKQuoteFieldVO';
import { TKQuoteFunctionManager } from '../TKQuoteFunctionManager';
import { TKFunctionFieldType, TKQuoteFunctionVO } from '../TKQuoteFunctionVO';
import { TKQuoteInputVO } from '../TKQuoteInputVO';
import { TKQuoteOutputVO } from '../TKQuoteOutputVO';
import { TKQuoteOutsetVO } from '../TKQuoteOutsetVO';

export enum TKComQuotePushMsgType {
  /**
   *  行情订阅包
   */
  PublishStock = 100,

  /**
   *  行情取消订阅包
   */
  UnPublishStock = 102,

  /**
   *  实时行情订阅请求包
   */
  PublishStockQuote = 104
}
;

/**
 *
 *  行情推送网络通信层 ，用法按照以下步骤：
 //初始化上下文
 1、initCTX;
 //连接服务器
 2、connect:(NSString *)host :(int)port;
 //订阅股票
 3、publishStocks:(NSArray *)stocks;
 //取消订阅
 4、unPublishStocks
 //断开连接
 5、disConnect;
 //释放上下文
 6、releaseCTX;
 */
export class TKComQuotePushASClient extends TKComBusV3Client {
  /**
   包头长度
   */
  private static readonly HEADlENGTH: number = 10;
  /**
   *  订阅的股票列表
   */
  private stocks: Set<string> = new Set<string>();
  /**
   * 订阅类型
   */
  private quotePushMsgType: TKComQuotePushMsgType = TKComQuotePushMsgType.PublishStock;
  /**
   *  实时行情订阅的行情字段
   */
  private quotePushFields: string = "";
  /**
   * 行情订阅类型
   */
  private quotePushTypeFields: string = "";

  /*******************************请求业务处理方法*******************************/

  /**
   * 获取当前定义的股票列表
   * @returns
   */
  public getPublishStocks(): Array<string> {
    return Array.from(this.stocks);
  }

  /**
   *  重新订阅股票代码
   *
   * @param stocks 股票代码列表
   */
  public rePublishStocks() {
    if (this.stocks && this.stocks.size > 0) {
      if (this.quotePushMsgType == TKComQuotePushMsgType.PublishStock) {
        this.publishStocks(Array.from(this.stocks), this.quotePushTypeFields)
      } else if (this.quotePushMsgType == TKComQuotePushMsgType.PublishStockQuote) {
        this.publishQuoteStocks(Array.from(this.stocks), this.quotePushFields);
      }
    }
  }

  /**
   * 订阅股票代码,A股，期权等订阅股票代码,默认订阅全部类型，包含实时行情,成交明细,1分钟K线,分时数据都会订阅推送
   *
   * @param stocks     股票代码列表
   * @param typeField  类型(0:所有, 1:实时行情, 2:成交明细, 3:1分钟K线, 4:分时数据),默认是0
   */
  public publishStocks(stocks: Array<string>, typeField: string = "0") {
    typeField = typeField ?? "0";
    this.publishQHStocks(stocks, typeField);
  };

  /**
   *  期货行情订阅股票代码
   *
   * @param stocks     股票代码列表
   * @param typeFields 类型多个用:分隔(0:所有, 1:实时行情, 2:成交明细, 3:分时数据, 4:1分钟K线)，例如1:2代表订阅实时行情和成交明细
   */
  public publishQHStocks(stocks: Array<string>, typeFields: string = "0") {
    typeFields = typeFields ?? "0"
    stocks = stocks ?? new Array<string>();
    TKLog.info(`${this.className}(${this.host}:${this.port})---->订阅股票推送信息(${stocks.join("|")}:${typeFields})`);
    if (stocks.length > 0) {
      this.stocks = this.stocks ?? new Set<string>();
      this.stocks = new Set(Array.from(this.stocks).concat(stocks));
      this.quotePushMsgType = TKComQuotePushMsgType.PublishStock;
      this.quotePushTypeFields = typeFields;
      if (this.isConnect && this.isVitify) {
        let reqParam: Record<string, Object> = {
          "funcno": `${this.quotePushMsgType}`,
          "type": `${typeFields}`,
          "count": `${stocks.length}`,
          "stockCode": stocks.join("|")
        };
        let dataVO: TKDataVO = this.getSendData(TKMapHelper.getString(reqParam, "funcno"), reqParam);
        this.sendData(dataVO);
      }
    }
  };

  /*
   *  期货行情订阅股票实时行情代码，目前期货行情专用
   *
   *  @param stocks     股票代码列表
   *  @param fields     实时行情字段名称，多个用:分隔，例如1:2
   */
  public publishQuoteStocks(stocks: Array<string>, fields: string) {
    fields = fields ?? ""
    stocks = stocks ?? new Array<string>();
    TKLog.info(`${this.className}(${this.host}:${this.port})---->订阅股票实时行情推送信息(${stocks.join("|")}:${fields})`);
    if (stocks && stocks.length > 0) {
      this.stocks = this.stocks ?? new Set<string>();
      this.stocks = new Set(Array.from(this.stocks).concat(stocks));
      this.quotePushMsgType = TKComQuotePushMsgType.PublishStockQuote;
      this.quotePushFields = fields;
      if (this.isConnect && this.isVitify) {
        let reqParam: Record<string, Object> = {
          "funcno": `${this.quotePushMsgType}`,
          "count": `${stocks.length}`,
          "stockCode": stocks.join("|"),
          "field": fields
        };
        let dataVO: TKDataVO = this.getSendData(TKMapHelper.getString(reqParam, "funcno"), reqParam);
        this.sendData(dataVO);
      }
    }
  }


  /**
   *  取消指定的股票列表，默认取消所有
   * @param stocks
   */
  public unPublishStocks(stocks?: Array<string>) {
    if (stocks) {
      if (this.stocks) {
        for (let stock of stocks) {
          this.stocks.delete(stock);
        }
      }
    } else {
      this.stocks.clear();
    }
    TKLog.info(`${this.className}(${this.host}:${this.port})---->取消股票订阅(${Array.from(this.stocks).join("|")})`);
    if (this.isConnect && this.isVitify) {
      let reqParam: Record<string, Object> = {};
      reqParam["funcno"] = TKComQuotePushMsgType.UnPublishStock;
      if (stocks && stocks.length > 0) {
        reqParam["count"] = stocks.length;
        reqParam["stockCode"] = stocks.join("|");
        reqParam["type"] = 0;
      }
      let dataVO: TKDataVO = this.getSendData(TKMapHelper.getString(reqParam, "funcno"), reqParam);
      this.sendData(dataVO);
    }
  }

  /**
   * 获取当前订阅的股票列表
   */
  public getStocks(): Array<string> {
    return Array.from(this.stocks);
  }

  /*******************************TKBusClientDelegate**************************/
  /*******************************请求数据解析转换**************************/

  /**
   *  获取请求对象
   *
   * @param flowNo 流水号
   * @param data   数据
   *
   * @return
   */
  public getSendData(flowNo: string, data: Object, userInfo?: Record<string, Object>): TKDataVO {
    let reqParam: Record<string, Object> = {};
    if (data) {
      Object.entries(data as Record<string, Object>).forEach((e, i) => {
        let key: string = e[0] as string;
        let value: Object = e[1] as Object;
        reqParam[key.toLowerCase()] = value;
      })
    }
    //功能号
    let funcNo: string = TKMapHelper.getString(reqParam, "funcno");
    if (TKStringHelper.isBlank(funcNo)) {
      funcNo = TKMapHelper.getString(reqParam, "funcNo");
    }

    //请求参数数据
    let bodyData: ArrayBuffer = this.parseInputParam(reqParam, funcNo, flowNo);
    //获取功能号配置
    //获取功能号配置
    let quoteDomain: TKQuoteDomainVO = TKQuoteFunctionManager.shareInstance().getQuoteDomainVOByName(this.serverName);
    let functionVO: TKQuoteFunctionVO = quoteDomain.getFunctionByJsonFuncNo(funcNo) as TKQuoteFunctionVO;
    //构建对象
    let dataVO: TKDataVO = {
      flowNo: flowNo,
      funcNo: functionVO.byteFuncNo,
      data: bodyData,
      dataMode: TKDataVOMode.Business
    } as TKDataVO;
    return dataVO;
  }

  //获取入参长度
  private getInputsByteLength(reqParam: Record<string, Object>, functionVO: TKQuoteFunctionVO): number {
    let inputsByteLength: number = 0;
    if (functionVO) {
      let inputVOs = functionVO.inputs;
      if (inputVOs && inputVOs.length > 0) {
        for (let inputVO of inputVOs) {
          let jsonName: string = inputVO.jsonName;
          let length: number = inputVO.length;
          let defaultValue: string = inputVO.defaultValue;
          if (TKStringHelper.isBlank(TKMapHelper.getString(reqParam, jsonName))) {
            reqParam[jsonName] = defaultValue;
          }
          if (TKStringHelper.isBlank(TKMapHelper.getString(reqParam, jsonName))) {
            if (inputVO.excludedNull) {
              continue;
            }
          }
          switch (inputVO.type) {
            case TKFunctionFieldType.Type: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.PType: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.Field: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.CFlag: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.Kline: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.Stock: {
              let param: string = TKMapHelper.getString(reqParam, jsonName);
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Stocks: {
              let param: string = TKMapHelper.getString(reqParam, jsonName);
              if (TKStringHelper.isNotBlank(param)) {
                let inputs: Array<string> = param.split("|");
                if (inputs && inputs.length > 0) {
                  inputsByteLength += (inputs.length * length);
                }
              }
              break;
            }
            case TKFunctionFieldType.Float: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Double: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Int: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.UInt: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Char: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.Bool: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "false");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Long: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.ULong: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.LongLong: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.ULongLong: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Short: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.UShort: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Byte: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            default: {
              inputsByteLength += length;
              break;
            }
          }
        }
      }
    }
    return inputsByteLength;
  }

  /**
   *  获取入参数据
   *
   * @param reqParam 请求参数
   * @param funcNo   功能号
   *
   * @return 入参2进制数据
   */
  private parseInputParam(reqParam: Record<string, Object>, funcNo: string, flowNo: string): ArrayBuffer {
    let quoteDomain: TKQuoteDomainVO = TKQuoteFunctionManager.shareInstance().getQuoteDomainVOByName(this.serverName);
    //获取功能号配置
    let functionVO: TKQuoteFunctionVO | undefined = quoteDomain.getFunctionByJsonFuncNo(funcNo);
    if (functionVO) {
      let bodyData: TKWriteDataView = new TKWriteDataView(this.getInputsByteLength(reqParam, functionVO));
      let inputVOs: Array<TKQuoteInputVO> = functionVO.inputs;
      if (inputVOs && inputVOs.length > 0) {
        for (let inputVO of inputVOs) {
          let jsonName: string = inputVO.jsonName;
          let length: number = inputVO.length;
          if (TKStringHelper.isBlank(TKMapHelper.getString(reqParam, jsonName))) {
            reqParam[jsonName] = inputVO.defaultValue;
          }
          if (TKStringHelper.isBlank(TKMapHelper.getString(reqParam, jsonName))) {
            if (inputVO.excludedNull) {
              continue;
            }
          }
          switch (inputVO.type) {
            case TKFunctionFieldType.Type: {
              let typesStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putType(typesStr, length);
              break;
            }
            case TKFunctionFieldType.PType: {
              let ptypesStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putType(ptypesStr, length);
              break;
            }
            case TKFunctionFieldType.Field: {
              let fieldsStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putField(fieldsStr, length);
              break;
            }
            case TKFunctionFieldType.CFlag: {
              let cflag: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putCFlag(cflag, length);
              break;
            }
            case TKFunctionFieldType.Kline: {
              let kline: string = TKMapHelper.getString(reqParam, jsonName, "day");
              bodyData.putKline(kline);
              break;
            }
            case TKFunctionFieldType.Stock: {
              let stockStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putStock(stockStr, length);
              break;
            }
            case TKFunctionFieldType.Stocks: {
              let stocksStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putStocks(stocksStr, length);
              break;
            }
            case TKFunctionFieldType.Float: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putFloat(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Double: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putDouble(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Int: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putInt(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.UInt: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putUInt(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Char: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putChar(TKWriteDataView.CHAR_GBK, inputStr, length);
              break;
            }
            case TKFunctionFieldType.Bool: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "false");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putBool(input == "true");
              }
              break;
            }
            case TKFunctionFieldType.Long: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putLong(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.ULong: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putULong(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.LongLong: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putLong(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.ULongLong: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putULong(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Short: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putShort(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.UShort: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putUShort(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Byte: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putByte(Number(input));
              }
              break;
            }
            default: {
              break;
            }
          }
        }
        let dataBuffer: ArrayBuffer = bodyData.buffer;
        let offset: number = bodyData.offset;
        return dataBuffer.slice(0, offset);
      }
      return bodyData.buffer;
    }
    return new ArrayBuffer(0);
  }

  /**
   *  处理加工发送数据
   *
   * @param data 数据
   *
   * @return 处理后的结果
   */
  public async processSendData(dataVO: TKDataVO): Promise<ArrayBuffer> {
    if (dataVO.dataMode == TKDataVOMode.Business) {
      let bodyData: ArrayBuffer = dataVO.data;
      //发送数据包
      let origDataLength: number = bodyData.byteLength;
      let bBuffer: ArrayBuffer = this.buildTHSendMessage(dataVO.funcNo, dataVO.flowNo, bodyData, origDataLength);
      return bBuffer;
    }
    return dataVO.data;
  }


  /**
   *  从整个结果数据中截取需要的数据包
   *
   * @param data 数据
   *
   * @return
   */
  public getResultData(data: ArrayBuffer): ArrayBuffer | undefined {
    let responseLength: number = this.getResponseHeaderLength();
    if (data && data.byteLength >= responseLength) {
      let readDataView: TKReadDataView = new TKReadDataView(data);
      let tag: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 2);
      //获取消息类型
      let messageType: number = readDataView.getInt();
      if (tag == TKComQuotePushASClient.TH_TAG && messageType < 30) {
        responseLength = this.getCertVitifyResponseHeaderLength();
      }
      if (data.byteLength >= responseLength) {
        if (tag == TKComQuotePushASClient.TH_TAG) {
          readDataView.offset = (responseLength - 4);
          //头部最后4位是一个整形，用来存放主体的长度
          let bodyLength: number = readDataView.getUInt();
          let packegeLength: number = responseLength + bodyLength;
          TKLog.debug(`${this.className}(${this.host}:${this.port})---->响应包头:(业务包类别:${tag},已收到数据长度:${data.byteLength},业务包数据长度:${packegeLength},业务包包体长度:${bodyLength})`);
          //截取数据包
          if (data.byteLength >= packegeLength) {
            return readDataView.buffer.slice(0, packegeLength);
          }
        }
      }
    }
    return undefined;
  }

  /*********************************处理响应数据*************************************/

  /**
   *  解析认证包体
   *
   * @param bodyData 认证包体
   */
  protected parseTHResultData(messageType: number, bodyData: ArrayBuffer, origDataLength: number): void {
    if (messageType < 30) {
      super.parseTHResultData(messageType, bodyData, origDataLength);
    } else {
      let quoteDomain: TKQuoteDomainVO | undefined = TKQuoteFunctionManager.shareInstance()
        .getQuoteDomainVOByName(this.serverName);
      if (quoteDomain) {
        let functionVO: TKQuoteFunctionVO | undefined = quoteDomain.getFunctionByByteFuncNo(`${messageType}`);
        if (functionVO) {
          if (bodyData && bodyData.byteLength > 0) {
            let readDataView: TKReadDataView = new TKReadDataView(bodyData);
            let results: Array<Object> = new Array<Object>();
            let fields: Array<Object> = new Array<Object>();
            let notiName: string = functionVO.notiName;
            let outsets: Array<TKQuoteOutsetVO> = functionVO.outsets;
            let outputVOs: Array<TKQuoteOutputVO> = functionVO.outputs;
            if (outsets && outsets.length > 0) {
              for (let outset of outsets) {
                if (readDataView.isOutBounds(outset.length)) {
                  readDataView.addOffset(outset.length);
                  TKLog.warn(`${this.className}(${this.host}:${this.port})---->接收推送数据(业务功能号:${messageType})解析outset时接收数据和本地配置数据长度不匹配!`);
                  break;
                }
                switch (outset.type) {
                  case TKFunctionFieldType.Field: {
                    fields = readDataView.getField(outset.length);
                    break;
                  }
                  default:
                    break;
                }
              }
            }
            if (fields && fields.length > 0) {
              //没有输出配置，要按照自定义的结果字段展示
              let fieldVOs: Array<TKQuoteFieldVO> = new Array<TKQuoteFieldVO>();
              for (let i = 0; i < fields.length; i++) {
                if (fields[i] == 1) {
                  let serno: number = i + 1;
                  let fieldVO: TKQuoteFieldVO | undefined = quoteDomain.getFieldBySerno(serno);
                  if (fieldVO) {
                    fieldVOs.push(fieldVO);
                  }
                }
              }
              if (fieldVOs && fieldVOs.length > 0) {
                let dataRow: Record<string, Object> = {};
                for (let fieldVO of fieldVOs) {
                  if (readDataView.isOutBounds(fieldVO.length)) {
                    TKLog.warn(`${this.className}(${this.host}:${this.port})---->接收推送数据(业务功能号:${messageType})解析field时接收数据和本地配置数据长度不匹配!`);
                    break;
                  }
                  this.parseDataRowForDic(readDataView, fieldVO.name, fieldVO.type, fieldVO.length, dataRow);
                }
                results.push(dataRow);
              }
            } else {
              //解析普通返回结果集
              if (outputVOs && outputVOs.length > 0) {
                let dataRow: Record<string, Object> = {};
                for (let outputVO of outputVOs) {
                  if (readDataView.isOutBounds(outputVO.length)) {
                    TKLog.warn(`${this.className}(${this.host}:${this.port})---->接收推送数据(业务功能号:${messageType})解析output时接收数据和本地配置数据长度不匹配!`);
                    break;
                  }
                  this.parseDataRowForDic(readDataView, outputVO.jsonName, outputVO.type, outputVO.length, dataRow);
                }
                results.push(dataRow);
              }
            }
            TKLog.info(`${this.className}(${this.host}:${this.port})---->接收推送数据(通知名称:${notiName},业务功能号:${messageType})---->${TKObjectHelper.toJsonStr(results)}`);
            if (TKStringHelper.isNotBlank(notiName)) {
              TKNotificationCenter.defaultCenter.postNotificationName(notiName, results);
            } else {
              TKLog.error(`${this.className}(${this.host}:${this.port})---->接收推送数据(通知名称:${notiName},业务功能号:${messageType})通知没配置定义`);
            }
          }
        }
      }
    }
  }

  /**
   *
   *  解析字段到行数据里面，这里的行数据是数据字典格式
   *
   * @param bodyData    要解析的2进制数据
   * @param name        名称
   * @param type        类型
   * @param length      长度
   * @param dataRow     行数组数据,存放解析的结果
   */
  private parseDataRowForDic(readDataView: TKReadDataView, name: string, type: TKFunctionFieldType, length: number,
    dataRow: Record<string, Object>) {
    switch (type) {
      case TKFunctionFieldType.Float: {
        let output: number = readDataView.getFloat();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Double: {
        let output: number = readDataView.getDouble();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Char: {
        let output: string = readDataView.getChar(TKReadDataView.CHAR_GBK, length);
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Stock: {
        let output: string = readDataView.getChar(TKReadDataView.CHAR_GBK, length);
        if (TKStringHelper.isNotBlank(output)) {
          output = output.substring(0, 2) + ":" + output.substring(2, output.length);
        }
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Int: {
        let output: number = readDataView.getInt();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.UInt: {
        let output: number = readDataView.getUInt();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Short: {
        let output: number = readDataView.getShort();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.UShort: {
        let output: number = readDataView.getUShort();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Long: {
        let output: number = readDataView.getLong();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.ULong: {
        let output: number = readDataView.getULong();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.LongLong: {
        let output: number = readDataView.getLong();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.ULongLong: {
        let output: number = readDataView.getULong();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Byte: {
        let output: number = readDataView.getByte();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Bool: {
        let output: boolean = readDataView.getBool();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Time: {
        let hqTime: string = readDataView.getChar(TKReadDataView.CHAR_GBK, length);
        if (hqTime.length > 19) {
          hqTime = hqTime.substring(0, 19);
        }
        dataRow[name] = hqTime;
        break;
      }
      default:
        break;
    }
  }
}
