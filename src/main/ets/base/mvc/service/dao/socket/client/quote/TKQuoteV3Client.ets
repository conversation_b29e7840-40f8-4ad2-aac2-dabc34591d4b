/**
 *
 *  行情V3网络通信层
 */
import { TKHexHelper } from '../../../../../../../util/crypto/TKHexHelper';
import { TKZlibHelper } from '../../../../../../../util/crypto/TKZlibHelper';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { TKComBusV3Client } from '../common/TKComBusV3Client';
import { TKDataVO, TKDataVOMode } from '../common/TKDataVO';
import { TKReadDataView } from '../common/TKReadDataView';
import { TKWriteDataView } from '../common/TKWriteDataView';
import { TKGatewayManager } from '../gateway/TKGatewayManager';
import { TKServer } from '../gateway/TKServer';
import { TKQuoteDomainVO } from './TKQuoteDomainVO';
import { TKQuoteFieldVO } from './TKQuoteFieldVO';
import { TKQuoteFunctionManager } from './TKQuoteFunctionManager';
import { TKFunctionFieldType, TKQuoteFunctionVO } from './TKQuoteFunctionVO';
import { TKQuoteInputVO } from './TKQuoteInputVO';
import { TKQuoteOutputVO } from './TKQuoteOutputVO';
import { TKQuoteOutsetVO } from './TKQuoteOutsetVO';
import { buffer } from '@kit.ArkTS';

/**
 * 接口数据模式
 */
export enum TKFunctionMode {
  /**
   * 简单格式-值数组
   */
  ARRAY = 0,

  /**
   * 标准格式-数据字典
   */
  JSON = 1,

  /**
   *  标准格式-别名数据字典Bean
   */
  BEAN = 2
}
;

/**
 * 扩展信息
 */
export interface TKQuoteUserInfo {
  //行情服务id
  busServerId: string,

  //行情接口模式
  functionMode: TKFunctionMode,

  //动态行情字段
  fields: Array<number>
}

export class TKQuoteV3Client extends TKComBusV3Client {
  /**
   *  请求头长度
   */
  private static readonly TK_REQUEST_HEADLENGTH: number = 35;
  /**
   *  响应头长度
   */
  private static readonly TK_RESPONSE_HEADLENGTH: number = 35;
  /**
   *  版本号
   */
  private static readonly TK_VERSION: number = 0x00010000;
  /**
   *  数据类型
   */
  private static readonly TK_DATATYPE: number = 0;
  /**
   *  错误码
   */
  private static readonly TK_CODE: number = 0;
  /**
   *  行情请求扩展信息
   */
  private reqUserInfoMap: Map<string, TKQuoteUserInfo> = new Map<string, TKQuoteUserInfo>();
  /**
   *  错误号映射表
   */
  private errorInfoMap: Record<string, string> = {
    "0": "正常返回",
    "-1": "找不到股票代码",
    "-2": "包体长度有误",
    "-100": "服务器正在初始化"
  };

  private getTKResponseHeaderLength(): number {
    return TKQuoteV3Client.TK_RESPONSE_HEADLENGTH;
  }

  private getTKRequestHeaderLength(): number {
    return TKQuoteV3Client.TK_REQUEST_HEADLENGTH;
  }

  /****************************重新父类方法****************************/

  /**
   *  释放上下文
   */
  public releaseCTX(): void {
    super.releaseCTX();
    this.reqUserInfoMap.clear();
  }

  /****************************数据包解析器****************************/

  /**
   *  从整个结果数据中截取需要的数据包
   *
   * @param data 数据
   *
   * @return
   */
  public getResultData(data: ArrayBuffer): ArrayBuffer | undefined {
    let responseLength: number = this.getResponseHeaderLength();
    if (data && data.byteLength >= responseLength) {
      let readDataView: TKReadDataView = new TKReadDataView(data);
      let tag: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 2);
      //获取消息类型
      let messageType: number = readDataView.getInt();
      if (tag == TKQuoteV3Client.TH_TAG && messageType < 30) {
        responseLength = this.getCertVitifyResponseHeaderLength();
      }
      if (data.byteLength >= responseLength) {
        //头部最后4位是一个整形，用来存放主体的长度
        let bodyLength: number = 0;
        let packegeLength: number = 0;
        if (tag == TKQuoteV3Client.TH_TAG) {
          readDataView.offset = responseLength - 4;
          bodyLength = readDataView.getUInt();
          packegeLength = responseLength + bodyLength;
        } else if (tag == this.getTag()) {
          if (data.byteLength >= this.getTKResponseHeaderLength()) {
            readDataView.offset = 7;
            bodyLength = readDataView.getUInt();
            packegeLength = this.getTKResponseHeaderLength() + bodyLength;
          } else {
            return undefined;
          }
        } else {
          return undefined;
        }
        TKLog.debug(`${this.className}(${this.host}:${this.port})---->响应包头:(业务包类别:${tag},已收到数据长度:${data.byteLength},业务包数据长度:${packegeLength},业务包包体长度:${bodyLength})`);
        //截取数据包
        if (data.byteLength >= packegeLength) {
          //打印网络响应包时间
          if (tag == this.getTag()) {
            readDataView.offset = 19;
            let flowNo: number = readDataView.getInt();
            TKLog.debug(`${this.className}(${this.host}:${this.port})---->数据读取完成---->业务包流水号:${flowNo}`);
          }
          return data.slice(0, packegeLength);
        }
      }
    }
    return undefined;
  }

  /****************************请求数据解析转换****************************/

  /**
   *  获取请求对象
   *
   * @param flowNo 流水号
   * @param data   数据
   *
   * @return
   */
  public getSendData(flowNo: string, data: Object, userInfo?: Record<string, Object>): TKDataVO {
    let reqParam: Record<string, Object> = {};
    if (data) {
      Object.entries(data as Record<string, Object>).forEach((e, i) => {
        let key: string = e[0] as string;
        let value: Object = e[1] as Object;
        reqParam[key.toLowerCase()] = value;
      })
    }
    //功能号
    let funcNo: string = TKMapHelper.getString(reqParam, "funcno");
    if (TKStringHelper.isBlank(funcNo)) {
      funcNo = TKMapHelper.getString(reqParam, "funcNo");
    }
    let busServerId: string = userInfo ? TKMapHelper.getString(userInfo, "busServerId") : "";
    if (TKStringHelper.isBlank(busServerId)) {
      busServerId = this.serverName;
    }
    //获取功能号配置
    let quoteDomain: TKQuoteDomainVO = TKQuoteFunctionManager.shareInstance().getQuoteDomainVOByName(busServerId);
    let functionVO: TKQuoteFunctionVO = quoteDomain.getFunctionByJsonFuncNo(funcNo) as TKQuoteFunctionVO;
    let functionMode: number = userInfo ? TKMapHelper.getNumber(userInfo, "functionMode", -1) : -1;
    if (functionMode == -1) {
      functionMode = functionVO.mode;
    }
    let quoteUserInfo: TKQuoteUserInfo = {
      busServerId: busServerId,
      functionMode: functionMode,
      fields: new Array<number>()
    } as TKQuoteUserInfo;
    //请求参数数据
    let bodyData: ArrayBuffer = this.parseInputParam(reqParam, funcNo, flowNo, quoteUserInfo);
    //构建对象
    let dataVO: TKDataVO = {
      flowNo: flowNo,
      funcNo: functionVO.byteFuncNo,
      data: bodyData,
      dataMode: TKDataVOMode.Business
    } as TKDataVO;
    return dataVO;
  }

  //获取入参长度
  private getInputsByteLength(reqParam: Record<string, Object>, functionVO: TKQuoteFunctionVO,
    functionMode: TKFunctionMode): number {
    let inputsByteLength: number = 0;
    if (functionVO) {
      let inputVOs = functionVO.inputs;
      if (inputVOs && inputVOs.length > 0) {
        for (let inputVO of inputVOs) {
          let jsonName: string = inputVO.getJsonName(functionMode);
          let length: number = inputVO.length;
          let defaultValue: string = inputVO.defaultValue;
          if (TKStringHelper.isBlank(TKMapHelper.getString(reqParam, jsonName))) {
            reqParam[jsonName] = defaultValue;
          }
          if (TKStringHelper.isBlank(TKMapHelper.getString(reqParam, jsonName))) {
            if (inputVO.excludedNull) {
              continue;
            }
          }
          switch (inputVO.type) {
            case TKFunctionFieldType.Type: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.PType: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.Field: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.CFlag: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.Kline: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.Stock: {
              let param: string = TKMapHelper.getString(reqParam, jsonName);
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Stocks: {
              let param: string = TKMapHelper.getString(reqParam, jsonName);
              if (TKStringHelper.isNotBlank(param)) {
                let inputs: Array<string> = param.split("|");
                if (inputs && inputs.length > 0) {
                  inputsByteLength += (inputs.length * length);
                }
              }
              break;
            }
            case TKFunctionFieldType.Float: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Double: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Int: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.UInt: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Char: {
              inputsByteLength += length;
              break;
            }
            case TKFunctionFieldType.Bool: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "false");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Long: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.ULong: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.LongLong: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.ULongLong: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Short: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.UShort: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            case TKFunctionFieldType.Byte: {
              let param: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = param.split("|");
              if (inputs && inputs.length > 0) {
                inputsByteLength += (inputs.length * length);
              }
              break;
            }
            default: {
              inputsByteLength += length;
              break;
            }
          }
        }
      }
    }
    return inputsByteLength;
  }

  /**
   *  获取入参数据
   *
   * @param reqParam 请求参数
   * @param funcNo   功能号
   *
   * @return 入参2进制数据
   */
  private parseInputParam(reqParam: Record<string, Object>, funcNo: string, flowNo: string,
    userInfo: TKQuoteUserInfo): ArrayBuffer {
    //记录请求来源的原始serverID
    if (TKStringHelper.isBlank(userInfo.busServerId)) {
      userInfo.busServerId = this.serverName;
    }
    this.reqUserInfoMap.set(flowNo, userInfo);
    let quoteDomain: TKQuoteDomainVO = TKQuoteFunctionManager.shareInstance()
      .getQuoteDomainVOByName(userInfo.busServerId);
    //获取功能号配置
    let functionVO: TKQuoteFunctionVO | undefined = quoteDomain.getFunctionByJsonFuncNo(funcNo);
    if (functionVO) {
      let bodyData: TKWriteDataView =
        new TKWriteDataView(this.getInputsByteLength(reqParam, functionVO, userInfo.functionMode));
      let inputVOs: Array<TKQuoteInputVO> = functionVO.inputs;
      if (inputVOs && inputVOs.length > 0) {
        for (let inputVO of inputVOs) {
          let jsonName: string = inputVO.getJsonName(userInfo.functionMode);
          let length: number = inputVO.length;
          if (TKStringHelper.isBlank(TKMapHelper.getString(reqParam, jsonName))) {
            reqParam[jsonName] = inputVO.defaultValue;
          }
          if (TKStringHelper.isBlank(TKMapHelper.getString(reqParam, jsonName))) {
            if (inputVO.excludedNull) {
              continue;
            }
          }
          switch (inputVO.type) {
            case TKFunctionFieldType.Type: {
              let typesStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putType(typesStr, length);
              break;
            }
            case TKFunctionFieldType.PType: {
              let ptypesStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putType(ptypesStr, length);
              break;
            }
            case TKFunctionFieldType.Field: {
              let fieldsStr: string = TKMapHelper.getString(reqParam, jsonName);
              if (TKStringHelper.isNotBlank(fieldsStr)) {
                let fields: Array<string> = fieldsStr.split(":");
                this.reqUserInfoMap.get(flowNo)!.fields = fields.map(field => Number(field));
              }
              bodyData.putField(fieldsStr, length);
              break;
            }
            case TKFunctionFieldType.CFlag: {
              let cflag: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putCFlag(cflag, length);
              break;
            }
            case TKFunctionFieldType.Kline: {
              let kline: string = TKMapHelper.getString(reqParam, jsonName, "day");
              bodyData.putKline(kline);
              break;
            }
            case TKFunctionFieldType.Stock: {
              let stockStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putStock(stockStr, length);
              break;
            }
            case TKFunctionFieldType.Stocks: {
              let stocksStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putStocks(stocksStr, length);
              break;
            }
            case TKFunctionFieldType.Float: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putFloat(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Double: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putDouble(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Int: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putInt(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.UInt: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putUInt(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Char: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName);
              bodyData.putChar(TKWriteDataView.CHAR_GBK, inputStr, length);
              break;
            }
            case TKFunctionFieldType.Bool: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "false");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putBool(input == "true");
              }
              break;
            }
            case TKFunctionFieldType.Long: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putLong(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.ULong: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putULong(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.LongLong: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putLong(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.ULongLong: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putULong(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Short: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putShort(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.UShort: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putUShort(Number(input));
              }
              break;
            }
            case TKFunctionFieldType.Byte: {
              let inputStr: string = TKMapHelper.getString(reqParam, jsonName, "0");
              let inputs: Array<string> = TKStringHelper.split(inputStr, "|");
              for (let input of inputs) {
                bodyData.putByte(Number(input));
              }
              break;
            }
            default: {
              break;
            }
          }
        }
        let dataBuffer: ArrayBuffer = bodyData.buffer;
        let offset: number = bodyData.offset;
        return dataBuffer.slice(0, offset);
      }
      return bodyData.buffer;
    }
    return new ArrayBuffer(0);
  }

  /**
   *  处理加工发送数据
   *
   * @param data 数据
   *
   * @return 处理后的结果
   */
  public async processSendData(dataVO: TKDataVO): Promise<ArrayBuffer> {
    if (dataVO.dataMode == TKDataVOMode.Business) {
      let bodyData: ArrayBuffer = dataVO.data;
      //发送数据包
      let origDataLength: number = bodyData.byteLength;
      let bBuffer: ArrayBuffer = this.buildTKSendMessage(dataVO.funcNo, dataVO.flowNo, bodyData, origDataLength);
      return bBuffer;
    }
    return dataVO.data;
  }

  /****************************发送请求****************************/

  /**
   *  构造请求数据
   *
   * @param messageType 消息类型
   * @param bodydata    消息体
   * @param origDataLength    消息体原始长度
   *
   * @return 发送的消息数据
   */
  protected buildTKSendMessage(messageType: string, flowNo: string, bodyData: ArrayBuffer,
    origDataLength: number): ArrayBuffer {
    //请求体
    if (!bodyData) {
      bodyData = new ArrayBuffer(0);
    }
    //计算请求包长度
    let bodySize: number = bodyData.byteLength;
    //构建消息数据对象
    let bBuffer: TKWriteDataView = new TKWriteDataView(this.getTKRequestHeaderLength() + bodySize);
    //包标记
    bBuffer.putChar(TKWriteDataView.CHAR_GBK, this.getTag(), 2);
    //协议版本号
    let server: TKServer | undefined = TKGatewayManager.shareInstance().getServer(this.serverName);
    let serverVersion: string = server!.serverVersion;
    let version: number = TKStringHelper.isBlank(serverVersion) ? TKQuoteV3Client.TK_VERSION :
    TKHexHelper.integerFromHexString(serverVersion);
    bBuffer.putInt(version);
    //数据类型 0表示正常，1表示加密，2表示压缩，3表示先加密后压缩
    let dataType: number = TKQuoteV3Client.TK_DATATYPE;
    bBuffer.putByte(dataType);
    //包体长度
    bBuffer.putUInt(bodySize);
    //原始包体长度
    bBuffer.putUInt(origDataLength);
    //分支号
    let branchId: number = Number(messageType.substring(0, 1));
    bBuffer.putUShort(branchId);
    //功能号
    let commandId: number = Number(messageType);
    bBuffer.putUShort(commandId);
    //流水号
    bBuffer.putUInt(Number(flowNo));
    //错误号
    let code: number = TKQuoteV3Client.TK_CODE;
    bBuffer.putInt(code);
    //保留字段
    bBuffer.putChar(TKWriteDataView.CHAR_GBK, "", 8);
    //包体内容
    bBuffer.putBytes(bodyData);
    TKLog.info(`${this.className}(${this.host}:${this.port})---->发送数据包(业务包流水号:${flowNo},业务包类别:${this.getTag()},业务包版本号:${TKHexHelper.hexStringFromInteger(version)},业务包数据类型:${TKQuoteV3Client.TK_DATATYPE},业务包体长度:${bodySize},业务包分支号:${branchId},业务包功能号:${messageType})`);
    return bBuffer.buffer;
  }

  /****************************处理响应数据***************************/

  /**
   *  转换结果数据到最后的resultVo的数据字典对象
   *
   * @param resultData 结果数据
   *
   * @return
   */
  protected async convertTKResultDataToResultDic(resultData: ArrayBuffer): Promise<Record<string, Object>> {
    let readDataView: TKReadDataView = new TKReadDataView(resultData);
    /*************************解析包头开始****************************/
    //获取包标记
    let tag: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 2);
    //获取版本号
    let version: number = readDataView.getInt();
    //数据类型
    let dataType: number = readDataView.getByte();
    //包体长度
    let dataLength: number = readDataView.getUInt();
    //原始包体长度
    let origDataLength: number = readDataView.getUInt();
    //分支号
    let branchId: number = readDataView.getShort();
    //功能号
    let comandId: number = readDataView.getShort();
    //流水号
    let flowNo: number = readDataView.getInt();
    //错误代码
    let code: number = readDataView.getInt();
    //备用字段
    let reserved: string = readDataView.getChar(TKReadDataView.CHAR_GBK, 8);
    /*************************解析包头结束****************************/

    //解析包体
    TKLog.info(`${this.className}(${this.host}:${this.port})---->读取数据包(业务包流水号:${flowNo},业务包类别:${tag},业务包版本号:${TKHexHelper.hexStringFromInteger(version)},业务包数据类型:${dataType},业务包体长度:${dataLength},业务包分支号:${branchId},业务包功能号:${comandId})`);
    //获取包体内容
    let bodyData: Uint8Array = dataLength > 0 ? new Uint8Array(readDataView.getBytes(dataLength)) : new Uint8Array();
    if (bodyData && bodyData.byteLength > 0) {
      if (dataType == 2) {
        bodyData = TKZlibHelper.unCompress(bodyData);
        if (bodyData.byteLength > origDataLength) {
          bodyData = bodyData.subarray(0, origDataLength);
        }
        if (!bodyData || bodyData.byteLength == 0) {
          TKLog.error(`${this.className}(${this.host}:${this.port})---->didReadDataError(flowNo:${flowNo},业务包类别:${tag},业务包功能号:${comandId},数据解压失败)`);
        }
      }
    }
    return this.parseBusinessData(buffer.from(bodyData).buffer, String(comandId), String(flowNo), code);
  }

  /****************************返回行情业务数据包相关解析***************************/
  private defaultValueForFieldType(type: TKFunctionFieldType): Object {
    let result: Object = "";
    switch (type) {
      case TKFunctionFieldType.Float: {
        result = 0.0;
        break;
      }
      case TKFunctionFieldType.Double: {
        result = 0.0;
        break;
      }
      case TKFunctionFieldType.Stock: {
        result = "";
        break;
      }
      case TKFunctionFieldType.Char: {
        result = "";
        break;
      }
      case TKFunctionFieldType.Int: {
        result = 0;
        break;
      }
      case TKFunctionFieldType.UInt: {
        result = 0;
        break;
      }
      case TKFunctionFieldType.Short: {
        result = 0;
        break;
      }
      case TKFunctionFieldType.UShort: {
        result = 0;
        break;
      }
      case TKFunctionFieldType.Long: {
        result = 0;
        break;
      }
      case TKFunctionFieldType.ULong: {
        result = 0;
        break;
      }
      case TKFunctionFieldType.LongLong: {
        result = 0;
        break;
      }
      case TKFunctionFieldType.ULongLong: {
        result = 0;
        break;
      }
      case TKFunctionFieldType.Byte: {
        result = 0;
        break;
      }
      case TKFunctionFieldType.Bool: {
        result = 0;
        break;
      }
      default: {
        break;
      }
    }
    return result;
  }

  /**
   *  解析字段到行数据里面，这里的行数据是数据字典格式
   *
   * @param readDataView 要解析的2进制数据
   * @param name        名称
   * @param type        类型
   * @param length      长度
   * @param dataRow     行数组数据,存放解析的结果
   */
  private parseDataRowForDic(readDataView: TKReadDataView, name: string, type: TKFunctionFieldType, length: number,
    dataRow: Record<string, Object>, funcNo: string, flowNo: string) {
    switch (type) {
      case TKFunctionFieldType.Float: {
        let output: number = readDataView.getFloat();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Double: {
        let output: number = readDataView.getDouble();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Char: {
        let output: string = readDataView.getChar(TKReadDataView.CHAR_GBK, length);
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Stock: {
        let output: string = readDataView.getChar(TKReadDataView.CHAR_GBK, length);
        if (TKStringHelper.isNotBlank(output)) {
          output = output.substring(0, 2) + ":" + output.substring(2, output.length);
        }
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Int: {
        let output: number = readDataView.getInt();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.UInt: {
        let output: number = readDataView.getUInt();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Short: {
        let output: number = readDataView.getShort();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.UShort: {
        let output: number = readDataView.getUShort();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Long: {
        let output: number = readDataView.getLong();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.ULong: {
        let output: number = readDataView.getULong();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.LongLong: {
        let output: number = readDataView.getLong();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.ULongLong: {
        let output: number = readDataView.getULong();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Byte: {
        let output: number = readDataView.getByte();
        dataRow[name] = output;
        break;
      }
      case TKFunctionFieldType.Bool: {
        let output: boolean = readDataView.getBool()
        dataRow[name] = Number(output);
        break;
      }
      default: {
        break;
      }
    }
  }

  /**
   *  解析字段到行数据里面，这里的行数据是数组格式
   *
   * @param readDataView 要解析的2进制数据
   * @param type        类型
   * @param length      长度
   * @param dataRow     行数组数据,存放解析的结果
   */
  private parseDataRowForArray(readDataView: TKReadDataView, type: TKFunctionFieldType, length: number,
    dataRow: Array<Object | null>, funcNo: string, flowNo: string) {
    if (readDataView.isOutBounds(length)) {
      let value: Object = this.defaultValueForFieldType(type);
      dataRow.push(value);
      readDataView.addOffset(length);
      return;
    }
    switch (type) {
      case TKFunctionFieldType.Float: {
        let output: number = readDataView.getFloat();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.Double: {
        let output: number = readDataView.getDouble();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.Stock: {
        let output: string = readDataView.getChar(TKReadDataView.CHAR_GBK, length);
        if (TKStringHelper.isNotBlank(output)) {
          output = output.substring(0, 2) + ":" + output.substring(2, output.length);
        }
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.Char: {
        let output: string = readDataView.getChar(TKReadDataView.CHAR_GBK, length);
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.Int: {
        let output: number = readDataView.getInt();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.UInt: {
        let output: number = readDataView.getUInt();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.Short: {
        let output: number = readDataView.getShort();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.UShort: {
        let output: number = readDataView.getUShort();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.Long: {
        let output: number = readDataView.getLong();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.ULong: {
        let output: number = readDataView.getULong();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.LongLong: {
        let output: number = readDataView.getLong();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.ULongLong: {
        let output: number = readDataView.getULong();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.Byte: {
        let output: number = readDataView.getByte();
        dataRow.push(output);
        break;
      }
      case TKFunctionFieldType.Bool: {
        let output: boolean = readDataView.getBool();
        dataRow.push(Number(output));
        break;
      }
      default:
        break;
    }
  }

  /**
   解析输出头设置

   @param readDataView 包体数据
   @param functionVo 功能号对象
   @param resultDic  结果对象
   @return {index,count,fields}
   */
  private parseBusinessOutsetsData(readDataView: TKReadDataView, functionVo: TKQuoteFunctionVO,
    resultDic: Record<string, Object>, funcNo: string, flowNo: string) {
    let outsetDic: Record<string, Object> = {};
    let count: number = 0;
    let fieldsType: string = "";
    let fields: Array<number> = new Array<number>();
    let outsets: Array<TKQuoteOutsetVO> = functionVo.outsets;
    let functionMode: number | undefined = this.reqUserInfoMap.get(flowNo)?.functionMode;
    if (outsets && outsets.length > 0) {
      for (let outset of outsets) {
        let typeName: string = outset.typeName;
        let type: number = outset.type;
        let length: number = outset.length;
        let name: string = outset.getJsonName(functionMode);
        if (readDataView.isOutBounds(length)) {
          readDataView.addOffset(length);
          break;
        }
        switch (type) {
          case TKFunctionFieldType.Field: {
            fields = readDataView.getField(length);
            fieldsType = typeName;
            break;
          }
          case TKFunctionFieldType.Count: {
            count = readDataView.getInt();
            resultDic[name] = count;
            if (count > 100000) {
              TKLog.error(`${this.className}(${this.host}:${this.port})---->返回数据条数出现异常!(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count})`);
            }
            break;
          }
          default: {
            this.parseDataRowForDic(readDataView, name, type, length, resultDic, funcNo, flowNo);
            break;
          }
        }
      }
    }
    //处理数据条数
    if (count == 0) {
      if (readDataView.isValidate) {
        count = 1;
      }
    } else {
      if (!readDataView.isValidate) {
        count = 0;
      }
    }
    outsetDic["count"] = count;
    outsetDic["fieldsType"] = fieldsType;
    outsetDic["fields"] = fields;
    return outsetDic;
  }

  /**
   解析一维固定字段结果集
   @param readDataView 包体
   @param quoteDomain 功能号域
   @param functionVo 功能号
   @param count 数据条数
   @return
   */
  private parseBusinessOutputsData(readDataView: TKReadDataView, quoteDomain: TKQuoteDomainVO,
    functionVO: TKQuoteFunctionVO, count: number, funcNo: string, flowNo: string) {
    let functionMode: number | undefined = this.reqUserInfoMap.get(flowNo)?.functionMode;
    //结果集
    let dataList: Array<Object> = new Array<Object>();
    if (count > 0) {
      let outputVOs: Array<TKQuoteOutputVO> = functionVO.outputs;
      if (outputVOs && outputVOs.length > 0) {
        let repeatRowNum: number = functionVO.repeatRowNum;
        //每条记录的长度
        let rowLength: number = readDataView.validateLength / (count * repeatRowNum);
        if (rowLength > 0) {
          for (let i = 0; i < count; i++) {
            if (functionVO.getFunctionMode(functionMode) == 0) {
              let dataRow: Array<Object> = new Array<Object>();
              for (let k = 0; k < repeatRowNum; k++) {
                //每条记录的数据
                let rowData: TKReadDataView = new TKReadDataView(readDataView.getBytes(rowLength));
                for (let outputVO of outputVOs) {
                  this.parseDataRowForArray(rowData, outputVO.type, outputVO.length, dataRow, funcNo, flowNo);
                }
              }
              dataList.push(dataRow);
            } else {
              for (let k = 0; k < repeatRowNum; k++) {
                let dataRow: Record<string, Object> = {};
                //每条记录的数据
                let rowData: TKReadDataView = new TKReadDataView(readDataView.getBytes(rowLength));
                for (let outputVO of outputVOs) {
                  if (rowData.isOutBounds(outputVO.length)) {
                    break;
                  }
                  let jsonName: string = outputVO.getJsonName(functionMode);
                  this.parseDataRowForDic(rowData, jsonName, outputVO.type, outputVO.length, dataRow, funcNo, flowNo);
                }
                dataList.push(dataRow);
              }
            }
          }
        }
      }
    }
    return dataList;
  }

  /**
   一维动态字段解析
   @param readDataView 包体
   @param quoteDomain 功能号域
   @param functionVo 功能号
   @param count 数据条数
   @param fieldsType 动态字段类型
   @param fields 动态字段
   @param funcNo 功能号
   @param flowNo 流水号
   @return
   */
  private parseBusinessFieldData(readDataView: TKReadDataView, quoteDomain: TKQuoteDomainVO,
    functionVO: TKQuoteFunctionVO, count: number, fieldsType: string, fields: Array<number>, funcNo: string,
    flowNo: string) {
    //结果集
    let functionMode: number | undefined = this.reqUserInfoMap.get(flowNo)?.functionMode;
    let dataList: Array<Object> = new Array<Object>();
    if (fields && fields.length > 0) {
      if (count > 0) {
        let fieldVOs: Array<TKQuoteFieldVO> = new Array<TKQuoteFieldVO>();
        for (let i = 0; i < fields.length; i++) {
          if (fields[i] == 1) {
            let serno: number = i + 1;
            let fieldVO: TKQuoteFieldVO | undefined = quoteDomain.getFieldBySerno(serno, fieldsType);
            if (fieldVO) {
              fieldVOs.push(fieldVO);
            }
          }
        }
        if (fieldVOs && fieldVOs.length > 0) {
          let repeatRowNum: number = functionVO.repeatRowNum;
          //每条记录的长度
          let rowLength: number = readDataView.validateLength / (count * repeatRowNum);
          if (rowLength > 0) {
            for (let i = 0; i < count; i++) {
              if (functionVO.getFunctionMode(functionMode) == 0) {
                let dataRow: Array<Object | null> = new Array<Object | null>();
                for (let k = 0; k < repeatRowNum; k++) {
                  let rowData: TKReadDataView = new TKReadDataView(readDataView.getBytes(rowLength));
                  for (let fieldVO of fieldVOs) {
                    for (let temp = dataRow.length; temp < ((fieldVO.serno - 1) + (fields.length * k)); temp++) {
                      dataRow.push(null);
                    }
                    if (fieldsType == "field" && fieldVO.serno == 24 && functionVO.isStockIndex) {
                      this.parseDataRowForArray(rowData, fieldVO.type, 11, dataRow, funcNo, flowNo);
                    } else {
                      this.parseDataRowForArray(rowData, fieldVO.type, fieldVO.length, dataRow, funcNo, flowNo);
                    }
                  }
                }
                let dr: Array<Object> = new Array<Object>();
                if (this.reqUserInfoMap) {
                  let fieldSns: Array<number> | undefined = this.reqUserInfoMap.get(flowNo)?.fields;
                  if (fieldSns && fieldSns.length > 0) {
                    for (let k = 0; k < repeatRowNum; k++) {
                      for (let fieldSn of fieldSns) {
                        let fieldSnDataIndex: number = (fieldSn - 1) + (fields.length * k);
                        let fieldSnData: Object | null = null;
                        if (fieldSnDataIndex < dataRow.length) {
                          fieldSnData = dataRow[fieldSnDataIndex];
                        }
                        //处理默认空数据
                        if (fieldSnData === null) {
                          let fieldSnVO: TKQuoteFieldVO | undefined = quoteDomain.getFieldBySerno(fieldSn, fieldsType);
                          if (fieldSnVO) {
                            fieldSnData = this.defaultValueForFieldType(fieldSnVO.type);
                          }
                        }
                        dr.push(fieldSnData as Object);
                      }
                    }
                  }
                }
                dataList.push(dr);
              } else {
                for (let k = 0; k < repeatRowNum; k++) {
                  let dataRow: Record<string, Object> = {};
                  let rowData: TKReadDataView = new TKReadDataView(readDataView.getBytes(rowLength));
                  for (let fieldVO of fieldVOs) {
                    if (fieldsType == "field" && fieldVO.serno == 24 && functionVO.isStockIndex) {
                      if (rowData.isOutBounds(11)) {
                        break;
                      }
                      this.parseDataRowForDic(rowData, fieldVO.getJsonName(functionMode), fieldVO.type, 11, dataRow,
                        funcNo, flowNo);
                    } else {
                      if (rowData.isOutBounds(fieldVO.length)) {
                        break;
                      }
                      this.parseDataRowForDic(rowData, fieldVO.getJsonName(functionMode), fieldVO.type, fieldVO.length,
                        dataRow, funcNo, flowNo);
                    }
                  }
                  dataList.push(dataRow);
                }
              }
            }
          }
        }
      }
    }
    return dataList;
  }

  /**
   解析二维输出动态字段结果
   @param readDataView 包体
   @param quoteDomain 功能号域
   @param functionVo 功能号
   @param count 数据条数
   @param fieldsType 动态字段类型
   @param fields 动态字段
   @param funcNo 功能号
   @param flowNo 流水号
   @return
   */
  private parseBusinessOutputsFieldData(readDataView: TKReadDataView, quoteDomain: TKQuoteDomainVO,
    functionVO: TKQuoteFunctionVO, count: number, fieldsType: string, fields: Array<number>, funcNo: string,
    flowNo: string) {
    //结果集
    let functionMode: number | undefined = this.reqUserInfoMap.get(flowNo)?.functionMode;
    let dataList: Array<Object> = new Array<Object>();
    if (count > 0) {
      let fieldVOs: Array<TKQuoteFieldVO> = new Array<TKQuoteFieldVO>();
      if (fields && fields.length > 0) {
        for (let i = 0; i < fields.length; i++) {
          if (fields[i] == 1) {
            let fieldSn: number = i + 1;
            let fieldVO: TKQuoteFieldVO | undefined = quoteDomain.getFieldBySerno(fieldSn, fieldsType);
            if (fieldVO) {
              fieldVOs.push(fieldVO);
            }
          }
        }
      }
      for (let i = 0; i < count; i++) {
        let repeatRowNum: number = functionVO.repeatRowNum;
        let outputVOs: Array<TKQuoteOutputVO> = functionVO.outputs;
        if (outputVOs && outputVOs.length > 0) {
          if (functionVO.getFunctionMode(functionMode) == 0) {
            let dataRow: Array<Object> = new Array<Object>();
            let count0: number = 0;
            for (let outputVO of outputVOs) {
              if (outputVO.type == TKFunctionFieldType.Count) {
                if (!readDataView.isOutBounds(outputVO.length)) {
                  count0 = readDataView.getInt();
                  if (count0 > 100000) {
                    TKLog.error(`${this.className}(${this.host}:${this.port})---->返回二级数据条数出现异常!(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
                  }
                } else {
                  readDataView.addOffset(outputVO.length);
                }
              } else {
                this.parseDataRowForArray(readDataView, outputVO.type, outputVO.length, dataRow, funcNo, flowNo);
              }
            }
            //查看返回第二层结构的数据条数
            TKLog.info(`${this.className}(${this.host}:${this.port})---->返回第二层数据条数(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
            if (fieldVOs && fieldVOs.length > 0) {
              //处理第二层结构
              let dataRow0: Array<Object> = new Array<Object>();
              if (count0 > 0) {
                //每条记录的长度
                for (let j = 0; j < count0; j++) {
                  //处理第三层结构
                  let dataRow1: Array<Object | null> = new Array<Object | null>();
                  for (let k = 0; k < repeatRowNum; k++) {
                    for (let fieldVO of fieldVOs) {
                      for (let temp = dataRow1.length; temp < ((fieldVO.serno - 1) + (fields.length * k)); temp++) {
                        dataRow1.push(null);
                      }
                      this.parseDataRowForArray(readDataView, fieldVO.type, fieldVO.length, dataRow1, funcNo, flowNo);
                    }
                  }
                  let dr: Array<Object | null> = new Array<Object | null>();
                  if (this.reqUserInfoMap) {
                    let fieldSns: Array<number> | undefined = this.reqUserInfoMap.get(flowNo)?.fields;
                    if (fieldSns && fieldSns.length > 0) {
                      for (let k = 0; k < repeatRowNum; k++) {
                        for (let fieldSn of fieldSns) {
                          let fieldSnDataIndex: number = (fieldSn - 1) + (fields.length * k);
                          let fieldSnData: Object | null = null;
                          if (fieldSnDataIndex < dataRow1.length) {
                            fieldSnData = dataRow1[fieldSnDataIndex];
                          }
                          //处理默认空数据
                          if (fieldSnData === null) {
                            let fieldSnVO: TKQuoteFieldVO | undefined =
                              quoteDomain.getFieldBySerno(fieldSn, fieldsType);
                            if (fieldSnVO) {
                              fieldSnData = this.defaultValueForFieldType(fieldSnVO.type);
                            }
                          }
                          dr.push(fieldSnData);
                        }
                      }
                    }
                  }
                  dataRow0.push(dr);
                }
              }
              dataRow.push(dataRow0);
            }
            dataList.push(dataRow);
          } else {
            let dataRow: Record<string, Object> = {};
            let count0: number = 0;
            let fieldData: string = "fieldData";
            for (let outputVO of outputVOs) {
              let jsonName: string = outputVO.getJsonName(functionMode);
              if (outputVO.type != TKFunctionFieldType.FieldData) {
                if (readDataView.isOutBounds(outputVO.length)) {
                  readDataView.addOffset(outputVO.length);
                  break;
                }
              }
              if (outputVO.type == TKFunctionFieldType.Count) {
                count0 = readDataView.getInt();
                dataRow[jsonName] = count0;
                if (count0 > 100000) {
                  TKLog.error(`${this.className}(${this.host}:${this.port})---->返回二级数据条数出现异常!(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
                }
              } else if (outputVO.type == TKFunctionFieldType.FieldData) {
                fieldData = jsonName;
              } else {
                this.parseDataRowForDic(readDataView, jsonName, outputVO.type, outputVO.length, dataRow, funcNo,
                  flowNo);
              }
            }
            //查看返回第二层结构的数据条数
            TKLog.info(`${this.className}(${this.host}:${this.port})---->返回第二层数据条数(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
            if (fieldVOs && fieldVOs.length > 0) {
              //处理第二层结构
              let dataRow0: Array<Object> = new Array<Object>();
              if (count0 > 0) {
                if (functionVO.getFunctionMode(functionMode) == 1) {
                  for (let j = 0; j < count0; j++) {
                    //处理第三层结构
                    let dataRow1: Array<Object | null> = new Array<Object | null>();
                    for (let k = 0; k < repeatRowNum; k++) {
                      for (let fieldVO of fieldVOs) {
                        for (let temp = dataRow1.length; temp < ((fieldVO.serno - 1) + (fields.length * k)); temp++) {
                          dataRow1.push(null);
                        }
                        this.parseDataRowForArray(readDataView, fieldVO.type, fieldVO.length, dataRow1, funcNo, flowNo);
                      }
                    }
                    let dr: Array<Object | null> = new Array<Object | null>();
                    if (this.reqUserInfoMap) {
                      let fieldSns: Array<number> | undefined = this.reqUserInfoMap.get(flowNo)?.fields;
                      if (fieldSns && fieldSns.length > 0) {
                        for (let k = 0; k < repeatRowNum; k++) {
                          for (let fieldSn of fieldSns) {
                            let fieldSnDataIndex: number = (fieldSn - 1) + (fields.length * k);
                            let fieldSnData: Object | null = null;
                            if (fieldSnDataIndex < dataRow1.length) {
                              fieldSnData = dataRow1[fieldSnDataIndex];
                            }
                            //处理默认空数据
                            if (fieldSnData === null) {
                              let fieldSnVO: TKQuoteFieldVO | undefined =
                                quoteDomain.getFieldBySerno(fieldSn, fieldsType);
                              if (fieldSnVO) {
                                fieldSnData = this.defaultValueForFieldType(fieldSnVO.type);
                              }
                            }
                            dr.push(fieldSnData);
                          }
                        }
                      }
                    }
                    dataRow0.push(dr);
                  }
                } else {
                  for (let j = 0; j < count0; j++) {
                    //处理第三层结构
                    for (let k = 0; k < repeatRowNum; k++) {
                      let dataRow1: Record<string, Object> = {};
                      for (let fieldVO of fieldVOs) {
                        if (readDataView.isOutBounds(fieldVO.length)) {
                          break;
                        }
                        this.parseDataRowForDic(readDataView, fieldVO.getJsonName(functionMode), fieldVO.type,
                          fieldVO.length, dataRow1, funcNo, flowNo);
                      }
                      dataRow0.push(dataRow1);
                    }
                  }
                }
              }
              dataRow[fieldData] = dataRow0;
            }
            dataList.push(dataRow);
          }
        }
      }
    }
    return dataList;
  }

  /**
   解析二维输出固定字段结果
   @param readDataView 包体
   @param quoteDomain 功能号域
   @param functionVo 功能号
   @param count 数据条数
   @param funcNo 功能号
   @param flowNo 流水号
   @return
   */
  private parseBusinessExtOutputsOtherData(readDataView: TKReadDataView, quoteDomain: TKQuoteDomainVO,
    functionVO: TKQuoteFunctionVO, count: number, funcNo: string, flowNo: string) {
    //结果集
    let functionMode: number | undefined = this.reqUserInfoMap.get(flowNo)?.functionMode;
    let dataList: Array<Object> = new Array<Object>();
    if (count > 0) {
      for (let i = 0; i < count; i++) {
        let repeatRowNum: number = functionVO.repeatRowNum;
        let outputVOs: Array<TKQuoteOutputVO> = functionVO.outputs;
        let extoutputVOs = functionVO.extoutputs;
        if (outputVOs && outputVOs.length > 0) {
          if (functionVO.getFunctionMode(functionMode) == 0) {
            let dataRow: Array<Object> = new Array<Object>();
            let count0: number = 0;
            for (let outputVO of outputVOs) {
              if (outputVO.type == TKFunctionFieldType.Count) {
                if (!readDataView.isOutBounds(outputVO.length)) {
                  count0 = readDataView.getInt();
                  if (count0 > 100000) {
                    TKLog.error(`${this.className}(${this.host}:${this.port})---->返回二级数据条数出现异常!(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
                  }
                } else {
                  readDataView.addOffset(outputVO.length);
                }
              } else {
                this.parseDataRowForArray(readDataView, outputVO.type, outputVO.length, dataRow, funcNo, flowNo);
              }
            }
            TKLog.info(`${this.className}(${this.host}:${this.port})---->返回第二层数据条数(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
            if (extoutputVOs && extoutputVOs.length > 0) {
              //处理第二层结构
              let dataRow0: Array<Object> = new Array<Object>();
              if (count0 > 0) {
                //每条记录的长度
                for (let j = 0; j < count0; j++) {
                  //处理第三层结构
                  let dataRow1: Array<Object> = new Array<Object>();
                  for (let k = 0; k < repeatRowNum; k++) {
                    for (let extoutputVO of extoutputVOs) {
                      this.parseDataRowForArray(readDataView, extoutputVO.type, extoutputVO.length, dataRow1, funcNo,
                        flowNo);
                    }
                  }
                  dataRow0.push(dataRow1);
                }
              }
              dataRow.push(dataRow0);
            }
            dataList.push(dataRow);
          } else {
            let dataRow: Record<string, Object> = {};
            let count0: number = 0;
            let fieldData: string = "fieldData";
            for (let outputVO of outputVOs) {
              let jsonName: string = outputVO.getJsonName(functionMode);
              if (outputVO.type != TKFunctionFieldType.FieldData) {
                if (readDataView.isOutBounds(outputVO.length)) {
                  readDataView.addOffset(outputVO.length);
                  break;
                }
              }
              if (outputVO.type == TKFunctionFieldType.Count) {
                count0 = readDataView.getInt();
                dataRow[jsonName] = count0;
                if (count0 > 100000) {
                  TKLog.error(`${this.className}(${this.host}:${this.port})---->返回二级数据条数出现异常!(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
                }
              } else if (outputVO.type == TKFunctionFieldType.FieldData) {
                fieldData = jsonName;
              } else {
                this.parseDataRowForDic(readDataView, jsonName, outputVO.type, outputVO.length, dataRow, funcNo,
                  flowNo);
              }
            }
            TKLog.info(`${this.className}(${this.host}:${this.port})---->返回第二层数据条数(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
            //查看返回第二层结构的数据条数
            if (extoutputVOs && extoutputVOs.length > 0) {
              //处理第二层结构
              let dataRow0: Array<Object> = new Array<Object>();
              if (count0 > 0) {
                //每条记录的长度
                if (functionVO.getFunctionMode(functionMode) == 1) {
                  for (let j = 0; j < count0; j++) {
                    //处理第三层结构
                    let dataRow1: Array<Object> = new Array<Object>();
                    for (let k = 0; k < repeatRowNum; k++) {
                      for (let extoutputVO of extoutputVOs) {
                        this.parseDataRowForArray(readDataView, extoutputVO.type, extoutputVO.length, dataRow1, funcNo,
                          flowNo);
                      }
                    }
                    dataRow0.push(dataRow1);
                  }
                } else {
                  for (let j = 0; j < count0; j++) {
                    //处理第三层结构
                    for (let k = 0; k < repeatRowNum; k++) {
                      let dataRow1: Record<string, Object> = {};
                      for (let extoutputVO of extoutputVOs) {
                        if (readDataView.isOutBounds(extoutputVO.length)) {
                          break;
                        }
                        this.parseDataRowForDic(readDataView, extoutputVO.getJsonName(functionMode), extoutputVO.type,
                          extoutputVO.length, dataRow1, funcNo, flowNo);
                      }
                      dataRow0.push(dataRow1);
                    }
                  }
                }
              }
              dataRow[fieldData] = dataRow0;
            }
            dataList.push(dataRow);
          }
        }
      }
    }
    return dataList;
  }

  /**
   解析二维输出固定字段结果
   @param readDataView 包体
   @param quoteDomain 功能号域
   @param functionVo 功能号
   @param count 数据条数
   @param funcNo 功能号
   @param flowNo 流水号
   @return
   */
  private parseBusinessOutputsOtherData(readDataView: TKReadDataView, quoteDomain: TKQuoteDomainVO,
    functionVO: TKQuoteFunctionVO, count: number, fieldsType: string, fields: Array<number>, funcNo: string,
    flowNo: string) {
    //结果集
    let functionMode: number | undefined = this.reqUserInfoMap.get(flowNo)?.functionMode;
    let dataList: Array<Object> = new Array<Object>();
    if (count > 0) {
      let fieldVOs: Array<TKQuoteFieldVO> = new Array<TKQuoteFieldVO>();
      fields = this.reqUserInfoMap.get(flowNo)?.fields ?? new Array<number>();
      if (fields && fields.length > 0) {
        for (let i = 0; i < fields.length; i++) {
          let fieldSn = fields[i];
          let fieldVO: TKQuoteFieldVO | undefined = quoteDomain.getFieldBySerno(fieldSn, fieldsType);
          if (fieldVO) {
            fieldVOs.push(fieldVO);
          }
        }
      }
      for (let i = 0; i < count; i++) {
        let repeatRowNum: number = functionVO.repeatRowNum;
        let outputVOs: Array<TKQuoteOutputVO> = functionVO.outputs;
        if (outputVOs && outputVOs.length > 0) {
          if (functionVO.getFunctionMode(functionMode) == 0) {
            let dataRow: Array<Object> = new Array<Object>();
            let count0: number = 0;
            for (let outputVO of outputVOs) {
              if (outputVO.type == TKFunctionFieldType.Count) {
                if (!readDataView.isOutBounds(outputVO.length)) {
                  count0 = readDataView.getInt();
                  if (count0 > 100000) {
                    TKLog.error(`${this.className}(${this.host}:${this.port})---->返回二级数据条数出现异常!(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
                  }
                } else {
                  readDataView.addOffset(outputVO.length);
                }
              } else {
                this.parseDataRowForArray(readDataView, outputVO.type, outputVO.length, dataRow, funcNo, flowNo);
              }
            }
            //查看返回第二层结构的数据条数
            TKLog.info(`${this.className}(${this.host}:${this.port})---->返回第二层数据条数(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
            if (fieldVOs && fieldVOs.length > 0) {
              //处理第二层结构
              let dataRow0: Array<Object> = new Array<Object>();
              if (count0 > 0) {
                //每条记录的长度
                for (let j = 0; j < count0; j++) {
                  //处理第三层结构
                  let dataRow1: Array<Object> = new Array<Object>();
                  for (let k = 0; k < repeatRowNum; k++) {
                    for (let fieldVO of fieldVOs) {
                      this.parseDataRowForArray(readDataView, fieldVO.type, fieldVO.length, dataRow1, funcNo, flowNo);
                    }
                  }
                  dataRow0.push(dataRow1);
                }
              }
              dataRow.push(dataRow0);
            }
            dataList.push(dataRow);
          } else {
            let dataRow: Record<string, Object> = {};
            let count0: number = 0;
            let fieldData: string = "fieldData";
            for (let outputVO of outputVOs) {
              let jsonName: string = outputVO.getJsonName(functionMode);
              if (outputVO.type != TKFunctionFieldType.FieldData) {
                if (readDataView.isOutBounds(outputVO.length)) {
                  readDataView.addOffset(outputVO.length);
                  break;
                }
              }
              if (outputVO.type == TKFunctionFieldType.Count) {
                count0 = readDataView.getInt();
                dataRow[jsonName] = count0;
                if (count0 > 100000) {
                  TKLog.error(`${this.className}(${this.host}:${this.port})---->返回二级数据条数出现异常!(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
                }
              } else if (outputVO.type == TKFunctionFieldType.FieldData) {
                fieldData = jsonName;
              } else {
                this.parseDataRowForDic(readDataView, jsonName, outputVO.type, outputVO.length, dataRow, funcNo,
                  flowNo);
              }
            }
            //查看返回第二层结构的数据条数
            TKLog.info(`${this.className}(${this.host}:${this.port})---->返回第二层数据条数(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count0})`);
            if (fieldVOs && fieldVOs.length > 0) {
              //处理第二层结构
              let dataRow0: Array<Object> = new Array<Object>();
              if (count0 > 0) {
                //每条记录的长度
                if (functionVO.getFunctionMode(functionMode) == 1) {
                  for (let j = 0; j < count0; j++) {
                    //处理第三层结构
                    let dataRow1: Array<Object> = new Array<Object>();
                    for (let k = 0; k < repeatRowNum; k++) {
                      for (let fieldVO of fieldVOs) {
                        this.parseDataRowForArray(readDataView, fieldVO.type, fieldVO.length, dataRow1, funcNo, flowNo);
                      }
                    }
                    dataRow0.push(dataRow1);
                  }
                } else {
                  for (let j = 0; j < count0; j++) {
                    //处理第三层结构
                    for (let k = 0; k < repeatRowNum; k++) {
                      let dataRow1: Record<string, Object> = {};
                      for (let fieldVO of fieldVOs) {
                        if (readDataView.isOutBounds(fieldVO.length)) {
                          break;
                        }
                        this.parseDataRowForDic(readDataView, fieldVO.getJsonName(functionMode), fieldVO.type,
                          fieldVO.length, dataRow1, funcNo, flowNo);
                      }
                      dataRow0.push(dataRow1);
                    }
                  }
                }
              }
              dataRow[fieldData] = dataRow0;
            }
            dataList.push(dataRow);
          }
        }
      }
    }
    return dataList;
  };

  /**
   *  解析业务数据
   *
   * @param data   2进制数据
   * @param funcNo 功能号
   */
  private parseBusinessData(bodyData: ArrayBuffer, funcNo: string, flowNo: string, errorNo: number) {
    //结果对象
    let resultDic: Record<string, Object> = {};
    let error_no: number = (errorNo > 0) ? 0 : errorNo;
    let error_info: string = TKMapHelper.getString(this.errorInfoMap, error_no + "", "");
    //结果集
    let dataList: Array<Object> = new Array<Object>();
    try {
      if (bodyData && bodyData.byteLength > 0) {
        let readDataView: TKReadDataView = new TKReadDataView(bodyData);
        if (error_no == 0) {
          let busServerId: string = this.reqUserInfoMap.get(flowNo)?.busServerId ?? "";
          if (TKStringHelper.isBlank(busServerId)) {
            busServerId = this.serverName;
          }
          let quoteDomain: TKQuoteDomainVO = TKQuoteFunctionManager.shareInstance().getQuoteDomainVOByName(busServerId);
          let functionVO: TKQuoteFunctionVO | undefined = quoteDomain.getFunctionByByteFuncNo(funcNo);
          if (functionVO) {
            //解析输出参数规则
            let outSetDic: Record<string, Object> =
              this.parseBusinessOutsetsData(readDataView, functionVO, resultDic, funcNo, flowNo);
            //返回的结果集数目
            let count: number = TKMapHelper.getNumber(outSetDic, "count");
            //动态股票字段类型
            let fieldsType: string = TKMapHelper.getString(outSetDic, "fieldsType");
            //动态股票字段
            let fields: Array<number> =
              TKMapHelper.getObject(outSetDic, "fields", new Array<number>()) as Array<number>;
            //查看返回的数据条数
            TKLog.info(`${this.className}(${this.host}:${this.port})---->返回数据条数(业务包流水号:${flowNo},业务包功能号:${funcNo},业务包数据条数:${count})`);
            let outputVOs: Array<TKQuoteOutputVO> = functionVO.outputs;
            let extoutputVOs: Array<TKQuoteOutputVO> = functionVO.extoutputs;
            if (outputVOs && outputVOs.length > 0) {
              if (TKStringHelper.isNotBlank(fieldsType)) {
                if (fields && fields.length > 0) {
                  //二维动态字段解析
                  dataList =
                    this.parseBusinessOutputsFieldData(readDataView, quoteDomain, functionVO, count, fieldsType, fields,
                      funcNo, flowNo);
                } else {
                  //二维固定字段解析
                  dataList =
                    this.parseBusinessOutputsOtherData(readDataView, quoteDomain, functionVO, count, fieldsType, fields,
                      funcNo, flowNo);
                }
              } else if (extoutputVOs && extoutputVOs.length > 0) {
                //二维固定字段解析
                dataList =
                  this.parseBusinessExtOutputsOtherData(readDataView, quoteDomain, functionVO, count, funcNo, flowNo);
              } else {
                //普通结果集解析
                dataList = this.parseBusinessOutputsData(readDataView, quoteDomain, functionVO, count, funcNo, flowNo);
              }
            } else {
              //一维动态字段解析
              dataList =
                this.parseBusinessFieldData(readDataView, quoteDomain, functionVO, count, fieldsType, fields, funcNo,
                  flowNo);
            }
          }
        } else {
          error_info = readDataView.getChar(TKReadDataView.CHAR_GBK, readDataView.byteLength) ?? "";
          error_info = error_info.trim();
        }
      }
    } catch (error) {
      error_no = -101;
      error_info = error.message;
    } finally {
      this.reqUserInfoMap.delete(flowNo);
    }
    resultDic["errorNo"] = error_no;
    resultDic["errorInfo"] = error_info;
    resultDic["flowNo"] = flowNo;
    resultDic["extend"] = `${this.getTag()}|${funcNo}`;
    resultDic["results"] = dataList;
    return resultDic;
  }
}