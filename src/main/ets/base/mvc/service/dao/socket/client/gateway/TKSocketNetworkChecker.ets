import { TKComBusTestSpeedClient, TKSocketMsgType } from '../common/TKComBusTestSpeedClient';
import { TKNetHelper } from '../../../../../../../util/net/TKNetHelper';
import { TKBusClientDelegate, TKBusMsgType } from '../common/TKComBusClient';
import { TKLog } from '../../../../../../../util/logger/TKLog';
import { TKObjectHelper } from '../../../../../../../util/data/TKObjectHelper';
import { TKMapHelper } from '../../../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../../../util/string/TKStringHelper';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>el<PERSON>, TKNumberRadix } from '../../../../../../../util/number/TKNumberHelper';

/**
 * 网络检测回调
 */
export type TKSocketNetworkCheckerBlock = (isFinished: boolean, errorNo: number, errorInfo: string) => void;

/**
 *网络检测
 */
export class TKSocketNetworkChecker implements TKBusClientDelegate {
  /**
   * 当前类名
   */
  private className?: string;
  /**
   * 开始检测时间
   */
  private beginCheckTime?: number;
  /**
   * 链接地址
   */
  private host?: string;
  /**
   * 链接端口
   */
  private port?: number;
  /**
   * socket连接
   */
  private testSpeedClient?: TKComBusTestSpeedClient;
  /**
   * 网络诊断回调函数
   */
  private socketNetworkCheckerCallBack?: TKSocketNetworkCheckerBlock;

  public constructor(host: string, port: number) {
    this.host = host;
    this.port = port;
    this.className = "TKSocketNetworkChecker";
  }

  /**
   * 初始化Socket
   */
  public initInnerSocket() {
    if (!this.testSpeedClient) {
      this.testSpeedClient = new TKComBusTestSpeedClient();
      this.testSpeedClient.serverName = "";
      this.testSpeedClient.delegate = this;
    }
    //初始化链接
    this.testSpeedClient.initCTX();
  }

  /**
   * 释放Socket
   */
  public releaseInnerSocket() {
    this.testSpeedClient?.disConnect();
  }

  /**
   *  执行回调
   */
  private doSocketNetworkCheckerCallBack(isFinished: boolean, errorNo: number, errorInfo: string) {
    this.socketNetworkCheckerCallBack?.(isFinished, errorNo, errorInfo);
    if (isFinished) {
      this.socketNetworkCheckerCallBack = undefined;
    }
  }

  /**
   *  进行线程网络检测
   */
  public start(socketNetworkCheckerCallBack: TKSocketNetworkCheckerBlock) {
    this.initInnerSocket();
    this.socketNetworkCheckerCallBack = socketNetworkCheckerCallBack;
    if (TKNetHelper.isNetAvailable()) {
      this.beginCheckTime = new Date().getTime();
      this.testSpeedClient?.connect(this.host!, this.port!);
    } else {
      let currentTime: number = new Date().getTime();
      let connectTime: number = currentTime - this.beginCheckTime!;
      let errorInfo: string = `网络未连接(耗时:${connectTime}毫秒)`;
      this.doSocketNetworkCheckerCallBack(true, -1, errorInfo);
    }
  }

  /**
   *  处理代理
   *
   * @param msgType
   * @param wparam
   * @param lparam
   */
  public onNotifyMessage(obj: Object, msgType: TKBusMsgType, wparam?: Object, lparam?: Object) {
    let busClient: TKComBusTestSpeedClient = obj as TKComBusTestSpeedClient;
    switch (msgType) {
      case TKBusMsgType.Connect: {
        this.onNotifyConnectMessage(busClient);
        break;
      }
      case TKBusMsgType.DisConnect: {
        //流水号
        let flowNo: string = String(wparam ?? "")
        //错误对象
        let error: Record<string, Object> | undefined = lparam as Record<string, Object>;
        this.onNotifyDisConnectMessage(busClient, flowNo, error);
        break;
      }
      case TKBusMsgType.Result: {
        //流水号
        let flowNo: string = String(wparam ?? "")
        //结果集
        let result: Record<string, Object> = (lparam ?? {}) as Record<string, Object>;
        this.onNotifyResultMessage(busClient, flowNo, result);
        break;
      }
      default:
        break;
    }
  }

  /**
   * 处理连接成功的消息
   */
  private onNotifyConnectMessage(busClient: TKComBusTestSpeedClient) {
    TKLog.info(`${this.className}网关服务器(${this.host}:${this.port})didConnectToHost`);
    let currentTime: number = new Date().getTime();
    let connectTime: number = currentTime - this.beginCheckTime!;
    let errorInfo: string = `服务器[${busClient.connectedHost}]已连接(耗时:${connectTime}毫秒)`;
    this.doSocketNetworkCheckerCallBack(false, 0, errorInfo);
    //进行测速
    busClient.doSendTestSpeed();
  }

  /**
   * 处理连接异常断开的消息
   */
  private onNotifyDisConnectMessage(busClient: TKComBusTestSpeedClient, flowNo: string,
    error?: Record<string, Object>) {
    if (error) {
      TKLog.info(`${this.className}网关服务器(${this.host}:${this.port})didDisconnectWithError:${TKObjectHelper.toJsonStr(error)}`);
      busClient.disConnect();
      let currentTime: number = new Date().getTime();
      let connectTime: number = currentTime - this.beginCheckTime!;
      let errorInfo: string =
        `服务器[${busClient.connectedHost}]网络连接异常(耗时:${connectTime}毫秒),原因:${TKObjectHelper.toJsonStr(error)}`;
      this.doSocketNetworkCheckerCallBack(true, -1, errorInfo);
    }
  }

  /**
   * 处理请求返回结果
   */
  private onNotifyResultMessage(busClient: TKComBusTestSpeedClient, flowNo: string, result: Record<string, Object>) {
    let data: string = TKMapHelper.getString(result, "data");
    if (Number(flowNo) == TKSocketMsgType.TestSpeedReturn) {
      this.parseTestSpeedData(busClient, data);
    }
  }

  /**
   *  解析测试速度数据
   */
  private parseTestSpeedData(busClient: TKComBusTestSpeedClient, speedData: string) {
    TKLog.info(`${this.className}(${this.host}:${this.port})---->TestSpeedData---->${speedData}`);
    busClient.disConnect();
    if (TKStringHelper.isNotBlank(speedData)) {
      let currentTime: number = new Date().getTime();
      let connectTime: number = currentTime - this.beginCheckTime!;
      let dataSize: string = TKNumberHelper.formatNumberRadix(1024, 2, TKNumberRadix.BIN_MEM);
      let errorInfo: string =
        `服务器[${busClient.connectedHost}]测速数据已返回(大小:${dataSize},耗时:${connectTime}毫秒)`;
      this.doSocketNetworkCheckerCallBack(true, 0, errorInfo);
    }
  }
}