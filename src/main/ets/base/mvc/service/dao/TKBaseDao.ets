import { TKObjectHelper } from '../../../../util/data/TKObjectHelper';
import { TKLog } from '../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../../util/system/TKSystemHelper';
import { TKReqParamVO } from '../../model/TKReqParamVO';
import { TKServiceDaoDelegate } from '../protocol/TKServiceDaoDelegate';

/***
 * 基础Dao处理逻辑
 */
export abstract class TKBaseDao implements TKServiceDaoDelegate {
  /**
   *  处理请求
   *
   * @param reqParamVO 请求对象
   */
  public invoke(reqParamVO: TKReqParamVO) {
    this.debugRequest(reqParamVO);
    reqParamVO.beginTime = new Date().getTime();
    this.buildCommonHttpHead(reqParamVO);
  }

  /**
   * 构建公共请求头
   */
  private buildCommonHttpHead(reqParamVO: TKReqParamVO) {
    let headerFieldMap: Record<string, Object | undefined> = {};
    if (reqParamVO.headerFieldDic) {
      headerFieldMap = reqParamVO.headerFieldDic;
    }
    let userAgent: string =
      TKMapHelper.getString(headerFieldMap, "User-Agent", TKSystemHelper.getConfig("webViewPool.userAgent"));
    if (TKStringHelper.isNotBlank(userAgent)) {
      headerFieldMap["User-Agent"] = userAgent;
    }
    reqParamVO.headerFieldDic = headerFieldMap;
  }

  /**
   *  调试模式
   */
  private debugRequest(reqParamVO: TKReqParamVO) {
    let reqStr: string = "";
    let reqParam: Record<string, Object | undefined> = reqParamVO.reqParam;
    if (reqParam) {
      Object.entries(reqParam).forEach((e, i) => {
        let key: string = e[0] as string;
        let value: Object = e[1] as Object;
        if (!TKStringHelper.endsWith(key, "@@F") && TKObjectHelper.nonNull(value)) {
          reqStr += `${key}:${value}\n`;
        }
      })
    }
    TKLog.debug(`\n-------请求URL---------\n${reqParamVO.url}\n-------请求流水号-------\nflowNo:${reqParamVO.flowNo}\n-------请求参数begin-------\n${reqStr}-------请求参数end--------`);
  }


  /**
   *  清理请求
   *
   * @param flowNo 流水号
   */
  public clearRequest(flowNo: string) {

  }

  /**
   *  清除组请求
   *
   * @param groupNo 组号
   */
  public clearGroup(groupNo: string) {

  }
}