import { TKLoadInfoVO } from '../../model/TKLoadInfoVO';
import { TKReqParamVO } from '../../model/TKReqParamVO';
import { TKResultVO } from '../../model/TKResultVO';

/**
 * 通用回调处理
 */
export type TKServiceCallBackFunc = (resultVO: TKResultVO) => void;

export interface TKServiceDelegate {
  /**
   *  处理失败
   */
  onFault(resulVO: TKResultVO): void;

  /**
   *  请求成功
   *
   * @param resultVO 返回的结果对象
   */
  onResult(resulVO: TKResultVO): void;

  /**
   *  请求服务
   *
   * @param reqParamVO   请求对象
   * @param callBackFunc 回调函数
   */
  serviceInvoke(reqParamVO: TKReqParamVO, callBackFunc?: TKServiceCallBackFunc): void;

  /**
   *  根据流水号获取请求
   *
   * @param flowNo 流水号
   *
   * @return 请求对象
   */
  getRequestWithFlowNo(flowNo: string): TKReqParamVO | undefined;

  /**
   *  清除请求对象，不执行回调函数
   *
   * @param flowNo  流水号
   */
  clearRequest(flowNo: string): void;

  /**
   *  清理所有请求，不执行回调函数
   */
  clearAllRequest(): void;

  /**
   *取消请求对象，执行回调函数
   *
   * @param flowNo 流水号
   */
  cancelRequest(flowNo: string): void;

  /**
   *  取消所有请求，执行回调函数
   */
  cancelAllRequest(): void;

  /**
   *  获取当前请求队列长度
   */
  getRequestQueueLength(): number;

  /**
   *  请求开始
   */
  onOpen(reqParamVO: TKReqParamVO): void;

  /**
   * @param result 数据对象
   */
  onProgress(reqParamVO: TKReqParamVO, loadInfoVO: TKLoadInfoVO): void;

  /**
   *  请求关闭
   */
  onClose(reqParamVO: TKReqParamVO): void;

}