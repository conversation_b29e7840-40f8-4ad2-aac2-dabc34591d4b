/**
 * 结果过滤器
 */
import { TKReqParamVO } from '../../model/TKReqParamVO';
import { TKResultVO } from '../../model/TKResultVO';

export interface TKServiceFilterDelegate {

  /**
   *  请求拦截
   *
   * @param reqParamVo 请求对象
   *
   * @return 是否继续执行
   */
  requestFilter?(reqParamVO: TKReqParamVO): boolean;

  /**
   *  结果拦截器
   *
   * @param resultVo 返回对象
   *
   * @return 是否继续执行
   */
  resultFilter?(resultVO: TKResultVO): boolean;

}