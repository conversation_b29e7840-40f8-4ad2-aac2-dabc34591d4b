/**
 *  服务通信Dao协议
 */
import { TKReqParamVO } from '../../model/TKReqParamVO';

export interface TKServiceDaoDelegate {

  /**
   *  处理请求
   *
   * @param reqParamVo 请求对象
   */
  invoke(reqParamVO: TKReqParamVO): void;

  /**
   *  清除请求对象
   *
   * @param flowNo 流水号
   */
  clearRequest(flowNo: string): void;

  /**
   *  清除组请求对象
   *
   * @param groupNo 组号
   */
  clearGroup(groupNo: string): void;

}