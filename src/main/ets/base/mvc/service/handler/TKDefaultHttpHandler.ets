import { TKObjectHelper } from '../../../../util/data/TKObjectHelper';
import { TKLog } from '../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../util/map/TKMapHelper';
import { TKResultVO } from '../../model/TKResultVO';
import { TKProcessDataDelegate } from '../protocol/TKProcessDataDelegate';

/**
 * 默认数据处理器
 */
export class TKDefaultHttpHandler implements TKProcessDataDelegate {
  /**
   *  解析json协议数据
   *
   * @param resultData 返回的json数据 数据字典类型
   *
   * @return ResultVO
   */
  public processResultData(resultData: Object): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    resultVO.originData = resultData;
    if (typeof resultData === 'object') {
      resultVO.isStandardResult = true;
      let resultDic: Record<string, Object> = resultData as Record<string, Object>;
      if (TKObjectHelper.isNull(resultDic["error_no"])) {
        if (TKObjectHelper.nonNull(resultDic["code"]) && TKObjectHelper.nonNull(resultDic["msg"])) {
          let code: string = TKMapHelper.getString(resultDic, "code");
          let msg: string = TKMapHelper.getString(resultDic, "msg");
          let data: Object | undefined = TKMapHelper.getObject(resultDic, "data");
          resultDic = {};
          resultDic["error_no"] = code;
          resultDic["error_info"] = msg;
          resultDic["results"] = data ?? [];
          resultVO.isStandardResult = false;
        } else {
          let errorNo: string = TKMapHelper.getString(resultDic, "errorNo");
          let errorInfo: string = TKMapHelper.getString(resultDic, "errorInfo");
          let results: Record<string, Object> = resultDic;
          resultDic = {};
          resultDic["error_no"] = errorNo;
          resultDic["error_info"] = errorInfo;
          resultDic["results"] = results;
          resultVO.isStandardResult = false;
        }
      }
      resultVO.errorType = TKMapHelper.getNumber(resultDic, "error_type");
      resultVO.errorNo = TKMapHelper.getNumber(resultDic, "error_no");
      resultVO.errorInfo = TKMapHelper.getString(resultDic, "error_info");
      resultVO.serverTime = TKMapHelper.getNumber(resultDic, "excute_time");
      let dsObj: Object | undefined = TKMapHelper.getObject(resultDic, "dsName");
      if (typeof dsObj === "string") {
        dsObj = [dsObj];
      }
      let dsName: Array<string> | undefined = dsObj as Array<string> | undefined;
      if (dsName) {
        if (dsName.length == 0) {
          dsName = ["results"];
        }
        for (let key of dsName) {
          let result: Object | undefined = TKMapHelper.getObject(resultDic, key);
          if (Array.isArray(result)) {
            resultVO.setResults(result, key);
          } else if (typeof result === "object") {
            resultVO.setResults([result], key);
          } else {
            if (result !== undefined) {
              let results: Array<Record<string, Object>> = [{ "content": result }];
              resultVO.setResults(results, key);
            } else {
              resultVO.setResults([], key);
            }
          }
        }
      } else {
        let result: Object | undefined = TKMapHelper.getObject(resultDic, "results");
        if (Array.isArray(result)) {
          resultVO.setResults(result, "results");
        } else if (typeof result === "object") {
          resultVO.setResults([result], "results");
        } else {
          if (result !== undefined) {
            let results: Array<Record<string, Object>> = [{ "content": result }];
            resultVO.setResults(results, "results");
          } else {
            resultVO.setResults([resultDic], "results");
          }
        }
        dsName = ["results"];
      }
      resultVO.dsName = dsName;
      resultVO.fields = TKMapHelper.getObject(resultDic, "fields") as Array<string> | undefined ?? [];
    } else {
      TKLog.error("解析的数据格式错误，应该为JSON！");
    }
    return resultVO;
  }
}
