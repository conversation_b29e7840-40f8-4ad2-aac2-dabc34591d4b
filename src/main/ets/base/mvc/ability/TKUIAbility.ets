import { AbilityConstant, UIAbility, Want } from '@kit.AbilityKit';
import { window } from '@kit.ArkUI';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKLog } from '../../../util/logger/TKLog';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKAppEngine } from '../../engine/TKAppEngine';

/**
 * Lift cycle management of Ability.
 */
export class TKUIAbility extends UIAbility {
  /**
   * 加载的路由页面
   */
  private _pagePath: string | undefined = undefined;
  /**
   * 状态栏背景颜色
   */
  private _statusBarColor: string | undefined = undefined;
  /**
   * 状态栏文字内容颜色
   */
  private _statusBarContentColor: string | undefined = undefined;
  /**
   * 窗体页面是否全屏，默认全屏
   */
  private _isLayoutFullScreen: boolean = true;

  /**
   * 设置加载的路由page
   * @param loadContent
   */
  protected setPagePath(pagePath: string) {
    this._pagePath = pagePath
  }

  /**
   * 设置状态栏背景的颜色
   * @param statusBarContentColor
   */
  protected setStatusBarColor(statusBarColor: string) {
    this._statusBarColor = statusBarColor
  }

  /**
   * 设置状态栏内容的颜色
   * @param statusBarContentColor
   */
  protected setStatusBarContentColor(statusBarContentColor: string) {
    this._statusBarContentColor = statusBarContentColor
  }

  /**
   * 设置窗体是否全屏
   * @param windowLayoutFullScreen
   */
  protected isLayoutFullScreen(isLayoutFullScreen: boolean) {
    this._isLayoutFullScreen = isLayoutFullScreen
  }

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    super.onCreate(want, launchParam);
  }

  onDestroy(): void {
    super.onDestroy();
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // 初始化思迪基础框架
    TKAppEngine.shareInstance().start({
      context: this.context,
      finishCallBack: () => {
        if (this._pagePath) {
          windowStage.loadContent(this._pagePath, (err, data) => {
            if (err.code) {
              TKLog.error("loadContent失败" + err.message);
              return
            }
            let windowClass: window.Window = windowStage.getMainWindowSync(); // 获取应用主窗口
            windowClass.setWindowLayoutFullScreen(this._isLayoutFullScreen)
              .then(() => {
                console.info('Succeeded in setting the window layout to full-screen mode.');
              })
              .catch((err: BusinessError) => {
                console.error('Failed to set the window layout to full-screen mode. Cause:' + JSON.stringify(err));
              });
            let windowSystemBarProperties: window.SystemBarProperties = {};
            if (TKStringHelper.isNotBlank(this._statusBarColor)) {
              windowSystemBarProperties.statusBarColor = this._statusBarColor;
            }
            if (TKStringHelper.isNotBlank(this._statusBarContentColor)) {
              windowSystemBarProperties.statusBarContentColor = this._statusBarContentColor;
            }
            if (Object.entries(windowSystemBarProperties).length > 0) {
              windowClass.setWindowSystemBarProperties(windowSystemBarProperties);
            }
          })
        }
      }
    });
  }

  onWindowStageDestroy(): void {
    super.onWindowStageDestroy();
  }

  onForeground(): void {
    super.onForeground();
  }

  onBackground(): void {
    super.onBackground();
  }
}