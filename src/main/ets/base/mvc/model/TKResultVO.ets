/**
 * 响应对象
 */
import { TKObjectHelper } from '../../../util/data/TKObjectHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKReqParamVO } from './TKReqParamVO';

export enum TKResultErrorType {
  //业务异常错误
  Business,
  //网络异常错误
  Network
}

export class TKResultVO {
  /**
   *  错误类型,用于区分网络异常还是业务异常
   */
  public errorType: TKResultErrorType = TKResultErrorType.Business;
  /**
   *  错误号
   */
  public errorNo: number = 0;
  /**
   *  错误信息
   */
  public errorInfo: string = "";
  /**
   *  结果集名称集合
   */
  public dsName: Array<string> | undefined = undefined;
  /**
   *  结果集名称集合
   */
  public serverTime: number = 0;
  /**
   *  请求对象
   */
  public reqParamVO: TKReqParamVO | undefined = undefined;
  /**
   *  Http的响应的头
   */
  public respHeaderFieldDic: Record<string, Object | undefined> | undefined = undefined;
  /**
   *  Http的返回cookie数组
   */
  public cookies: Array<Object> | undefined = undefined;
  /**
   *  请求结果集的头，使用于单结果集
   */
  public fields: Array<string> | undefined = undefined;
  /**
   *  是否缓存命中的数据
   */
  public isCacheData: boolean = false;
  /**
   *  是否思迪标准的数据结构
   */
  public isStandardResult: boolean = true;
  /**
   * 内部多结果集合映射
   */
  public resultMap: Record<string, Object> = {};
  /**
   * 原始数据对象
   */
  private _originData: Object | undefined = undefined;

  /**
   * 获取默认的结果集
   * @return 默认结果集
   */
  public get results(): Object {
    return this.getResults();
  }

  /**
   * 设置结果集
   * @param results
   */
  public set results(results: Object | null | undefined) {
    this.setResults(results)
  }

  /**
   * 设置原始数据
   * @param originData
   */
  public set originData(originData: Object | null | undefined) {
    if (TKObjectHelper.nonNull(originData)) {
      this._originData = originData as Object;
    } else {
      this._originData = undefined;
    }
  }

  public get originData(): Object | undefined {
    if (TKObjectHelper.nonNull(this._originData)) {
      return this._originData;
    } else {
      return this.toJson();
    }
  }

  /**
   *  获取指定的结果集
   *
   * @param dsName 结果集名称
   *
   * @return 结果集
   */
  public getResults(dsName: string = ""): Object {
    if (TKStringHelper.isBlank(dsName)) {
      if (this.dsName && this.dsName.length > 0) {
        if (this.dsName.indexOf("results") >= 0) {
          dsName = "results";
        } else {
          dsName = this.dsName[0];
        }
      } else {
        dsName = "results";
      }
    }
    let results: Array<Object> = this.resultMap[dsName] as Array<Object>;
    if (this.reqParamVO && !this.reqParamVO.isReturnList) {
      if (results && results.length > 0) {
        return results[0];
      } else {
        return {} as Record<string, Object>;
      }
    }
    return results;
  }

  /**
   *  设置指定的结果集
   *
   * @param results 结果集
   * @param dsName  结果集名称
   */
  public setResults(results: Object | null | undefined, dsName: string = "results") {
    if (TKStringHelper.isBlank(dsName)) {
      if (this.dsName && this.dsName.length > 0) {
        if (this.dsName.indexOf("results") >= 0) {
          dsName = "results";
        } else {
          dsName = this.dsName[0];
        }
      } else {
        dsName = "results";
      }
    }
    if (!results) {
      results = new Array<Object>();
    }
    if (Array.isArray(results)) {
      this.resultMap[dsName] = results;
    } else {
      results = [results];
      this.resultMap[dsName] = results;
    }
  }

  /**
   * 转成json
   * @returns
   */
  public toJson(): Record<string, Object | undefined> {
    let json: Record<string, Object | undefined> = TKObjectHelper.toJson(this) as Record<string, Object | undefined>;
    json["error_no"] = json["errorNo"];
    json["error_info"] = json["errorInfo"];
    json["results"] = undefined;
    json["errorNo"] = undefined;
    json["errorInfo"] = undefined;
    json = TKObjectHelper.assign(json, this.resultMap);
    return json as Record<string, Object | undefined>;
  }

  /**
   * 转成json字符串
   * @returns
   */
  public toJsonStr(): string {
    return JSON.stringify(this.toJson());
  }
}