/**
 *  Dao的类型
 */
import { TKCacheType } from '../../../util/cache/domain/TKCacheVO';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKServiceDelegate } from '../service/protocol/TKServiceDelegate';
import { TKLoadInfoVO } from './TKLoadInfoVO';

/**
 * 适配Dao
 */
export enum TKDaoType {
  Http,
  HttpToSocket,
  Socket
}

/**
 编码格式
 */
export enum TKCharEncoding {
  DEFAULT,
  UTF_8,
  GBK
}
;

/**
 *  Dao请求模式
 */
export enum TKDaoMode {
  /**
   *  短连接
   */
  Short,
  /**
   *  长连接
   */
  Long
}
;

/**
 * 加密模式
 */
export enum TKEncryMode {
  /**
   *  AES加密
   */
  Aes,
  /**
   *  DES加密
   */
  Des
}
;

/**
 * 数据类型
 */
export enum TKDataType {
  /**
   * <AUTHOR> 2015-09-15 09:09:57
   *
   *  正常数据
   */
  Normal,
  /**
   * <AUTHOR> 2015-09-15 09:09:12
   *
   *  加密
   */
  Encryt,
  /**
   * <AUTHOR> 2016-11-17 20:11:47
   *
   *  压缩
   */
  Compress,
  /**
   * <AUTHOR> 2016-11-17 20:11:27
   *
   *  先加密后压缩
   */
  Encryt_Compress,
  /**
   * <AUTHOR> 2016-11-17 20:11:00
   *
   *  先压缩后加密
   */
  Compress_Encryt
}
;

/**
 * 请求内容格式
 */
export enum TKContentType {
  /**
   *  默认自动适配模式
   */
  NONE,

  /**
   *  application/x-www-form-urlencoded 类型
   */
  WWW_FORM,

  /**
   *  multipart/form-data 类型
   */
  FORM_DATA,

  /**
   *  application/json 类型
   */
  JSON
}
;

/**
 * 行情接口数据模式
 */
export enum TKQuoteFunctionMode {
  /**
   *  默认自动适配模式
   */
  Auto = -1,

  /**
   * 简单格式-值数组
   */
  ARRAY = 0,

  /**
   * 标准格式-数据字典
   */
  JSON = 1,

  /**
   *  标准格式-别名数据字典Bean
   */
  BEAN = 2
}
;

/**
 *  显示上传进度数据
 */
export type TKUploadBlock = (loadInfoVO: TKLoadInfoVO) => void;

/**
 * 请求对象
 */
export class TKReqParamVO {
  /**
   *  流水号
   */
  public flowNo: string = "";
  /**
   *  请求模块标示
   */
  public reqModule: string = "";
  /**
   *  是否过滤重复请求，进行请求拦截
   */
  public isFilterRepeatRequest: boolean = true;
  /**
   *  重复请求，进行请求拦截时间，单位毫秒
   */
  public filterRepeatRequestTimeOut: number = 0;
  /**
   *  是否自动添加系统公共参数
   */
  public isAutoAddSysComParam: boolean = true;
  /**
   *  是否全局请求，全局请求不可以取消掉,默认是NO
   */
  public isGlobRequest: boolean = false;
  /**
   *  Http的请求的头
   */
  public headerFieldDic: Record<string, Object | undefined> = {};
  /**
   *  请求对象
   */
  public reqParam: Record<string, Object | undefined> = {};
  /**
   *  URL地址
   */
  public url: string = "";
  /**
   *  是否post请求
   */
  public isPost: boolean = true;
  /**
   *
   *  Http请求方法
   */
  private _httpMethod: string = "";
  /**
   * <AUTHOR> 2017-01-19 16:01:54
   *
   *  Http请求ContentType
   */
  public contentType: TKContentType = TKContentType.NONE;
  /**
   *  请求超时时间，单位秒
   */
  public timeOut: number = 60;
  /**
   *  请求的协议
   */
  public protocol: TKDaoType = TKDaoType.Http;
  /**
   *  调用开始时间
   */
  public beginTime: number = 0;
  /**
   *  是否显示缓冲效果（转菊花）
   */
  public isShowWait: boolean = false;
  /**
   *  缓冲效果的文字
   */
  public waitTip: string = "加载中";
  /**
   *  缓冲效果得角度
   */
  public waitAngle: number = 0;
  /**
   *  是否返回list数据
   */
  public isReturnList: boolean = true;
  /**
   *  请求组号,如果是javascript请求，传对应浏览器对象的名称
   */
  public group: string = "";
  /**
   *  扩展字段对象，目前javascript协议的时候传的是WebView对象，其他后面在定义
   */
  public userInfo: Object | undefined = undefined;
  /**
   *  是否上传文件
   */
  private _isUpload: boolean = false;
  /**
   *  上传代理
   */
  public uploadBlock: TKUploadBlock | undefined = undefined;
  /**
   *  是否缓存
   */
  public isCache: boolean = false;
  /**
   *  缓存类型
   */
  public cacheType: TKCacheType = TKCacheType.Mem;
  /**
   *
   *  缓存时间，单位是秒
   */
  public cacheTime: number = 0;
  /**
   *  自定义dao的实现类名称，一般不需要设置
   */
  public daoName: string = "";
  /**
   *  是否微服务RestFull接口
   */
  public isRestFull: boolean = false;
  /**
   *  是否保持原始的参数格式
   */
  public isKeepOriginalParam:boolean = false;
  /**
   *  是否对参数进行url编码
   */
  public isURLEncode: boolean = false;
  /**
   *  是否对参数进行签名
   */
  public isURLSign: boolean = false;
  /**
   *  签名key
   */
  public signKey: string = "";
  /**
   *  签名ID
   */
  public signAppId: string = "";
  /**
   *
   *  是否对参数进行加密
   */
  public isURLEncry: boolean = false;
  /**
   *  是否对响应包进行加密
   */
  public isURLResponseEncry: boolean = false;
  /**
   *  对参数进行加密模式
   */
  public encryMode: TKEncryMode = TKEncryMode.Aes;
  /**
   *  加密key
   */
  public encryKey: string = "";
  /**
   渠道ID
   */
  public channelId: string = "";
  /**
   *  请求公司编号
   */
  public companyId: string = "";
  /**
   *  系统编号
   */
  public systemId: string = "";
  /**
   *  socket的服务器ID,即BusConfig.xml里面配置的server的id，如果为空就自动用url代替
   */
  private _busServerId: string = ""
  /**
   *  socket的服务器接口功能号，如果为空，自动用reqParam里面的funcno或者funcNo代替
   */
  private _busFuncNo: string = "";
  /**
   *  是否登录请求
   */
  public isLoginReq: boolean = false;
  /**
   *  返回字符编码集
   */
  public charEncoding: TKCharEncoding = TKCharEncoding.DEFAULT;
  /**
   *  数据类型协议
   */
  public dataType: TKDataType = TKDataType.Normal;
  /**
   *  Dao请求模式,目前针对2进制socket协议有效，支持长短连接
   */
  public daoMode: TKDaoMode = TKDaoMode.Long;
  /**
   *  Https是否验证SSl证书的合法性
   */
  public isValidatesSSLCertificate: boolean = true;
  /**
   * 长连接服务器唯一标示
   */
  public busClientUUID: string = "";
  /**
   *  是否有回调函数
   */
  public isHasCallBackFunc: boolean = false;
  /**
   * 行情接口模式
   */
  public quoteFunctionMode: TKQuoteFunctionMode = TKQuoteFunctionMode.Auto;
  /**
   *  数据服务代理对象，一般走默认不需要设置
   */
  private _serviceDelegate: WeakRef<TKServiceDelegate> | undefined = undefined;

  public set serviceDelegate(serviceDelegate: TKServiceDelegate | undefined) {
    if (serviceDelegate) {
      this._serviceDelegate = new WeakRef(serviceDelegate);
    } else {
      this._serviceDelegate = undefined;
    }
  }

  public get serviceDelegate(): TKServiceDelegate | undefined {
    return this._serviceDelegate?.deref();
  }

  public set httpMethod(httpMethod: string) {
    this._httpMethod = httpMethod;
  }

  public get httpMethod(): string {
    if (TKStringHelper.isBlank(this._httpMethod)) {
      this._httpMethod = this.isPost ? "POST" : "GET";
    }
    return this._httpMethod;
  }

  public set isUpload(isUpload: boolean) {
    this._isUpload = isUpload;
    if (this._isUpload) {
      this.isPost = true;
    }
  }

  public get isUpload(): boolean {
    return this._isUpload;
  }

  public set busServerId(busServerId: string) {
    this._busServerId = busServerId;
  }

  public get busServerId(): string {
    if (TKStringHelper.isBlank(this._busServerId)) {
      this._busServerId = this.url;
    }
    return this._busServerId;
  }

  public set busFuncNo(busFuncNo: string) {
    this._busFuncNo = busFuncNo;
  }

  public get busFuncNo(): string {
    if (TKStringHelper.isBlank(this._busFuncNo)) {
      if (this.reqParam) {
        this._busFuncNo = TKMapHelper.getString(this.reqParam, "funcNo");
        if (TKStringHelper.isBlank(this._busFuncNo)) {
          this._busFuncNo = TKMapHelper.getString(this.reqParam, "funcno");
        }
      }
    }
    return this._busFuncNo;
  }
}