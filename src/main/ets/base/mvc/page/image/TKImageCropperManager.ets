/**
 *  代理类
 */
import { TKBottomMenu, TKMenuDialogOption } from '../../../../components/dialog/TKMenuDialog';
import { TKStringHelper } from '../../../../util/string/TKStringHelper';
import { TKNoPermissionTip, TKPermission, TKPermissionHelper } from '../../../../util/system/TKPermissionHelper';
import { TKDialogHelper } from '../../../../util/ui/TKDialogHelper';
import { abilityAccessCtrl } from '@kit.AbilityKit';
import { TKPhotoSelectOptions, TKPickerHelper } from '../../../../util/ui/TKPickerHelper';
import { camera, cameraPicker } from '@kit.CameraKit';
import { TKFileHelper } from '../../../../util/file/TKFileHelper';
import { TKUUIDHelper } from '../../../../util/crypto/TKUUIDHelper';
import { fileUri } from '@kit.CoreFileKit';

export interface TKImageCropperManagerDelegate {
  /**
   *  处理最后的图片
   *
   * @param image
   */
  processCropperImages: (imageUris: Array<string>) => void;

  /**
   *  处理照片的异常情况，例如没有权限
   *
   * @param errorNo    错误号
   * @param errorInfo  错误信息
   */
  processCropperImageWithErrorNo: (errorNo: number, errorInfo: string) => void;

}

/**
 * 图片拍照或者相册处理管理器
 */
export class TKImageCropperManager {
  /**
   *  代理
   */
  public delegate: TKImageCropperManagerDelegate | undefined = undefined;
  /**
   *  是否要剪切图片
   */
  public isCutImage: boolean = true;
  /**
   *  是否用身份证拍照模式
   */
  public isUseIdCardCamera: boolean = false;
  /**
   *  是否自动保存拍照图片到相册
   */
  public isAutoSavePhoto: boolean = true;
  /**
   *  状态栏背景颜色
   */
  public statusBarBgColor?: ResourceColor;
  /**
   *  如果Title不为空，用来设置Ttile的文字颜色
   */
  public titleColor?: ResourceColor;
  /**
   * 主颜色
   */
  public mainColor?: ResourceColor;
  /**
   *  是否前置摄像头
   */
  public isFrontCamera: boolean = true;
  /**
   闪光灯的模式
   */
  public cameraFlashMode: boolean = false;
  /**
   * 是否多选
   */
  public isMulSelect: boolean = false;
  /**
   * 最大选择数目
   */
  public selectMaxNum: number = 10;
  private static instance: TKImageCropperManager | undefined = undefined;

  /**
   * @return
   */
  public static shareInstance(): TKImageCropperManager {
    if (!TKImageCropperManager.instance) {
      TKImageCropperManager.instance = new TKImageCropperManager();
    }
    return TKImageCropperManager.instance;
  }

  /**
   *  选择模式
   *
   * @param type 选择模式(0:照片+拍照,1:照片,2:拍照)
   */
  public showChoiceSheet(type: string = "0") {
    if (type === "0") {
      TKDialogHelper.showActionSheet({
        mainColor: this.mainColor,
        bottomMenu: [{ tag: "0", name: "拍照" }, { tag: "1", name: "从相册中选取" }],
        onItemClick: (item: TKBottomMenu) => {
          //拍照
          if (item.tag == "0") {
            this.processCamera();
          } else {
            this.processPhoto();
          }
        },
        onCancel: () => {
        }
      } as TKMenuDialogOption);
    } else if (type === "1") {
      this.processPhoto();
    } else {
      this.processCamera();
    }
  }

  /**
   * 处理拍照
   */
  private async processCamera() {
    let grantStatus = await TKPermissionHelper.requestPermissionFromUser(TKPermission.CAMERA);
    if (grantStatus == abilityAccessCtrl.GrantStatus.PERMISSION_DENIED) {
      if (this.delegate && this.delegate.processCropperImageWithErrorNo) {
        this.delegate.processCropperImageWithErrorNo(-5, TKNoPermissionTip.CAMERA_TIP);
      }
    } else {
      let tempFileUri: string | undefined =
        this.isAutoSavePhoto ? undefined : `${TKFileHelper.tempDir()}/photo/${TKUUIDHelper.uuid()}.jpg`;
      if (TKStringHelper.isNotBlank(tempFileUri)) {
        TKFileHelper.createFile(tempFileUri!);
      }
      let saveUri: string | undefined =
        TKStringHelper.isNotBlank(tempFileUri) ? fileUri.getUriFromPath(tempFileUri) : undefined;
      let imageURI: string = await TKPickerHelper.openCamera({
        mediaTypes: [cameraPicker.PickerMediaType.PHOTO],
        cameraPosition: this.isFrontCamera ? camera.CameraPosition.CAMERA_POSITION_FRONT :
        camera.CameraPosition.CAMERA_POSITION_BACK,
        saveUri: saveUri
      });
      if (TKStringHelper.isNotBlank(imageURI)) {
        if (this.delegate && this.delegate.processCropperImages) {
          this.delegate.processCropperImages([imageURI]);
        }
      } else {
        if (this.delegate && this.delegate.processCropperImageWithErrorNo) {
          this.delegate.processCropperImageWithErrorNo(-9, "用户取消操作");
        }
      }
    }
  }

  /**
   * 相册处理
   */
  private async processPhoto() {
    let imageUris: Array<string> = await TKPickerHelper.selectPhoto({
      maxSelectNumber: this.isMulSelect ? this.selectMaxNum : 1,
      isPhotoTakingSupported: false,
    } as TKPhotoSelectOptions);
    if (imageUris && imageUris.length > 0) {
      if (this.delegate && this.delegate.processCropperImages) {
        this.delegate.processCropperImages(imageUris);
      }
    } else {
      if (this.delegate && this.delegate.processCropperImageWithErrorNo) {
        this.delegate.processCropperImageWithErrorNo(-9, "用户取消操作");
      }
    }
  }
}