/**
 *  代理类
 */
import { TKBottomMenu, TKMenuDialogOption } from '../../../../components/dialog/TKMenuDialog';
import { TKStringHelper } from '../../../../util/string/TKStringHelper';
import { TKNoPermissionTip, TKPermission, TKPermissionHelper } from '../../../../util/system/TKPermissionHelper';
import { TKDialogHelper } from '../../../../util/ui/TKDialogHelper';
import { abilityAccessCtrl } from '@kit.AbilityKit';
import { TKPhotoSelectOptions, TKPickerHelper } from '../../../../util/ui/TKPickerHelper';
import { camera, cameraPicker } from '@kit.CameraKit';
import { TKFileHelper } from '../../../../util/file/TKFileHelper';
import { TKUUIDHelper } from '../../../../util/crypto/TKUUIDHelper';
import { fileUri } from '@kit.CoreFileKit';
import { photoAccessHelper } from '@kit.MediaLibraryKit';

export interface TKVideoCropperManagerDelegate {
  /**
   *  处理最后的视频
   *
   * @param image
   */
  processCropperVideo: (movieUrl: string) => void;

  /**
   *  处理视频的异常情况，例如没有权限
   *
   * @param errorNo    错误号
   * @param errorInfo  错误信息
   */
  processCropperVideoWithErrorNo: (errorNo: number, errorInfo: string) => void;

}

/**
 * 视频上传管理器
 */
export class TKVideoCropperManager {
  /**
   *  代理
   */
  public delegate: TKVideoCropperManagerDelegate | undefined = undefined;
  /**
   *  是否要压缩视频
   */
  public isCompress: boolean = true;
  /**
   *  是否自动保存拍照图片到相册
   */
  public isAutoSavePhoto: boolean = true;
  /**
   *  状态栏背景颜色
   */
  public statusBarBgColor?: ResourceColor;
  /**
   *  如果Title不为空，用来设置Ttile的文字颜色
   */
  public titleColor?: ResourceColor;
  /**
   * 主颜色
   */
  public mainColor?: ResourceColor;
  /**
   *  是否前置摄像头
   */
  public isFrontCamera: boolean = true;
  private static instance: TKVideoCropperManager | undefined = undefined;

  /**
   * @return
   */
  public static shareInstance(): TKVideoCropperManager {
    if (!TKVideoCropperManager.instance) {
      TKVideoCropperManager.instance = new TKVideoCropperManager();
    }
    return TKVideoCropperManager.instance;
  }

  /**
   *  选择模式
   *
   * @param type 选择模式(0:相册+录制,1:相册,2:录制)
   */
  public showChoiceSheet(type: string = "0") {
    if (type === "0") {
      TKDialogHelper.showActionSheet({
        mainColor: this.mainColor,
        bottomMenu: [{ tag: "0", name: "录制" }, { tag: "1", name: "从相册中选取" }],
        onItemClick: (item: TKBottomMenu) => {
          //录像
          if (item.tag == "0") {
            this.processCamera();
          } else {
            this.processPhoto();
          }
        },
        onCancel: () => {

        }
      } as TKMenuDialogOption);
    } else if (type === "1") {
      this.processPhoto();
    } else {
      this.processCamera();
    }
  }

  /**
   * 处理录制
   */
  private async processCamera() {
    let grantStatus = await TKPermissionHelper.requestPermissionFromUser(TKPermission.CAMERA);
    if (grantStatus == abilityAccessCtrl.GrantStatus.PERMISSION_DENIED) {
      if (this.delegate && this.delegate.processCropperVideoWithErrorNo) {
        this.delegate.processCropperVideoWithErrorNo(-5, TKNoPermissionTip.CAMERA_TIP);
      }
    } else {
      let tempFileUri: string | undefined =
        this.isAutoSavePhoto ? undefined : `${TKFileHelper.tempDir()}/photo/${TKUUIDHelper.uuid()}.mp4`;
      if (TKStringHelper.isNotBlank(tempFileUri)) {
        TKFileHelper.createFile(tempFileUri!);
      }
      let saveUri: string | undefined =
        TKStringHelper.isNotBlank(tempFileUri) ? fileUri.getUriFromPath(tempFileUri) : undefined;
      let videoURI: string = await TKPickerHelper.openCamera({
        mediaTypes: [cameraPicker.PickerMediaType.VIDEO],
        cameraPosition: this.isFrontCamera ? camera.CameraPosition.CAMERA_POSITION_FRONT :
        camera.CameraPosition.CAMERA_POSITION_BACK,
        saveUri: saveUri
      });
      if (TKStringHelper.isNotBlank(videoURI)) {
        if (this.delegate && this.delegate.processCropperVideo) {
          this.delegate.processCropperVideo(videoURI);
        }
      } else {
        if (this.delegate && this.delegate.processCropperVideoWithErrorNo) {
          this.delegate.processCropperVideoWithErrorNo(-9, "用户取消操作");
        }
      }
    }
  }

  /**
   * 相册处理
   */
  private async processPhoto() {
    //可选择的媒体文件类型，若无此参数，则默认为图片和视频类型。
    let videoUris: Array<string> = await TKPickerHelper.selectPhoto({
      maxSelectNumber: 1,
      isPhotoTakingSupported: false,
      MIMEType: photoAccessHelper.PhotoViewMIMETypes.VIDEO_TYPE
    } as TKPhotoSelectOptions);
    if (videoUris && videoUris.length > 0) {
      if (this.delegate && this.delegate.processCropperVideo) {
        this.delegate.processCropperVideo(videoUris[0]);
      }
    } else {
      if (this.delegate && this.delegate.processCropperVideoWithErrorNo) {
        this.delegate.processCropperVideoWithErrorNo(-9, "用户取消操作");
      }
    }
  }
}