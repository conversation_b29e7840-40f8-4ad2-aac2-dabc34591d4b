import { common, wantAgent } from '@kit.AbilityKit';
import { avSession } from '@kit.AVSessionKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKLog } from '../../../../../util/logger/TKLog';
import { TKBackgroundTaskHelper } from '../../../../../util/system/TKBackgroundTaskHelper';
import { TKContextHelper } from '../../../../../util/system/TKContextHelper';
import backgroundTaskManager from '@ohos.resourceschedule.backgroundTaskManager';
import { TKVideoData } from './TKVideoData';
import { TKImageHelper } from '../../../../../util/ui/TKImageHelper';

/**
 * 视频会话管理器
 */
export class TKAvSessionController {
  private static instance?: TKAvSessionController;
  private context?: common.UIAbilityContext;
  private avSession?: avSession.AVSession;
  private avSessionMetadata?: avSession.AVMetadata;

  constructor() {
    this.initAvSession();
  }

  public static getInstance(): TKAvSessionController {
    if (!TKAvSessionController.instance) {
      TKAvSessionController.instance = new TKAvSessionController();
    }
    return TKAvSessionController.instance;
  }

  public initAvSession() {
    this.context = TKContextHelper.getCurrentUIAbilityContext();
    avSession.createAVSession(this.context, "TK_SHORT_AUDIO_SESSION", 'video').then(async (avSession) => {
      this.avSession = avSession;
      TKLog.info(`session create successed : sessionId : ${this.avSession.sessionId}`);
      TKBackgroundTaskHelper.startBackgroundTask(backgroundTaskManager.BackgroundMode.AUDIO_PLAYBACK, this.context);
      this.setLaunchAbility();
      this.avSession.activate();
    });
  }

  public getAvSession() {
    return this.avSession;
  }

  public getAvSessionMetadata() {
    return this.avSessionMetadata;
  }

  public async setAVMetadata(curSource: TKVideoData, duration: number) {
    if (curSource) {
      const imagePixMap = curSource.head ? await TKImageHelper.getPixelMapFromResource(curSource.head) : undefined;
      let metadata: avSession.AVMetadata = {
        assetId: `${curSource.index}`,
        title: curSource.title,
        mediaImage: imagePixMap,
        duration: duration
      };
      if (this.avSession) {
        this.avSession.setAVMetadata(metadata).then(() => {
          this.avSessionMetadata = metadata;
          TKLog.info('SetAVMetadata successfully');
        }).catch((err: BusinessError) => {
          TKLog.error(`SetAVMetadata BusinessError: code: ${err.code}, message: ${err.message}`);
        });
      }
    }
  }

  private setLaunchAbility() {
    if (!this.context) {
      return;
    }
    const wantAgentInfo: wantAgent.WantAgentInfo = {
      wants: [
        {
          bundleName: this.context.abilityInfo.bundleName,
          abilityName: this.context.abilityInfo.name
        }
      ],
      operationType: wantAgent.OperationType.START_ABILITIES,
      requestCode: 0,
      wantAgentFlags: [wantAgent.WantAgentFlags.UPDATE_PRESENT_FLAG]
    };
    wantAgent.getWantAgent(wantAgentInfo).then((agent) => {
      if (this.avSession) {
        this.avSession.setLaunchAbility(agent);
      }
    });
  }

  public setAvSessionPlayState(playbackState: avSession.AVPlaybackState) {
    if (this.avSession) {
      this.avSession.setAVPlaybackState(playbackState, (err: BusinessError) => {
        if (err) {
          TKLog.error(`SetAVPlaybackState BusinessError: code: ${err.code}, message: ${err.message}`);
        } else {
          TKLog.info('SetAVPlaybackState successfully');
        }
      });
    }
  }

  public async unregisterSessionListener() {
    if (!this.avSession) {
      return;
    }
    this.avSession.off('play');
    this.avSession.off('pause');
    this.avSession.off('playNext');
    this.avSession.off('playPrevious');
    this.avSession.off('setLoopMode');
    this.avSession.off('seek');
    this.avSession.off('toggleFavorite');
    TKBackgroundTaskHelper.stopBackgroundTask(this.context);
  }
}