import { TKAttribute } from '../../../../../components/common/attribute/TKAttribute';
import { TKObjectHelper } from '../../../../../util/data/TKObjectHelper';
import { TKDeviceHelper } from '../../../../../util/dev/TKDeviceHelper';
import { TKMapHelper } from '../../../../../util/map/TKMapHelper';
import { TKBarName, TKWindowHelper } from '../../../../../util/ui/TKWindowHelper';
import { TKRouterHelper } from '../../../../router/TKRouterHelper';
import { TKAvPlayerController } from './TKAvPlayerController';
import { TKVideoData } from './TKVideoData';
import { componentUtils, window } from '@kit.ArkUI';
import { TKDateHelper } from '../../../../../util/date/TKDateHelper';
import { TKNotificationCenter } from '../../../../notification/TKNotificationCenter';
import { TKWindowListener } from '../../../../device/listener/TKWindowListener';
import { TKNotification } from '../../../../notification/TKNotification';

/**
 * 视频常量
 */
export class TKVideoConstants {
  /**
   * Track Thickness size min
   */
  static readonly TRACK_SIZE_MIN: number = 4;
  /**
   * Track Thickness borderRadius
   */
  static readonly TRACK_BORDER_RADIUS: number = 2;
  /**
   * Track Thickness size max
   */
  static readonly TRACK_SIZE_MAX: number = 6;
  /**
   * video base index
   */
  static readonly Z_INDEX_BASE: number = 1;
  /**
   * video Play button index
   */
  static readonly Z_INDEX_VIDEO_PLAY: number = 2;
  /**
   * Slider index
   */
  static readonly SLIDER_INDEX: number = 3;
  /**
   * video max index
   */
  static readonly Z_INDEX_MAX: number = 5;
  /**
   * Width percent full
   */
  static readonly WIDTH_FULL_PERCENT: string = '100%';
  /**
   * Height percent full
   */
  static readonly HEIGHT_FULL_PERCENT: string = '100%';
  /**
   * Timer interval number
   */
  static readonly TIMER_INTERVAL: number = 100;
}

/**
 * 子组件的控制属性类
 */
@Observed
export class TKVideoPageAttribute extends TKAttribute {
  //视频标题
  title?: string = "视频播放";
  //视频URL
  url?: string = "";
  //是否隐藏进度条
  isHiddenProgress?: boolean = false;
  //是否播放完自动关闭
  isAutoClose?: boolean = true;
}

@Component
export struct TKVideoPage {
  /**
   * 基本控制属性
   */
  @State attribute: TKVideoPageAttribute = new TKVideoPageAttribute();
  /**
   * 处理系统返回键
   */
  @Consume @Watch('onBackPress') onBackPressChanged: boolean;
  /**
   * 处理系统返回键
   */
  @Consume onBackPressFilter: boolean;
  /**
   * 视频播放控制器
   */
  @State @Watch("onAvPlayerChange") private avPlayerController: TKAvPlayerController = new TKAvPlayerController();
  /**
   * 视频播放容器
   */
  private xComponentController = new XComponentController();
  /**
   * 视频播放对象
   */
  private videoData: TKVideoData = new TKVideoData();
  /**
   * 是否显示时间条
   */
  @State private isTimeDisplay: number = 0;
  /**
   * 滑块大小
   */
  @State private trackThicknessSize: number = TKVideoConstants.TRACK_SIZE_MIN;
  /**
   * 是否拖拽进度条
   */
  @State private isSliderDragging: boolean = false;
  /**
   * 是否进行拖拽手势
   */
  @State private isSliderGesture: boolean = false;
  /**
   * 时间进度条的内容
   */
  @State private currentStringTime: string = '00:00';
  /**
   * 拖拽手势的X坐标
   */
  @State private panStartX: number = 0;
  /**
   * 拖拽时候视频播放的时间
   */
  @State private panStartTime: number = 0;
  /**
   * 滑动的宽度
   */
  @State private slideWidth: number = 130;
  /**
   * 拖拽结束的时间
   */
  @State private panEndTime: number = 0;
  /**
   * 时间条的样式
   */
  @State private sliderStyle: SliderStyle = SliderStyle.NONE;
  /**
   * 是否横屏
   */
  @State private isFullLandscapeScreen: boolean = false;
  /**
   * 是否显示标题
   */
  @State private isShowTitle: boolean = true;
  /**
   * 是否显示横竖屏切换
   */
  @State private isShowFullScreenIcon: boolean = true;
  /**
   * 视频宽度
   */
  @State private videoWidth: number = 0;
  /**
   * 视频高度
   */
  @State private videoHeight: number = 0;
  /**
   * 视频截取比例
   */
  @State private videoAspectRatio: number = 0;
  /**
   * 屏幕宽度
   */
  @State private screenWidth: number = TKDeviceHelper.getScreenWidth();
  /**
   * 屏幕高度
   */
  @State private screenHeight: number = TKDeviceHelper.getScreenHeight();
  /**
   * 状态栏高度
   */
  @State private statusHeight: number = TKWindowHelper.getStatusBarHeightSync();
  /**
   * 底部栏高度
   */
  @State private bottomHeight: number = TKWindowHelper.getNavigationBottomBarHeightSync();

  aboutToAppear() {
    TKObjectHelper.fixDefault(this.attribute, TKVideoPageAttribute);
    let param: Record<string, Object> = TKRouterHelper.getParams(this);
    this.attribute.url = TKMapHelper.getString(param, "url", this.attribute.url);
    this.attribute.title = TKMapHelper.getString(param, "title", this.attribute.title);
    this.attribute.isHiddenProgress =
      TKMapHelper.getBoolean(param, "isHiddenProgress", this.attribute.isHiddenProgress);
    this.attribute.isAutoClose = TKMapHelper.getBoolean(param, "isAutoClose", this.attribute.isAutoClose);
    this.videoData.title = this.attribute.title;
    this.videoData.url = this.attribute.url;
    this.onVideoDataChange();
    this.onWindowSizeChangeListener();
  }

  private windowSizeChangeHandler(notification: TKNotification) {
    let size: window.Size = notification.obj as window.Size;
    this.isFullLandscapeScreen = (size.width > size.height);
    this.screenWidth = TKDeviceHelper.getScreenWidth();
    this.screenHeight = TKDeviceHelper.getScreenHeight();
    this.isShowTitle = this.isFullLandscapeScreen ? false : true;
    this.isShowFullScreenIcon = true;
    this.onAvPlayerChange(true);
  }

  private onWindowSizeChangeListener() {
    TKNotificationCenter.defaultCenter.addObserver(this, this.windowSizeChangeHandler,
      TKWindowListener.NOTE_WINDOW_CHANGE);
  }

  private offWindowSizeChangeListener() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }

  aboutToDisappear(): void {
    this.avPlayerController.releaseVideo();
    this.offWindowSizeChangeListener();
  }

  build() {
    Stack({ alignContent: Alignment.BottomEnd }) {
      this.PlayTop()
      this.PlayView()
      this.PlayControl()
    }
    .width(TKVideoConstants.WIDTH_FULL_PERCENT)
    .height(TKVideoConstants.HEIGHT_FULL_PERCENT)
    .backgroundColor(Color.Black)
    .gesture(
      PanGesture({ direction: PanDirection.Horizontal })
        .onActionStart((event: GestureEvent) => {
          this.isSliderGesture = true;
          this.panStartX = event.offsetX;
          this.panStartTime = this.avPlayerController.currentTime;
          this.sliderOnchange(this.panStartTime, SliderChangeMode.Begin);
        })
        .onActionUpdate((event: GestureEvent) => {
          this.isSliderGesture = true;
          let panTime =
            this.panStartTime +
              (this.panStartX + event.offsetX) / this.slideWidth * this.avPlayerController!.durationTime;
          this.panEndTime = Math.min(Math.max(0, panTime), this.avPlayerController!.durationTime);
          this.sliderOnchange(this.panEndTime, SliderChangeMode.Moving);
        })
        .onActionEnd(() => {
          this.sliderOnchange(this.panEndTime, SliderChangeMode.End);
          this.isSliderGesture = false;
        })
    )
  }

  @Builder
  PlayTop() {
    Row() {
      Button({ type: ButtonType.Circle }) {
        Image($r('sys.media.ohos_ic_public_arrow_left'))
          .fillColor(Color.White)
          .width(24)
          .height(24)
      }
      .width(40)
      .height(40)
      .margin({ left: 16 })
      .backgroundColor("#1AFFFFFF")
      .onClick(() => {
        this.routeBack();
      })
    }
    .width("15%")
    .zIndex(TKVideoConstants.Z_INDEX_MAX)
    .position({
      x: 0,
      y: px2vp(this.statusHeight)
    })
    .visibility(Visibility.Visible)

    Row() {
      Text(this.attribute.title)
        .fontFamily('PingFang SC')
        .fontColor(Color.White)
        .fontSize(19)
        .textAlign(TextAlign.Center)
        .height(40)
    }
    .width("70%")
    .justifyContent(FlexAlign.Center)
    .zIndex(TKVideoConstants.Z_INDEX_MAX)
    .position({
      x: px2vp(this.screenWidth * 0.15),
      y: px2vp(this.statusHeight)
    })
    .visibility(this.isShowTitle ? Visibility.Visible : Visibility.Hidden)

    Row() {
      Button({ type: ButtonType.Circle }) {
        Image($r(`${this.isFullLandscapeScreen ? 'app.media.tk_framework_video_portrait' :
          'app.media.tk_framework_video_landscape'}`))
          .fillColor(Color.White)
          .width(24)
          .height(24)
      }
      .width(40)
      .height(40)
      .margin({ right: 16 })
      .backgroundColor("#1AFFFFFF")
      .onClick(() => {
        this.onFullScreen();
      })
    }
    .width("15%")
    .justifyContent(FlexAlign.End)
    .zIndex(TKVideoConstants.Z_INDEX_MAX)
    .position({
      x: px2vp(this.screenWidth * 0.85),
      y: px2vp(this.statusHeight)
    })
    .visibility(this.isShowFullScreenIcon ? Visibility.Visible : Visibility.Hidden)
  }

  @Builder
  PlayView() {
    Stack({ alignContent: Alignment.Center }) {
      if (!this.avPlayerController.isPlaying) {
        Row() {
          Image($r('app.media.tk_framework_video_play'))
            .width(48)
            .aspectRatio(1)
        }
        .width(TKVideoConstants.WIDTH_FULL_PERCENT)
        .justifyContent(this.isFullLandscapeScreen ? FlexAlign.SpaceEvenly : FlexAlign.SpaceAround)
        .zIndex(TKVideoConstants.Z_INDEX_VIDEO_PLAY)
      }
      XComponent({
        id: 'XComponent',
        type: XComponentType.SURFACE,
        controller: this.xComponentController
      }).onLoad(async () => {
        this.avPlayerController.setSurfaceID(this.xComponentController.getXComponentSurfaceId());
        this.avPlayerController.initAVPlayer(this.videoData);
      }).zIndex(TKVideoConstants.Z_INDEX_BASE).aspectRatio(this.videoAspectRatio)
    }
    .zIndex(TKVideoConstants.Z_INDEX_BASE)
    .width(TKVideoConstants.WIDTH_FULL_PERCENT)
    .height(TKVideoConstants.HEIGHT_FULL_PERCENT)
    .onClick(() => {
      this.pauseOrResumeVideo();
    })
  }

  @Builder
  PlayControl() {
    Column() {
      Row() {
        Text(this.currentStringTime)
          .fontSize(16)
          .fontColor(Color.Red)
          .opacity(0.9)
          .margin({ left: 2 })
          .width('45%')
          .textAlign(TextAlign.End)
          .zIndex(TKVideoConstants.SLIDER_INDEX)
        Divider()
          .vertical(true)
          .height(16)
          .width(2)
          .backgroundBlurStyle(BlurStyle.Regular, { colorMode: ThemeColorMode.LIGHT })
          .color(Color.White)
          .opacity(0.9)
          .margin({ left: 8, right: 8 })
          .rotate({
            x: 0,
            y: 0,
            z: 0,
            centerX: '50%',
            centerY: '50%',
            angle: 30
          })
        Text(this.avPlayerController.durationStringTime)
          .fontSize(16)
          .fontColor(Color.White)
          .margin({ right: 2 })
          .width('45%')
          .textAlign(TextAlign.Start)
          .opacity(0.9)
          .zIndex(TKVideoConstants.SLIDER_INDEX)
      }
      .margin({ bottom: 8 })
      .alignItems(VerticalAlign.Center)
      .opacity(this.isTimeDisplay)

      Row() {
        Slider({
          value: this.isSliderGesture ? this.panEndTime : this.avPlayerController!.currentTime,
          step: 0.1,
          min: 0,
          max: this.avPlayerController!.durationTime,
          style: this.sliderStyle
        })
          .id('video_slider')
          .height(this.isSliderDragging ? 40 : 24)
          .trackColor("#1AFFFFFF")
          .showSteps(false)
          .blockSize({ width: 16, height: 16 })
          .blockColor($r('sys.color.background_primary'))
          .layoutWeight(1)
          .trackThickness(this.trackThicknessSize)
          .trackBorderRadius(2)
          .selectedBorderRadius(2)
          .zIndex(TKVideoConstants.SLIDER_INDEX)
          .onAreaChange(() => {
            let videoSlider: componentUtils.ComponentInfo = componentUtils.getRectangleById('video_slider');
            this.slideWidth = px2vp(videoSlider.size.width);
          })
          .onChange((value: number, mode: SliderChangeMode) => {
            this.sliderOnchange(value, mode);
          })
      }
      .width(TKVideoConstants.WIDTH_FULL_PERCENT)
      .visibility(this.avPlayerController.isPlaying && this.attribute.isHiddenProgress ? Visibility.Hidden :
      Visibility.Visible)
    }.margin({
      left: 16,
      right: 16,
      bottom: px2vp(this.bottomHeight) + (this.isSliderDragging ? 48 : 8)
    }).zIndex(TKVideoConstants.Z_INDEX_MAX)
  }

  /**
   * 视频数据变更触发
   */
  onVideoDataChange() {
    if (this.avPlayerController.isReady) {
      this.avPlayerController.playVideo();
      this.avPlayerController.setIsPlaying(true);
      this.trackThicknessSize = TKVideoConstants.TRACK_SIZE_MIN;
    } else {
      let countNum = 0;
      let intervalFlag = setInterval(() => {
        countNum++;
        if (this.avPlayerController.isReady) {
          countNum = 0;
          this.avPlayerController.playVideo();
          this.avPlayerController.setIsPlaying(true);
          this.trackThicknessSize = TKVideoConstants.TRACK_SIZE_MIN;
          clearInterval(intervalFlag);
        } else {
          if (countNum > 15) {
            countNum = 0;
            this.avPlayerController.initAVPlayer(this.videoData);
          }
        }
      }, TKVideoConstants.TIMER_INTERVAL);
    }
  }

  /**
   * 视频播放器改变
   */
  onAvPlayerChange(isForce: boolean = false) {
    if (this.avPlayerController.isFinish) {
      if (this.attribute.isAutoClose) {
        this.avPlayerController.isFinish = false;
        this.routeBack();
        return;
      }
    }
    if (this.videoWidth != this.avPlayerController.videoWidth ||
      this.videoHeight != this.avPlayerController.videoHeight || isForce) {
      this.videoWidth = this.avPlayerController.videoWidth;
      this.videoHeight = this.avPlayerController.videoHeight;
      if (this.screenWidth > this.screenHeight) {
        let surfaceHeight: number = this.screenHeight - (TKWindowHelper.getStatusBarHeightSync() * 2);
        const heightRatio: number = surfaceHeight / this.videoHeight;
        let surfaceWidth: number = this.videoWidth * heightRatio;
        this.xComponentController.setXComponentSurfaceRect({
          surfaceWidth: surfaceWidth,
          surfaceHeight: surfaceHeight
        });
        let maxVideoWidth: number = this.screenWidth - TKWindowHelper.getStatusBarHeightSync() -
        TKWindowHelper.getNavigationBottomBarHeightSync() - 360;
        this.videoAspectRatio = (surfaceWidth > maxVideoWidth ? maxVideoWidth : surfaceWidth) / surfaceHeight;
      } else {
        let surfaceWidth: number = this.screenWidth;
        const widthRatio: number = surfaceWidth / this.videoWidth;
        let surfaceHeight: number = this.videoHeight * widthRatio;
        this.xComponentController.setXComponentSurfaceRect({
          surfaceWidth: surfaceWidth,
          surfaceHeight: surfaceHeight
        });
        let maxVideoHeight: number = this.screenHeight - TKWindowHelper.getStatusBarHeightSync() -
        TKWindowHelper.getNavigationBottomBarHeightSync() - 360;
        this.videoAspectRatio = surfaceWidth / (surfaceHeight > maxVideoHeight ? maxVideoHeight : surfaceHeight);
      }
    }
  }

  /**
   * 启动或者暂停视频
   */
  async pauseOrResumeVideo() {
    if (this.avPlayerController.isPlaying) {
      this.avPlayerController.pauseVideo();
      this.avPlayerController.setIsPlaying(false);
      this.isTimeDisplay = 0;
      this.trackThicknessSize = TKVideoConstants.TRACK_SIZE_MIN;
      await TKWindowHelper.setWindowKeepScreenOn(false);
      return;
    }
    if (this.avPlayerController.isReady) {
      this.avPlayerController.playVideo();
      this.avPlayerController.setIsPlaying(true);
      this.isTimeDisplay = 0;
      this.trackThicknessSize = TKVideoConstants.TRACK_SIZE_MIN;
    } else {
      let intervalFlag = setInterval(async () => {
        if (this.avPlayerController.isReady) {
          this.avPlayerController.playVideo();
          this.avPlayerController.setIsPlaying(true);
          this.isTimeDisplay = 0;
          this.trackThicknessSize = TKVideoConstants.TRACK_SIZE_MIN;
          clearInterval(intervalFlag);
        }
      }, TKVideoConstants.TIMER_INTERVAL);
    }
    await TKWindowHelper.setWindowKeepScreenOn(true);
  }

  /**
   * 时间条拖拉
   * @param seconds
   * @param mode
   */
  sliderOnchange(seconds: number, mode: SliderChangeMode) {
    let seekTime: number = seconds * this.avPlayerController!.duration / this.avPlayerController!.durationTime;
    this.currentStringTime = TKDateHelper.formatSeconds(Math.floor(seekTime / 1000));
    this.avPlayerController!.setCurrentStringTime(this.currentStringTime);
    switch (mode) {
      case SliderChangeMode.Begin:
        break;
      case SliderChangeMode.Click:
        break;
      case SliderChangeMode.Moving:
        this.isSliderDragging = true;
        this.isTimeDisplay = 1;
        this.trackThicknessSize = TKVideoConstants.TRACK_SIZE_MAX;
        this.sliderStyle = SliderStyle.OutSet;
        break;
      case SliderChangeMode.End:
        this.avPlayerController!.seek(seekTime);
        this.isTimeDisplay = 0;
        this.trackThicknessSize = TKVideoConstants.TRACK_SIZE_MIN;
        this.sliderStyle = SliderStyle.NONE;
        this.isSliderDragging = false;
        break;
      default:
        break;
    }
  }

  /**
   * 监听返回按键
   * @returns
   */
  onBackPress(): boolean {
    this.onBackPressFilter = true;
    this.routeBack();
    return true;
  }

  /**
   * 页面关闭
   */
  routeBack() {
    if (this.isFullLandscapeScreen) {
      this.onFullScreen();
    }
    let progress: number = 0;
    if (this.avPlayerController.currentTime > 0 && this.avPlayerController.durationTime > 0) {
      progress = (this.avPlayerController.currentTime / this.avPlayerController.durationTime);
    }
    TKRouterHelper.back({
      navComponentOrPathStack: this, result: {
        "progress": progress
      }
    });
  }

  /**
   * 全屏点击
   */
  onFullScreen() {
    this.isShowTitle = false;
    this.isShowFullScreenIcon = false;
    // this.screenWidth = TKDeviceHelper.getScreenHeight();
    // this.screenHeight = TKDeviceHelper.getScreenWidth();
    // this.onAvPlayerChange(true);
    if (this.isFullLandscapeScreen) {
      TKWindowHelper.setWindowSystemBarEnable([TKBarName.status, TKBarName.navigationIndicator]);
      TKWindowHelper.enableLandscapeMultiWindow(false);
      TKWindowHelper.setPreferredOrientation(window.Orientation.PORTRAIT);
    } else {
      TKWindowHelper.setWindowSystemBarEnable([]);
      TKWindowHelper.enableLandscapeMultiWindow(true);
      TKWindowHelper.setPreferredOrientation(window.Orientation.LANDSCAPE);
    }
  }
}