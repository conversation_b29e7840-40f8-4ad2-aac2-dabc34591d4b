import { common } from '@kit.AbilityKit';
import { media } from '@kit.MediaKit';
import { audio } from '@kit.AudioKit';
import { avSession } from '@kit.AVSessionKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKVideoData } from './TKVideoData';
import { TKAvSessionController } from './TKAvSessionController';
import { TKLog } from '../../../../../util/logger/TKLog';
import { TKDateHelper } from '../../../../../util/date/TKDateHelper';
import { TKContextHelper } from '../../../../../util/system/TKContextHelper';

/**
 * 播放器状态
 */
export enum TKAVPlayerState {
  /**
   * Idle state of avPlayer.
   */
  IDLE = 'idle',

  /**
   * Initialized state of avPlayer.
   */
  INITIALIZED = 'initialized',

  /**
   * Prepared state of avPlayer.
   */
  PREPARED = 'prepared',

  /**
   * Playing state of avPlayer.
   */
  PLAYING = 'playing',

  /**
   * Pause state of avPlayer.
   */
  PAUSED = 'paused',

  /**
   * Completed state of avPlayer.
   */
  COMPLETED = 'completed',

  /**
   * Stopped state of avPlayer.
   */
  STOPPED = 'stopped',

  /**
   * Release state of avPlayer.
   */
  RELEASED = 'released',

  /**
   * Error state of avPlayer.
   */
  ERROR = 'error'
}

@Observed
export class TKAvPlayerController {
  @Track surfaceID: string = '';
  @Track isPlaying: boolean = false;
  @Track isReady: boolean = false;
  @Track isFinish: boolean = false;
  @Track currentTime: number = 0;
  @Track durationStringTime: string = '00:00';
  @Track currentStringTime: string = '00:00';
  @Track duration: number = 0;
  @Track durationTime: number = 0;
  @Track videoWidth: number = 0
  @Track videoHeight: number = 0
  private avPlayer?: media.AVPlayer;
  private curIndex: number = 0;
  private curSource: TKVideoData = new TKVideoData();
  private context: common.UIAbilityContext = TKContextHelper.getCurrentUIAbilityContext();

  public initAVPlayer(curSource: TKVideoData) {
    this.curSource = curSource;
    media.createAVPlayer().then((player: media.AVPlayer) => {
      if (player !== null) {
        this.avPlayer = player;
        if (this.curSource.url &&
          (this.curSource.url.startsWith("http://") || this.curSource.url.startsWith("https://"))) {
          this.avPlayer.url = this.curSource.url;
        } else {
          let fileDescriptor = this.context?.resourceManager.getRawFdSync(this.curSource.url!);
          if (fileDescriptor) {
            let avFileDescriptor: media.AVFileDescriptor =
              { fd: fileDescriptor.fd, offset: fileDescriptor.offset, length: fileDescriptor.length };
            this.avPlayer.fdSrc = avFileDescriptor;
          }
        }
        this.setAVPlayerCallback(this.avPlayer);
        if (this.curIndex === this.curSource.index) {
          TKAvSessionController.getInstance().setAVMetadata(this.curSource, 0);
        }
        this.setAvSessionListener();
        TKLog.info('createAVPlayer success:' + ` this.curIndex:${this.curIndex}`);
      } else {
        TKLog.error('createAVPlayer fail');
      }
    }).catch((error: BusinessError) => {
      TKLog.error(`AVPlayer catchCallback, error message:${error.message}`);
    });
  }

  private setAVPlayerCallback(avPlayer: media.AVPlayer) {
    if (!this.avPlayer) {
      return;
    }
    avPlayer.on('timeUpdate', (time: number) => {
      if (time > this.currentTime * 1000) {
        animateTo({ duration: 1000, curve: Curve.Linear }, () => {
          this.currentTime = Math.floor(time / 1000);
        });
      } else {
        this.currentTime = Math.floor(time / 1000);
        if (time === 0) {
          this.updateIsPlay(true);
        }
      }
      this.currentStringTime = TKDateHelper.formatSeconds(time / 1000);
    })

    // The error callback function is triggered when an error occurs during avPlayer operations,
    // at which point the reset interface is called to initiate the reset process
    avPlayer.on('error', (err: BusinessError) => {
      TKLog.error(`Invoke avPlayer failed, code is ${err.code}, message is ${err.message}` +
        `----state:${avPlayer.state} this.curIndex:${this.curIndex}`);
      avPlayer.reset(); // resets the resources and triggers the idle state
    })
    this.setInterruptCallback()
    this.setStateChangeCallback(avPlayer);
    this.setOutputDeviceChangeCallback();
  }

  public async setAvSessionListener() {
    TKAvSessionController.getInstance().getAvSession()?.on('play', () => this.sessionPlayCallback());
    TKAvSessionController.getInstance().getAvSession()?.on('pause', () => this.sessionPauseCallback());
    TKAvSessionController.getInstance().getAvSession()?.on('stop', () => this.sessionStopCallback());
    TKAvSessionController.getInstance().getAvSession()?.on('fastForward',
      (time?: number) => this.sessionFastForwardCallback(time));
    TKAvSessionController.getInstance().getAvSession()?.on('rewind',
      (time?: number) => this.sessionRewindCallback(time));
    TKAvSessionController.getInstance().getAvSession()?.on('seek',
      (seekTime: number) => this.sessionSeekCallback(seekTime));
  }

  private setInterruptCallback() {
    if (!this.avPlayer) {
      return;
    }
    this.avPlayer.on('audioInterrupt', async (interruptEvent: audio.InterruptEvent) => {
      // When an audio interruption event occurs, the `audioRenderer` receives the `interruptEvent` callback. Here,
      // handle it based on its content:
      // 1. Optional: Read the type of `interruptEvent.forceType` to determine if the system has already enforced
      // the corresponding action.
      //    Note: Under the default focus strategy, `INTERRUPT_HINT_RESUME` is of type `INTERRUPT_SHARE`, while all
      //    other hint types are of `INTERRUPT_FORCE`.Therefore, checking `forceType` may not be necessary.
      // 2. Required: Read the type of `interruptEvent.hintType` and perform the corresponding handling.
      if (interruptEvent.forceType === audio.InterruptForceType.INTERRUPT_FORCE) {
        // For the INTERRUPT_FORCE type: Audio-related processing has been performed by the system, and the
        // application needs to update its own state and make the corresponding adjustments.
        switch (interruptEvent.hintType) {
          case audio.InterruptHint.INTERRUPT_HINT_PAUSE:
            // This branch indicates that the system has paused the audio stream (temporarily lost focus).
            // To maintain consistency in state, the application should switch to the audio paused state.
            // Temporarily lost focus: After other audio streams release the audio focus, this audio stream
            // will receive a corresponding resume audio interruption event, at which point it can resume
            // playback on its own.
            this.updateIsPlay(false);
            this.pauseVideo();
            break;
          case audio.InterruptHint.INTERRUPT_HINT_STOP:
            // This branch indicates that the system has stopped the audio stream (permanently lost focus).
            // To maintain consistency in state, the application should switch to the audio paused state.
            // Permanently lost focus: No further audio interruption events will be received. To resume
            // playback, user intervention is required.
            this.updateIsPlay(false);
            this.pauseVideo();
            break;
          case audio.InterruptHint.INTERRUPT_HINT_DUCK:
            // This branch indicates that the system has reduced the audio volume (default to 20% of the normal volume).
            // To maintain consistency in state, the application should switch to the reduced volume playback state.
            // If the application does not accept reduced volume playback, it can choose an alternative handling method
            // here, such as pausing playback.
            break;
          case audio.InterruptHint.INTERRUPT_HINT_UNDUCK:
            // This branch indicates that the system has restored the audio volume to normal. To maintain
            // consistency in state, the application should switch to the normal volume playback state.
            break;
          default:
            break;
        }
      } else if (interruptEvent.forceType === audio.InterruptForceType.INTERRUPT_SHARE) {
        // For the INTERRUPT_SHARE type: The application can choose to perform related actions or ignore the
        // audio interruption event.
        switch (interruptEvent.hintType) {
          case audio.InterruptHint.INTERRUPT_HINT_RESUME:
            // This branch indicates that the audio stream, which was paused due to temporary loss of focus,
            // can now resume playing. It is recommended that the application resumes playback and switches
            // to the audio playback state.
            // If the application does not want to resume playback at this point, it can ignore this audio
            // interruption event and take no action.
            // Resume playback by explicitly calling `start()` here, and record the result of the `start()`
            // call in a flag variable `started`.
            this.playVideo();
            break;
          default:
            break;
        }
      }
    })
  }

  private setStateChangeCallback(avPlayer: media.AVPlayer) {
    avPlayer.on('stateChange', async (state: string) => {
      switch (state) {
        case 'idle':
          TKLog.info('AVPlayer state idle called.' + ` this.curIndex:${this.curIndex}`);
          this.isFinish = false;
          break;
        case 'initialized':
          TKLog.info('AVPlayer state initialized called.' + ` this.curIndex:${this.curIndex}`);
          avPlayer.surfaceId = this.surfaceID;
          avPlayer.prepare();
          this.isFinish = false;
          break;
        case 'prepared':
          avPlayer.audioInterruptMode = audio.InterruptMode.INDEPENDENT_MODE;
          TKLog.info(`AVPlayer state prepared called. this.curIndex:${this.curIndex}`);
          this.isReady = true;
          avPlayer.loop = false;
          avPlayer.audioInterruptMode = audio.InterruptMode.SHARE_MODE;
          this.videoWidth = avPlayer.width;
          this.videoHeight = avPlayer.height;
          this.duration = avPlayer.duration;
          this.durationTime = Math.floor(this.duration / 1000);
          this.durationStringTime = TKDateHelper.formatSeconds(this.durationTime);
          this.isFinish = false;
          if (this.curIndex === this.curSource.index) {
            this.playVideo();
          }
          break;
        case 'completed':
          TKLog.info('AVPlayer state completed called.' + ` this.curIndex:${this.curIndex}`);
          this.isPlaying = false;
          this.isFinish = true;
          break;
        case 'playing':
          this.isPlaying = true;
          this.isFinish = false;
          TKLog.info('AVPlayer state playing called.' + ` this.curIndex:${this.curIndex}`);
          break;
        case 'paused':
          TKLog.info('AVPlayer state paused called.' + ` this.curIndex:${this.curIndex}`);
          this.isFinish = false;
          break;
        case 'stopped':
          TKLog.info('AVPlayer state stopped called.' + ` this.curIndex:${this.curIndex}`);
          this.isFinish = false;
          break;
        case 'released':
          TKLog.info('AVPlayer state released called.' + ` this.curIndex:${this.curIndex}`);
          this.isFinish = false;
          break;
        case 'error':
          TKLog.info('AVPlayer state error called.' + ` this.curIndex:${this.curIndex}`);
          this.isFinish = false;
          break;
        default:
          TKLog.info('AVPlayer state unknown called.' + ` this.curIndex:${this.curIndex}`);
          this.isFinish = false;
          break;
      }
    })
  }

  private setOutputDeviceChangeCallback() {
    if (!this.avPlayer) {
      return;
    }
    this.avPlayer.on('audioOutputDeviceChangeWithInfo', (data: audio.AudioStreamDeviceChangeInfo) => {
      if (data.changeReason === audio.AudioStreamDeviceChangeReason.REASON_OLD_DEVICE_UNAVAILABLE) {
        TKLog.info(`Device break: ${data.changeReason}`);
        this.pauseVideo();
      } else if (data.changeReason === audio.AudioStreamDeviceChangeReason.REASON_NEW_DEVICE_AVAILABLE) {
        TKLog.info(`Device connect: ${data.changeReason}`);
      }
    });
  }

  private updateIsPlay(isPlay: boolean) {
    if (this.curIndex !== this.curSource.index) {
      return;
    }
    this.isPlaying = isPlay;
    TKAvSessionController.getInstance().setAvSessionPlayState({
      state: isPlay ? avSession.PlaybackState.PLAYBACK_STATE_PLAY : avSession.PlaybackState.PLAYBACK_STATE_PAUSE,
      position: {
        elapsedTime: this.currentTime * 1000,
        updateTime: new Date().getTime()
      },
      duration: this.duration
    });
  }

  private isCurAvSession(): boolean {
    let curAvMetadata = TKAvSessionController.getInstance().getAvSessionMetadata();
    if (!curAvMetadata) {
      TKLog.error('playVideo failed, AvMetadata is undefined');
    }
    if (curAvMetadata?.assetId === `${this.curSource?.index}`) {
      return true;
    }
    return false;
  }

  private sessionPlayCallback() {
    if (this.isCurAvSession()) {
      this.playVideo();
    }
  }

  private sessionPauseCallback() {
    if (this.isCurAvSession()) {
      this.pauseVideo();
    }
  }

  private sessionStopCallback() {
    if (this.isCurAvSession()) {
      this.stopVideo();
    }
  }

  private sessionFastForwardCallback(time?: number) {
    if (this.isCurAvSession()) {
      if (!time) {
        return;
      }
      if (this.isCurAvSession()) {
        this.seek((this.currentTime + time) * 1000);
      }
    }
  }

  private sessionRewindCallback(time?: number) {
    if (!time) {
      return;
    }
    if (this.isCurAvSession()) {
      this.seek((this.currentTime - time) * 1000);
    }
  }

  private sessionSeekCallback(seekTime: number) {
    if (this.isCurAvSession()) {
      this.seek(seekTime);
    }
  }

  public playVideo() {
    if (this.avPlayer) {
      if (this.avPlayer.state !== TKAVPlayerState.PREPARED && this.avPlayer.state !== TKAVPlayerState.PAUSED &&
        this.avPlayer.state !== TKAVPlayerState.COMPLETED) {
        return;
      }
      TKAvSessionController.getInstance().setAVMetadata(this.curSource, this.duration);
      //Writing in the avPlayer.play callback can cause a delay in the state change of the playback control center.
      this.updateIsPlay(true);
      TKLog.info(`playVideo: state:${this.avPlayer.state} this.curIndex:${this.curIndex}`);
      this.avPlayer.play((err: BusinessError) => {
        if (err) {
          this.updateIsPlay(false); //If playback fails, revert the state of the playback control center.
          TKLog.error(`playVideo failed, code is ${err.code}, message is ${err.message}`);
        } else {
          TKLog.info(`playVideo success , this.curIndex:${this.curIndex}`);
        }
      });
    } else {
      TKLog.info(`playVideo: avPlayer NULL, this.curIndex:${this.curIndex}`);
    }
  }

  public pauseVideo() {
    if (this.avPlayer) {
      TKLog.info(`pauseVideo: state:${this.avPlayer.state} this.curIndex:${this.curIndex}`);
      this.updateIsPlay(false);
      this.avPlayer.pause((err: BusinessError) => {
        if (err) {
          this.updateIsPlay(true);
          TKLog.error(`pauseVideo failed, code is ${err.code}, message is ${err.message}, this.curIndex:${this.curIndex}`);
        } else {
          TKLog.info(`pauseVideo success , this.curIndex:${this.curIndex}`);
        }
      });
    } else {
      TKLog.info(`pauseVideo avPlayer NULL,  this.curIndex:${this.curIndex}`);
    }
  }

  public stopVideo() {
    if (this.avPlayer) {
      TKLog.info(`stopVideo: state: ${this.avPlayer.state} this.curIndex:${this.curIndex}`);
      if (this.avPlayer.state !== TKAVPlayerState.PREPARED && this.avPlayer.state !== TKAVPlayerState.PLAYING &&
        this.avPlayer.state !== TKAVPlayerState.PAUSED && this.avPlayer.state !== TKAVPlayerState.COMPLETED) {
        return;
      }
      this.updateIsPlay(false);
      this.avPlayer.stop((err: BusinessError) => {
        if (err) {
          this.updateIsPlay(true);
          TKLog.error(`stopVideo failed, code is ${err.code}, message is ${err.message}`);
        } else {
          this.avPlayer!.release();
          TKLog.info(`stopVideo success, this.curIndex:${this.curIndex}`);
        }
      });
    } else {
      TKLog.info(`stopVideo avPlayer NULL,  this.curIndex:${this.curIndex}`);
    }
  }

  // Parameter seekTime unit - milliseconds (ms)
  public seek(seekTime: number) {
    if (this.avPlayer) {
      seekTime = Math.max(seekTime, 0);
      seekTime = Math.min(seekTime, this.duration);
      if (this.avPlayer.state !== TKAVPlayerState.PREPARED && this.avPlayer.state !== TKAVPlayerState.PLAYING &&
        this.avPlayer.state !== TKAVPlayerState.PAUSED && this.avPlayer.state !== TKAVPlayerState.COMPLETED) {
        return;
      }
      this.currentTime = seekTime / 1000;
      this.avPlayer.seek(seekTime, media.SeekMode.SEEK_PREV_SYNC);
      this.updateIsPlay(this.isPlaying);
    }
  }

  public setCurIndex(curIndex: number) {
    this.curIndex = curIndex;
  }

  public setIsPlaying(isPlayer: boolean) {
    this.isPlaying = isPlayer;
  }

  public setDurationTime(durationTime: number) {
    this.durationTime = durationTime;
  }

  public setDurationStringTime(durationStringTime: string) {
    this.durationStringTime = durationStringTime;
  }

  public setCurrentStringTime(currentStringTime: string) {
    this.currentStringTime = currentStringTime;
  }

  public setSurfaceID(surfaceID: string) {
    this.surfaceID = surfaceID;
  }

  public releaseVideo(index: number = 0) {
    if (this.avPlayer) {
      TKLog.info(`releaseVideo: state:${this.avPlayer.state} this.curIndex:${this.curIndex} this.index:${index}`);
      this.avPlayer.off('timeUpdate');
      this.avPlayer.off('seekDone');
      this.avPlayer.off('speedDone');
      this.avPlayer.off('error');
      this.avPlayer.off('stateChange');
      this.avPlayer.release();
      TKAvSessionController.getInstance().unregisterSessionListener();
    }
  }
}