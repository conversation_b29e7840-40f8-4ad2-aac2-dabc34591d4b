import { TKNavRouterInfo } from '../../../../router/TKRouterHelper';
import { TKVideoPage } from './TKVideoPage';

/**
 * 构建动态路由
 * @returns
 */
export function buildTKComVideoPageRouteInfo(): TKNavRouterInfo {
  return {
    name: "TKComVideoPage",
    buildFunction: wrapBuilder(buildTKComVideoPage)
  }
}

@Builder
export function buildTKComVideoPage(param?: ESObject) {
  TKComVideoPage()
}

/**
 * 视频播放组件
 */
@Entry({ routeName: 'TKComVideoPage' })
@Component
export struct TKComVideoPage {
  // 实现H5内部页面的逐级返回
  @Provide({allowOverride:'onBackPressChanged'}) onBackPressChanged: boolean = false;
  @Provide({allowOverride:'onBackPressFilter'}) onBackPressFilter: boolean = true;

  // 实现H5内部页面的逐级返回
  onBackPress(): boolean {
    this.onBackPressChanged = !this.onBackPressChanged;
    return this.onBackPressFilter;
  }

  build() {
    NavDestination() {
      TKVideoPage()
    }.hideTitleBar(true)
    .onBackPressed(() => {
      return this.onBackPress();
    }).width("100%")
    .height("100%")
  }
}