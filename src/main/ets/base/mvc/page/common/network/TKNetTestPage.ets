import { TKAttribute } from '../../../../../components/common/attribute/TKAttribute';
import { TKObjectHelper } from '../../../../../util/data/TKObjectHelper';
import { TKRouterHelper } from '../../../../router/TKRouterHelper';
import { TKMapHelper } from '../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../util/string/TKStringHelper';
import { TKThemeManager } from '../../../../theme/TKThemeManager';
import { TKTitleBar } from '../../../../../components/titilebar/TKTitleBar';
import { TKBottomBar } from '../../../../../components/bottombar/TKBottomBar';
import { TKSystemHelper } from '../../../../../util/system/TKSystemHelper';
import { TKDeviceHelper, TKDeviceMemory } from '../../../../../util/dev/TKDeviceHelper';
import { TKNetHelper } from '../../../../../util/net/TKNetHelper';
import { TKComBusClient } from '../../../service/dao/socket/client/common/TKComBusClient';
import { TKBusClientManager } from '../../../service/dao/socket/client/common/TKBusClientManager';
import { TKHttpRoomServerListener } from '../../../service/dao/http/client/gateway/TKHttpRoomServerListener';
import { TKGatewayManager } from '../../../service/dao/socket/client/gateway/TKGatewayManager';
import { TKNetAddress } from '../../../service/dao/socket/client/gateway/TKNetAddress';
import { TKHttpAddress } from '../../../service/dao/http/client/gateway/TKHttpAddress';
import { TKSocketNetworkChecker } from '../../../service/dao/socket/client/gateway/TKSocketNetworkChecker';
import { TKURLRequestHelper } from '../../../../../util/net/TKURLRequestHelper';
import { TKNumberHelper, TKNumberRadix } from '../../../../../util/number/TKNumberHelper';
import { TKPasteboardHelper } from '../../../../../util/system/TKPasteboardHelper';
import { TKDialogHelper } from '../../../../../util/ui/TKDialogHelper';
import { TKReqParamVO } from '../../../model/TKReqParamVO';
import { TKCommonService } from '../../../service/TKCommonService';

/**
 * 子组件的控制属性类
 */
@Observed
export class TKNetTestPageAttribute extends TKAttribute {
  //页面标题
  title?: string = "应用网络诊断";
  //返回按钮模式（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
  backBtnMode?: string = "1";
  //复制按钮文本
  copyBtnText?: string = "复制到剪切板";
  //发送按钮文本
  sendBtnText?: string = "发送诊断报告";
}

/**
 * 子组件的样式属性类
 */
@Observed
export class TKNetTestPageStyleAttribute extends TKAttribute {
  //标题文字颜色
  titleColor?: ResourceColor;
  //状态栏颜色
  statusColor?: ResourceColor;
  //状态栏样式
  statusStyle?: string;
  //复制按钮背景颜色(例如#FFFFFF)
  copyBtnBgColor?: ResourceColor;
  //复制按钮文字颜色(例如#FFFFFF)
  copyBtnTextColor?: ResourceColor;
  //发送按钮背景颜色(例如#FFFFFF)
  sendBtnBgColor?: ResourceColor;
  //发送按钮文字颜色(例如#FFFFFF)
  sendBtnTextColor?: ResourceColor;
}

/**
 * 基础框架网络诊断测试功能
 */
@Component
export struct TKNetTestPage {
  /**
   * 基本控制属性
   */
  @State attribute: TKNetTestPageAttribute = new TKNetTestPageAttribute();
  /**
   * 样式属性
   */
  @State styleAttribute: TKNetTestPageStyleAttribute = new TKNetTestPageStyleAttribute();
  /**
   * 实际页面底部区域的高度
   */
  @State private bottomBarHeight: number = 0;
  /**
   * 实际页面顶部区域的高度（标题栏 + 状态栏）
   */
  @State private titleBarHeight: number = 0;
  /**
   * 是否完成测试
   */
  @State private isNetTestFinished: boolean = false;
  /**
   * 是否包含发送按钮
   */
  @State private isHasSendBtn: boolean = false;
  /**
   *  网络诊断内容
   */
  @State private netTestContent: string = "";
  /**
   * Socket 检查对象
   */
  private socketChecker?: TKSocketNetworkChecker;
  /**
   * http 请求对象
   */
  private commonService: TKCommonService = new TKCommonService();

  aboutToAppear() {
    TKObjectHelper.fixDefault(this.attribute, TKNetTestPageAttribute);
    TKObjectHelper.fixDefault(this.styleAttribute, TKNetTestPageStyleAttribute);
    let param: Record<string, Object> = TKRouterHelper.getParams(this);
    this.attribute.title = TKMapHelper.getString(param, "title", this.attribute.title);
    this.attribute.backBtnMode = TKMapHelper.getString(param, "backBtnMode", this.attribute.backBtnMode);
    this.attribute.copyBtnText = TKMapHelper.getString(param, "copyBtnText", this.attribute.copyBtnText);
    if (TKStringHelper.isBlank(this.styleAttribute.statusStyle)) {
      this.styleAttribute.statusStyle = TKMapHelper.getString(param, "statusStyle");
    }
    if (this.styleAttribute.titleColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.titleColor?.toString())) {
      this.styleAttribute.titleColor = TKMapHelper.getObject(param, "titleColor",
        TKThemeManager.shareInstance().getCssRulesetByClassName("TKNetTestPageTitle").color ?? "#FFFFFFFF");
    }
    if (this.styleAttribute.statusColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.statusColor?.toString())) {
      let statusBarColor: ResourceColor = TKMapHelper.getObject(param, "statusBarColor");
      if (!statusBarColor || statusBarColor == '#00FFFFFF') {
        statusBarColor =
          TKThemeManager.shareInstance().getCssRulesetByClassName("TKNetTestPageTitle").backgroundColor ?? "#FF1061FF";
      }
      this.styleAttribute.statusColor = statusBarColor;
    }
    if (this.styleAttribute.copyBtnBgColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.copyBtnBgColor?.toString())) {
      this.styleAttribute.copyBtnBgColor =
        TKMapHelper.getObject(param, "copyBtnBgColor",
          TKThemeManager.shareInstance().getCssRulesetByClassName("TKNetTestPageCopyBtn").backgroundColor ??
            "#FFFF7D00");
    }
    if (this.styleAttribute.copyBtnTextColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.copyBtnTextColor?.toString())) {
      this.styleAttribute.copyBtnTextColor =
        TKMapHelper.getObject(param, "copyBtnTextColor",
          TKThemeManager.shareInstance().getCssRulesetByClassName("TKNetTestPageCopyBtn").color ?? "#FFFFFFFF");
    }
    if (this.styleAttribute.sendBtnBgColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.sendBtnBgColor?.toString())) {
      this.styleAttribute.sendBtnBgColor =
        TKMapHelper.getObject(param, "sendBtnBgColor",
          TKThemeManager.shareInstance().getCssRulesetByClassName("TKNetTestPageSendBtn").backgroundColor ??
          this.styleAttribute.copyBtnBgColor);
    }
    if (this.styleAttribute.sendBtnTextColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.sendBtnTextColor?.toString())) {
      this.styleAttribute.sendBtnTextColor =
        TKMapHelper.getObject(param, "sendBtnTextColor",
          TKThemeManager.shareInstance().getCssRulesetByClassName("TKNetTestPageSendBtn").color ??
          this.styleAttribute.copyBtnTextColor);
    }
    //是否包含发送按钮
    this.isHasSendBtn = TKStringHelper.isNotBlank(TKSystemHelper.getConfig("networkTest.uploadUrl"));
    //测试内容
    this.netTestContent = "";
    //是否完成测试
    this.isNetTestFinished = false;
    //开始进行网络诊断测试
    this.startNetworkTest();
  }

  /**
   * 开始进行网络诊断测试
   */
  private async startNetworkTest() {
    this.getDeviceInfo();
  }

  /**
   *  获取基础设备信息
   */
  private async getDeviceInfo() {
    let content: string = "";
    content += `设备类型：${TKDeviceHelper.getDeviceName()}\n`;
    content += `系统版本：${TKDeviceHelper.getDeviceSysVersion()}\n`;
    content += `系统语言：${TKDeviceHelper.getDeviceSysLanguage()}\n`;
    content += `系统时区：${TKDeviceHelper.getDeviceTimeZone()}\n`;
    content += `应用名称：${TKSystemHelper.getAppDisplayName()}\n`;
    content += `应用版本：${TKSystemHelper.getVersion()}(${TKSystemHelper.getVersionCode()})\n`;
    let deviceMemInfo: TKDeviceMemory = TKDeviceHelper.getDeviceMemory();
    content += `内存信息：共${deviceMemInfo.totalMem}，可用${deviceMemInfo.availableMem}，剩余${deviceMemInfo.freeMem}\n`;
    content += `CPU信息：系统${TKDeviceHelper.getDeviceCpu()}，应用${TKDeviceHelper.getAppCpu()}\n`;
    content += `电池信息：${TKDeviceHelper.getDeviceBattery()}\n`;
    content += `是否模拟器：${TKDeviceHelper.isEmulator() ? "是" : "否"}\n`;
    content += `设备ID：${TKDeviceHelper.getDeviceUUID()}\n`;
    content += `网络类型：${TKNetHelper.getNetworkTypeInfo()}\n`;
    content += `连接状态：${TKNetHelper.isNetAvailable() ? "已连接" : "已断开"}\n`;
    content += `运营商：${TKNetHelper.getPhoneOperatorName()}\n`;
    content += `WIFI信息：${await TKNetHelper.getWiFiName()}\n`;
    content += `\n当前用户使用站点\n`;
    this.netTestContent = content;
    this.getCurrentUseAddress();
  }

  /**
   * 显示当前的地址
   */
  private async getCurrentUseAddress() {
    let content: string = this.netTestContent;
    let socketConfig: string = TKSystemHelper.getConfig("networkTest.socket");
    let httpConfig: string = TKSystemHelper.getConfig("networkTest.http");
    if (TKStringHelper.isNotBlank(socketConfig)) {
      let socketUrls: Array<string> = socketConfig.split("|");
      if (socketUrls && socketUrls.length > 0) {
        for (let socketUrl of socketUrls) {
          if (!socketUrl.includes(".") && !socketUrl.includes(":")) {
            let comBusClient: TKComBusClient | undefined =
              TKBusClientManager.shareInstance().getCacheTKBusLongClient(socketUrl);
            if (comBusClient) {
              content += `${socketUrl}：${comBusClient.connectedHost}:${comBusClient.connectedPort}\n`;
            } else {
              content += `${socketUrl}：\n`;
            }
          }
        }
      }
    }
    if (TKStringHelper.isNotBlank(httpConfig)) {
      let httpUrls: Array<string> = httpConfig.split("|");
      if (httpUrls && httpUrls.length > 0) {
        for (let httpUrl of httpUrls) {
          if (!httpUrl.includes(".") && !httpUrl.includes(":")) {
            let fastHost: string = TKHttpRoomServerListener.shareInstance().getFlastHost(httpUrl);
            content += `${httpUrl}：${TKNetHelper.getSchemeHostPortByURL(fastHost)}\n`;
          }
        }
      }
    }
    content += `\n开始应用网络诊断，预计耗时几十秒...\n`;
    this.netTestContent = content;
    this.getOutputIP();
  }

  /**
   *  获取外网出口IP
   */
  private async getOutputIP() {
    let content: string = this.netTestContent;
    content += `外网IP：${TKNetHelper.getIP()}\n`;
    this.netTestContent = content;
    this.checkLocalNetworkInfo();
  }

  /**
   *  获取本地网络信息
   */
  private async checkLocalNetworkInfo() {
    let content: string = this.netTestContent;
    content += `本地IP：${TKNetHelper.getLocalIP()}\n`;
    content += `本地DNS：${TKNetHelper.getOutPutDNSServers().join(",")}\n`;
    let cellNetworkInfo = TKNetHelper.getCellNetworkInfo();
    let wifiNetworkInfo = TKNetHelper.getWifiNetworkInfo();
    if (cellNetworkInfo) {
      let cellLocalAddresses: Array<string> = new Array<string>();
      if (TKStringHelper.isNotBlank(cellNetworkInfo.ipv4LocalAddress)) {
        cellLocalAddresses.push(cellNetworkInfo.ipv4LocalAddress);
      }
      if (TKStringHelper.isNotBlank(cellNetworkInfo.ipv6LocalAddress)) {
        cellLocalAddresses.push(cellNetworkInfo.ipv6LocalAddress);
      }
      content += `蜂窝网络本地IP：${cellLocalAddresses.join("/")}\n`;
      let cellNetmaskAddresses: Array<string> = new Array<string>();
      if (TKStringHelper.isNotBlank(cellNetworkInfo.ipv4NetmaskAddress)) {
        cellNetmaskAddresses.push(cellNetworkInfo.ipv4NetmaskAddress);
      }
      if (TKStringHelper.isNotBlank(cellNetworkInfo.ipv6NetmaskAddress)) {
        cellNetmaskAddresses.push(cellNetworkInfo.ipv6NetmaskAddress);
      }
      content += `蜂窝网络子网掩码：${cellNetmaskAddresses.join("/")}\n`;
      let cellBroadcastAddresses: Array<string> = new Array<string>();
      if (TKStringHelper.isNotBlank(cellNetworkInfo.ipv4BroadcastAddress)) {
        cellBroadcastAddresses.push(cellNetworkInfo.ipv4BroadcastAddress);
      }
      if (TKStringHelper.isNotBlank(cellNetworkInfo.ipv6BroadcastAddress)) {
        cellBroadcastAddresses.push(cellNetworkInfo.ipv6BroadcastAddress);
      }
      content += `蜂窝网络广播地址：${cellNetmaskAddresses.join("/")}\n`;
    }
    if (wifiNetworkInfo) {
      let wifiLocalAddresses: Array<string> = new Array<string>();
      if (TKStringHelper.isNotBlank(wifiNetworkInfo.ipv4LocalAddress)) {
        wifiLocalAddresses.push(wifiNetworkInfo.ipv4LocalAddress);
      }
      if (TKStringHelper.isNotBlank(wifiNetworkInfo.ipv6LocalAddress)) {
        wifiLocalAddresses.push(wifiNetworkInfo.ipv6LocalAddress);
      }
      content += `WIFI网络本地IP：${wifiLocalAddresses.join("/")}\n`;
      let wifiNetmaskAddresses: Array<string> = new Array<string>();
      if (TKStringHelper.isNotBlank(wifiNetworkInfo.ipv4NetmaskAddress)) {
        wifiNetmaskAddresses.push(wifiNetworkInfo.ipv4NetmaskAddress);
      }
      if (TKStringHelper.isNotBlank(wifiNetworkInfo.ipv6NetmaskAddress)) {
        wifiNetmaskAddresses.push(wifiNetworkInfo.ipv6NetmaskAddress);
      }
      content += `WIFI网络子网掩码：${wifiNetmaskAddresses.join("/")}\n`;
      let wifiBroadcastAddresses: Array<string> = new Array<string>();
      if (TKStringHelper.isNotBlank(wifiNetworkInfo.ipv4BroadcastAddress)) {
        wifiBroadcastAddresses.push(wifiNetworkInfo.ipv4BroadcastAddress);
      }
      if (TKStringHelper.isNotBlank(wifiNetworkInfo.ipv6BroadcastAddress)) {
        wifiBroadcastAddresses.push(wifiNetworkInfo.ipv6BroadcastAddress);
      }
      content += `WIFI网络广播地址：${wifiBroadcastAddresses.join("/")}\n`;
      let wifiRouterAddresses: Array<string> = new Array<string>();
      if (TKStringHelper.isNotBlank(wifiNetworkInfo.ipv4RouterAddress)) {
        wifiRouterAddresses.push(wifiNetworkInfo.ipv4RouterAddress);
      }
      if (TKStringHelper.isNotBlank(wifiNetworkInfo.ipv6RouterAddress)) {
        wifiRouterAddresses.push(wifiNetworkInfo.ipv6RouterAddress);
      }
      content += `WIFI路由网关地址：${wifiRouterAddresses.join("/")}\n`;
    }
    this.netTestContent = content;
    this.checkDNSDomain();
  }

  /**
   * 检测域名解析
   */
  private async checkDNSDomain() {
    let content: string = this.netTestContent;
    let dnsDomains: Array<string> = new Array<string>();
    let socketConfig: string = TKSystemHelper.getConfig("networkTest.socket");
    let httpConfig: string = TKSystemHelper.getConfig("networkTest.http");
    if (TKStringHelper.isNotBlank(socketConfig)) {
      let socketUrls: Array<string> = socketConfig.split("|");
      if (socketUrls && socketUrls.length > 0) {
        for (let socketUrl of socketUrls) {
          if (socketUrl.includes(":")) {
            let domain: string = TKNetHelper.getHostByURL(socketUrl);
            if (!dnsDomains.includes(domain)) {
              if (TKNetHelper.isDomainURL(domain)) {
                dnsDomains.push(domain);
              }
            }
          } else {
            let server = TKGatewayManager.shareInstance().getServer(socketUrl);
            if (server) {
              let addresses: Array<TKNetAddress> = server.getAllNetworkAddresses();
              if (addresses && addresses.length > 0) {
                for (let address of addresses) {
                  if (!dnsDomains.includes(address.ip)) {
                    if (TKNetHelper.isDomainURL(address.ip)) {
                      dnsDomains.push(address.ip)
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    if (TKStringHelper.isNotBlank(httpConfig)) {
      let httpUrls: Array<string> = httpConfig.split("|");
      if (httpUrls && httpUrls.length > 0) {
        for (let httpUrl of httpUrls) {
          if (httpUrl.includes(".") || httpUrl.includes(":")) {
            let domain = TKNetHelper.getHostByURL(httpUrl);
            if (!dnsDomains.includes(domain)) {
              if (TKNetHelper.isDomainURL(domain)) {
                dnsDomains.push(domain);
              }
            }
          } else {
            let addresses: Array<TKHttpAddress> = TKHttpRoomServerListener.shareInstance().getServerAddresses(httpUrl);
            for (let address of addresses) {
              let domain: string = TKNetHelper.getHostByURL(address.url);
              if (!dnsDomains.includes(domain)) {
                if (TKNetHelper.isDomainURL(domain)) {
                  dnsDomains.push(domain);
                }
              }
            }
          }
        }
      }
    }
    if (dnsDomains && dnsDomains.length > 0) {
      content += `\n开始网络域名DNS诊断测试...\n`;
      for (let domain of dnsDomains) {
        content += `\n诊断域名：${domain}\n`;
        let dnsIpv4BeginTime: number = new Date().getTime();
        let ipv4DNS: Array<string> = await TKNetHelper.getDNSIPV4AddressByHostName(domain);
        let dnsIpv4EndTime: number = new Date().getTime();
        let dnsIpv4Time: number = dnsIpv4EndTime - dnsIpv4BeginTime;
        if (ipv4DNS && ipv4DNS.length > 0) {
          content += `IPV4解析结果：${ipv4DNS.join(",")}\n`;
          content += `IPV4外部DNS解析：成功(耗时:${dnsIpv4Time}毫秒)\n`;
        } else {
          content += `IPV4外部DNS解析：失败(耗时:${dnsIpv4Time}毫秒)\n`;
        }
        let dnsIpv6BeginTime: number = new Date().getTime();
        let ipv6DNS: Array<string> = await TKNetHelper.getDNSIPV6AddressByHostName(domain);
        let dnsIpv6EndTime: number = new Date().getTime();
        let dnsIpv6Time = dnsIpv6EndTime - dnsIpv6BeginTime;
        if (ipv6DNS && ipv6DNS.length > 0) {
          content += `IPV6解析结果：${ipv6DNS.join(",")}\n`;
          content += `IPV6外部DNS解析：成功(耗时:${dnsIpv6Time}毫秒)\n`;
        } else {
          content += `IPV6外部DNS解析：失败(耗时:${dnsIpv6Time}毫秒)\n`;
        }
        this.netTestContent = content;
      }
    }
    this.checkSocketConnectSpeedResult();
  }

  /**
   * 检测连接速度
   */
  private async checkSocketConnectSpeedResult() {
    let socketServers: Array<string> = new Array<string>();
    let socketConfig: string = TKSystemHelper.getConfig("networkTest.socket");
    if (TKStringHelper.isNotBlank(socketConfig)) {
      let socketUrls: Array<string> = socketConfig.split("|");
      if (socketUrls && socketUrls.length > 0) {
        for (let socketUrl of socketUrls) {
          if (socketUrl.includes(":")) {
            let socketServer: string = TKNetHelper.getHostPortByURL(socketUrl);
            if (TKNetHelper.formatAddress(socketServer).length == 2) {
              if (!socketServers.includes(socketServer)) {
                socketServers.push(socketServer);
              }
            }
          } else {
            let server = TKGatewayManager.shareInstance().getServer(socketUrl);
            if (server) {
              let addresses: Array<TKNetAddress> = server.getAllNetworkAddresses();
              if (addresses && addresses.length > 0) {
                for (let address of addresses) {
                  let socketServer: string = `${address.ip}:${address.port}`;
                  if (address.ip.includes(":")) {
                    socketServer = `[${address.ip}]:${address.port}`;
                  }
                  if (!socketServers.includes(socketServer)) {
                    socketServers.push(socketServer);
                  }
                }
              }
            }
          }
        }
      }
    }
    let content: string = this.netTestContent;
    content += `\n开始网络地址数据访问诊断测试...\n`;
    this.netTestContent = content;
    this.checkSocketConnectSpeedResultInner(socketServers);
  }

  /**
   * 检测Socket连接速度
   */
  private async checkSocketConnectSpeedResultInner(socketServers: Array<string>) {
    if (socketServers && socketServers.length > 0) {
      let socketServer: string = socketServers.shift() as string;
      let temp: Array<string> = TKNetHelper.formatAddress(socketServer);
      if (temp && temp.length == 2) {
        let content: string = this.netTestContent;
        content += `\n诊断地址：${socketServer}\n`;
        this.netTestContent = content;
        let host: string = temp[0];
        let port: number = Number(temp[temp.length -1]);
        this.socketChecker = new TKSocketNetworkChecker(host, port);
        this.socketChecker.start((isFinished, errorNo, errorInfo) => {
          if (isFinished) {
            content += `诊断结果：${errorInfo}\n`;
            this.netTestContent = content;
            this.checkSocketConnectSpeedResultInner(socketServers);
          } else {
            content += `诊断过程：${errorInfo}\n`;
          }
        });
      }
    } else {
      this.checkHttpConnectSpeedResult();
    }
  }

  /**
   * 检测Http连接速度
   */
  private checkHttpConnectSpeedResult() {
    let httpServers: Array<string> = new Array<string>();
    let httpConfig: string = TKSystemHelper.getConfig("networkTest.http");
    if (TKStringHelper.isNotBlank(httpConfig)) {
      let httpUrls: Array<string> = httpConfig.split("|");
      if (httpUrls && httpUrls.length > 0) {
        for (let httpUrl of httpUrls) {
          if (httpUrl.includes(".") || httpUrl.includes(":")) {
            let httpServer: string = `${TKNetHelper.getSchemeHostPortByURL(httpUrl)}/speed.jsp`;
            if (!httpServers.includes(httpServer)) {
              httpServers.push(httpServer);
            }
          } else {
            let addresses: Array<TKHttpAddress> = TKHttpRoomServerListener.shareInstance().getServerAddresses(httpUrl);
            for (let address of addresses) {
              let httpServer: string = `${TKNetHelper.getSchemeHostPortByURL(address.url)}/speed.jsp`;
              if (!httpServers.includes(httpServer)) {
                httpServers.push(httpServer);
              }
            }
          }
        }
      }
    }
    this.checkHttpConnectSpeedResultInner(httpServers);
  }

  /**
   * 检测Http连接速度
   */
  private async checkHttpConnectSpeedResultInner(httpServers: Array<string>) {
    if (httpServers && httpServers.length > 0) {
      let httpServer: string = httpServers.shift() as string;
      let content: string = this.netTestContent;
      content += `\n诊断地址：${httpServer}\n`;
      this.netTestContent = content;
      let httpCheckBeginTime: number = new Date().getTime();
      TKURLRequestHelper.getRequest({
        url: httpServer
      }, (result) => {
        let httpCheckEndTime: number = new Date().getTime();
        let dataTime: number = httpCheckEndTime - httpCheckBeginTime;
        let statusCode: number = result.statusCode;
        let data: Uint8Array = result.result;
        if (statusCode == 200) {
          let dataSize: string = TKNumberHelper.formatNumberRadix(data.byteLength, 2, TKNumberRadix.BIN_MEM);
          content += `诊断结果：测试数据已返回(大小:${dataSize},耗时:${dataTime}毫秒)\n`;
        } else {
          content += `诊断结果：网络异常(statusCode:${statusCode},耗时:${dataTime}毫秒)\n`;
        }
        this.netTestContent = content;
        this.checkHttpConnectSpeedResultInner(httpServers);
      });
    } else {
      let content: string = this.netTestContent;
      content += `\n应用网络诊断已结束...`;
      this.netTestContent = content;
      this.isNetTestFinished = true;
    }
  }

  /**
   * 复制到剪切板
   */
  private copyToPanel() {
    TKPasteboardHelper.copyDataText(this.netTestContent);
    TKDialogHelper.showToast("内容已复制到剪贴板...");
  }

  /**
   * 发送到服务器
   */
  private sendReport() {
    let url: string = TKSystemHelper.getConfig("networkTest.uploadUrl");
    let reqMode: string = TKSystemHelper.getConfig("networkTest.reqMode");
    if (TKStringHelper.isNotBlank(url)) {
      let reqParamVO: TKReqParamVO = this.commonService.createReqParamByReqMode(url, reqMode);
      let paramConfig: Record<string, Object | undefined> = reqParamVO.reqParam;
      paramConfig = paramConfig ?? {};
      let paramKeys: Array<string> = TKMapHelper.keys(paramConfig);
      if (paramKeys.includes("channel") && TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "channel"))) {
        paramConfig["channel"] = "5";
      }
      if (paramKeys.includes("soft_no") && TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "soft_no"))) {
        paramConfig["soft_no"] = TKSystemHelper.getAppIdentifier();
      }
      if (paramKeys.includes("version_code") &&
      TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "version_code"))) {
        paramConfig["version_code"] = TKSystemHelper.getVersionCode();
      }
      if (paramKeys.includes("device_id") && TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "device_id"))) {
        paramConfig["device_id"] = TKDeviceHelper.getDeviceUUID();
      }
      paramConfig["content"] = this.netTestContent;
      reqParamVO.reqParam = paramConfig;
      reqParamVO.isShowWait = true;
      this.commonService.serviceInvoke(reqParamVO, (resultVO) => {
        if (resultVO.errorNo == 0) {
          TKDialogHelper.showToast("上传成功！");
        } else {
          TKDialogHelper.showToast("上传失败！");
        }
      });
    } else {
      TKDialogHelper.showToast("上传地址为空！");
    }
  }

  build() {
    RelativeContainer() {
      this.TKNetTestTitleComponent()
      this.TKNetTestContentComponent()
      this.TKNetTestBtnComponent()
      this.TKNetTestBottomBarComponent()
    }.width("100%")
    .height("100%")
  }

  @Builder
  TKNetTestTitleComponent() {
    TKTitleBar({
      attribute: {
        isLayoutFullScreen: true,
        statusBarEnable: true,
        titleBarEnable: true,
        title: this.attribute.title,
        leftBtnMode: this.attribute.backBtnMode,
        backPressEnable: true,
        onTitleBarHeight: (height) => {
          this.titleBarHeight = height;
        }
      },
      styleAttribute: {
        statusStyle: this.styleAttribute.statusStyle,
        titleColor: this.styleAttribute.titleColor,
        titleBgColor: this.styleAttribute.statusColor
      }
    })
      .width("100%")
      .height(px2vp(this.titleBarHeight))
      .margin({ top: (px2vp(this.titleBarHeight) == 0) ? -1 : 0 })
      .alignRules({
        top: { anchor: "__container__", align: VerticalAlign.Top },
        left: { anchor: "__container__", align: HorizontalAlign.Start }
      })
      .id("TKNetTestPageTitleBar")
  }

  @Builder
  TKNetTestContentComponent() {
    Scroll() {
      Text(this.netTestContent)
        .fontSize(14)
        .fontFamily('3PingFang SC')
        .textAlign(TextAlign.Start)
        .align(Alignment.Top)
        .wordBreak(WordBreak.BREAK_ALL)
        .backgroundColor(Color.White)
        .enabled(false)
        .margin({
          top: 10,
          left: 10,
          right: 10,
          bottom: 10
        })
    }
    .scrollable(ScrollDirection.Vertical)
    .scrollBar(BarState.Off)
    .scrollBarWidth(3)
    .edgeEffect(EdgeEffect.Spring)
    .alignRules({
      top: { anchor: "TKNetTestPageTitleBar", align: VerticalAlign.Bottom },
      bottom: { anchor: "TKNetTestPageBtn", align: VerticalAlign.Top },
      left: { anchor: "__container__", align: HorizontalAlign.Start },
      right: { anchor: '__container__', align: HorizontalAlign.End },
    })
    .id("TKNetTestPageContent")
  }

  @Builder
  TKNetTestBtnComponent() {
    Row() {
      //确定按钮
      Text(this.attribute.copyBtnText)
        .fontSize(18)
        .fontFamily('PingFang SC')
        .height(44)
        .textAlign(TextAlign.Center)
        .width(this.isHasSendBtn ? "41%" : "88%")
        .margin({ left: "6%" })
        .backgroundColor(this.styleAttribute.copyBtnBgColor)
        .fontColor(this.styleAttribute.copyBtnTextColor)
        .borderColor(this.styleAttribute.copyBtnTextColor)
        .borderWidth(1)
        .borderRadius(10)
        .onClick(() => {
          this.copyToPanel();
        })
        .visibility(this.isNetTestFinished ? Visibility.Visible : Visibility.Hidden)

      //发送按钮
      if (this.isHasSendBtn) {
        Text(this.attribute.sendBtnText)
          .fontSize(18)
          .fontFamily('PingFang SC')
          .height(44)
          .textAlign(TextAlign.Center)
          .width("41%")
          .margin({ left: "6%" })
          .backgroundColor(this.styleAttribute.sendBtnBgColor)
          .fontColor(this.styleAttribute.sendBtnTextColor)
          .borderColor(this.styleAttribute.sendBtnTextColor)
          .borderWidth(1)
          .borderRadius(10)
          .onClick(() => {
            this.sendReport();
          })
          .visibility(this.isNetTestFinished ? Visibility.Visible : Visibility.Hidden)
      }
    }
    .alignRules({
      left: { anchor: '__container__', align: HorizontalAlign.Start },
      bottom: { anchor: "TKNetTestPageBottom", align: VerticalAlign.Top },
      right: { anchor: '__container__', align: HorizontalAlign.End }
    }).width("100%")
    .id("TKNetTestPageBtn")
  }

  @Builder
  TKNetTestBottomBarComponent() {
    TKBottomBar({
      attribute: {
        isLayoutFullScreen: true,
        bottomBarEnable: true,
        onBottomBarHeight: (height) => {
          this.bottomBarHeight = height;
        }
      }
    }).width("100%")
      .height(px2vp(this.bottomBarHeight))
      .alignRules({
        bottom: { anchor: "__container__", align: VerticalAlign.Bottom },
        left: { anchor: "__container__", align: HorizontalAlign.Start },
        right: { anchor: '__container__', align: HorizontalAlign.End }
      })
      .id("TKNetTestPageBottom")
  }
}