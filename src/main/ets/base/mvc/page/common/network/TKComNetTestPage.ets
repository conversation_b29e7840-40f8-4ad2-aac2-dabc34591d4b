import { TKNavRouterInfo } from '../../../../router/TKRouterHelper';
import { TKNetTestPage } from './TKNetTestPage';

/**
 * 构建动态路由
 * @returns
 */
export function buildTKComNetTestPageRouteInfo(): TKNavRouterInfo {
  return {
    name: "TKComNetTestPage",
    buildFunction: wrapBuilder(buildTKComNetTestPage)
  }
}

@Builder
export function buildTKComNetTestPage(param?: ESObject) {
  TKComNetTestPage()
}

/**
 * 基础框架网络诊断测试功能
 */
@Entry({ routeName: 'TKComNetTestPage' })
@Component
export struct TKComNetTestPage {
  // 实现H5内部页面的逐级返回
  @Provide({ allowOverride: 'onBackPressChanged' }) onBackPressChanged: boolean = false;
  @Provide({ allowOverride: 'onBackPressFilter' }) onBackPressFilter: boolean = true;

  // 实现H5内部页面的逐级返回
  onBackPress(): boolean {
    this.onBackPressChanged = !this.onBackPressChanged;
    return this.onBackPressFilter;
  }

  build() {
    NavDestination() {
      TKNetTestPage()
    }.hideTitleBar(true)
    .onBackPressed(() => {
      return this.onBackPress();
    }).width("100%")
    .height("100%")
  }
}