import { TKBottomBar } from '../../../../../components/bottombar/TKBottomBar';
import { TKJSProxyController } from '../../../../../components/webview/TKJSProxyController';
import { TKAttribute } from '../../../../../components/common/attribute/TKAttribute';
import { TKWebAttrEventController } from '../../../../../components/webview/TKWebAttrEventController';
import { TKObjectHelper } from '../../../../../util/data/TKObjectHelper';
import { TKMapHelper } from '../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../util/string/TKStringHelper';
import { TKTimer } from '../../../../../util/timer/TKTimer';
import { TKFoldDisplayListener } from '../../../../device/listener/TKFoldDisplayListener';
import { TKNotificationCenter } from '../../../../notification/TKNotificationCenter';
import { TKRouterHelper } from '../../../../router/TKRouterHelper';
import { TKThemeManager } from '../../../../theme/TKThemeManager';
import { TKWebPage } from '../web/TKWebPage';
import { display } from '@kit.ArkUI';
import { TKNotification } from '../../../../notification/TKNotification';

/**
 * 子组件的控制属性类
 */
@Observed
export class TKPdfPageAttribute extends TKAttribute {
  //文件地址
  url?: string = "";
  //文件标题
  title?: string = "内容查看";
  //是否显示加载过渡页
  isShowLoading?: boolean = true;
  //返回按钮模式（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
  leftBtnMode?: string = "0";
  //加载超时时间
  loadTimeOut?: number;
  //文件后缀
  suffix?: string = "pdf";
  //阅读停留查看最少时间
  readTime?: number = 0;
  //确认按钮文本（有传该文本值才展示确认按钮）
  okBtnReadText?: string;
  //右侧按钮模式（0：文本，1：图片）
  rightBtnMode?: string = "0";
  //右侧按钮（右侧按钮是文本模式传文本内容；是图片模式传图片名称）
  rightBtnTxt?: string;
  //右侧按钮动作，方便上层区分按钮点击逻辑
  rightBtnAction?: string = "action";
  //右侧按钮动作附加JSON格式参数
  rightBtnActionParam?: Record<string, Object>;
  //是否执行关闭回调(0:不执行，1：执行）默认是0
  isCallBackForClose?: boolean;
}

/**
 * 子组件的样式属性类
 */
@Observed
export class TKPdfPageStyleAttribute extends TKAttribute {
  //标题文字颜色
  titleColor?: ResourceColor;
  //状态栏颜色
  statusColor?: ResourceColor;
  //状态栏样式
  statusStyle?: string;
  //进度条颜色
  progressColor?: ResourceColor = Color.Blue;
  //确认按钮背景颜色(例如#FFFFFF)
  okBtnReadBgColor?: ResourceColor;
  //确认按钮文字颜色(例如#FFFFFF)
  okBtnReadTextColor?: ResourceColor;
  //确认按钮倒计时背景颜色(例如#FFFFFF)
  okBtnReadTimerBgColor?: ResourceColor;
  //确认按钮倒计时文字颜色(例如#FFFFFF)
  okBtnReadTimerTextColor?: ResourceColor;
  //显示加载过渡页的GIF名称
  loadingGIF?: Resource;
  //显示加载失败的图片名称
  loadFailedImage?: Resource;
}

@Component
export struct TKPdfPage {
  /**
   * 下载PDF的通知
   */
  public static NOTE_DOWNLOAD_PDF: string = "note_download_pdf";
  /**
   * 其他PDF操作Action通知
   */
  public static NOTE_ACTION_PDF: string = "note_action_pdf";
  /**
   * 计时器
   */
  private readTimer: TKTimer | undefined = undefined;
  /**
   * 是否加载完成
   */
  private isLoadFinish: boolean = false;
  /**
   * 实际页面底部区域的高度
   */
  @State private bottomBarHeight: number = 0;
  /**
   * 基本控制属性
   */
  @State attribute: TKPdfPageAttribute = new TKPdfPageAttribute();
  /**
   * 样式属性
   */
  @State styleAttribute: TKPdfPageStyleAttribute = new TKPdfPageStyleAttribute();
  /**
   *   是否折叠屏展开
   */
  @State private isFoldExpanded: boolean = false;
  @State private isRebuild: boolean = false;

  aboutToAppear() {
    TKObjectHelper.fixDefault(this.attribute, TKPdfPageAttribute);
    TKObjectHelper.fixDefault(this.styleAttribute, TKPdfPageStyleAttribute);
    let param: Record<string, Object> = TKRouterHelper.getParams(this);
    this.attribute.url = TKMapHelper.getString(param, "url", this.attribute.url);
    this.attribute.title = TKMapHelper.getString(param, "title", this.attribute.title);
    this.attribute.isShowLoading = TKMapHelper.getBoolean(param, "isShowLoading", this.attribute.isShowLoading);
    this.attribute.leftBtnMode = TKMapHelper.getString(param, "btnMode", this.attribute.leftBtnMode);
    this.attribute.loadTimeOut = TKMapHelper.getNumber(param, "loadTimeOut", this.attribute.loadTimeOut);
    this.attribute.suffix = TKMapHelper.getString(param, "suffix", this.attribute.suffix);
    this.attribute.readTime = TKMapHelper.getNumber(param, "readTime", this.attribute.readTime);
    this.attribute.okBtnReadText = TKMapHelper.getString(param, "okBtnReadText", this.attribute.okBtnReadText);
    this.attribute.rightBtnMode = TKMapHelper.getString(param, "rightBtnMode", this.attribute.rightBtnMode);
    this.attribute.rightBtnTxt = TKMapHelper.getString(param, "rightBtnTxt", this.attribute.rightBtnTxt);
    this.attribute.rightBtnAction = TKMapHelper.getString(param, "rightBtnAction", this.attribute.rightBtnAction);
    this.attribute.rightBtnActionParam =
      TKMapHelper.getJSON(param, "rightBtnActionParam", this.attribute.rightBtnActionParam);
    this.attribute.isCallBackForClose =
      TKMapHelper.getBoolean(param, "isCallBackForClose", this.attribute.isCallBackForClose);
    if (TKStringHelper.isBlank(this.attribute.rightBtnTxt)) {
      if (TKMapHelper.getBoolean(param, "isShowDownLoadBtn")) {
        this.attribute.rightBtnTxt = "tk_framework_download_btn";
        this.attribute.rightBtnMode = "1";
        this.attribute.rightBtnAction = TKPdfPage.NOTE_DOWNLOAD_PDF;
      }
    }

    if (TKStringHelper.isBlank(this.styleAttribute.statusStyle)) {
      this.styleAttribute.statusStyle = TKMapHelper.getString(param, "statusStyle");
    }
    if (this.styleAttribute.titleColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.titleColor?.toString())) {
      this.styleAttribute.titleColor = TKMapHelper.getObject(param, "titleColor",
        TKThemeManager.shareInstance().getCssRulesetByClassName("TKPdfPageTitle").color ?? "#FFFFFFFF");
    }
    if (this.styleAttribute.statusColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.statusColor?.toString())) {
      this.styleAttribute.statusColor = TKMapHelper.getObject(param, "statusBarColor",
        TKThemeManager.shareInstance().getCssRulesetByClassName("TKPdfPageTitle").backgroundColor ?? "#FF1061FF");
    }
    this.styleAttribute.progressColor =
      TKMapHelper.getObject(param, "progressColor", this.styleAttribute.progressColor);
    if (this.styleAttribute.okBtnReadBgColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.okBtnReadBgColor?.toString())) {
      this.styleAttribute.okBtnReadBgColor =
        TKMapHelper.getObject(param, "okBtnReadBgColor",
          TKThemeManager.shareInstance().getCssRulesetByClassName("TKPdfPageBottom").backgroundColor ?? "#FFFF7D00");
    }
    if (this.styleAttribute.okBtnReadTextColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.okBtnReadTextColor?.toString())) {
      this.styleAttribute.okBtnReadTextColor =
        TKMapHelper.getObject(param, "okBtnReadTextColor",
          TKThemeManager.shareInstance().getCssRulesetByClassName("TKPdfPageBottom").color ?? "#FFFFFFFF");
    }
    this.styleAttribute.okBtnReadTimerBgColor =
      TKMapHelper.getObject(param, "okBtnReadTimerBgColor", this.styleAttribute.okBtnReadTimerBgColor);
    this.styleAttribute.okBtnReadTimerBgColor =
      TKStringHelper.isBlank(this.styleAttribute.okBtnReadTimerBgColor as string) ?
      this.styleAttribute.okBtnReadBgColor :
      this.styleAttribute.okBtnReadTimerBgColor;
    this.styleAttribute.okBtnReadTimerTextColor =
      TKMapHelper.getObject(param, "okBtnReadTimerTextColor", this.styleAttribute.okBtnReadTimerTextColor);
    this.styleAttribute.okBtnReadTimerTextColor =
      TKStringHelper.isBlank(this.styleAttribute.okBtnReadTimerTextColor as string)
        ? this.styleAttribute.okBtnReadTextColor : this.styleAttribute.okBtnReadTimerTextColor;
    let loadFailedImage: string = TKMapHelper.getString(param, "loadFailedImage");
    if (TKStringHelper.isNotBlank(loadFailedImage)) {
      this.styleAttribute.loadFailedImage = $r(`app.media.${loadFailedImage}`);
    }
    let loadingGIF: string = TKMapHelper.getString(param, "loadingGIF");
    if (TKStringHelper.isNotBlank(loadingGIF)) {
      this.styleAttribute.loadingGIF = $r(`app.media.${loadingGIF}`);
    }
    this.isFoldExpanded =
      TKFoldDisplayListener.shareInstance().getFoldStatus() == display.FoldStatus.FOLD_STATUS_EXPANDED ||
        TKFoldDisplayListener.shareInstance().getFoldStatus() == display.FoldStatus.FOLD_STATUS_HALF_FOLDED;
    this.onFoldDisplayListener();
  }

  /**
   * 监听折叠屏
   */
  private onFoldDisplayListener() {
    TKNotificationCenter.defaultCenter.addObserver(this, async (notification: TKNotification) => {
      setTimeout(() => {
        let isFoldExpanded = notification.obj == display.FoldStatus.FOLD_STATUS_EXPANDED ||
          notification.obj == display.FoldStatus.FOLD_STATUS_HALF_FOLDED;
        if (this.isFoldExpanded != isFoldExpanded) {
          this.isFoldExpanded = isFoldExpanded;
          this.isRebuild = !this.isRebuild;
        }
      }, 300);
    }, TKFoldDisplayListener.NOTE_FOLD_CHANGE)
  }

  /**
   * 取消监听折叠屏
   */
  private offFoldDisplayListener() {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }

  aboutToDisappear(): void {
    if (this.readTimer) {
      this.readTimer.stop();
      this.readTimer = undefined;
    }
    this.offFoldDisplayListener();
  }

  checkReadTime() {
    if (this.attribute.readTime! > 0) {
      if (!this.readTimer) {
        this.readTimer = new TKTimer(1000, this, () => {
          this.attribute.readTime!--;
          if (this.attribute.readTime! <= 0) {
            this.readTimer?.stop();
            this.readTimer = undefined;
          }
        });
      }
      this.readTimer.start();
    }
  }

  routeBack(flag: string) {
    if (this.attribute.isCallBackForClose && TKStringHelper.isNotBlank(this.attribute.okBtnReadText)) {
      let result: Record<string, Object> = {
        "funcNo": "50241",
        "flag": flag,
      };
      TKRouterHelper.back({ result: result, navComponentOrPathStack: this });
    } else {
      TKRouterHelper.back({ navComponentOrPathStack: this });
    }
  }

  build() {
    RelativeContainer() {
      if (this.isRebuild) {
        this.TKPdfWebComponent()
      } else {
        this.TKPdfWebComponent()
      }
      this.TKPdfBtnComponent()
      this.TKPdfBottomBarComponent()
    }.width("100%")
    .height("100%")
  }

  @Builder
  TKPdfWebComponent() {
    TKWebPage({
      url: `${this.attribute.url}#toolbar=0&navpanes=0&scrollbar=0&statusbar=0&messages=0&download=0`,
      // data: `<!DOCTYPE html><html>
      //         <head>
      //           <meta name="viewport" content="width=device-width, initial-scale=1.0">
      //           <style>
      //              body, html {
      //                margin: 0;
      //                padding: 0;
      //                height: 100%;
      //                overflow: hidden;
      //                -webkit-tap-highlight-color: transparent;
      //                background-color: white !important;
      //              }
      //              #pdf-container {
      //                 width: 100%;
      //                 height: 100%;
      //                 position: relative;
      //                 background-color: white !important;
      //              }
      //              embed, iframe, object {
      //                 width: 100%;
      //                 height: 100%;
      //                 border: none;
      //                 position: absolute;
      //                 top: 0;
      //                 left: 0;
      //                 background-color: white !important;
      //              }
      //             .toolbar, #toolbar, .download-button {
      //                 display: none !important;
      //                 background-color: white !important;
      //              }
      //             ::selection {
      //               background: transparent;
      //               background-color: white !important;
      //             }
      //             .pdf-viewer {
      //               background: white !important;
      //             }
      //
      //             :root {
      //               background-color: white !important;
      //               color-scheme: light !important;
      //             }
      //
      //             @media (prefers-color-scheme: dark) {
      //               :root {
      //                 background-color: white !important;
      //                 color-scheme: light !important;
      //               }
      //             }
      //           </style>
      //        </head>
      //        <body>
      //          <div id="pdf-container">
      //             <embed src="${this.attribute.url}#toolbar=0&navpanes=0&scrollbar=0&statusbar=0&messages=0&download=0" type="application/pdf"/>
      //          </div>
      //        </body>
      //   </html>`,
      attribute: {
        isH5GoBack: false,
        isLayoutFullScreen: true,
        isUseWebViewAutoResize: false,
        isUseRouterParamUrl: false,
        statusBarEnable: true,
        bottomBarEnable: false,
        titleBarEnable: true,
        title: this.attribute.title,
        isShowLoading: this.attribute.isShowLoading,
        leftBtnMode: this.attribute.leftBtnMode,
        backPressEnable: true,
        loadTimeout: this.attribute.loadTimeOut,
        rightBtnMode: this.attribute.rightBtnMode,
        rightBtnTxt: this.attribute.rightBtnTxt,
        rightBtnAction: this.attribute.rightBtnAction,
        rightBtnActionParam: this.attribute.rightBtnActionParam,
        onBackClick: (event?: ClickEvent, jSProxyController?: TKJSProxyController): boolean => {
          this.routeBack("0");
          return true;
        },
        onRightBtnClick: (event?: ClickEvent, action?: string, data?: Record<string, Object>,
          jSProxyController?: TKJSProxyController): boolean => {
          if (action == TKPdfPage.NOTE_DOWNLOAD_PDF) {
            let result: Record<string, Object> = {
              "funcNo": "50242",
              "action": action ?? "",
              "url": this.attribute.url ?? "",
              "param": this.attribute.rightBtnActionParam ?? {} as Record<string, Object>,
            };
            TKRouterHelper.back({ result: result, isRouterBack: false, navComponentOrPathStack: this });
          } else {
            let result: Record<string, Object> = {
              "funcNo": "50243",
              "action": action ?? "",
              "url": this.attribute.url ?? "",
              "param": this.attribute.rightBtnActionParam ?? {} as Record<string, Object>,
            };
            TKRouterHelper.back({ result: result, isRouterBack: false, navComponentOrPathStack: this });
          }
          return true;
        }
      },
      styleAttribute: {
        progressColor: this.styleAttribute.progressColor,
        titleColor: this.styleAttribute.titleColor,
        titleBgColor: this.styleAttribute.statusColor,
        statusStyle: this.styleAttribute.statusStyle,
        fullLoadingImage: this.styleAttribute.loadingGIF,
        errorImage: this.styleAttribute.loadFailedImage,
      },
      webAttrEventController: TKWebAttrEventController.builder()
        .onPageEnd((event) => {
          //页面加载完毕
          if (!this.isLoadFinish) {
            this.isLoadFinish = true;
            this.checkReadTime();
          }
        })
    })
      .alignRules({
        top: { anchor: '__container__', align: VerticalAlign.Top },
        left: { anchor: '__container__', align: HorizontalAlign.Start },
        bottom: { anchor: "TKPdfPage_Btn", align: VerticalAlign.Top },
        right: { anchor: '__container__', align: HorizontalAlign.End }
      })
      .id("TKPdfPage_Web")
  }

  @Builder
  TKPdfBtnComponent() {
    Row() {
      if (TKStringHelper.isNotBlank(this.attribute.okBtnReadText)) {
        //确定按钮
        Text() {
          Span(this.attribute.okBtnReadText)
            .fontColor(this.attribute.readTime == 0 ? this.styleAttribute.okBtnReadTextColor :
            this.styleAttribute.okBtnReadTimerTextColor)
            .fontSize(19)
            .fontFamily('PingFang SC')
          Span(this.attribute.readTime == 0 ? "" : "（" + this.attribute.readTime + "S）")
            .fontSize(15)
            .fontFamily('PingFang SC')
            .fontWeight(FontWeight.Bold)
            .fontColor(this.attribute.readTime == 0 ? this.styleAttribute.okBtnReadTextColor :
            this.styleAttribute.okBtnReadTimerTextColor)
        }
        .height(44)
        .textAlign(TextAlign.Center)
        .width("90%")
        .margin({ left: "5%" })
        .backgroundColor(this.attribute.readTime == 0 ? this.styleAttribute.okBtnReadBgColor :
        this.styleAttribute.okBtnReadTimerBgColor)
        .borderRadius(10)
        .onClick(() => {
          this.routeBack("1");
        })
        .enabled(this.attribute.readTime == 0)
      }
    }
    .alignRules({
      left: { anchor: '__container__', align: HorizontalAlign.Start },
      bottom: { anchor: "TKPdfPage_Bottom", align: VerticalAlign.Top },
      right: { anchor: '__container__', align: HorizontalAlign.End }
    }).width("100%")
    .height(TKStringHelper.isBlank(this.attribute.okBtnReadText) ? 0 : undefined)
    .id("TKPdfPage_Btn")
  }

  @Builder
  TKPdfBottomBarComponent() {
    TKBottomBar({
      attribute: {
        isLayoutFullScreen: true,
        bottomBarEnable: true,
        onBottomBarHeight: (height) => {
          this.bottomBarHeight = height;
        }
      }
    }).width("100%")
      .height(px2vp(this.bottomBarHeight))
      .alignRules({
        bottom: { anchor: "__container__", align: VerticalAlign.Bottom },
        left: { anchor: "__container__", align: HorizontalAlign.Start },
      })
      .id("TKPdfPage_Bottom")
  }
}



