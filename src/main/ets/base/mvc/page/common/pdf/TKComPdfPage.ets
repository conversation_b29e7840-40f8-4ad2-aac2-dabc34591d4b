import { TKNavRouterInfo } from '../../../../router/TKRouterHelper';
import { TKPdfPage } from './TKPdfPage';

/**
 * 构建动态路由
 * @returns
 */
export function buildTKComPdfPageRouteInfo(): TKNavRouterInfo {
  return {
    name: "TKComPdfPage",
    buildFunction: wrapBuilder(buildTKComPdfPage)
  }
}

@Builder
export function buildTKComPdfPage(param?: ESObject) {
  TKComPdfPage()
}

/**
 * 基础框架TKWebPage入口页面，支持系统返回键逐级H5页面的返回
 */
@Entry({ routeName: 'TKComPdfPage' })
@Component
export struct TKComPdfPage {
  // 实现H5内部页面的逐级返回
  @Provide({ allowOverride: 'onBackPressChanged' }) onBackPressChanged: boolean = false;
  @Provide({ allowOverride: 'onBackPressFilter' }) onBackPressFilter: boolean = true;

  // 实现H5内部页面的逐级返回
  onBackPress(): boolean {
    this.onBackPressChanged = !this.onBackPressChanged;
    return this.onBackPressFilter;
  }

  build() {
    NavDestination() {
      TKPdfPage()
    }.hideTitleBar(true)
    .onBackPressed(() => {
      return this.onBackPress();
    }).width("100%")
    .height("100%")
  }
}