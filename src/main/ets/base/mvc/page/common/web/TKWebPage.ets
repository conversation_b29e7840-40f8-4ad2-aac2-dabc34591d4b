import { T<PERSON><PERSON>eb, TKWeb<PERSON>ttribute, TKWebStyleAttribute } from '../../../../../components/webview/TKWeb';
import { TKTitleBarController } from '../../../../../components/titilebar/TKTitleBarController';
import { TKTitleBar, TKTitleBarStyleAttribute } from '../../../../../components/titilebar/TKTitleBar';
import { TKBottomBar } from '../../../../../components/bottombar/TKBottomBar';
import { TKMapHelper } from '../../../../../util/map/TKMapHelper';
import { TKWindowHelper } from '../../../../../util/ui/TKWindowHelper';
import { TKRouterHelper } from '../../../../router/TKRouterHelper';
import { TKStringHelper } from '../../../../../util/string/TKStringHelper';
import { TKJSProxyController } from '../../../../../components/webview/TKJSProxyController';
import { TKWebAttrEventController } from '../../../../../components/webview/TKWebAttrEventController';
import { TKObjectHelper } from '../../../../../util/data/TKObjectHelper';
import { TKNotificationCenter } from '../../../../notification/TKNotificationCenter';
import { TKPageContainer, TKPageShowHideType } from '../../../../../components/common/TKPageContainer';
import { TKThemeManager } from '../../../../theme/TKThemeManager';
import { TKSystemHelper } from '../../../../../util/system/TKSystemHelper';
import { TKDataHelper } from '../../../../../util/data/TKDataHelper';
import { WebHeader } from '@kit.ArkUI';
import { TKLog } from '../../../../../util/logger/TKLog';
import { TKApplicationStateChangeListener } from '../../../../../util/system/TKContextHelper';
import { TKNotification } from '../../../../notification/TKNotification';

/**
 * 子组件的控制属性类
 */
@Observed
export class TKWebPageAttribute extends TKWebAttribute {
  /**
   * 是否是全屏
   */
  public isLayoutFullScreen?: boolean;
  /**
   * 是否安全区自适应
   */
  public isUseWebViewAutoResize?: boolean = true;
  /**
   * 是否使用路由参数带的URL
   */
  public isUseRouterParamUrl?: boolean = true;
  /**
   * 是否开启H5进行App前后台监听
   */
  public isH5ListenerAppStatus?: boolean = false;
  /**
   * 标题文本
   */
  public title?: string = '';
  /**
   *  是否支持滑动返回
   */
  public isSupportSwipingBack?: boolean = true;
  /**
   * 是否跟着浏览器进行title切换
   */
  public isChangeTitle?: boolean = false;
  /**
   * 状态栏显示/隐藏，入口页设置了全屏才会生效，非全屏无法隐藏系统状态栏
   */
  public statusBarEnable?: boolean;
  /**
   * 是否含有底部Tabbar
   */
  public bottomBarEnable?: boolean;
  /**
   * 标题栏显示/隐藏
   */
  public titleBarEnable?: boolean;
  /**
   * 是否有tabbar
   */
  public tabBarEnable?: boolean = false;
  /**
   * 返回按钮的显示/隐藏
   */
  public backPressEnable?: boolean = true;
  /**
   * 关闭键按钮显示/隐藏
   */
  public closePressEnable?: boolean = false;
  /**
   * 分享键按钮显示/隐藏
   */
  public sharePressEnable?: boolean = false;
  /**
   * 分享参数
   */
  public shareParam?: Record<string, Object> = {};
  /**
   * 按钮模式（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
   */
  public leftBtnMode?: string = "1";
  /**
   * 是否执行H5返回
   */
  public isH5GoBack?: boolean = false;
  /**
   * 右侧按钮模式（0：文本，1：图片)
   */
  public rightBtnMode?: string = "1";
  /**
   * 右侧按钮文本
   */
  public rightBtnTxt?: string;
  /**
   * 右侧按钮动作
   */
  public rightBtnAction?: string;
  /**
   * 右侧按钮动作参数
   */
  public rightBtnActionParam?: Record<string, Object>;
  /**
   * 回调左上角返回按钮事件
   */
  public onBackClick?: (event?: ClickEvent, jSProxyController?: TKJSProxyController) => boolean;
  /**
   * 回调左上角关闭按钮事件
   */
  public onCloseClick?: (event?: ClickEvent, jSProxyController?: TKJSProxyController) => boolean;
  /**
   * 回调右上角点击按钮事件
   */
  public onRightBtnClick?: (event?: ClickEvent, action?: string, data?: Record<string, Object>,
    jSProxyController?: TKJSProxyController) => boolean;
  /**
   * 打开模块
   */
  public onOpenModule?: (data?: Record<string, Object>, jSProxyController?: TKJSProxyController) => boolean;
}

/**
 * 子组件的样式属性类
 */
@Observed
export class TKWebPageStyleAttribute extends TKWebStyleAttribute {
  /**
   * 导航栏背景图
   */
  public titleBgImage?: Resource;
  /**
   * 状态栏风格，状态栏上的小图标颜色风格(0:黑色，1:白色)
   */
  public statusStyle?: string;
  /**
   * 标题的颜色
   */
  public titleColor?: ResourceColor;
  /**
   * 标题栏背景颜色
   */
  public titleBgColor?: ResourceColor;
  /**
   * 底部栏非安全区背景颜色
   */
  public bottomAreaBgColor?: ResourceColor;
  /**
   * 左侧按钮颜色
   */
  public leftBtnColor?: ResourceColor;
  /**
   * 右侧按钮颜色
   */
  public rightBtnColor?: ResourceColor;
}

@Component
export struct TKWebPage {
  /**
   * 分享通知
   */
  public static NOTE_WEBVIEW_SHARE: string = "note_webview_share";
  /**
   * 打开模块通知
   */
  public static NOTE_OPEN_MODULE: string = "note_open_module";
  /**
   * 显示隐藏Tabbar通知
   */
  public static NOTE_SHOW_HIDE_TAB: string = "note_show_hide_tab";
  /**
   * 插件事件通知
   */
  public static NOTE_PLUGIN_EVENT: string = "note_plugin_event";
  /**
   * 流水号
   */
  private flowNo: string = "";
  /**
   * Web加载的 url
   */
  @Prop @Watch('onSrcUpdated') url: string = '';
  /**
   * Web加载的内容
   */
  @Prop @Watch('onDataUpdated') data: string = '';
  /**
   * 实际页面顶部区域的高度（标题栏 + 状态栏）
   */
  @State private titleBarHeight: number = 0;
  /**
   * 实际页面底部区域的高度
   */
  @State private bottomBarHeight: number = 0;
  /**
   * Web的回调监听方法的控制器
   */
  public webAttrEventController?: TKWebAttrEventController;
  /**
   * 代理控制器
   */
  @State jSProxyController?: TKJSProxyController = undefined;
  /**
   * 处理系统返回键
   */
  @Consume @Watch('onBackClick') onBackPressChanged: boolean;
  /**
   * 处理系统返回键
   */
  @Consume onBackPressFilter: boolean;
  /**
   * 子组件的控制属性
   */
  @State attribute: TKWebPageAttribute = new TKWebPageAttribute();
  /**
   * 子组件的样式属性
   */
  @State styleAttribute: TKWebPageStyleAttribute = new TKWebPageStyleAttribute();
  /**
   * 是否重构底部安全区
   */
  @State reBuildBottomBar: boolean = false;
  /**
   * 是否重构标题栏
   */
  @State reBuildTitleBar: boolean = false;
  /**
   * 是否展示底部安全区
   */
  private bottomBarEnable: boolean = false;
  /**
   * 是否初始化页面
   */
  private isWebPageInitAppear: boolean = false;
  /**
   * 是否初始化页面展示
   */
  private isWebPageInitShow: boolean = false;
  /**
   * 微信重定向URL
   */
  private wxZFRedirectUrl: string = "";

  aboutToAppear(): void {
    //初始化变量
    this.isWebPageInitAppear = false;
    this.isWebPageInitShow = false;
    this.attribute = TKObjectHelper.fixDefault(this.attribute, TKWebPageAttribute);
    this.styleAttribute = TKObjectHelper.fixDefault(this.styleAttribute, TKWebPageStyleAttribute);

    const params: Record<string, Object> = TKRouterHelper.getParams(this);
    this.flowNo = TKMapHelper.getString(params, "@flowNo", this.flowNo);
    let url: string = this.attribute.isUseRouterParamUrl ? TKMapHelper.getString(params, "url", this.url) : this.url;

    this.attribute.moduleName = TKMapHelper.getString(params, "moduleName", this.attribute.moduleName);
    this.attribute.isUseWebViewAutoResize =
      TKMapHelper.getBoolean(params, "isUseWebViewAutoResize", this.attribute.isUseWebViewAutoResize);
    this.attribute.isSupportReInitH5 =
      TKMapHelper.getBoolean(params, "isSupportReInitH5", this.attribute.isSupportReInitH5);
    this.attribute.isSupportSwipingBack =
      TKMapHelper.getBoolean(params, "isSupportSwipingBack", this.attribute.isSupportSwipingBack);
    this.attribute.title = TKMapHelper.getString(params, "title", this.attribute.title);
    this.attribute.isChangeTitle = TKMapHelper.getBoolean(params, "isChangeTitle", this.attribute.isChangeTitle);
    this.attribute.isEncodeURL = TKMapHelper.getBoolean(params, "isEncodeURL", this.attribute.isEncodeURL);
    this.attribute.customUserAgent = TKMapHelper.getString(params, "customUserAgent", this.attribute.customUserAgent);

    if (this.attribute.isLayoutFullScreen === undefined) {
      this.attribute.isLayoutFullScreen = TKMapHelper.getBoolean(params, "isLayoutFullScreen", true);
    }
    if (TKStringHelper.isBlank(this.styleAttribute.statusStyle)) {
      this.styleAttribute.statusStyle = TKMapHelper.getString(params, "statusStyle");
    }
    if (this.styleAttribute.titleColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.titleColor?.toString())) {
      this.styleAttribute.titleColor = TKMapHelper.getObject(params, "titleColor",
        TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBar").color ?? "#FF000000");
    }
    if (this.styleAttribute.titleBgColor === undefined ||
    TKStringHelper.isBlank(this.styleAttribute.titleBgColor?.toString())) {
      this.styleAttribute.titleBgColor = TKMapHelper.getObject(params, "titleBgColor",
        TKMapHelper.getObject(params, "statusBarColor",
          TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBar").backgroundColor ?? "#00FFFFFF"));
    }
    let titleBgImage: string = TKMapHelper.getString(params, "titleBgImage");
    if (TKStringHelper.isNotBlank(titleBgImage)) {
      this.styleAttribute.titleBgImage = $r(`app.media.${titleBgImage}`);
    }
    this.styleAttribute.bottomAreaBgColor =
      TKMapHelper.getObject(params, "bottomAreaBgColor", this.styleAttribute.bottomAreaBgColor);
    this.styleAttribute.progressColor =
      TKMapHelper.getObject(params, "progressColor", this.styleAttribute.progressColor ?? Color.Blue);

    this.attribute.backPressEnable =
      TKMapHelper.getBoolean(params, "backPressEnable", this.attribute.backPressEnable);
    this.attribute.closePressEnable =
      TKMapHelper.getBoolean(params, "closePressEnable", this.attribute.closePressEnable);
    this.attribute.sharePressEnable =
      TKMapHelper.getBoolean(params, "sharePressEnable", this.attribute.sharePressEnable);
    this.attribute.shareParam = TKMapHelper.getObject(params, "shareParam", {} as Record<string, Object>);
    this.attribute.leftBtnMode =
      TKMapHelper.getString(params, "leftBtnMode", this.attribute.leftBtnMode);
    this.styleAttribute.leftBtnColor =
      TKMapHelper.getString(params, "leftBtnColor",
        (this.styleAttribute.leftBtnColor ?? this.styleAttribute.titleColor) as string);
    this.styleAttribute.rightBtnColor =
      TKMapHelper.getString(params, "rightBtnColor",
        (this.styleAttribute.rightBtnColor ?? this.styleAttribute.titleColor) as string);
    this.attribute.isH5GoBack = TKMapHelper.getBoolean(params, "isH5GoBack", this.attribute.isH5GoBack);
    this.attribute.rightBtnMode = TKMapHelper.getString(params, "rightBtnMode", this.attribute.rightBtnMode);
    this.attribute.rightBtnTxt = TKMapHelper.getString(params, "rightBtnTxt", this.attribute.rightBtnTxt);
    this.attribute.rightBtnAction = TKMapHelper.getString(params, "rightBtnAction", this.attribute.rightBtnAction);
    this.attribute.rightBtnActionParam =
      TKMapHelper.getObject(params, "rightBtnActionParam", this.attribute.rightBtnActionParam);
    if (TKStringHelper.isBlank(this.attribute.rightBtnTxt)) {
      if (this.attribute.sharePressEnable) {
        this.attribute.rightBtnMode = "1";
        this.attribute.rightBtnTxt = "tk_framework_share_btn";
        this.attribute.rightBtnAction = TKWebPage.NOTE_WEBVIEW_SHARE;
        this.attribute.rightBtnActionParam = this.attribute.shareParam;
      }
    }

    this.styleAttribute.backgroundColor =
      TKMapHelper.getObject(params, "backgroundColor", this.styleAttribute.backgroundColor);
    this.styleAttribute.opacity =
      TKMapHelper.getNumber(params, "opacity", this.styleAttribute.opacity);

    this.attribute.isShowLoading = TKMapHelper.getBoolean(params, "isShowLoading", this.attribute.isShowLoading);
    let errorImage: string = TKMapHelper.getString(params, "errorImage");
    if (TKStringHelper.isNotBlank(errorImage)) {
      this.styleAttribute.errorImage = $r(`app.media.${errorImage}`);
    }
    let fullLoadingImage: string = TKMapHelper.getString(params, "fullLoadingImage");
    if (TKStringHelper.isNotBlank(fullLoadingImage)) {
      this.styleAttribute.fullLoadingImage = $r(`app.media.${fullLoadingImage}`);
    }
    if (this.jSProxyController) {
      this.jSProxyController.setModuleName(this.attribute.moduleName);
    } else {
      this.jSProxyController = new TKJSProxyController(this.attribute.moduleName);
    }
    this.attribute.tabBarEnable = TKMapHelper.getBoolean(params, "tabBarEnable", this.attribute.tabBarEnable);
    let titleBarEnable: Object | undefined =
      TKMapHelper.getObject(params, "titleBarEnable", this.attribute.titleBarEnable);
    if (titleBarEnable === undefined) {
      this.attribute.titleBarEnable = (TKStringHelper.isNotEmpty(this.attribute.title) || this.attribute.isChangeTitle);
    }
    if (!this.attribute.isLayoutFullScreen) {
      this.attribute.statusBarEnable = this.attribute.statusBarEnable ?? false;
      this.attribute.bottomBarEnable = this.attribute.bottomBarEnable ?? false;
      this.attribute.statusBarEnable =
        TKMapHelper.getBoolean(params, "statusBarEnable", this.attribute.statusBarEnable);
      this.attribute.bottomBarEnable =
        TKMapHelper.getBoolean(params, "bottomBarEnable", this.attribute.bottomBarEnable);
    } else {
      this.attribute.statusBarEnable =
        this.attribute.statusBarEnable ?? (this.attribute.isUseWebViewAutoResize ? false : true);
      this.attribute.bottomBarEnable =
        this.attribute.bottomBarEnable ?? (this.attribute.isUseWebViewAutoResize ? false : true);
      this.attribute.statusBarEnable =
        TKMapHelper.getBoolean(params, "statusBarEnable", this.attribute.statusBarEnable);
      this.attribute.bottomBarEnable =
        TKMapHelper.getBoolean(params, "bottomBarEnable", this.attribute.bottomBarEnable);
      if (this.attribute.titleBarEnable) {
        this.attribute.statusBarEnable = true;
        this.attribute.bottomBarEnable = true;
      }
    }
    //是否展示错误页面返回键
    if (this.attribute.titleBarEnable || TKRouterHelper.getRouteStackLength(this) <= 1) {
      this.attribute.errorBackPressEnable = false;
    } else {
      this.attribute.errorBackPressEnable = this.attribute.backPressEnable;
    }
    //处理H5沉浸式适配逻辑
    this.url = this.addFullScreenH5URLSafeArea(url);
    //记录初始化的底部展示状态
    this.bottomBarEnable = this.attribute.bottomBarEnable;
    //完成初始化逻辑
    this.isWebPageInitAppear = true;

    //添加前后台相关通知监听
    TKNotificationCenter.defaultCenter.addObserver(this, this.applicationDidBecomeActive,
      TKApplicationStateChangeListener.NOTE_TKAPPLICATION_FOREGROUND);
    TKNotificationCenter.defaultCenter.addObserver(this, this.applicationDidEnterBackground,
      TKApplicationStateChangeListener.NOTE_TKAPPLICATION_BACKGROUND);
    TKNotificationCenter.defaultCenter.addObserver(this, this.handleShowHideTabbar, TKWebPage.NOTE_SHOW_HIDE_TAB);
  }

  onSrcUpdated(): void {
    if (this.isWebPageInitAppear) {
      this.url = this.addFullScreenH5URLSafeArea(this.url);
    }
  }

  onDataUpdated(): void {

  }

  onPageShow(type: TKPageShowHideType = TKPageShowHideType.Page): void {
    if (this.isWebPageInitShow) {
      // 防止新打开的页面修改了页面样式，调用router.back时没有重置回来
      TKRouterHelper.refreshCurRoutePageStyle(this);
    } else {
      this.isWebPageInitShow = true;
    }
  }

  aboutToDisappear(): void {
    TKNotificationCenter.defaultCenter.removeObserver(this);
  }

  /**
   * 返回按钮
   * @param event
   * @returns
   */
  onBackClick(event?: ClickEvent): void {
    // 处理H5内部页面返回问题
    this.onBackPressFilter = true;
    if (this.attribute.onBackClick && this.attribute.onBackClick(event, this.jSProxyController)) {
      return;
    }
    if (this.jSProxyController?.isPageInit && this.jSProxyController?.webviewController &&
    this.attribute.isSupportSwipingBack) {
      if (this.attribute.isH5GoBack) {
        let jsParam: Record<string, Object> = {
          "funcNo": "50107",
          "isNativeSwipingBack": "1"
        };
        this.jSProxyController?.sendPlugMsgToH5(jsParam);
      } else {
        if (this.jSProxyController?.webviewController.accessBackward()) {
          this.jSProxyController?.webviewController.backward();
        } else {
          TKRouterHelper.back({ navComponentOrPathStack: this });
        }
      }
    } else {
      TKRouterHelper.back({ navComponentOrPathStack: this });
    }
  }

  /**
   * 关闭按钮
   * @param event
   * @returns
   */
  onCloseClick(event?: ClickEvent): void {
    if (this.attribute.onCloseClick && this.attribute.onCloseClick(event, this.jSProxyController)) {
      return;
    }
    TKRouterHelper.back({ navComponentOrPathStack: this });
  }

  /**
   * 右边按钮
   * @param event
   * @returns
   */
  async onRightBtnClick(event?: ClickEvent, action?: string, data?: Record<string, Object>): Promise<void> {
    if (this.attribute.onRightBtnClick && this.attribute.onRightBtnClick(event, action, data, this.jSProxyController)) {
      return;
    }
    if (action == TKWebPage.NOTE_WEBVIEW_SHARE) {
      let param: Record<string, Object> = {};
      param["funcNo"] = "50117";
      let paramExt: Record<string, Object> = {};
      paramExt["action"] = "share";
      if (data) {
        TKObjectHelper.assign(paramExt, data);
      }
      let title: string = TKMapHelper.getString(paramExt, "shareTitle");
      if (TKStringHelper.isBlank(title)) {
        title = this.attribute.title ?? "";
      }
      if (TKStringHelper.isNotBlank(title)) {
        paramExt["shareTitle"] = title;
      }
      let url: string = TKMapHelper.getString(paramExt, "shareUrl");
      if (TKStringHelper.isBlank(url)) {
        url = await this.jSProxyController?.webviewController?.runJavaScript("window.top.location.href") as string;
        if (TKStringHelper.isBlank(url)) {
          url = this.jSProxyController?.url as string ?? "";
        }
      }
      paramExt["shareUrl"] = url;
      param["paramExt"] = paramExt;
      this.jSProxyController?.sendPlugMsgToH5(param);
      TKNotificationCenter.defaultCenter.postNotificationName(action, this.jSProxyController, param);
    }
  }

  /**
   * 状态栏修改
   */
  onChangeStatusTitleStyle(eventParam?: Record<string, Object>) {
    if (eventParam) {
      this.styleAttribute.statusStyle =
        TKMapHelper.getString(eventParam, "statusStyle", this.styleAttribute.statusStyle as string);
      this.styleAttribute.titleBgColor =
        TKMapHelper.getString(eventParam, "statusColor", this.styleAttribute.titleBgColor as string);
      this.styleAttribute.bottomAreaBgColor =
        TKMapHelper.getString(eventParam, "bottomAreaBgColor", this.styleAttribute.bottomAreaBgColor as string);
      // 处理状态栏的风格
      if (TKStringHelper.isNotBlank(this.styleAttribute.statusStyle)) {
        TKWindowHelper.setWindowSystemBarProperties({
          statusBarContentColor: '1' === this.styleAttribute.statusStyle ? '#ffffffff' : '#ff000000'
        });
      }
      if (TKStringHelper.isNotBlank(this.styleAttribute.titleBgColor)) {
        if (this.attribute.isLayoutFullScreen) {
          TKWindowHelper.getWindowSystemBarProperties().then((windowSystemBarProperties) => {
            if (windowSystemBarProperties.statusBarColor &&
              windowSystemBarProperties.statusBarColor.toUpperCase() != '#00FFFFFF') {
              TKWindowHelper.setWindowSystemBarProperties({
                statusBarColor: this.styleAttribute.titleBgColor as string
              });
            }
          });
        } else {
          TKWindowHelper.setWindowSystemBarProperties({
            statusBarColor: this.styleAttribute.titleBgColor as string
          });
        }
      }
    }
  }

  /**
   * 打开模块
   * @param data
   * @returns
   */
  onOpenModule(data?: Record<string, Object>): void {
    if (this.attribute.onOpenModule && this.attribute.onOpenModule(data, this.jSProxyController)) {
      return;
    }
    TKNotificationCenter.defaultCenter.postNotificationName(TKWebPage.NOTE_OPEN_MODULE, this.jSProxyController, data);
  }

  @BuilderParam customTitleView?: (titleBarController: TKTitleBarController) => void

  /**
   * 添加偏移参数
   * @param url
   * @returns
   */
  private addFullScreenH5URLSafeArea(url: string): string {
    if (this.attribute.isLayoutFullScreen && !this.attribute.titleBarEnable &&
      (!this.attribute.statusBarEnable || !this.attribute.bottomBarEnable)) {
      if (TKStringHelper.isNotBlank(url)) {
        if (url.indexOf("topmargin=") < 0 && url.indexOf("bottommargin=") < 0) {
          let statusBarHeight: number =
            this.attribute.statusBarEnable ? 0 : px2vp(TKWindowHelper.getStatusBarHeightSync());
          let bottomBarHeight: number =
            (this.attribute.bottomBarEnable || this.attribute.tabBarEnable) ? 0 :
            px2vp(TKWindowHelper.getNavigationBottomBarHeightSync());
          let urlStr: string = url;
          let queryString: string = "";
          let safeAreaInsertBeforeFlagChars: string = "#!/";
          let index: number = url.indexOf(safeAreaInsertBeforeFlagChars);
          if (index > 0) {
            urlStr = url.substring(0, index);
            queryString = url.substring(index, url.length);
          } else {
            index = url.indexOf("?")
            if (index > 0) {
              urlStr = url.substring(0, index);
              queryString = url.substring(index, url.length);
            }
          }
          if (url.indexOf(safeAreaInsertBeforeFlagChars) > 0) {
            if (urlStr.indexOf("?") > 0) {
              urlStr += `&topmargin=${statusBarHeight}&bottommargin=${bottomBarHeight}`;
            } else {
              urlStr += `?topmargin=${statusBarHeight}&bottommargin=${bottomBarHeight}`;
            }
          } else {
            if (queryString.startsWith("?")) {
              queryString += `&topmargin=${statusBarHeight}&bottommargin=${bottomBarHeight}`;
            } else {
              queryString += `?topmargin=${statusBarHeight}&bottommargin=${bottomBarHeight}`;
            }
          }
          return urlStr + queryString;
        }
      }
    }
    return url;
  }

  build() {
    RelativeContainer() {
      if (this.reBuildTitleBar) {
        this.TKTitleBarComponent()
      } else {
        this.TKTitleBarComponent()
      }
      this.TKWebComponent()
      if (this.reBuildBottomBar) {
        this.TKBottomBarComponent()
      } else {
        this.TKBottomBarComponent()
      }
    }.width('100%')
    .height('100%')
  }

  @Builder
  TKTitleBarComponent() {
    TKTitleBar({
      customTitleView: this.customTitleView,
      attribute: {
        isLayoutFullScreen: this.attribute.isLayoutFullScreen ?? true,
        statusBarEnable: this.attribute.statusBarEnable ?? false,
        title: this.attribute.title ?? '',
        titleBarEnable: this.attribute.titleBarEnable ?? false,
        leftBtnMode: this.attribute.leftBtnMode ?? "1",
        backPressEnable: this.attribute.backPressEnable ?? true,
        closePressEnable: this.attribute.closePressEnable ?? false,
        rightBtnMode: this.attribute.rightBtnMode ?? "1",
        rightBtnTxt: this.attribute.rightBtnTxt,
        rightBtnAction: this.attribute.rightBtnAction,
        rightBtnActionParam: this.attribute.rightBtnActionParam,
        onBackClick: (event?: ClickEvent): boolean => {
          this.onBackClick(event);
          return true;
        },
        onCloseClick: (event?: ClickEvent): boolean => {
          this.onCloseClick(event);
          return true;
        },
        onRightBtnClick: (event?: ClickEvent, action?: string, data?: Record<string, Object>): boolean => {
          this.onRightBtnClick(event, action, data);
          return true;
        },
        onTitleBarHeight: (height) => {
          this.titleBarHeight = height;
        }
      },
      styleAttribute: this.styleAttribute as TKTitleBarStyleAttribute
    })
      .width("100%")
      .height(px2vp(this.titleBarHeight))
      .margin({ top: (this.attribute.isLayoutFullScreen && this.titleBarHeight == 0) ? -1 : 0 })
      .alignRules({
        top: { anchor: "__container__", align: VerticalAlign.Top },
        left: { anchor: "__container__", align: HorizontalAlign.Start }
      })
      .id("TKTitleBar")
  }

  @Builder
  TKWebComponent() {
    Row() {
      if (TKStringHelper.isNotBlank(this.url) || TKStringHelper.isNotBlank(this.data)) {
        TKPageContainer({ page: new WeakRef(this) }) {
          TKWeb({
            src: this.url,
            data: this.data,
            attribute: this.attribute,
            styleAttribute: this.styleAttribute,
            jSProxyController: this.jSProxyController,
            webAttrEventController: TKWebAttrEventController.builder()
              .onAppear(() => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onAppear) {
                  this.webAttrEventController.webAttrEvent.onAppear()
                }
              })
              .onDisAppear(() => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onDisAppear) {
                  this.webAttrEventController.webAttrEvent.onDisAppear();
                }
              })
              .onControllerAttached(() => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onControllerAttached) {
                  this.webAttrEventController.webAttrEvent.onControllerAttached();
                }
              })
              .onAreaChange((oldValue: Area, newValue: Area) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onAreaChange) {
                  this.webAttrEventController.webAttrEvent.onAreaChange(oldValue, newValue);
                }
              })
              .onVisibleAreaChange((isVisible: boolean, currentRatio: number) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onVisibleAreaChange) {
                  this.webAttrEventController.webAttrEvent.onVisibleAreaChange(isVisible, currentRatio);
                }
              })
              .onOverrideUrlLoading((webResourceRequest: WebResourceRequest) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onOverrideUrlLoading) {
                  return this.webAttrEventController.webAttrEvent.onOverrideUrlLoading(webResourceRequest);
                } else {
                  return false;
                }
              })
              .onLoadIntercept((event?: OnLoadInterceptEvent) => {
                //是否打开三方支付
                let isOpenThirdPay: string = TKSystemHelper.getConfig("thirdPay.isOpen");
                if (isOpenThirdPay == "1") {
                  //处理微信支付
                  let isContinue: boolean = this.processH5WXZFRequestForIsContinueResult(event);
                  if (!isContinue) {
                    return true;
                  }
                  //处理支付宝支付
                  isContinue = this.processH5AliZFRequestForIsContinueResult(event);
                  if (!isContinue) {
                    return true;
                  }
                }
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onLoadIntercept) {
                  return this.webAttrEventController.webAttrEvent.onLoadIntercept(event);
                }
                return false;
              })
              .onInterceptRequest((event?: OnInterceptRequestEvent) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onInterceptRequest) {
                  return this.webAttrEventController.webAttrEvent.onInterceptRequest(event);
                }
                return null;
              })
              .onTitleReceive((event) => {
                if (event) {
                  // 获取H5的tite显示到原生标题栏中,进行title联动
                  if (this.attribute.isChangeTitle) {
                    this.attribute.title = event.title;
                    this.reBuildTitleBar = !this.reBuildTitleBar;
                  }
                }
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onTitleReceive) {
                  this.webAttrEventController.webAttrEvent.onTitleReceive(event);
                }
              })
              .onPageBegin((event) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPageBegin) {
                  this.webAttrEventController.webAttrEvent.onPageBegin(event);
                }
              })
              .onPageEnd((event) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPageEnd) {
                  this.webAttrEventController.webAttrEvent.onPageEnd(event);
                }
              })
              .onPageVisible((event) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPageVisible) {
                  this.webAttrEventController.webAttrEvent.onPageVisible(event);
                }
              })
              .onErrorReceive(() => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onErrorReceive) {
                  this.webAttrEventController.webAttrEvent.onErrorReceive()
                }
              })
              .geolocationAccess((this.webAttrEventController &&
              this.webAttrEventController.webAttrEvent.geolocationAccess) ?
              this.webAttrEventController.webAttrEvent.geolocationAccess : false)
              .onGeolocationShow((event) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onGeolocationShow) {
                  this.webAttrEventController.webAttrEvent.onGeolocationShow(event);
                }
              })
              .onPermissionRequest((event) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPermissionRequest) {
                  this.webAttrEventController.webAttrEvent.onPermissionRequest(event);
                }
              })
              .onShowFileSelector((event) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onShowFileSelector) {
                  return this.webAttrEventController.webAttrEvent.onShowFileSelector(event);
                }
                return true // 当返回值为true时，用户可以调用系统提供的弹窗能力。当回调返回false时，函数中绘制的自定义弹窗无效
              })
              .onConsole((event) => {
                if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onConsole) {
                  return this.webAttrEventController.webAttrEvent.onConsole(event);
                }
                return false;
              })
              .onPluginEvent((eventId: string, eventParam?: Record<string, Object>,
                jsProxyController?: TKJSProxyController) => {
                jsProxyController = jsProxyController ?? this.jSProxyController;
                if (eventId == "50100") {
                  jsProxyController?.onPageLoadFinish();
                  if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPageLoadFinish) {
                    this.webAttrEventController.webAttrEvent.onPageLoadFinish();
                  }
                } else if (eventId == "50119") {
                  this.onChangeStatusTitleStyle(eventParam);
                } else if (eventId == "50114") {
                  this.onCloseClick();
                } else if (eventId == "50101") {
                  this.onOpenModule(eventParam);
                } else if (eventId == "50128") {
                  this.attribute.isH5ListenerAppStatus =
                    TKMapHelper.getBoolean(eventParam!, "isH5ListenerAppStatus", false);
                } else if (eventId == "50108") {
                  TKNotificationCenter.defaultCenter.postNotificationName(TKWebPage.NOTE_SHOW_HIDE_TAB,
                    jsProxyController, eventParam);
                } else {
                  if (this.webAttrEventController && this.webAttrEventController.webAttrEvent.onPluginEvent) {
                    this.webAttrEventController.webAttrEvent.onPluginEvent(eventId, eventParam, jsProxyController);
                  } else {
                    TKNotificationCenter.defaultCenter.postNotificationName(TKWebPage.NOTE_PLUGIN_EVENT,
                      jsProxyController, eventParam);
                  }
                }
              })
          })
        }
      }
    }
    .width('100%')
    .alignRules({
      top: { anchor: "TKTitleBar", align: VerticalAlign.Bottom },
      bottom: { anchor: "TKBottomBar", align: VerticalAlign.Top },
      left: { anchor: "__container__", align: HorizontalAlign.Start },
    })
  }

  @Builder
  TKBottomBarComponent() {
    TKBottomBar({
      attribute: {
        isLayoutFullScreen: this.attribute.isLayoutFullScreen ?? true,
        bottomBarEnable: this.attribute.bottomBarEnable ?? false,
        onBottomBarHeight: (height) => {
          this.bottomBarHeight = height;
        }
      },
      styleAttribute: {
        bottomAreaBgColor: this.styleAttribute.bottomAreaBgColor
      }
    }).width("100%")
      .height(px2vp(this.bottomBarHeight))
      .alignRules({
        bottom: { anchor: "__container__", align: VerticalAlign.Bottom },
        left: { anchor: "__container__", align: HorizontalAlign.Start },
      })
      .id("TKBottomBar")
  }

  /**
   * 处理微信支付
   * @param event
   * @returns
   */
  private processH5WXZFRequestForIsContinueResult(event?: OnLoadInterceptEvent): boolean {
    //是否开启wxzf，整个流程如下：
    //1:H5页面跳转WXZF中间页面，并把自己当前的页面地址通过redirect_url参数传给WXZF中间页面,以便ZF完成后进行页面回跳
    //2:WebView监听到第一步的请求就把redirect_url记录在临时变量里面self.wxZFRedirectUrl，以便进行最终加载使用，同时把redirect_url入参改成App定义的Scheme协议参数，Scheme是用一个WXZF合法识别的域名作为内容（并设置到Refer请求头中，以便WX识别），以便WXZF完成后可以正常回调跳转回来（WXZF完成后WXApp优先根据redirect_url进行回跳，没有这个入参的话，再根据Refer头中的域名进行回跳）
    //3:WebView把第2步新生成的WXZF链接请求进行加载，WXZF中间页会打开APPZF界面
    //4:WXZF完成后根据redirect_url(没有redirect_url则取Refer头)跳转到App配置的Scheme协议上面，这样就从WXZF页面回到了App的主界面，同时触发了WebView监听（确定，取消，超时5秒都会自动根据redirect_url进行回跳，没有redirect_url则直接走history.back()），WebView监听到以后，就把第二步记录的self.wxZFRedirectUrl进行加载(没有self.wxZFRedirectUrl则直接刷新当前页)，实现H5页面刷新
    //4:H5页面刷新后，根据参数就知道WXZF的结果然后进行相关业务流程处理
    if (event && event.data.getRequestHeader()) {
      let wxZFScheme: string = TKSystemHelper.getConfig("thirdPay.wxPayScheme");
      if (TKStringHelper.isNotBlank(wxZFScheme)) {
        let referer: string = `${wxZFScheme}://`;
        let currentUrl: string = event?.data.getRequestUrl();
        //拦截微信ZF中间页，修改将schemere作为direct_url，微信ZF完将跳回APP
        let wxDomain: string = "https://wx.tenpay.com";
        let wxPreReq: string = "weixin://wap/pay";
        if (TKStringHelper.startsWith(currentUrl, wxDomain)) {
          let param: Map<string, Object> = TKDataHelper.urlToFormatMap(currentUrl);
          let redirectUrl: string = TKMapHelper.getString(param, "redirect_url");
          if (redirectUrl != referer) {
            //临时存储redirect_url
            this.wxZFRedirectUrl = decodeURIComponent(redirectUrl);
            let key: string = "redirect_url=";
            let index: number = currentUrl.indexOf(key);
            let reqUrl: string = "";
            if (index > 0) {
              reqUrl = currentUrl.substring(0, index + key.length);
              reqUrl += referer;
            } else {
              if (currentUrl.indexOf("?") > 0) {
                reqUrl = `${currentUrl}&redirect_url=${referer}`;
              } else {
                reqUrl = `${currentUrl}?redirect_url=${referer}`;
              }
            }
            this.jSProxyController?.loadUrl(reqUrl, [{ headerKey: "Referer", headerValue: referer } as WebHeader]);
            return false;
          }
        } else if (currentUrl.startsWith(wxPreReq)) {
          //拦截H5WXZF请求
          TKSystemHelper.openAppURL(currentUrl, (success) => {
            TKLog.info(`打开微信支付结果为:url-->${currentUrl},success--->${success}`);
          });
          return false;
        } else if (currentUrl == referer) {
          //拦截被替换成scheme的回调页面
          if (TKStringHelper.isNotBlank(this.wxZFRedirectUrl)) {
            //此处先解码，由于redirectUrl被完全编码了，包括协议和参数
            this.wxZFRedirectUrl = decodeURIComponent(this.wxZFRedirectUrl);
            //重新请求只需要参数编码
            this.jSProxyController?.loadUrl(encodeURI(this.wxZFRedirectUrl),
              [{ headerKey: "Referer", headerValue: referer } as WebHeader]);
            this.wxZFRedirectUrl = "";
          }
          return false;
        }
      }
    }
    return true;
  }

  /**
   * 处理支付宝支付
   * @param event
   * @returns
   */
  private processH5AliZFRequestForIsContinueResult(event?: OnLoadInterceptEvent): boolean {
    //是否开启ZFB,流程如下:
    //1:H5跳转aliZF,WebView监听到后修改fromAppUrlScheme参数以便ZF完成后，回到我们自己的APP
    if (event && event.data.getRequestHeader()) {
      let aliZFScheme: string = TKSystemHelper.getConfig("thirdPay.aliPayScheme")
      if (TKStringHelper.isNotBlank(aliZFScheme)) {
        let currentUrl: string = event.data.getRequestUrl();
        let aliReqPre: string = "alipay://alipayclient/?";
        if (currentUrl.startsWith(aliReqPre)) {
          currentUrl = decodeURIComponent(currentUrl);
          let paramStr: string = TKStringHelper.replace(currentUrl, aliReqPre, "");
          let paramDic: Record<string, Object> = JSON.parse(paramStr);
          if (paramDic) {
            paramDic["fromAppUrlScheme"] = aliZFScheme;
            paramStr = encodeURIComponent(JSON.stringify(paramDic));
            let zfUrl: string = `${aliReqPre}${paramStr}`;
            TKSystemHelper.openAppURL(zfUrl, (success) => {
              TKLog.info(`打开支付宝支付结果为:url-->${zfUrl},success--->${success}`);
            });
          }
          return false;
        }
      }
    }
    return true;
  }

  /**
   *  处理应用进入前台
   *
   * @param notification
   */
  private applicationDidBecomeActive(notification: TKNotification) {
    if (this.attribute.isH5ListenerAppStatus) {
      this.jSProxyController?.sendPlugMsgToH5({ "funcNo": "50129", "appStatus": "0" });
    }
  }

  /**
   *  处理应用进入后台
   *
   * @param notification
   */
  private applicationDidEnterBackground(notification: TKNotification) {
    if (this.attribute.isH5ListenerAppStatus) {
      this.jSProxyController?.sendPlugMsgToH5({ "funcNo": "50129", "appStatus": "2" });
    }
  }

  /**
   * 处理tabbar隐藏展示
   * @param notification
   */
  private handleShowHideTabbar(notification: TKNotification) {
    if (this.attribute.tabBarEnable) {
      let eventParam: Record<string, Object> = notification.userInfo as Record<string, Object>;
      let isShowTabBar: boolean = TKMapHelper.getBoolean(eventParam, "flag");
      if (isShowTabBar) {
        this.attribute.bottomBarEnable = this.bottomBarEnable;
      } else {
        this.attribute.bottomBarEnable = true;
      }
      this.reBuildBottomBar = this.attribute.bottomBarEnable;
    }
  }
}