import { TKNavRouterInfo } from '../../../../router/TKRouterHelper';
import { TKWebPage } from './TKWebPage';

/**
 * 构建动态路由
 * @returns
 */
export function buildTKComWebPageRouteInfo(): TKNavRouterInfo {
  return {
    name: "TKComWebPage",
    buildFunction: wrapBuilder(buildTKComWebPage)
  }
}

@Builder
export function buildTKComWebPage(param?: ESObject) {
  TKComWebPage()
}

/**
 * 基础框架TKWebPage入口页面，支持系统返回键逐级H5页面的返回
 */
@Entry({ routeName: 'TKComWebPage' })
@Component
export struct TKComWebPage {
  // 实现H5内部页面的逐级返回
  @Provide({ allowOverride: 'onBackPressChanged' }) onBackPressChanged: boolean = false;
  @Provide({ allowOverride: 'onBackPressFilter' }) onBackPressFilter: boolean = true;

  // 实现H5内部页面的逐级返回
  onBackPress(): boolean {
    this.onBackPressChanged = !this.onBackPressChanged;
    return this.onBackPressFilter;
  }

  build() {
    NavDestination() {
      TKWebPage()
    }.hideTitleBar(true)
    .onBackPressed(() => {
      return this.onBackPress();
    }).width("100%")
    .height("100%")
  }
}