import { BuilderNode, FrameNode } from '@kit.ArkUI';
import { TKJSProxyController } from '../../../../../components/webview/TKJSProxyController';
import { TKWebAttrEventController } from '../../../../../components/webview/TKWebAttrEventController';
import { TKWeb } from '../../../../../components/webview/TKWeb';
import { TKLog } from '../../../../../util/logger/TKLog';
import { TKMapHelper } from '../../../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../../../util/system/TKSystemHelper';

@Builder
export function TKWebComponent(option: Record<string, Object>) {
  TKWeb({
    src: "",
    data: TKMapHelper.getString(option, "data"),
    jSProxyController: TKMapHelper.getObject(option, "jsProxyController"),
    webAttrEventController: TKWebAttrEventController.builder()
      .onControllerAttached(() => {
        if (TKStringHelper.isBlank(TKMapHelper.getString(option, "data"))) {
          let url: string = TKMapHelper.getString(option, "url");
          let referer: string = TKMapHelper.getString(option, "referer");
          let jSProxyController: TKJSProxyController =
            TKMapHelper.getObject(option, "jsProxyController") as TKJSProxyController;
          jSProxyController.loadUrl(url, [{ headerKey: "Referer", headerValue: referer }])
        }
      })
      .onInterceptRequest((event?: OnInterceptRequestEvent) => {
        if (!TKH5ThirdZFManager.shareInstance().webviewOnInterceptRequest(event)) {
          return;
        }
        return null;
      })
      .onErrorReceive(() => {
        TKH5ThirdZFManager.shareInstance().webViewOnErrorReceive();
      })
  }).width(0.01).height(0.01).position({ left: 0, top: 0 })
}

/**
 * H5三方支付
 */
export class TKH5ThirdZFManager {
  //单例对象
  private static instance?: TKH5ThirdZFManager;
  //webview节点
  private webviewFrameNode?: FrameNode = undefined;
  //回调函数
  private handleResultCallBack?: (result: string) => void;
  //父组件
  private parentComponent?: CustomComponent;

  private constructor() {
  }

  public static shareInstance(): TKH5ThirdZFManager {
    if (!TKH5ThirdZFManager.instance) {
      TKH5ThirdZFManager.instance = new TKH5ThirdZFManager();
    }
    return TKH5ThirdZFManager.instance;
  }

  /**
   * 加载三方页面
   */
  public loadH5ThirdZFTransitionPage(parentComponent: CustomComponent, type: string, content: string,
    handleResultCallBack?: (result: string) => void) {
    this.parentComponent = parentComponent;
    this.handleResultCallBack = handleResultCallBack;
    if (type == "0") {
      //微信支付
      let wxPayUrl: string = "weixin://wap/pay";
      if (TKSystemHelper.isInstallAppWithURL(wxPayUrl)) {
        this.loadH5WXZFTransitionPage(content);
      } else {
        this.finishLoadH5ThirdZFWithResult("0");
        return;
      }
    } else if (type == "1") {
      //支付宝支付
      let aliPayUrl: string = "alipay://alipayclient/?";
      if (TKSystemHelper.isInstallAppWithURL(aliPayUrl)) {
        this.loadH5AliZFTransitionPage(content);
      } else {
        this.finishLoadH5ThirdZFWithResult("0");
        return;
      }
    } else {
      this.finishLoadH5ThirdZFWithResult("2");
      return;
    }
    setTimeout(() => {
      this.finishLoadH5ThirdZFWithResult("2");
    }, 20000);
  }

  /**
   * 完成加载三方页面，并回调结果(0:未安装，1:已安装，2：其他)
   */
  private finishLoadH5ThirdZFWithResult(result: string) {
    if (this.handleResultCallBack) {
      this.handleResultCallBack(result);
    }
    this.handleResultCallBack = undefined;
    if (this.webviewFrameNode && this.webviewFrameNode.getParent()) {
      this.webviewFrameNode.getParent()?.removeChild(this.webviewFrameNode);
    }
    this.webviewFrameNode = undefined;
  }

  /**
   * 加载微信支付
   * @param url
   */
  private loadH5WXZFTransitionPage(url: string) {
    let wxPayScheme: string = TKSystemHelper.getConfig("thirdPay.wxPayScheme");
    if (TKStringHelper.isNotBlank(wxPayScheme)) {
      let referer: string = `${wxPayScheme}://`;
      //拦截微信支付中间页，修改将scheme作为direct_url，微信支付完将跳回APP
      let wxDomain: string = "https://wx.tenpay.com";
      if (url.startsWith(wxDomain)) {
        let key: string = "redirect_url=";
        let index: number = url.indexOf(key);
        let reqUrl: string = "";
        if (index > 0) {
          reqUrl = url.substring(0, index + key.length);
          reqUrl += referer;
        } else {
          if (url.indexOf("?") > 0) {
            reqUrl = `${url}&redirect_url=${referer}`;
          } else {
            reqUrl = `${url}?redirect_url=${referer}`;
          }
        }
        if (this.webviewFrameNode && this.webviewFrameNode.getParent()) {
          this.webviewFrameNode.getParent()?.removeChild(this.webviewFrameNode);
          this.webviewFrameNode = undefined;
        }
        let parentFrameNode: FrameNode | null | undefined =
          this.parentComponent?.getUIContext().getFrameNodeByUniqueId(this.parentComponent?.getUniqueId());
        if (parentFrameNode) {
          let buildNode: BuilderNode<[Record<string, Object>]> =
            new BuilderNode<[Record<string, Object>]>(this.parentComponent!.getUIContext());
          buildNode.build(wrapBuilder<[Record<string, Object>]>(TKWebComponent), {
            "url": reqUrl,
            "referer": referer,
            "jsProxyController": new TKJSProxyController("ThirdZF")
          } as Record<string, Object>);
          this.webviewFrameNode = buildNode.getFrameNode() as FrameNode;
          parentFrameNode.appendChild(this.webviewFrameNode);
        }
      } else {
        TKLog.error(`打开微信支付中间页失败，原因为：${url}--->URL格式不对`);
      }
    } else {
      TKLog.error(`打开微信支付中间页失败，原因为：配置文件中未配置`);
    }
  }

  /**
   * 支付宝支付
   * @param html
   */
  private loadH5AliZFTransitionPage(html: string) {
    let htmlString: string =
      `<html><head><meta name='viewport' content='initial-scale=1.0, maximum-scale=1.0, user-scalable=no'/><style type='text/css'>body {font-size:16px;}</style></head><body>${html}</body></html>`;
    if (this.webviewFrameNode && this.webviewFrameNode.getParent()) {
      this.webviewFrameNode.getParent()?.removeChild(this.webviewFrameNode);
      this.webviewFrameNode = undefined;
    }
    let parentFrameNode: FrameNode | null | undefined =
      this.parentComponent?.getUIContext().getFrameNodeByUniqueId(this.parentComponent?.getUniqueId());
    if (parentFrameNode) {
      let buildNode: BuilderNode<[Record<string, Object>]> =
        new BuilderNode<[Record<string, Object>]>(this.parentComponent!.getUIContext());
      buildNode.build(wrapBuilder<[Record<string, Object>]>(TKWebComponent), {
        "data": htmlString,
        "jsProxyController": new TKJSProxyController("ThirdZF")
      } as Record<string, Object>);
      this.webviewFrameNode = buildNode.getFrameNode() as FrameNode;
      parentFrameNode.appendChild(this.webviewFrameNode);
    }
  }

  /**
   * 支付失败
   */
  public webViewOnErrorReceive() {
    this.finishLoadH5ThirdZFWithResult("2");
  }

  //支付拦截
  public webviewOnInterceptRequest(event?: OnInterceptRequestEvent): boolean {
    //处理微信支付
    let isContinue: boolean = this.processH5WXZFRequestForIsContinueResult(event);
    if (!isContinue) {
      return false;
    }
    //处理支付宝支付
    isContinue = this.processH5AliZFRequestForIsContinueResult(event);
    if (!isContinue) {
      return false;
    }
    return true;
  }

  /**
   *  拦截处理微信，并决定请求是否继续执行
   */
  private processH5WXZFRequestForIsContinueResult(event?: OnInterceptRequestEvent): boolean {
    if (event && event.request.getRequestHeader()) {
      let currentUrl: string = event.request.getRequestUrl();
      let wxPreReq: string = "weixin://wap/pay";
      if (currentUrl.startsWith(wxPreReq)) {
        //拦截H5微信支付请求
        TKSystemHelper.openAppURL(currentUrl, (success) => {
          TKLog.info(`打开微信支付结果为:url-->${currentUrl},success--->${success}`);
          if (success) {
            this.finishLoadH5ThirdZFWithResult("1");
          } else {
            this.finishLoadH5ThirdZFWithResult("0");
          }
        });
        return false;
      }
    }
    return true;
  }

  /**
   * 处理支付宝支付
   * @param event
   * @returns
   */
  private processH5AliZFRequestForIsContinueResult(event?: OnInterceptRequestEvent): boolean {
    //是否开启支付宝,流程如下:
    //1:H5跳转aliZF,WebView监听到后修改fromAppUrlScheme参数以便支付完成后，回到我们自己的APP
    if (event && event.request.getRequestHeader()) {
      let aliZFScheme: string = TKSystemHelper.getConfig("thirdPay.aliPayScheme")
      if (TKStringHelper.isNotBlank(aliZFScheme)) {
        let currentUrl: string = event.request.getRequestUrl();
        let aliReqPre: string = "alipay://alipayclient/?";
        if (currentUrl.startsWith(aliReqPre)) {
          currentUrl = decodeURIComponent(currentUrl);
          let paramStr: string = TKStringHelper.replace(currentUrl, aliReqPre, "");
          let paramDic: Record<string, Object> = JSON.parse(paramStr);
          if (paramDic) {
            paramDic["fromAppUrlScheme"] = aliZFScheme;
            paramStr = encodeURIComponent(JSON.stringify(paramDic));
            let zfUrl: string = `${aliReqPre}${paramStr}`;
            TKSystemHelper.openAppURL(zfUrl, (success) => {
              TKLog.info(`打开支付宝支付结果为:url-->${zfUrl},success--->${success}`);
              if (success) {
                this.finishLoadH5ThirdZFWithResult("1");
              } else {
                this.finishLoadH5ThirdZFWithResult("0");
              }
            });
          }
          return false;
        }
      }
    }
    return true;
  }
}
