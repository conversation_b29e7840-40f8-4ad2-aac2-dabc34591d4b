/**
 *  插件调用处理
 */
import { TKResultVO } from '../mvc/model/TKResultVO';
import { TKJSProxyController } from '../../components/webview/TKJSProxyController';
import { TKJSProxyControllerManager } from '../../components/webview/TKJSProxyControllerManager';
import { TKCommonService } from '../mvc/service/TKCommonService';
import { TKContextHelper } from '../../util/system/TKContextHelper';
import { common } from '@kit.AbilityKit';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKNotificationCenter } from '../notification/TKNotificationCenter';

/**
 *  插件回调函数
 *
 * @param result
 */
export type TKPluginCallBackFunc = (result: Record<string, Object>) => void;

export interface TKPluginInvokeDelegate {
  /**
   *  插件处理协议
   *
   * @param param 参数
   *
   * @return 数据
   */
  pluginInvoke(param: Record<string, Object>): TKResultVO;
}

/**
 *  js插件基础类
 */
export class TKBasePlugin implements TKPluginInvokeDelegate {
  /**
   * 插件事件通知
   */
  public static NOTE_PLUGIN_EVENT: string = "note_plugin_event";
  /**
   * 打开模块通知
   */
  public static NOTE_OPEN_MODULE: string = "note_open_module";
  /**
   * 显示隐藏Tabbar通知
   */
  public static NOTE_SHOW_HIDE_TAB: string = "note_show_hide_tab";
  /**
   * 全局服务类
   */
  private static commonTKPluginService?: TKCommonService;
  /**
   *  是否需要缓存插件
   */
  public isCache: boolean = false;
  /**
   *  是否同步插件，默认是NO
   */
  public isSyncPlugin: boolean = false;
  /**
   *  是否H5调用
   */
  public isH5: boolean = false;
  /**
   *  是否是JS回调模式
   */
  public isUseJsCallBack: boolean = false;
  /**
   *  请求流水号
   */
  public flowNo: string = "";
  /**
   *  回调函数
   */
  public callBackFunc: TKPluginCallBackFunc | undefined = undefined;
  /**
   *  当前的webviewController对象名称
   */
  public webControllerName: string = "";
  /**
   * 模块名称
   */
  public moduleName: string = "";
  /**
   * 当前Nav子组件对象或者堆栈对象
   */
  private _navComponentOrPathStack?: NavPathStack | WeakRef<CustomComponent>;

  protected get commonService(): TKCommonService {
    if (!TKBasePlugin.commonTKPluginService) {
      TKBasePlugin.commonTKPluginService = new TKCommonService();
    }
    return TKBasePlugin.commonTKPluginService;
  }

  public constructor() {
    this.isCache = true;
  }

  public set navComponentOrPathStack(navComponentOrPathStack: CustomComponent | NavPathStack | undefined) {
    if (navComponentOrPathStack instanceof NavPathStack) {
      this._navComponentOrPathStack = navComponentOrPathStack;
    } else {
      this._navComponentOrPathStack = navComponentOrPathStack ? new WeakRef(navComponentOrPathStack) : undefined;
    }
  }

  public get navComponentOrPathStack(): CustomComponent | NavPathStack | undefined {
    if (this._navComponentOrPathStack instanceof NavPathStack) {
      return this._navComponentOrPathStack;
    } else {
      return this._navComponentOrPathStack?.deref() ?? this.currentJSProxyController?.webComponent;
    }
  }

  /**
   * 获取当前上下文对象
   * @param component
   * @returns
   */
  public getContext(component?: Object): Context {
    return TKContextHelper.getCurrentContext(component);
  }

  /**
   * 获取当前UI上下文对象
   * @param component
   * @returns
   */
  public getUIContext(component?: Object): UIContext {
    return TKContextHelper.getCurrentUIContext(component);
  }

  /**
   * 获取当前UIAbiity上下文对象
   * @param component
   * @returns
   */
  public getUIAbilityContext(component?: Object): common.UIAbilityContext {
    return TKContextHelper.getCurrentUIAbilityContext(component);
  }

  /**
   *  执行插件请求
   *
   * @param param 参数
   *
   * @return 结果
   */
  public pluginInvoke(param: Record<string, Object>): TKResultVO {
    this.beforeServerInvoke(param);
    let resultVO: TKResultVO = this.serverInvoke(param);
    this.afterServerInvoke(resultVO);
    return resultVO;
  }

  /**
   * 前拦截
   */
  public beforeServerInvoke(param: Record<string, Object>) {
  }

  /**
   * 后拦截
   */
  public afterServerInvoke(resultVO: TKResultVO) {
    if (this.isSyncPlugin) {
      this.executeSyncResultForAsyncCallBack(resultVO);
    }
  }

  /**
   *  执行插件请求
   *
   * @param param 参数
   *
   * @return 结果
   */
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    return new TKResultVO();
  }

  /*
  *  执行插件的回调函数
  *
  * @param param 参数
  */
  public harmonyCallPluginCallBack(param: Record<string, Object>) {
    if (this.isH5) {
      this.harmonyCallJsWithParam(param);
    } else {
      if (this.callBackFunc) {
        this.callBackFunc(param);
      }
    }
  }

  /**
   *  当前的webviewController对象
   */
  public get currentJSProxyController(): TKJSProxyController | undefined {
    if (TKStringHelper.isNotBlank(this.webControllerName)) {
      return TKJSProxyControllerManager.shareInstance()
        .getJSProxyController(this.webControllerName);
    }
    return undefined;
  }

  /**
   *  鸿蒙调用JS
   *
   * @param param       参数
   */
  private harmonyCallJsWithParam(param: Record<string, Object>) {
    if (this.isH5) {
      if (this.callBackFunc) {
        let result: Record<string, Object> = {};
        result["@@Name"] = this.webControllerName;
        if (this.isUseJsCallBack) {
          result["@@Func"] = "callMessageByFlowNo";
          result["@@Data"] = [this.flowNo, param];
        } else {
          result["@@Func"] = "callMessage";
          result["@@Data"] = param;
        }
        this.callBackFunc(result);
      } else {
        if (this.currentJSProxyController) {
          if (this.isUseJsCallBack) {
            this.currentJSProxyController.sendFlowNoMsgToH5(this.flowNo, param);
          } else {
            this.currentJSProxyController.sendPlugMsgToH5(param);
          }
        } else {
          if (TKJSProxyController.harmonyCallJSFunctionFilter &&
          TKJSProxyController.harmonyCallJSFunctionFilter.onFilter) {
            if (this.isUseJsCallBack) {
              TKJSProxyController.harmonyCallJSFunctionFilter.onFilter(this.webControllerName, "callMessageByFlowNo",
                [this.flowNo, param]);
            } else {
              TKJSProxyController.harmonyCallJSFunctionFilter.onFilter(this.webControllerName, "callMessage", param);
            }
          }
        }
      }
    } else {
      if (this.callBackFunc) {
        this.callBackFunc(param);
      }
    }
  }

  /**
   *  原生调用js
   * @param funcParam         参数
   */
  public harmonyCallJS(funcParam: Record<string, Object> = {}) {
    this.harmonyCallJSFunction("callMessage", funcParam);
  }

  /**
   *  原生调用js
   * @param funcName          函数名
   * @param funcParam         参数
   */
  public harmonyCallJSFunction(funcName: string, funcParam?: Record<string, Object> | Array<Object>) {
    if (this.isH5) {
      if (this.callBackFunc) {
        let result: Record<string, Object> = {};
        result["@@Func"] = funcName;
        if (funcParam) {
          result["@@Data"] = funcParam;
        }
        this.callBackFunc(result);
      } else {
        if (this.currentJSProxyController) {
          this.currentJSProxyController.harmonyCallJSFunction(funcName, funcParam);
        } else {
          if (TKJSProxyController.harmonyCallJSFunctionFilter &&
          TKJSProxyController.harmonyCallJSFunctionFilter.onFilter) {
            TKJSProxyController.harmonyCallJSFunctionFilter.onFilter(this.webControllerName, funcName, funcParam);
          }
        }
      }
    }
  }

  /**
   *  执行同步结果集转成异步回调
   *
   * @param param 参数
   */
  private executeSyncResultForAsyncCallBack(resultVO: TKResultVO) {
    let pluginResultVO: TKResultVO = new TKResultVO();
    pluginResultVO.errorNo = resultVO.errorNo;
    pluginResultVO.errorInfo = resultVO.errorInfo;
    pluginResultVO.results = resultVO.results;
    let param: Record<string, Object> = pluginResultVO.toJson() as Record<string, Object>;
    if (this.isH5) {
      if (this.isUseJsCallBack) {
        this.harmonyCallJsWithParam(param);
      }
    } else {
      if (this.callBackFunc) {
        this.callBackFunc(param);
      }
    }
  }

  /**
   * 发送插件消息
   * @param funcNo
   * @param param
   */
  public onPluginEvent(funcNo: string, param?: Record<string, Object>) {
    param = param ?? {} as Record<string, Object>;
    param["funcNo"] = funcNo;
    param["moduleName"] = this.moduleName;
    if (this.currentJSProxyController) {
      this.currentJSProxyController.onPluginEvent(funcNo, param, this.currentJSProxyController);
    } else {
      if (funcNo == "50101") {
        TKNotificationCenter.defaultCenter.postNotificationName(TKBasePlugin.NOTE_OPEN_MODULE,
          this.currentJSProxyController, param);
      } else if (funcNo == "50108") {
        TKNotificationCenter.defaultCenter.postNotificationName(TKBasePlugin.NOTE_SHOW_HIDE_TAB,
          this.currentJSProxyController, param);
      } else {
        TKNotificationCenter.defaultCenter.postNotificationName(TKBasePlugin.NOTE_PLUGIN_EVENT,
          this.currentJSProxyController, param);
      }
    }
  }
}