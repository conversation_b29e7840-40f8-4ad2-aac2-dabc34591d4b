/**
 *  拨打电话
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';
import { call } from '@kit.TelephonyKit';

export class TKPlugin50220 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //电话号码
    let telNo: string = TKMapHelper.getString(param, "telNo");
    //拨打类型(0：进入拨打界面，1：直接拨打)
    let callType: string = TKMapHelper.getString(param, "callType", "0")
    if (TKStringHelper.isBlank(telNo)) {
      resultVO.errorNo = -5022001;
      resultVO.errorInfo = "电话号码不能为空!";
    } else {
      let isSupport = call.hasVoiceCapability();
      if (isSupport) {
        call.makeCall(telNo);
      } else {
        resultVO.errorNo = -5022002;
        resultVO.errorInfo = "拨打电话功能不支持!";
      }
    }
    return resultVO;
  }
}