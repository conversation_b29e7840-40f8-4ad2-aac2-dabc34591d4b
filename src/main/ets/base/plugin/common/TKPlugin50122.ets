import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  滑动返回，通知原生H5页面发生跳转，方便原生记录H5历史堆栈
 */
export class TKPlugin50122 extends TKBasePlugin {
  public constructor() {
    super();
    this.isCache = false;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //来源页面URL
    let fromPageUrl: string = TKMapHelper.getString(param, "fromPageUrl");
    //目标页面URL
    let toPageUrl: string = TKMapHelper.getString(param, "toPageUrl");
    if (TKStringHelper.isBlank(fromPageUrl)) {
      resultVO.errorNo = -5012201;
      resultVO.errorInfo = "来源页面URL不能为空!";
    } else if (TKStringHelper.isBlank(toPageUrl)) {
      resultVO.errorNo = -5012202;
      resultVO.errorInfo = "目标页面URL不能为空!";
    } else {
    }
    return resultVO;
  }
}