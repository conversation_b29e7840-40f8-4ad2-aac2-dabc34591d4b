import { TKLog } from '../../../util/logger/TKLog';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKContextHelper } from '../../../util/system/TKContextHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * <AUTHOR> 2015-06-04 17:06:55
 *
 *  通知原生打开系统浏览器的一个网页
 */
export class TKPlugin50109 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //url地址
    let url: string = TKMapHelper.getString(param, "url");
    if (TKStringHelper.isBlank(url)) {
      resultVO.errorNo = -5010901;
      resultVO.errorInfo = "网页地址不能为空";
    } else {
      // 启动系统浏览器
      TKContextHelper.getCurrentUIAbilityContext().startAbility({
        uri: url,
        action: 'ohos.want.action.viewData',
        entities: ['entity.system.browsable'],
      }).then(() => {
        TKLog.info('打开浏览器成功');
      }).catch((err: Error) => {
        TKLog.error(`打开浏览器失败: ${err.message}`)
      })
    }
    return resultVO;
  }
}