import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKRouterHelper } from '../../router/TKRouterHelper';
import { TKBasePlugin } from '../TKBasePlugin';

import('../../mvc/page/common/video/TKComVideoPage');

/**
 *   播放视频
 */
export class TKPlugin50275 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //视频文件标题
    let title: string = TKMapHelper.getString(param, "title", "视频播放");
    //视频文件地址
    let url: string = TKMapHelper.getString(param, "mediaUrl");
    //是否隐藏进度条(0:否，1:是)
    let isHiddenProgress: boolean = TKMapHelper.getBoolean(param, "isHiddenProgress", false);
    //播放完成是否自动关闭(0:否，1:是)
    let isAutoClose: boolean = TKMapHelper.getBoolean(param, "isAutoClose", true);
    //附加参数
    let jsonParam: Record<string, Object> = TKMapHelper.getJSON(param, "paramExt");
    if (TKStringHelper.isBlank(url)) {
      resultVO.errorNo = -5027501;
      resultVO.errorInfo = "视频文件地址不能为空";
    } else {
      url = encodeURI(url);
      TKRouterHelper.pushNamedRoute({
        name: 'TKComVideoPage',
        params: {
          "url": url,
          "title": title,
          "isHiddenProgress": isHiddenProgress,
          "isAutoClose": isAutoClose
        },
        pageStyle: {
          layoutFullScreen: true
        },
        onResult: (data) => {
          let progress: number = TKMapHelper.getNumber(data, "progress");
          let result: Record<string, Object> = {
            "funcNo": "50281",
            "result": (progress >= 0.9) ? "1" : "0",
            "paramExt": jsonParam
          };
          this.harmonyCallPluginCallBack(result);
        },
        navComponentOrPathStack: this.navComponentOrPathStack
      });
    }
    return resultVO;
  }
}