/**
 * 发送短信
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';
import { Want } from '@kit.AbilityKit';
import { TKContextHelper } from '../../../util/system/TKContextHelper';

export class TKPlugin50221 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //电话号码
    let telNo: string = TKMapHelper.getString(param, "telNo");
    //发送内容
    let content: string = TKMapHelper.getString(param, "content");
    //附加参数
    let jsonParam: Record<string, Object> = TKMapHelper.getJSON(param, "paramExt");
    let contactObjects: Array<Object> = telNo.split("|").map((t: string) => {
      return { "telephone": t } as Record<string, Object>
    });
    let want: Want = {
      bundleName: "com.ohos.mms",
      abilityName: "com.ohos.mms.MainAbility",
      parameters: {
        contactObjects: JSON.stringify(contactObjects),
        pageFlag: "conversation",
        content: content
      }
    };
    TKContextHelper.getCurrentUIAbilityContext().startAbilityForResult(want).then((data) => {
      let result: Record<string, Object> = {
        "funcNo": "50227",
        "paramExt": jsonParam,
        "error_no": 0,
        "error_info": "发送成功"
      }
      this.harmonyCallPluginCallBack(result);
    }).catch(() => {
      let result: Record<string, Object> = {
        "funcNo": "50227",
        "paramExt": jsonParam,
        "error_no": -5022104,
        "error_info": "发送失败"
      }
      this.harmonyCallPluginCallBack(result);
    });
    return resultVO;
  }
}