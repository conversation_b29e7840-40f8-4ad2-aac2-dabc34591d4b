import { TKNetHelper, TKNetworkType } from '../../../util/net/TKNetHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  获取设备网络环境
 */
export class TKPlugin50030 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //设备网络环境
    let network_type: TKNetworkType = TKNetHelper.getNetworkType();
    let deviceNetwork: string = "";
    switch (network_type) {
      case TKNetworkType.Network_No:
        deviceNetwork = "0";
        break;
      case TKNetworkType.Network_2G:
        deviceNetwork = "1";
        break;
      case TKNetworkType.Network_3G:
        deviceNetwork = "2";
        break;
      case TKNetworkType.Network_4G:
        deviceNetwork = "3";
        break;
      case TKNetworkType.Network_5G:
        deviceNetwork = "5";
        break;
      case TKNetworkType.Network_WIFI:
        deviceNetwork = "4";
        break;
      default:
        break;
    }
    let dataRow: Record<string, Object> = {
      "network": deviceNetwork
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}