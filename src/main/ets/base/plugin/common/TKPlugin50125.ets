/**
 *  获取当前系统的主题
 */
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKThemeManager } from '../../theme/TKThemeManager';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50125 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    let result: Record<string, Object> = {
      "theme": TKThemeManager.shareInstance().theme
    };
    resultVO.results = result;
    return resultVO;
  }
}