/**
 * 获取手势密码的设置状态
 */
import { TKPatternLock } from '../../../components/patternlock/TKPatternLock';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50263 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //账户
    let account: string = TKMapHelper.getString(param, "account");
    let password: string = TKPatternLock.getUserPassword(account);
    //根据手势密码判断是否设置
    let dataRow: Record<string, Object> = {
      "flag": TKStringHelper.isNotBlank(password) ? "1" : "0"
    }
    resultVO.results = dataRow;
    return resultVO;
  }
}