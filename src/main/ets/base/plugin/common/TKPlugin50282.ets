import { TKFileHelper } from '../../../util/file/TKFileHelper';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKVideoCropperManager, TKVideoCropperManagerDelegate } from '../../mvc/page/video/TKVideoCropperManager';
import { TKBasePlugin } from '../TKBasePlugin';
import { buffer } from '@kit.ArkTS';
import { TKReqParamVO } from '../../mvc/model/TKReqParamVO';
import { TKComponentContent, TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKProgressDialogOption } from '../../../components/dialog/TKProgressDialog';
import { TKLoadInfoVO } from '../../mvc/model/TKLoadInfoVO';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKPermissionHelper } from '../../../util/system/TKPermissionHelper';
import { TKNormalDialogOption } from '../../../components/dialog/TKNormalDialog';

/**
 *   选择相册或者录制，支持压缩
 */
export class TKPlugin50282 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //服务器地址
    let serverAddr: string = TKMapHelper.getString(param, "serverAddr");
    //文件标示
    let fileName: string = TKMapHelper.getString(param, "fileName");
    //附加参数
    let jsonParam: Record<string, Object> = TKMapHelper.getJSON(param, "paramExt");
    //是否启动压缩转换(0:非压缩，1:压缩)
    let isCompress: boolean = TKMapHelper.getBoolean(param, "isCompress", false);
    //标题文字颜色
    let titleColor: string = TKMapHelper.getString(param, "titleColor", "#ffffffff");
    //状态栏颜色
    let statusColor: string = TKMapHelper.getString(param, "statusColor");
    //状态栏样式
    let statusStyle: string = TKMapHelper.getString(param, "statusStyle");
    //加载模式(0:菊花，1：进度条)，默认0
    let loadingMode: string = TKMapHelper.getString(param, "loadingMode", "0");
    //摄像头模式
    let isFrontCamera: boolean = !(TKMapHelper.getString(param, "cameraFlag") == "1")
    //选择模式(0:相册+录制,1:相册,2:录制)
    let mode: string = TKMapHelper.getString(param, "mode", "0");
    //是否自动保存拍照图片到相册
    let isAutoSavePhoto: boolean = TKMapHelper.getBoolean(param, "isAutoSavePhoto", false);
    //主颜色
    let mainColor: string = TKMapHelper.getString(param, "mainColor", "#FF1A9BFF");
    if (TKStringHelper.isNotBlank(serverAddr)) {
      if (TKStringHelper.isBlank(fileName)) {
        resultVO.errorNo = -5028202;
        resultVO.errorInfo = "文件标示不能为空";
        return resultVO;
      }
    }
    //弹出视频组件
    TKVideoCropperManager.shareInstance().isCompress = isCompress;
    TKVideoCropperManager.shareInstance().isAutoSavePhoto = isAutoSavePhoto;
    TKVideoCropperManager.shareInstance().isFrontCamera = isFrontCamera;
    TKVideoCropperManager.shareInstance().mainColor = mainColor;
    TKVideoCropperManager.shareInstance().showChoiceSheet(mode);
    TKVideoCropperManager.shareInstance().delegate = {
      processCropperVideo: async (videoURI: string) => {
        let callBackFunc = (resultVO: TKResultVO) => {
          let jsParam: Record<string, Object> = (resultVO.results ?? {}) as Record<string, Object>;
          jsParam["funcNo"] = "50283";
          jsParam["error_no"] = resultVO.errorNo;
          jsParam["error_info"] = resultVO.errorInfo;
          jsParam["paramExt"] = jsonParam;
          jsParam["fileExtension"] = "mp4";
          jsParam["filePath"] = videoURI;
          this.harmonyCallPluginCallBack(jsParam);
        };
        if (TKStringHelper.isNotBlank(serverAddr)) {
          let reqParam: Record<string, Object> = jsonParam;
          reqParam[`${fileName}@@F`] = buffer.from(TKFileHelper.readFile(videoURI)).buffer;
          reqParam["file_extension"] = "mp4";
          let reqParamVO: TKReqParamVO = this.commonService.createReqParamVO();
          reqParamVO.isUpload = true;
          reqParamVO.isReturnList = false;
          reqParamVO.url = serverAddr;
          reqParamVO.reqParam = reqParam;
          let progress: TKComponentContent<TKProgressDialogOption> | undefined = undefined;
          if (loadingMode == "0") {
            reqParamVO.isShowWait = true;
            reqParamVO.waitTip = "正在上传...";
          } else {
            progress = TKDialogHelper.createProgressDialog({
              tip: "正在上传...",
              angle: reqParamVO.waitAngle
            } as TKProgressDialogOption);
            reqParamVO.uploadBlock = (loadInfoVO: TKLoadInfoVO) => {
              progress?.reuse({
                value: (loadInfoVO.progress * 100)
              } as TKProgressDialogOption);
            }
          }
          this.commonService.serviceInvoke(reqParamVO, (resultVO) => {
            callBackFunc(resultVO);
            if (progress) {
              progress.close();
              progress = undefined;
            }
          });
        } else {
          callBackFunc(new TKResultVO());
        }
      },
      processCropperVideoWithErrorNo: (errorNo: number, errorInfo: string) => {
        if (errorNo == -9 && TKSystemHelper.getConfig("system.isPluginBackspaceCallH5") == "0") {
          return;
        }
        let result: Record<string, Object> = {
          "funcNo": "50283",
          "error_no": (-5028200 + errorNo),
          "error_info": errorInfo,
          "paramExt": jsonParam
        }
        switch (errorNo) {
          case -8: {
            // 读取异常
            TKDialogHelper.showToast(errorInfo);
            break;
          }
          case -9: {
            //用户取消
            result["error_no"] = 0;
            break;
          }
          default: {
            // 没有权限
            TKDialogHelper.showAlertDialog({
              title: "温馨提示",
              message: errorInfo,
              confirmText: "去授权",
              cancelText: "暂不授权",
              confirm: () => {
                TKPermissionHelper.toAppSetting();
              }
            } as TKNormalDialogOption);
            break;
          }
        }
        this.harmonyCallPluginCallBack(result);
      }
    } as TKVideoCropperManagerDelegate;
    return resultVO;
  }
}