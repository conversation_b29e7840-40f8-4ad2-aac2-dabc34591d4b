import { TKNetHelper } from '../../../util/net/TKNetHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  获取设备IP地址
 */
export class TKPlugin50023 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //获取外网IP
    let ip: string = TKNetHelper.getIP();
    let lip: string = TKNetHelper.getLocalIP();
    let dataRow: Record<string, Object> = {
      "ip": ip,
      "lip": lip
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}