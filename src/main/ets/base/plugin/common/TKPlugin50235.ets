import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKH5ThirdZFManager } from '../../mvc/page/common/web/TKH5ThirdZFManager';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 打开H5三方支付
 */
export class TKPlugin50235 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //三方支付类型(0：微信支付，1：支付宝支付)
    let type: string = TKMapHelper.getString(param, "type");
    //三方支付内容
    let content: string = TKMapHelper.getString(param, "content");
    //附加参数
    let paramExt: Record<string, Object> = TKMapHelper.getJSON(param, "paramExt");
    if (TKStringHelper.isBlank(type)) {
      resultVO.errorNo = -5023501;
      resultVO.errorInfo = "类型不能为空";
      return resultVO;
    }
    if (TKStringHelper.isBlank(content)) {
      resultVO.errorNo = -5023502;
      resultVO.errorInfo = "内容不能为空";
      return resultVO;
    }
    TKH5ThirdZFManager.shareInstance()
      .loadH5ThirdZFTransitionPage(this.navComponentOrPathStack as CustomComponent, type, content, (flag) => {
        //打开H5三方支付相关App的结果(0:未安装，1:已安装，2：其他)
        let result: Record<string, Object> = {
          "funcNo": "50236",
          "type": type,
          "flag": flag,
          "paramExt": paramExt ?? {} as Record<string, Object>
        }
        this.harmonyCallPluginCallBack(result);
      });
    return resultVO;
  }
}