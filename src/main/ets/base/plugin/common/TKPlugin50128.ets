/**
 *  是否开启App前后台监听
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50128 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //是否开启App前后台监听
    let isNeedAppStatus: boolean = TKMapHelper.getBoolean(param, "isNeedAppStatus", false);
    let option: Record<string, Object> = {
      "isH5ListenerAppStatus": isNeedAppStatus,
    };
    this.onPluginEvent("50128", option);
    return resultVO;
  }
}