/**
 * 弹出通讯录
 */
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';
import { contact } from '@kit.ContactsKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';

export class TKPlugin50222 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //附加参数
    let jsonParam: Record<string, Object> = TKMapHelper.getJSON(param, "paramExt");
    contact.selectContacts({
      isMultiSelect: false
    }, (err: BusinessError, contacts: Array<contact.Contact>) => {
      if (err) {
        let result: Record<string, Object> = {
          "funcNo": "50223",
          "error_no": -5022301,
          "error_info": err.message,
          "paramExt": jsonParam
        };
        this.harmonyCallPluginCallBack(result);
      } else {
        let contactInfo: contact.Contact | undefined = contacts.length > 0 ? contacts[0] : undefined;
        let result: Record<string, Object> = {
          "funcNo": "50223",
          "error_no": 0,
          "error_info": "",
          "name": contactInfo?.name ?? contactInfo?.nickName ?? "",
          "phone": contactInfo?.phoneNumbers?.join(",") ?? "",
          "paramExt": jsonParam
        };
        result["phone"] = TKStringHelper.replace(TKMapHelper.getString(result, "phone"), "-|\(|\)| ", "");
        this.harmonyCallPluginCallBack(result);
      }
    });
    return resultVO;
  }
}