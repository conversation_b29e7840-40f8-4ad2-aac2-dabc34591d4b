import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';
import { TKPluginInvokeCenter } from '../TKPluginInvokeCenter';

/**
 *  H5插件检测
 */
export class TKPlugin50120 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    let plugins: string = TKMapHelper.getString(param, "plugins"); //插件列表，多个用|分割
    if (TKStringHelper.isBlank(plugins)) {
      resultVO.errorNo = -5012001;
      resultVO.errorInfo = "要检测的插件功能号不能为空!";
    } else {
      let pluginAry: Array<string> = plugins.split("|");
      let errorInfo: string = "";
      for (let plugin of pluginAry) {
        //如果不存在实例插件
        if (!TKPluginInvokeCenter.shareInstance().isPluginExist(plugin)) {
          errorInfo += (plugin + ",")
        }
      }
      if (TKStringHelper.isNotBlank(errorInfo)) {
        errorInfo = errorInfo.substring(0, errorInfo.length - 1);
        errorInfo = `插件[${errorInfo}]未定义!`;
        resultVO.errorInfo = errorInfo;
        resultVO.errorNo = -5012002;
      }
    }
    return resultVO;
  }
}