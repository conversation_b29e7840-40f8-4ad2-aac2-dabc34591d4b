import { TKNormalDialogOption } from '../../../components/dialog/TKNormalDialog';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 打开原生的信息提示框
 */
export class TKPlugin50110 extends TKBasePlugin {
  public constructor() {
    super();
    this.isCache = false;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //标题
    let title: string = TKMapHelper.getString(param, "title", "温馨提示");
    //内容
    let content: string = TKMapHelper.getString(param, "content");
    //类型
    let type: string = TKMapHelper.getString(param, "type", "0");
    //业务标志
    let flag: string = TKMapHelper.getString(param, "flag");
    //最大宽度
    let width: number = TKMapHelper.getNumber(param, "width");
    //最大高度
    let height: number = TKMapHelper.getNumber(param, "height");
    if (TKStringHelper.isBlank(content)) {
      resultVO.errorNo = -5011001;
      resultVO.errorInfo = "内容不能为空";
    } else {
      if (type === "0") {
        TKDialogHelper.showAlertDialog({
          title: title,
          message: content,
          contentMinHeight: height > 0 ? height : undefined,
          confirmText: "确定",
          confirm: () => {
            //回调H5动作
            let result: Record<string, Object> = {
              "flag": flag,
              "funcNo": "50111"
            }
            this.harmonyCallPluginCallBack(result);
          }
        } as TKNormalDialogOption);
      } else {
        TKDialogHelper.showAlertDialog({
          title: title,
          message: content,
          contentMinHeight: height > 0 ? height : undefined,
          confirmText: "确定",
          cancelText: "取消",
          confirm: () => {
            //回调H5动作
            let result: Record<string, Object> = {
              "flag": flag,
              "funcNo": "50111"
            }
            this.harmonyCallPluginCallBack(result);
          },
          cancel: () => {

          }
        } as TKNormalDialogOption);
      }
    }
    return resultVO;
  }
}