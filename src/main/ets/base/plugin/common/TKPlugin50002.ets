import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 修改应用配置
 */
export class TKPlugin50002 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    //一级标签
    let storeKey: string = TKMapHelper.getString(param, "storeKey");
    //二级标签
    let innerKey: string = TKMapHelper.getString(param, "innerKey");
    //修改值
    let value: string = TKMapHelper.getString(param, "value");
    let resultVO: TKResultVO = new TKResultVO();
    if (TKStringHelper.isBlank(storeKey)) {
      resultVO.errorNo = -5000201;
      resultVO.errorInfo = "一级标签不能为空!";
    } else if (TKStringHelper.isBlank(innerKey)) {
      resultVO.errorNo = -5000202;
      resultVO.errorInfo = "二级标签不能为空!";
    } else {
      //修改系统配置
      TKSystemHelper.setConfig(`${storeKey}.${innerKey}`, value);
    }
    return resultVO;
  }
}