import { TKNetHelper } from '../../../util/net/TKNetHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  获取设备网络运营商
 */
export class TKPlugin50031 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //设备网络运营商
    let phoneOperator: string = TKNetHelper.getPhoneOperator();
    //获取运营商信息(IMSI)
    let phoneIMSI: string = "";
    let dataRow: Record<string, Object> = {
      "phoneOperator": phoneOperator,
      "phoneIMSI": phoneIMSI,
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}