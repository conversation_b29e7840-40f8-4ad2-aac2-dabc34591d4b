import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKNetHelper, TKNetworkType } from '../../../util/net/TKNetHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 获取应用设备的详细信息
 */
export class TKPlugin50001 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //软件名称
    let softName: string = TKSystemHelper.getAppName();
    //包名
    let softIdentifier: string = TKSystemHelper.getAppIdentifier();
    //软件版本号(展示版本号)，包含H5升级版本
    let softVersion: string = TKSystemHelper.getVersion();
    //软件内部版本号(系统升级用)，包含H5升级版本
    let softVersionCode: string = TKSystemHelper.getVersionCode();
    //原生应用版本号
    let nativeVersion: string = TKSystemHelper.getAppVersion();
    //原生应用内部版本序号
    let nativeVersionCode: string = TKSystemHelper.getAppVersionCode();
    //设备型号，如:iphone6s，小米1
    let devicePlatform: string = TKDeviceHelper.getDevicePlatformInfo();
    //设备系统版本，如:ios7.0，Android4.2
    let deviceSysVersion: string = TKDeviceHelper.getDeviceSysVersion();
    //设备分辨率，如320x480px
    let deviceResolution: string = TKDeviceHelper.getScreenDisplay();
    //设备基带版本，Android专用
    let deviceBasePlatform: string = "";
    //设备内核版本，Android专用
    let deviceInnerPlatform: string = "";
    //设备内部版本号,Android专用
    let deviceInnerVersion: string = "";
    //设备名称
    let deviceName: string = TKDeviceHelper.getDeviceName();
    //设备国际移动设备识别码,Android专用,鸿蒙返回设备ID
    let deviceIMEI: string = TKDeviceHelper.getDeviceUUID();
    //设备移动设备识别码,Android专用,鸿蒙返回设备ID
    let deviceMEID: string = TKDeviceHelper.getDeviceUUID();
    //设备本地真实手机号,Android专用
    let deviceUMPN: string = "";
    //设备集成电路SIM卡识别码,Android专用
    let deviceICCID: string = "";
    //设备MAC地址
    let deviceMAC: string = TKDeviceHelper.getDeviceMac();
    //设备外网IP地址
    let deviceIP: string = TKNetHelper.getIP();
    //设备内网IP地址
    let deviceLIP: string = TKNetHelper.getLocalIP();
    //设备网络环境
    let network_type: TKNetworkType = TKNetHelper.getNetworkType();
    let deviceNetwork: string = "";
    switch (network_type) {
      case TKNetworkType.Network_No:
        deviceNetwork = "0";
        break;
      case TKNetworkType.Network_2G:
        deviceNetwork = "1";
        break;
      case TKNetworkType.Network_3G:
        deviceNetwork = "2";
        break;
      case TKNetworkType.Network_4G:
        deviceNetwork = "3";
        break;
      case TKNetworkType.Network_5G:
        deviceNetwork = "5";
        break;
      case TKNetworkType.Network_WIFI:
        deviceNetwork = "4";
        break;
      default:
        break;
    }
    //设备网络运营商
    let deviceOperator: string = TKNetHelper.getPhoneOperator();
    // 获取运营商信息(IMSI)
    let deviceIMSI: string = "";
    //是否越狱
    let isDeviceJailBreak: string = "";
    //其他备用信息
    let other: string = "";

    let dataRow: Record<string, Object> = {
      "softName": softName,
      "softIdentifier": softIdentifier,
      "softVersion": softVersion,
      "softVersionSn": softVersionCode,
      "nativeVersion": nativeVersion,
      "nativeVersionSn": nativeVersionCode,
      "devicePlatform": devicePlatform,
      "deviceSysVersion": deviceSysVersion,
      "deviceResoluation": deviceResolution,
      "deviceBasePlatform": deviceBasePlatform,
      "deviceInnerPlatform": deviceInnerPlatform,
      "deviceInnerVersion": deviceInnerVersion,
      "deviceName": deviceName,
      "deviceIMEI": deviceIMEI,
      "deviceMEID": deviceMEID,
      "deviceUMPN": deviceUMPN,
      "deviceICCID": deviceICCID,
      "deviceMAC": deviceMAC,
      "deviceIP": deviceIP,
      "deviceLIP": deviceLIP,
      "deviceNetwork": deviceNetwork,
      "deviceOperator": deviceOperator,
      "deviceIMSI": deviceIMSI,
      "isDeviceJailBreak": isDeviceJailBreak,
      "other": other
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}