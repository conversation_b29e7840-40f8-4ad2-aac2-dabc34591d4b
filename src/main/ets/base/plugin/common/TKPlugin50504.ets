import { TKJSNotiProxyManager } from '../../../components/webview/TKJSNotiProxyManager';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *   H5卸载原生广播
 */
export class TKPlugin50504 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    let notiName: string = TKMapHelper.getString(param, "notiName");
    if (TKStringHelper.isBlank(notiName)) {
      resultVO.errorNo = -5050401;
      resultVO.errorInfo = "通知名称不能为空!";
    } else {
      TKJSNotiProxyManager.shareInstance().unRegister(notiName, this.currentJSProxyController!);
    }
    return resultVO;
  }
}