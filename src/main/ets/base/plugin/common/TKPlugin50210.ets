/**
 *  打开原生键盘
 */
import { TKKeyBoardEventDelegate, TKKeyBoardType } from '../../../components/keyboard/delegate/TKKeyBoardEventDelegate';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50210 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //页面Id
    let pageId: string = TKMapHelper.getString(param, "pageId");
    //元素Id
    let eleId: string = TKMapHelper.getString(param, "eleId");
    //完成按钮显示字符
    let doneLable: string = TKMapHelper.getString(param, "doneLable");
    //完成按钮显示字符颜色，例如：#FFDDFF
    let doneLableColor: string = TKMapHelper.getString(param, "doneLableColor");
    //完成按钮背景颜色，例如：#FFDDFF
    let doneLableBgColor: string = TKMapHelper.getString(param, "doneLableBgColor");
    //完成按钮显示字符字体大小
    let doneLableFontSize: string = TKMapHelper.getString(param, "doneLableFontSize");
    //键盘类型
    let keyboardType: string = TKMapHelper.getString(param, "keyboardType");
    //是否强制隐藏标题(0:否，1:是)
    let isHideTitle: boolean = TKMapHelper.getBoolean(param, "isHideTitle", false);
    //附加参数
    let data: Record<string, Object> = TKMapHelper.getJSON(param, "data");
    //系统键盘展示
    if (keyboardType == "9") {
      TKDialogHelper.closeKeyBoard();
    } else {
      //自定义原生键盘
      if (TKStringHelper.isBlank(pageId)) {
        resultVO.errorNo = -5021001;
        resultVO.errorInfo = "页面Id不能为空!";
      } else if (TKStringHelper.isBlank(eleId)) {
        resultVO.errorNo = -5021002;
        resultVO.errorInfo = "元素Id不能为空!";
      } else if (TKStringHelper.isBlank(keyboardType)) {
        resultVO.errorNo = -5021003;
        resultVO.errorInfo = "键盘类型不能为空!";
      } else {
        TKDialogHelper.showKeyBoard({
          keyBoardType: this.getKeyBoardType(keyboardType),
          confirmConfig: {
            text: TKStringHelper.isNotBlank(doneLable) ? doneLable : undefined,
            textColor: TKStringHelper.isNotBlank(doneLableColor) ? doneLableColor : undefined,
            bgColor: TKStringHelper.isNotBlank(doneLableBgColor) ? doneLableBgColor : undefined,
            fontSize: TKStringHelper.isNotBlank(doneLableFontSize) ? Number(doneLableFontSize) : undefined,
            isHideTitle: isHideTitle
          }, delegate: {
            appendChar: (charStr) => {
              let result: Record<string, Object> = {
                "funcNo": "50212",
                "pageId": pageId,
                "eleId": eleId,
                "keyCode": charStr
              } as Record<string, Object>;
              this.harmonyCallPluginCallBack(result);
            },
            clearValue: () => {
              let result: Record<string, Object> = {
                "funcNo": "50212",
                "pageId": pageId,
                "eleId": eleId,
                "keyCode": "-2"
              } as Record<string, Object>;
              this.harmonyCallPluginCallBack(result);
            },
            doConfirm: () => {
              let result: Record<string, Object> = {
                "funcNo": "50212",
                "pageId": pageId,
                "eleId": eleId,
                "keyCode": "-3"
              } as Record<string, Object>;
              this.harmonyCallPluginCallBack(result);
            },
            hideKeyBoard: () => {
              let result: Record<string, Object> = {
                "funcNo": "50212",
                "pageId": pageId,
                "eleId": eleId,
                "keyCode": "-4"
              } as Record<string, Object>;
              this.harmonyCallPluginCallBack(result);
            },
            deleteChar: () => {
              let result: Record<string, Object> = {
                "funcNo": "50212",
                "pageId": pageId,
                "eleId": eleId,
                "keyCode": "-5"
              } as Record<string, Object>;
              this.harmonyCallPluginCallBack(result);
            },
            doForward: () => {
              let result: Record<string, Object> = {
                "funcNo": "50212",
                "pageId": pageId,
                "eleId": eleId,
                "keyCode": "-7"
              } as Record<string, Object>;
              this.harmonyCallPluginCallBack(result);
            },
            doGoBack: () => {
              let result: Record<string, Object> = {
                "funcNo": "50212",
                "pageId": pageId,
                "eleId": eleId,
                "keyCode": "-8"
              } as Record<string, Object>;
              this.harmonyCallPluginCallBack(result);
            },
            doOtherChar: (charStr) => {
              let result: Record<string, Object> = {
                "funcNo": "50212",
                "pageId": pageId,
                "eleId": eleId,
                "keyCode": charStr
              } as Record<string, Object>;
              this.harmonyCallPluginCallBack(result);
            }
          } as TKKeyBoardEventDelegate
        })
      }
    }
    return resultVO;
  }

  private getKeyBoardType(keyboardType: string): string {
    let type: string = keyboardType;
    if (keyboardType == "1") {
      type = TKKeyBoardType.Alpha;
    } else if (keyboardType == "2") {
      type = TKKeyBoardType.Stock;
    } else if (keyboardType == "3") {
      type = TKKeyBoardType.Num;
    } else if (keyboardType == "4") {
      type = TKKeyBoardType.RandNum;
    } else if (keyboardType == "5") {
      type = TKKeyBoardType.RandNumStrong;
    } else if (keyboardType == "6") {
      type = TKKeyBoardType.Trade;
    } else if (keyboardType == "7") {
      type = TKKeyBoardType.NumStrong;
    } else if (keyboardType == "8") {
      type = TKKeyBoardType.SBStock;
    } else if (keyboardType == "10") {
      type = TKKeyBoardType.MSNum;
    } else if (keyboardType == "11") {
      type = TKKeyBoardType.MSRandNum;
    } else if (keyboardType == "12") {
      type = TKKeyBoardType.MSAlpha;
    } else if (keyboardType == "13") {
      type = TKKeyBoardType.MSNumSymbol;
    } else if (keyboardType == "14") {
      type = TKKeyBoardType.MSSymbol;
    } else if (keyboardType == "20") {
      type = TKKeyBoardType.TFLoginNum;
    } else if (keyboardType == "21") {
      type = TKKeyBoardType.TFStock;
    } else if (keyboardType == "22") {
      type = TKKeyBoardType.TFBuyPrice;
    } else if (keyboardType == "23") {
      type = TKKeyBoardType.TFBuyNum;
    } else if (keyboardType == "24") {
      type = TKKeyBoardType.TFAlpha;
    } else if (keyboardType == "25") {
      type = TKKeyBoardType.TFNumSymbol;
    } else if (keyboardType == "26") {
      type = TKKeyBoardType.TFSymbol;
    } else if (keyboardType == "27") {
      type = TKKeyBoardType.TFLoginRandomNum;
    } else if (keyboardType == "30") {
      type = TKKeyBoardType.KCBStock;
    } else if (keyboardType == "31") {
      type = TKKeyBoardType.OptionSZFast;
    } else if (keyboardType == "32") {
      type = TKKeyBoardType.OptionSHFast;
    } else if (keyboardType == "33") {
      type = TKKeyBoardType.OptionSZNum;
    } else if (keyboardType == "34") {
      type = TKKeyBoardType.OptionSHNum;
    }
    return type;
  }
}