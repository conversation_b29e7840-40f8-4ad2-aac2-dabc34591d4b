import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 通知原生机器设备震动
 */
export class TKPlugin50116 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    TKDeviceHelper.startVibration();
    return resultVO;
  }
}