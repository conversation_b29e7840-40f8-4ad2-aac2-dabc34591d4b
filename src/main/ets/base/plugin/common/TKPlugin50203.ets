/**
 *  进行软件版本检测，并自动更新最新版本
 */
import { TKCacheManager } from '../../../util/cache/TKCacheManager';
import { TKObjectHelper } from '../../../util/data/TKObjectHelper';
import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKLog } from '../../../util/logger/TKLog';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKNetHelper } from '../../../util/net/TKNetHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKReqParamVO } from '../../mvc/model/TKReqParamVO';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKNotificationCenter } from '../../notification/TKNotificationCenter';
import { TKUpdateMode, TKVersionManager } from '../../version/TKVersionManager';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50203 extends TKBasePlugin {
  //是否有更新
  public static readonly NOTE_APP_ISUPDATE: string = "app_isUpdate";

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //更新检测地址
    let url: string = TKMapHelper.getString(param, "url", TKSystemHelper.getConfig("update.checkUrl"));
    let paramConfig: Record<string, Object | undefined> = param;
    paramConfig["url"] = undefined;
    if (TKMapHelper.getString(paramConfig, "funcNo") === "50203") {
      paramConfig["funcNo"] = undefined;
    }
    let reqMode: string = TKSystemHelper.getConfig("update.reqMode");
    let reqParamVO: TKReqParamVO = this.commonService.createReqParamByReqMode(url, reqMode);
    reqParamVO.isPost = false;
    reqParamVO.isReturnList = false;
    if (reqParamVO.reqParam) {
      TKObjectHelper.assign(paramConfig, reqParamVO.reqParam);
    }
    //功能号
    if (TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "funcNo"))) {
      paramConfig["funcNo"] = 901916;
    }
    //应用类型1：安卓，2：IOS  3：H5 5：鸿蒙
    if (TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "channel"))) {
      paramConfig["channel"] = "5";
    }
    //升级版本序号
    paramConfig["versionsn"] = TKSystemHelper.getVersionCode();
    //升级版本序号
    paramConfig["version_code"] = TKSystemHelper.getVersionCode();
    //应用包名
    if (TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "soft_no"))) {
      let soft_no: string = TKSystemHelper.getConfig("update.appCode", TKSystemHelper.getAppIdentifier());
      paramConfig["soft_no"] = soft_no;
    }
    //ip地址
    paramConfig["ip"] = TKNetHelper.getIP();
    //应用市场
    if (TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "app_market"))) {
      paramConfig["app_market"] = TKSystemHelper.getConfig("update.market");
    }
    //设备类型
    paramConfig["device_model"] = TKDeviceHelper.getDevicePlatformInfo();
    //设备ID
    if (TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "device_id"))) {
      paramConfig["device_id"] = TKDeviceHelper.getDeviceUUID();
    }
    //设备分辨率
    paramConfig["device_showp"] = TKDeviceHelper.getScreenDisplay();
    //操作系统版本
    paramConfig["device_os_version"] = TKDeviceHelper.getDeviceSysVersion();
    //运营商
    paramConfig["device_network_operator"] = TKNetHelper.getPhoneOperator();
    //网络制式
    paramConfig["device_network_type"] = TKNetHelper.getNetworkType();
    //原生版本序号
    paramConfig["native_version_code"] = TKSystemHelper.getAppVersionCode();
    //原生版本号
    paramConfig["native_version"] = TKSystemHelper.getAppVersion();
    //手机号码
    if (TKStringHelper.isBlank(TKMapHelper.getString(paramConfig, "phone_num"))) {
      let mobile: string | undefined = TKCacheManager.shareInstance().getFileCacheData("update.mobile");
      if (TKStringHelper.isNotBlank(mobile)) {
        paramConfig["phone_num"] = mobile;
      }
    }
    reqParamVO.reqParam = paramConfig;
    this.commonService.serviceInvoke(reqParamVO, (resultVO) => {
      if (resultVO.errorNo == 0) {
        let data: Record<string, Object> = resultVO.results as Record<string, Object>;
        //内部版本升级序号
        let versionSn: number = TKMapHelper.getNumber(data, "version_code");
        //版本名称
        let version: string = TKMapHelper.getString(data, "version_name");
        //版本Md5
        let versionMd5: string = TKMapHelper.getString(data, "version_md5");
        //下载地址
        let download_url: string = TKMapHelper.getString(data, "download_url");
        //解压密码
        let unzip_pwd: string = TKMapHelper.getString(data, "unzip_pwd");
        //强制更新标志
        let update_flag: string = TKMapHelper.getString(data, "update_flag");
        //强制更新提醒次数
        let md_update_num: number = TKMapHelper.getNumber(data, "md_update_num");
        //是否H5
        let isH5: string = TKMapHelper.getString(data, "isH5");
        //当前版本序号
        let currentVersionSn: number = Number(TKSystemHelper.getVersionCode());
        //是否静默更新
        let isShowUpdateTip: boolean = !(TKSystemHelper.getConfig("update.isShowUpdateTip") == "0")
        if (update_flag == "2") {
          isShowUpdateTip = false;
        }
        //判断是否进行更新
        if (versionSn > currentVersionSn) {
          TKVersionManager.shareInstance().unzippwd = unzip_pwd;
          TKVersionManager.shareInstance().isShowUpdateTip = isShowUpdateTip;
          if (update_flag != "2" || isH5 != "1") {
            let updateMode: TKUpdateMode = TKUpdateMode.Confirm;
            if (update_flag == "1" || update_flag == "2") {
              updateMode = TKUpdateMode.Alert;
            } else {
              md_update_num = 0;
            }
            //强制更新提醒次数处理
            if (updateMode == TKUpdateMode.Alert) {
              if (md_update_num > 0) {
                let cacheUpdateNumKey: string = `cache_update_version_${versionSn}_num`;
                let cacheUpdateLastNumKey: string = `cache_update_version_${versionSn}_lastnum`;
                let cacheUpdateNum: string | undefined =
                  TKCacheManager.shareInstance().getFileCacheData(cacheUpdateNumKey);
                if (!cacheUpdateNum || Number(cacheUpdateNum) != md_update_num) {
                  TKCacheManager.shareInstance().deleteFileCacheData(cacheUpdateLastNumKey);
                  TKCacheManager.shareInstance().saveFileCacheData(cacheUpdateNumKey, md_update_num + "");
                }
                let cacheLastUpdateNum: string | undefined =
                  TKCacheManager.shareInstance().getFileCacheData(cacheUpdateLastNumKey);
                if (TKStringHelper.isNotBlank(cacheLastUpdateNum)) {
                  md_update_num = Number(cacheLastUpdateNum);
                }
                if (md_update_num > 0) {
                  updateMode = TKUpdateMode.Confirm;
                  md_update_num--;
                  TKCacheManager.shareInstance().saveFileCacheData(cacheUpdateLastNumKey, md_update_num + "");
                }
              }
            }
            data["md_update_num"] = md_update_num;
            TKVersionManager.shareInstance().updateUIDelegate?.showUpdateUIWindow(updateMode, data, () => {
              TKVersionManager.shareInstance()
                .updateSoftware(download_url, version, versionSn + "", versionMd5, isH5 == "1");
            });
          } else {
            TKVersionManager.shareInstance()
              .updateSoftware(download_url, version, versionSn + "", versionMd5, isH5 == "1");
          }
          TKNotificationCenter.defaultCenter.postNotificationName(TKPlugin50203.NOTE_APP_ISUPDATE,
            { "app_isUpdate": "1", "update_flag": update_flag } as Record<string, Object>);
        } else {
          TKLog.info(`无需更新，当前版本序号:${currentVersionSn} ,服务器版本序号:${versionSn}`);
          TKNotificationCenter.defaultCenter.postNotificationName(TKPlugin50203.NOTE_APP_ISUPDATE,
            { "app_isUpdate": "0", "update_flag": update_flag } as Record<string, Object>);
        }
      } else {
        TKNotificationCenter.defaultCenter.postNotificationName(TKPlugin50203.NOTE_APP_ISUPDATE,
          { "app_isUpdate": "0", "update_flag": "" } as Record<string, Object>);
      }
    });
    return resultVO;
  }
}