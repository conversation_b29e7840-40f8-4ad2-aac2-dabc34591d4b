/**
 *   H5保存图片到相册
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';
import { TKPickerHelper } from '../../../util/ui/TKPickerHelper';

export class TKPlugin50131 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    let base64Image: string = TKMapHelper.getString(param, "base64Image");
    if (TKStringHelper.isBlank(base64Image)) {
      resultVO.errorNo = -5013101;
      resultVO.errorInfo = "保存的图片不能为空!";
      return resultVO;
    }
    if (base64Image.startsWith("data:image/")) {
      let base64Flag: string = ";base64,";
      let base64Index: number = base64Image.indexOf(base64Flag);
      if (base64Index > 0) {
        base64Index += base64Flag.length;
        base64Image = base64Image.substring(base64Index, base64Image.length);
      }
    }
    this.saveImage(base64Image);
    return resultVO;
  }

  private async saveImage(base64Image: string) {
    let saveFlag: boolean = await TKPickerHelper.savePhotoToGallery(base64Image);
    if (saveFlag) {
      TKDialogHelper.showToast("保存图片到相册成功");
    } else {
      TKDialogHelper.showToast("保存图片到相册失败");
    }
  }
}