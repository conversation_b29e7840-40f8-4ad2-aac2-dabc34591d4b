import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  获取设备SDK版本号
 */
export class TKPlugin50021 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //获取设备SDK版本号
    let systemVersion: string = TKDeviceHelper.getDeviceSysVersion();
    let dataRow: Record<string, Object> = {
      "systemVersion": systemVersion
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}