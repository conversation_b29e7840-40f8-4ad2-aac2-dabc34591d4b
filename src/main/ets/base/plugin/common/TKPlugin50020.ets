import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *   获取设备型号
 */
export class TKPlugin50020 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //获取设备型号
    let devicePlatformInfo: string = TKDeviceHelper.getDevicePlatformInfo()
    let dataRow: Record<string, Object> = {
      "deviceType": devicePlatformInfo
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}