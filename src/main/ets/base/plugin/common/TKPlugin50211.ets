/**
 *  关闭原生键盘
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50211 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //关闭模式（0：直接关闭，1：切换输入框关闭）
    let mode: string = TKMapHelper.getString(param, "mode", "1");
    TKDialogHelper.closeKeyBoard();
    return resultVO;
  }
}