/**
 *  进行软件的版本升级或者安装
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKVersionManager } from '../../version/TKVersionManager';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50201 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //APP的下载地址
    let url: string = TKMapHelper.getString(param, "url");
    //APP下载类型(0：原生，1：H5）
    let type: string = TKMapHelper.getString(param, "type");
    let isUpdateH5: boolean = (type === "1");
    //APP版本号
    let version: string = TKMapHelper.getString(param, "version");
    //APP内部版本升级序号
    let versionSn: string = TKMapHelper.getString(param, "versionSn");
    //APP版本Md5值
    let versionMd5: string = TKMapHelper.getString(param, "versionMd5");
    //下载更新提示语
    let tip: string = TKMapHelper.getString(param, "tip");
    //是否显示提示框进度条等提示(0：不显示，1：显示)
    let isShowUpdateTip: boolean = TKMapHelper.getBoolean(param, "isShowUpdateTip", true);
    if (TKStringHelper.isBlank(url)) {
      resultVO.errorNo = -5020101;
      resultVO.errorInfo = "APP下载地址不能为空!";
    } else if (TKStringHelper.isBlank(type)) {
      resultVO.errorNo = -5020102;
      resultVO.errorInfo = "APP下载类别不能为空!";
    } else if (TKStringHelper.isBlank(version)) {
      resultVO.errorNo = -5020103;
      resultVO.errorInfo = "APP下载版本不能为空!";
    } else if (TKStringHelper.isBlank(versionSn)) {
      resultVO.errorNo = -5020104;
      resultVO.errorInfo = "APP内部版本升级序号不能为空!";
    } else {
      TKVersionManager.shareInstance().isShowUpdateTip = isShowUpdateTip;
      TKVersionManager.shareInstance().updateSoftware(url, version, versionSn, versionMd5, isUpdateH5, tip);
    }
    return resultVO;
  }
}