import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  通知原生打开模块
 */
export class TKPlugin50101 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let params: Record<string, Object> = TKMapHelper.getJSON(param, "params");
    param["params"] = params;
    let resultVO: TKResultVO = new TKResultVO();
    this.onPluginEvent("50101", param);
    return resultVO;
  }
}