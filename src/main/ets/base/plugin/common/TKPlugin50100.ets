import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  通知原生H5加载完毕
 */
export class TKPlugin50100 extends TKBasePlugin {
  public constructor() {
    super();
    this.isCache = false;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    if (this.currentJSProxyController) {
      this.currentJSProxyController.onPluginEvent("50100");
    }
    return resultVO;
  }
}