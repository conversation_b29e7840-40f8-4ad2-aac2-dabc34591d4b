/**
 *  打开软件
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50202 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //APP的URL唯一标志
    let scheme: string = TKMapHelper.getString(param, "scheme");
    if (TKStringHelper.isBlank(scheme)) {
      resultVO.errorNo = -5020201;
      resultVO.errorInfo = "APP标志不能为空!";
    } else {
      if (scheme.indexOf("://") < 0) {
        scheme += "://";
      }
      //是否安装过该软件APP
      let isInstall: boolean = TKSystemHelper.isInstallAppWithURL(scheme);
      if (isInstall) {
        //打开软件
        TKSystemHelper.openAppURL(scheme);
      } else {
        resultVO.errorNo = -5020202;
        resultVO.errorInfo = "APP尚未安装!";
      }
    }
    return resultVO;
  }
}