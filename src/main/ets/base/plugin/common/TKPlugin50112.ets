import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKHttpRoomServerListener } from '../../mvc/service/dao/http/client/gateway/TKHttpRoomServerListener';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 获取备用站点中的最优站点
 */
export class TKPlugin50112 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //服务器站点名称
    let serverName: string = TKMapHelper.getString(param, "serverName");
    if (TKStringHelper.isBlank(serverName)) {
      resultVO.errorNo = -5011201;
      resultVO.errorInfo = "服务器站点名称不能为空";
    } else {
      //获取最快站点
      let url: string = TKHttpRoomServerListener.shareInstance().getFlastHost(serverName);
      if (TKStringHelper.isBlank(url)) {
        resultVO.errorNo = -5011202;
        resultVO.errorInfo = "获取站点失败";
      } else {
        let dataRow: Record<string, Object> = {
          "url": url
        };
        resultVO.results = dataRow;
      }
    }
    return resultVO;
  }
}