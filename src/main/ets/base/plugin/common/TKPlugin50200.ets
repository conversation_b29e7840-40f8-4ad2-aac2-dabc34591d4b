/**
 *  获取软件是否安装
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50200 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //APP的URL唯一标志
    let scheme: string = TKMapHelper.getString(param, "scheme");
    if (TKStringHelper.isBlank(scheme)) {
      resultVO.errorNo = -5020001;
      resultVO.errorInfo = "APP标志不能为空!";
    } else {
      //是否安装过该软件APP
      let isInstall: boolean = TKSystemHelper.isInstallAppWithURL(scheme);
      let dataRow: Record<string, Object> = {
        "isInstall": isInstall ? "1" : "0"
      };
      resultVO.results = dataRow;
    }
    return resultVO;
  }
}