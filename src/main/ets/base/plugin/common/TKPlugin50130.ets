/**
 *   H5保存文字到粘贴板
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKPasteboardHelper } from '../../../util/system/TKPasteboardHelper';
import { TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50130 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    let copyText: string = TKMapHelper.getString(param, "copyText");
    if (TKStringHelper.isBlank(copyText)) {
      resultVO.errorNo = -5013001;
      resultVO.errorInfo = "复制的文本不能为空!";
      return resultVO;
    }
    TKPasteboardHelper.copyDataText(copyText);
    TKDialogHelper.showToast("内容已复制到剪贴板...");
    return resultVO;
  }
}