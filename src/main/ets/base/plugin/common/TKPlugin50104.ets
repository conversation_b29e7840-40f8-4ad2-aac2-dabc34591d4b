import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKThemeManager } from '../../theme/TKThemeManager';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  设置主题
 */
export class TKPlugin50104 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //主题名称
    let theme: string = TKMapHelper.getString(param, "theme");
    if (TKStringHelper.isBlank(theme)) {
      resultVO.errorNo = -5010401;
      resultVO.errorInfo = "主题名称不能为空!";
    } else {
      //设置主题皮肤
      TKThemeManager.shareInstance().theme = theme;
    }
    return resultVO;
  }
}