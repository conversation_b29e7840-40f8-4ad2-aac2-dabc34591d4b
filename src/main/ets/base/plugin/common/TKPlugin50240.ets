/**
 *查看pdf文件
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKNotificationCenter } from '../../notification/TKNotificationCenter';
import { TKRouterHelper } from '../../router/TKRouterHelper';
import { TKThemeManager } from '../../theme/TKThemeManager';
import { TKBasePlugin } from '../TKBasePlugin';

import('../../mvc/page/common/pdf/TKComPdfPage');

export class TKPlugin50240 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //文件地址
    let url: string = TKMapHelper.getString(param, "url");
    //状态栏颜色
    let statusColor: Object =
      TKMapHelper.getObject(param, "statusColor",
        (TKThemeManager.shareInstance().getCssRulesetByClassName("TKPdfPageTitle").backgroundColor ??
          "#FF1061FF"));
    //状态栏风格(0:黑色，1：白色)
    let statusStyle: string = TKMapHelper.getString(param, "statusStyle");
    //文件标题
    let title: string = TKMapHelper.getString(param, "title", "内容查看");
    //标题文字颜色
    let titleColor: Object = TKMapHelper.getObject(param, "titleColor",
      TKThemeManager.shareInstance().getCssRulesetByClassName("TKPdfPageTitle").color ?? "#FFFFFFFF");
    //进度条颜色
    let progressColor: Object = TKMapHelper.getObject(param, "progressColor") as Object;
    //是否显示加载过渡页
    let isShowLoading: boolean = TKMapHelper.getBoolean(param, "isShowLoading", true);
    //显示加载过渡页的GIF名称
    let loadingGIF: string = TKMapHelper.getString(param, "loadingGIF");
    //显示加载失败的图片名称
    let loadFailedImage: string = TKMapHelper.getString(param, "loadFailedImage");
    //返回按钮模式（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
    let btnMode: string = TKMapHelper.getString(param, "btnMode", "0");
    //是否显示下载按钮
    let isShowDownLoadBtn: boolean = TKMapHelper.getBoolean(param, "isShowDownLoadBtn", false);
    //加载超时时间,单位秒
    let loadTimeOut: Object = TKMapHelper.getObject(param, "loadTimeOut") as Object;
    //文件后缀
    let suffix: string = TKMapHelper.getString(param, "suffix", "pdf");
    //阅读停留查看最少时间
    let readTime: number = TKMapHelper.getNumber(param, "readTime");
    //确认按钮背景颜色(例如#FFFFFF)
    let okBtnReadBgColor: Object = TKMapHelper.getObject(param, "okBtnReadBgColor") as Object;
    //确认按钮文字颜色(例如#FFFFFF)
    let okBtnReadTextColor: Object = TKMapHelper.getObject(param, "okBtnReadTextColor") as Object;
    //确认按钮倒计时背景颜色(例如#FFFFFF)
    let okBtnReadTimerBgColor: Object = TKMapHelper.getObject(param, "okBtnReadTimerBgColor") as Object;
    //确认按钮倒计时文字颜色(例如#FFFFFF)
    let okBtnReadTimerTextColor: Object = TKMapHelper.getObject(param, "okBtnReadTimerTextColor") as Object;
    //确认按钮文本（有传该文本值才展示确认按钮）
    let okBtnReadText: Object = TKMapHelper.getObject(param, "okBtnReadText") as Object;
    //是否执行关闭回调(0:不执行，1：执行）默认是0
    let isCallBackForClose: boolean = TKMapHelper.getBoolean(param, "isCallBackForClose", false);
    //右侧按钮（右侧按钮是文本模式传文本内容；是图片模式传图片名称）
    let rightBtnTxt: string = TKMapHelper.getString(param, "rightBtnTxt");
    //右侧按钮模式（0：文本，1：图片）
    let rightBtnMode: string = TKMapHelper.getString(param, "rightBtnMode", "0");
    //右侧按钮动作，方便上层区分按钮点击逻辑
    let rightBtnAction: string = TKMapHelper.getString(param, "rightBtnAction", "action");
    //右侧按钮动作附加JSON格式参数
    let rightBtnActionParam = TKMapHelper.getJSON(param, "rightBtnActionParam", {});

    if (TKStringHelper.isBlank(url)) {
      resultVO.errorNo = -5024001;
      resultVO.errorInfo = "pdf文件地址不能为空!";
    } else {
      TKRouterHelper.pushNamedRoute({
        name: 'TKComPdfPage',
        params: {
          //基础控制
          "moduleName": this.moduleName,
          //加载链接
          "url": url,
          "title": title,
          "suffix": suffix,
          "loadTimeOut": loadTimeOut,
          "isCallBackForClose": isCallBackForClose,
          //标题样式
          "statusStyle": statusStyle,
          "statusBarColor": statusColor,
          "titleColor": titleColor,
          "titleBgColor": statusColor,
          //标题按钮
          "btnMode": btnMode,
          "isShowDownLoadBtn": isShowDownLoadBtn,
          "rightBtnTxt": rightBtnTxt,
          "rightBtnMode": rightBtnMode,
          "rightBtnAction": rightBtnAction,
          "rightBtnActionParam": rightBtnActionParam,
          //底部按钮
          "okBtnReadBgColor": okBtnReadBgColor,
          "okBtnReadTextColor": okBtnReadTextColor,
          "okBtnReadTimerBgColor": okBtnReadTimerBgColor,
          "okBtnReadTimerTextColor": okBtnReadTimerTextColor,
          "okBtnReadText": okBtnReadText,
          "readTime": readTime,
          //加载效果
          "progressColor": progressColor,
          "isShowLoading": isShowLoading,
          "loadingGIF": loadingGIF,
          "loadFailedImage": loadFailedImage,
        },
        pageStyle: {
          layoutFullScreen: true
        },
        onResult: (data) => {
          let action: string = TKMapHelper.getString(data, "action");
          if (TKStringHelper.isNotBlank(action)) {
            if (this.isH5) {
              //广播通知
              TKNotificationCenter.defaultCenter.postNotificationName(action, this.currentJSProxyController, data);
            } else {
              this.harmonyCallPluginCallBack(data);
            }
          } else {
            this.harmonyCallPluginCallBack(data);
          }
        },
        navComponentOrPathStack: this.navComponentOrPathStack
      });
    }
    return resultVO;
  }
}