import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKAppEngine } from '../../engine/TKAppEngine';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *   统一模块交互消息定义
 */
export class TKPlugin50500 extends TKBasePlugin {
  public constructor() {
    super();
    this.isCache = false;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    //消息编号
    let msgNo: string = TKMapHelper.getString(param, "msgNo");
    //来源模块名称，为空代表来源是当前正在显示的模块
    let sourceModule: string = TKMapHelper.getString(param, "sourceModule");
    //目标模块名称
    let targetModule: string = TKMapHelper.getString(param, "targetModule");
    //排除模块名称
    let excludeModule: string = TKMapHelper.getString(param, "excludeModule");
    let excludeModules: Array<string> = excludeModule.split("|");
    //动作类型（0：弹层打开，1：PUSH打开，2：关闭模块，3：切换模块，4：广播通知）
    let action: string = TKMapHelper.getString(param, "action", "4");
    //附加参数
    let paramExt: Record<string, Object> = TKMapHelper.getJSON(param, "param");
    //是否绿色通道
    let isGreen: boolean = TKMapHelper.getBoolean(param, "isGreen");
    let resultVO: TKResultVO = new TKResultVO();
    if (TKStringHelper.isBlank(msgNo)) {
      resultVO.errorNo = -5050001;
      resultVO.errorInfo = "消息功能编号不能为空";
    } else if (TKStringHelper.isBlank(targetModule)) {
      resultVO.errorNo = -5050002;
      resultVO.errorInfo = "目标模块名不能为空";
    } else {
      TKAppEngine.shareInstance().sendModuleMessage({
        funcNo: msgNo,
        action: parseInt(action) < 2 ? 0 : parseInt(action),
        sourceModule: sourceModule,
        targetModule: targetModule,
        excludeModules: excludeModules,
        param: paramExt,
        isGreen: isGreen,
        callBackFunc: (resultVO) => {
          if (this.isUseJsCallBack) {
            let result: Record<string, Object> = {};
            result["error_no"] = resultVO.errorNo;
            result["error_info"] = resultVO.errorInfo;
            result["results"] = resultVO.results ?? [];
            this.harmonyCallPluginCallBack(result);
          }
        }
      });
    }
    return resultVO;
  }
}