import { TKLog } from '../../../util/logger/TKLog';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *   统计H5的日志
 */
export class TKPlugin50501 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    //日志内容
    let content: string = TKMapHelper.getString(param, "content");
    if (TKStringHelper.isNotBlank(content)) {
      TKLog.info("[TKWebView console]:" + content);
    }
    let resultVO: TKResultVO = new TKResultVO();
    return resultVO;
  }
}