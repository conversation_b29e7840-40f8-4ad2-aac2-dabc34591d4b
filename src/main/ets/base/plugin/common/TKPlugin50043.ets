import { TKCacheManager } from '../../../util/cache/TKCacheManager';
import { TKAesHelper } from '../../../util/crypto/TKAesHelper';
import { TKPasswordGenerator } from '../../../util/crypto/TKPasswordGenerator';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  从用户Cache缓存目录获取数据
 */
export class TKPlugin50043 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    //键
    let key: string = TKMapHelper.getString(param, "key");
    //是否加密
    let isEncrypt: boolean = TKMapHelper.getBoolean(param, "isEncrypt");
    let resultVO: TKResultVO = new TKResultVO();
    if (TKStringHelper.isBlank(key)) {
      resultVO.errorNo = -5004301;
      resultVO.errorInfo = "字段名不能为空!";
    } else {
      //获取内存缓存对象
      let value: Object = TKCacheManager.shareInstance().getFileCacheData(key) as Object;
      if (isEncrypt && typeof value === "string") {
        value =
          TKAesHelper.stringWithAesEncrypt(value.toString(), TKPasswordGenerator.shareInstance().generatorPassword());
      }
      let dataRow: Record<string, Object> = {
        "value": value ?? ""
      };
      resultVO.results = dataRow;
    }
    return resultVO;
  }
}