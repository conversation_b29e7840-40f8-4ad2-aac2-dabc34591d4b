import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 显示Toast提示
 */
export class TKPlugin50106 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //提示内容
    let content: string = TKMapHelper.getString(param, "content");
    if (TKStringHelper.isBlank(content)) {
      resultVO.errorNo = -5010601;
      resultVO.errorInfo = "提示内容不能为空!";
    } else {
      //进行Toast提示
      TKDialogHelper.showToast(content);
    }
    return resultVO;
  }
}