import { TKContextHelper } from '../../../util/system/TKContextHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 退出APP应用
 */
export class TKPlugin50105 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    TKContextHelper.getCurrentContext().getApplicationContext().killAllProcesses();
    return resultVO;
  }
}