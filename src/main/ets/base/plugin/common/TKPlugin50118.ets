import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';
import { rcp } from '@kit.RemoteCommunicationKit';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKJSProxyController } from '../../../components/webview/TKJSProxyController';

/**
 * 代理发送http/https相关的网络请求
 */
export class TKPlugin50118 extends TKBasePlugin {
  public constructor() {
    super();
    this.isCache = false;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let protocol: string = TKMapHelper.getString(param,
      "protocol"); //网络协议（0：HTTP/HTTPS 1:行情长连接 2:交易长连接 3:资讯长连接 4:天风统一接入长连接 5：思迪统一接入长连接 6：HTTP/HTTPS转思迪统一接入长连接 7：通用Socket长连接）
    let netLinkMode: string = TKMapHelper.getString(param, "netLinkMode"); //网络链路(0:普通链路，1：国密链路)，默认是0
    let url: string = TKMapHelper.getString(param, "url") //网络地址 (URL地址或站点名称)
    let paramMap: Record<string, Object> = TKMapHelper.getJSON(param, "paramMap");
    let headerMap: Record<string, Object> = TKMapHelper.getJSON(param, "headerMap");
    let flowNo: string = TKMapHelper.getString(param, "flowNo"); //请求流水号
    let isPost: boolean = TKMapHelper.getBoolean(param, "isPost", true); //是否post
    let timeOut: number = TKMapHelper.getNumber(param, "timeOut", 30); //超时时间
    //请求加密模式(0:正常，1:Http加签，2：Http加密，3：Http加密加签，4:Socket加密，5：Socket压缩，6：Socket压缩加密，7：Http加密加签并加密响应包，8：微服务正常，9：微服务加签，10：微服务加密加签，11：微服务加密加签并加密响应包)
    let mode: string = TKMapHelper.getString(param, "mode");
    //是否开启重复请求拦截机制，默认不开启
    let isFilterRepeatRequestL: boolean = TKMapHelper.getBoolean(param, "isFilterRepeatRequest");
    //重复请求，进行请求拦截时间，单位毫秒
    let filterRepeatRequestTimeOut: number = TKMapHelper.getNumber(param, "filterRepeatRequestTimeOut");
    //是否进行URL编码，默认编码
    let isEncodeURL: boolean = TKMapHelper.getBoolean(param, "isEncodeURL", true);
    //是否自动添加系统公共入参，默认添加
    let isAutoAddSysComParam: boolean = TKMapHelper.getBoolean(param, "isAutoAddSysComParam", true);
    //是否全局请求，默认NO
    let isGlobRequest: boolean = TKMapHelper.getBoolean(param, "isGlobRequest");
    let resultVO: TKResultVO = new TKResultVO();
    if (TKStringHelper.isBlank(url)) {
      resultVO.errorNo = -5011801;
      resultVO.errorInfo = "网络请求地址不能为空!";
    } else if (TKStringHelper.isBlank(flowNo)) {
      resultVO.errorNo = -5011802;
      resultVO.errorInfo = "网络请求流水号不能为空!";
    } else {
      this.commonService.doProxyNetWorkService(this.moduleName, protocol, url, paramMap, headerMap, isPost, timeOut,
        mode, isFilterRepeatRequestL, filterRepeatRequestTimeOut, isEncodeURL, isAutoAddSysComParam, isGlobRequest,
        netLinkMode, (resultVO) => {
          let resultDic: Record<string, Object> = resultVO.originData as Record<string, Object> ?? {};
          resultDic["errorType"] = resultVO.errorType;
          //行情兼容处理
          if (!resultDic["errorNo"]) {
            resultDic["errorNo"] = resultVO.errorNo;
            resultDic["errorInfo"] = resultVO.errorInfo;
          }
          let cookies: Array<Object> | undefined = resultVO.cookies;
          if (cookies && cookies.length > 0) {
            let jsCookies: Array<Object> = [];
            for (let cookie of cookies) {
              let cookieName: string = (cookie as rcp.ResponseCookie).name;
              let cookieValue: string = (cookie as rcp.ResponseCookie).value ?? "";
              let jsCookie: Record<string, Object> = {};
              jsCookie[cookieName] = cookieValue;
              jsCookies.push(jsCookie);
            }
            resultDic["cookies"] = jsCookies;
          }
          if (resultVO.respHeaderFieldDic) {
            resultDic["respHeaderFiledDic"] = resultVO.respHeaderFieldDic;
          }
          if (this.isH5) {
            if (this.isUseJsCallBack) {
              this.harmonyCallPluginCallBack(resultDic);
            } else {
              if (this.currentJSProxyController) {
                this.currentJSProxyController.sendHttpsCallbackToH5(flowNo, resultDic);
              } else {
                if (TKJSProxyController.harmonyCallJSFunctionFilter &&
                TKJSProxyController.harmonyCallJSFunctionFilter.onFilter) {
                  TKJSProxyController.harmonyCallJSFunctionFilter.onFilter(this.webControllerName, "httpsCallback",
                    [flowNo, resultDic]);
                }
              }
            }
          } else {
            this.harmonyCallPluginCallBack(resultDic);
          }
        });
    }
    return resultVO;
  }
}