/**
 *  获取APP进入后台的状态
 */
import { TKAppEngine } from '../../engine/TKAppEngine';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50124 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    let result: Record<string, Object> = {
      "appToBackFlag": TKAppEngine.shareInstance().isApplicationDidEnterBackground() ? "1" : "0"
    };
    resultVO.results = result;
    return resultVO;
  }
}