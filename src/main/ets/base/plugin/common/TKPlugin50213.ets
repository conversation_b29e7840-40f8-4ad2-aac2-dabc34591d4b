/**
 *  修改原生键盘
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50213 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //附加参数
    let data: Record<string, Object> = TKMapHelper.getJSON(param, "data");
    if (!data || Object.entries(data).length == 0) {
      resultVO.errorNo = -5021301;
      resultVO.errorInfo = "修改内容不能为空!";
    } else {

    }
    return resultVO;
  }
}