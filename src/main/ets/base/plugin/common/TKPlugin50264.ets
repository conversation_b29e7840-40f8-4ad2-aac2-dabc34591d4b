/**
 * 手势密码通用插件
 */
import { TKPatternLock, TKPatternLockOption, TKPatternLockType } from '../../../components/patternlock/TKPatternLock';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKComponentContent, TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50264 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //手势密码类型
    let flag: string = TKMapHelper.getString(param, "flag", "1");
    //账户
    let account: string = TKMapHelper.getString(param, "account");
    //是否可返回
    let isCanBack: boolean = TKMapHelper.getBoolean(param, "isCanBack", false);
    //返回键位置
    let position: string = TKMapHelper.getString(param, "position", "0");
    //最大输入错误次数
    let errorNum: number = TKMapHelper.getNumber(param, "errorNum", 5);
    //是否开启防截屏
    let isInterceptScreenshot: boolean = TKMapHelper.getBoolean(param, "isInterceptScreenshot", false);
    //弹出设置手势密码
    if (flag == "1") {
      TKDialogHelper.createPatternLock({
        type: TKPatternLockType.Setting,
        userAccount: account,
        isCanBack: isCanBack,
        backPosition: position,
        maxErrorNum: errorNum,
        isInterceptScreenshot: isInterceptScreenshot,
        onSetPassword: (result) => {
          this.onSetPassword(result);
        },
        onBackPassword: () => {
          this.onBackPassword();
        }
      }).open();
    } else if (flag == "2") {
      //弹出修改手势密码
      let patternLock: TKComponentContent<TKPatternLockOption> = TKDialogHelper.createPatternLock({
        type: TKPatternLockType.Reset,
        userAccount: account,
        isCanBack: isCanBack,
        backPosition: position,
        maxErrorNum: errorNum,
        isInterceptScreenshot: isInterceptScreenshot,
        onReSetPassword: (result) => {
          patternLock.update({
            type: TKPatternLockType.Setting
          });
        },
        onSetPassword: (result) => {
          this.onResetPassword(result);
        },
        onBackPassword: () => {
          this.onBackPassword();
        },
        onChangeAccount: () => {
          this.onChangeAccount();
        },
        onForgetPassword: () => {
          this.onForgetPassword();
        }
      });
      if (TKStringHelper.isNotBlank(TKPatternLock.getUserPassword(account))) {
        patternLock.open();
      }
    } else if (flag == "0") {
      //清除手势密码
      let patternLock: TKComponentContent<TKPatternLockOption> = TKDialogHelper.createPatternLock({
        type: TKPatternLockType.Cancel,
        userAccount: account,
        isCanBack: isCanBack,
        backPosition: position,
        maxErrorNum: errorNum,
        isInterceptScreenshot: isInterceptScreenshot,
        onCancelPassword: () => {
          this.onCancelPassword();
        },
        onBackPassword: () => {
          this.onBackPassword();
        },
        onChangeAccount: () => {
          this.onChangeAccount();
        },
        onForgetPassword: () => {
          this.onForgetPassword();
        }
      });
      if (TKStringHelper.isNotBlank(TKPatternLock.getUserPassword(account))) {
        patternLock.open();
      }
    } else if (flag == "3") {
      //强制清除手势密码
      TKPatternLock.clearUserPassword(account);
      this.forceCancelPassword();
    }
    return resultVO;
  }

  /**
   * 设置手势密码
   * @param result
   */
  private onSetPassword(result: string) {
    let param: Record<string, Object> = {
      "funcNo": "50265",
      "flag": "1",
      "result": result
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   * 返回关闭手势密码
   */
  private onBackPassword() {
    let param: Record<string, Object> = {
      "funcNo": "50265",
      "flag": "4",
      "result": "0"
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   * 取消手势密码
   */
  private onCancelPassword() {
    let param: Record<string, Object> = {
      "funcNo": "50265",
      "flag": "0",
      "result": "0"
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   *  修改重置密码
   */
  private onResetPassword(result: string) {
    let param: Record<string, Object> = {
      "funcNo": "50265",
      "flag": "2",
      "result": result
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   *
   *  强制取消密码
   * @param result 密码
   * @return
   */
  private forceCancelPassword() {
    let param: Record<string, Object> = {
      "funcNo": "50265",
      "flag": "5",
      "result": "0"
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   * 忘记密码
   */
  private onForgetPassword() {
    let param: Record<string, Object> = {
      "funcNo": "50262",
      "type": "0"
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   * 切换账号
   */
  private onChangeAccount() {
    let param: Record<string, Object> = {
      "funcNo": "50262",
      "type": "1"
    }
    this.harmonyCallPluginCallBack(param);
  }
}