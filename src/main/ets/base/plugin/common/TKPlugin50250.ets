/**
 * 弹出日期控件
 */
import { TKDateHelper } from '../../../util/date/TKDateHelper';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50250 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //年
    let year: string = TKMapHelper.getString(param, "year");
    //月
    let month: string = TKMapHelper.getString(param, "month");
    //日
    let day: string = TKMapHelper.getString(param, "day");
    //选择器
    let selector: string = TKMapHelper.getString(param, "selector");
    //主颜色
    let mainColor: string = TKMapHelper.getString(param, "mainColor", "#FF1A9BFF");
    if (TKStringHelper.isBlank(year)) {
      resultVO.errorNo = -5025001;
      resultVO.errorInfo = "年份不能为空！";
    } else if (TKStringHelper.isBlank(month)) {
      resultVO.errorNo = -5025002;
      resultVO.errorInfo = "月份不能为空！";
    } else if (TKStringHelper.isBlank(day)) {
      resultVO.errorNo = -5025003;
      resultVO.errorInfo = "日期不能为空！";
    } else {
      //初始化的值
      let selectedDate: Date = TKDateHelper.parseDateStr(`${year}-${month}-${day}`);
      if (!selectedDate) {
        selectedDate = new Date();
      }
      DatePickerDialog.show({
        alignment: DialogAlignment.Bottom,
        selected: selectedDate,
        disappearTextStyle: { color: Color.Gray, font: { size: '14fp', weight: FontWeight.Regular } },
        textStyle: { color: Color.Gray, font: { size: '18fp', weight: FontWeight.Normal } },
        selectedTextStyle: { color: '#FF191919', font: { size: '22fp', weight: FontWeight.Bold } },
        acceptButtonStyle: {
          fontSize: '20fp',
          fontColor: mainColor
        },
        cancelButtonStyle: { fontSize: '20fp', fontColor: Color.Gray },
        onDateAccept: (value: Date) => {
          let dateStr: string = TKDateHelper.formatDate(value, "YYYY-MM-DD");
          let result: Record<string, Object> = {
            "selector": selector,
            "date": dateStr,
            "funcNo": "50251"
          };
          this.harmonyCallPluginCallBack(result);
        },
        onCancel: () => {
        }
      });
    }
    return resultVO;
  }
}