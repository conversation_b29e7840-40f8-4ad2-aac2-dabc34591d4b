import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 获取应用配置
 */
export class TKPlugin50000 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    //一级标签
    let storeKey: string = TKMapHelper.getString(param, "storeKey");
    //二级标签
    let innerKey: string = TKMapHelper.getString(param, "innerKey");
    let resultVO: TKResultVO = new TKResultVO();
    if (TKStringHelper.isNotBlank(storeKey)) {
      resultVO.errorNo = -5000001;
      resultVO.errorInfo = "一级标签不能为空!";
    } else if (TKStringHelper.isNotBlank(innerKey)) {
      resultVO.errorNo = -5000002;
      resultVO.errorInfo = "二级标签不能为空!";
    } else {
      //取出系统配置
      let result: string = TKSystemHelper.getConfig(`${storeKey}.${innerKey}`);
      let dataRow: Record<string, Object> = {
        "result": result
      };
      resultVO.results = dataRow;
    }
    return resultVO;
  }
}