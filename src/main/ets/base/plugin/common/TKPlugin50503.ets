import { TKJSNotiProxyManager } from '../../../components/webview/TKJSNotiProxyManager';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *   H5注册原生广播
 */
export class TKPlugin50503 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    let notiName: string = TKMapHelper.getString(param, "notiName");
    let callbackFuncNo: string = TKMapHelper.getString(param, "callbackFuncNo");
    if (TKStringHelper.isBlank(notiName)) {
      resultVO.errorNo = -5050301;
      resultVO.errorInfo = "通知名称不能为空!";
    } else if (TKStringHelper.isBlank(callbackFuncNo)) {
      resultVO.errorNo = -5050302;
      resultVO.errorInfo = "H5回调函数功能号不能为空!";
    } else {
      TKJSNotiProxyManager.shareInstance().registor(notiName, this.currentJSProxyController!, callbackFuncNo);
    }
    return resultVO;
  }
}