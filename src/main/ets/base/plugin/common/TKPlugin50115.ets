import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKWindowHelper } from '../../../util/ui/TKWindowHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKRouterHelper } from '../../router/TKRouterHelper';
import { TKThemeManager } from '../../theme/TKThemeManager';
import { TKBasePlugin } from '../TKBasePlugin';
import('../../mvc/page/common/web/TKComWebPage');

/**
 * 实现通知原生打开一个WebView,加载url地址
 */
export class TKPlugin50115 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //是否使用沉浸式全屏H5模式(0:否，1：是)
    let isUseFullScreenH5: boolean = TKMapHelper.getBoolean(param, "isUseFullScreenH5");
    if (!isUseFullScreenH5) {
      isUseFullScreenH5 = TKWindowHelper.getWindowLayoutFullScreenSync();
      param["isUseWebViewAutoResize"] = "0";
    }
    //连接地址
    let url: string = TKMapHelper.getString(param, "url");
    //导航栏背景图
    let titleBgImage: string = TKMapHelper.getString(param, "navbarImage");
    //状态栏颜色
    let statusColor: string =
      TKMapHelper.getString(param, "statusColor",
        (TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBar").backgroundColor as string ??
          "#FF1061FF"));
    //状态栏风格(0:黑色，1：白色)
    let statusStyle: string = TKMapHelper.getString(param, "statusStyle");
    //标题
    let title: string = TKMapHelper.getString(param, "title");
    //标题颜色
    let titleColor: Object = TKMapHelper.getObject(param, "titleColor",
      TKThemeManager.shareInstance().getCssRulesetByClassName("TKTitleBar").color ?? "#FFFFFFFF");
    //左右测按钮颜色
    let btnColor: string = TKMapHelper.getString(param, "btnColor");
    //左测按钮颜色
    let leftBtnColor: string = TKMapHelper.getString(param, "leftBtnColor", btnColor);
    //右测按钮颜色
    let rightBtnColor: string = TKMapHelper.getString(param, "rightBtnColor", btnColor);
    //进度条颜色
    let progressColor: string = TKMapHelper.getString(param, "progressColor", "#FF0000FF");
    //webview背景色
    let bgColor: string = TKMapHelper.getString(param, "bgColor");
    //webview透明度
    let alpha: number = TKMapHelper.getNumber(param, "alpha", 1.0);
    //标题文字是否根据webview的Title而变化（0：固定，1：改变）
    let isChangeTitle: boolean = TKMapHelper.getBoolean(param, "isChangeTitle");
    //是否显示返回按钮(0:否，1：是)
    let isShowBackBtn: boolean = TKMapHelper.getBoolean(param, "isShowBackBtn", true);
    //是否显示关闭按钮(0:否，1：是)
    let isShowCloseBtn: boolean = TKMapHelper.getBoolean(param, "isShowCloseBtn", false);
    //是否显示分享按钮(0:否，1：是)
    let isShowShareBtn: boolean = TKMapHelper.getBoolean(param, "isShowShareBtn", false);
    //按钮模式（0：文本，1：图片，2：文本+图片，3：返回图片，关闭文字）
    let btnMode: string = TKMapHelper.getString(param, "btnMode", "0");
    //是否支持仿微信的二维码识别(0:否，1：是)
    let isSupportReadQRCodeImage: boolean = TKMapHelper.getBoolean(param, "isSupportReadQRCodeImage");
    //是否加载的为思迪的H5页面(0:否，1：是)
    let isTKH5: boolean = TKMapHelper.getBoolean(param, "isTKH5", true);
    //是否进行URL编码
    let isEncodeURL: boolean = TKMapHelper.getBoolean(param, "isEncodeURL", true);
    //分享附加参数
    let shareExtParam: Record<string, Object> = TKMapHelper.getJSON(param, "shareExtParam");
    //是否关闭顶部页面后再打开新的页面(0:否，1：是)
    let isCloseTopView: boolean = TKMapHelper.getBoolean(param, "isCloseTopView");
    //是否显示加载过渡页
    let isShowLoading: boolean = TKMapHelper.getBoolean(param, "isShowLoading", false);
    //显示加载过渡页的GIF名称
    let loadingGIFName: string = TKMapHelper.getString(param, "loadingGIF");
    //显示加载失败的图片名称
    let loadFailedImageName: string = TKMapHelper.getString(param, "loadFailedImage");
    //是否支持页面显示的时候自动初始化H5的50113方法(0:否，1：是)
    let isSupportReInitH5: boolean = TKMapHelper.getBoolean(param, "isSupportReInitH5");
    //是否执行H5的JS返回方法(0：不执行，走浏览器默认返回，1：执行，走H5的50107插件)
    let isH5GoBack: boolean = TKMapHelper.getBoolean(param, "isH5GoBack");
    //WebView容器类名，不为空就以此参数来实例化容器
    let containerClass: string = TKMapHelper.getString(param, "containerClass", "TKComWebPage");
    //webview是否使用系统自动的安全区适配机制（0：否，走框架机制，1：是，走系统机制）
    let isUseWebViewAutoResize: boolean = TKMapHelper.getBoolean(param, "isUseWebViewAutoResize", true);
    //自定义userAgent头
    let customUserAgent: string = TKMapHelper.getString(param, "customUserAgent");
    if (TKStringHelper.isBlank(this.moduleName)) {
      resultVO.errorNo = -5011501;
      resultVO.errorInfo = "模块名不能为空!";
    } else if (TKStringHelper.isBlank(url)) {
      resultVO.errorNo = -5011502;
      resultVO.errorInfo = "链接地址不能为空!";
    } else {
      let param: Record<string, Object> = {
        //基础控制
        "moduleName": this.moduleName,
        "isLayoutFullScreen": isUseFullScreenH5,
        "isUseWebViewAutoResize": isUseWebViewAutoResize,
        "isSupportReInitH5": isSupportReInitH5,
        //加载链接
        "url": url,
        "title": title,
        "isEncodeURL": isEncodeURL,
        "isChangeTitle": isChangeTitle,
        "customUserAgent": customUserAgent,
        //标题样式
        "titleBgImage": titleBgImage,
        "statusStyle": statusStyle,
        "statusBarColor": statusColor,
        "titleColor": titleColor,
        "titleBgColor": statusColor,
        //标题按钮
        "backPressEnable": isShowBackBtn,
        "closePressEnable": isShowCloseBtn,
        "sharePressEnable": isShowShareBtn,
        "shareParam": shareExtParam,
        "leftBtnMode": btnMode,
        "leftBtnColor": leftBtnColor,
        "rightBtnColor": rightBtnColor,
        "isH5GoBack": isH5GoBack,
        //背景设置
        "backgroundColor": bgColor,
        "opacity": alpha,
        //加载效果
        "progressColor": progressColor,
        "isShowLoading": isShowLoading,
        "errorImage": loadFailedImageName,
        "fullLoadingImage": loadingGIFName
      }
      // 路由一个新页面
      if (isCloseTopView) {
        TKRouterHelper.replaceNamedRoute({
          name: containerClass,
          params: param,
          navComponentOrPathStack: this.navComponentOrPathStack
        });
      } else {
        TKRouterHelper.pushNamedRoute({
          name: containerClass,
          params: param,
          navComponentOrPathStack: this.navComponentOrPathStack
        });
      }
    }
    return resultVO;
  }
}