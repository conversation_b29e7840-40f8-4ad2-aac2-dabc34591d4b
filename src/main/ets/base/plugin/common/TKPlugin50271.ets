/**
 *弹出扫描图片二维码组件
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';
import { TKScanHelper } from '../../../util/scan/TKScanHelper';

export class TKPlugin50271 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //是否显示相册
    let isShowPhoto: boolean = TKMapHelper.getBoolean(param, "isShowPhoto", true);
    //标题
    let title: string = TKMapHelper.getString(param, "title", "扫描二维码");
    //附加参数
    let jsonParam: Record<string, Object> = TKMapHelper.getJSON(param, "paramExt");
    TKScanHelper.startScanForResult({
      enableAlbum: isShowPhoto
    }).then((data) => {
      let result: Record<string, Object> = {
        "funcNo": "50272",
        "error_no": 0,
        "error_info": "",
        "content": data,
        "paramExt": jsonParam
      };
      this.harmonyCallPluginCallBack(result);
    })
    return resultVO;
  }
}