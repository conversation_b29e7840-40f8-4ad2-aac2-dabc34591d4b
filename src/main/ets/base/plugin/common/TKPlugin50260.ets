/**
 * 设置手势密码
 */
import { TKPatternLockType } from '../../../components/patternlock/TKPatternLock';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50260 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //账户
    let account: string = TKMapHelper.getString(param, "account");
    //是否可返回
    let isCanBack: boolean = TKMapHelper.getBoolean(param, "isCanBack", false);
    //返回键位置
    let position: string = TKMapHelper.getString(param, "position", "0");
    //是否开启防截屏
    let isInterceptScreenshot: boolean = TKMapHelper.getBoolean(param, "isInterceptScreenshot", false);
    TKDialogHelper.createPatternLock({
      type: TKPatternLockType.Setting,
      userAccount: account,
      isCanBack: isCanBack,
      backPosition: position,
      isInterceptScreenshot: isInterceptScreenshot,
      onSetPassword: (result) => {
        this.onSetPassword(result);
      },
      onBackPassword: () => {
        this.onBackPassword();
      }
    }).open();
    return resultVO;
  }

  /**
   * 设置手势密码
   * @param password
   */
  private onSetPassword(result: string) {
    let param: Record<string, Object> = {
      "funcNo": "50265",
      "flag": "1",
      "result": result
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   * 返回关闭手势密码
   */
  private onBackPassword() {
    let param: Record<string, Object> = {
      "funcNo": "50265",
      "flag": "4",
      "result": "0"
    }
    this.harmonyCallPluginCallBack(param);
  }
}