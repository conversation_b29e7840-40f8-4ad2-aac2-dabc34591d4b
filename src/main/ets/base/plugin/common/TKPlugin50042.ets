import { TKCacheManager } from '../../../util/cache/TKCacheManager';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  保存数据到用户Cache缓存目录
 */
export class TKPlugin50042 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    //键
    let key: string = TKMapHelper.getString(param, "key");
    //值
    let value: Object = TKMapHelper.getObject(param, "value") as Object;
    //时间
    let time: number = TKMapHelper.getNumber(param, "time");
    //是否加密
    let isEncrypt: boolean = TKMapHelper.getBoolean(param, "isEncrypt");
    let resultVO: TKResultVO = new TKResultVO();
    if (TKStringHelper.isBlank(key)) {
      resultVO.errorNo = -5004201;
      resultVO.errorInfo = "字段名不能为空!";
    } else {
      //保存用户本地缓存
      TKCacheManager.shareInstance().saveFileCacheData(key, value, time, isEncrypt);
    }
    return resultVO;
  }
}