import { TKLocationManager } from '../../../util/location/TKLocationManager';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';
import { geoLocationManager } from '@kit.LocationKit';

/**
 *  获取设备地理位置
 */
export class TKPlugin50025 extends TKBasePlugin {
  public constructor() {
    super();
    this.isCache = false;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //是否启用原生地图定位机制
    let isUseSysMap: string = TKMapHelper.getString(param, "isUseSysMap");
    //附加参数
    let jsonParam: Record<string, Object> = TKMapHelper.getJSON(param, "paramExt");
    //获取地理位置
    if (!TKLocationManager.shareInstance().isLocationEnabled()) {
      resultVO.errorNo = -5002500;
      resultVO.errorInfo = "未开启定位功能";
      return resultVO;
    }
    TKLocationManager.shareInstance().requestLocationPermissions().then((result) => {
      let dataRow: Record<string, Object> = {
        "funcNo": "50026",
        "province": "", //省份
        "city": "", //城市
        "address": "", //街道
        "lng": "", //经度
        "lat": "", //维度
        "paramExt": jsonParam,
      };
      if (result) {
        TKLocationManager.shareInstance().getCurrentLocationEasy((location: geoLocationManager.Location) => {
          dataRow["lng"] = location.longitude;
          dataRow["lat"] = location.latitude;
          TKLocationManager.shareInstance()
            .getAddressFromLocation(location.latitude, location.longitude)
            .then((address) => {
              dataRow["address"] = address.placeName ?? "";
              dataRow["province"] = address.administrativeArea ?? "";
              dataRow["city"] = address.subAdministrativeArea ?? address.locality ?? "";
              this.harmonyCallPluginCallBack(dataRow);
            })
        });
      }
    })
    return resultVO;
  }
}