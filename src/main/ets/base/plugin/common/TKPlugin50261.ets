/**
 * 验证手势密码
 */
import { TKPatternLock, TKPatternLockOption, TKPatternLockType } from '../../../components/patternlock/TKPatternLock';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKApplicationStateChangeListener } from '../../../util/system/TKContextHelper';
import { TKComponentContent, TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKNotificationCenter } from '../../notification/TKNotificationCenter';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50261 extends TKBasePlugin {
  //手势密码
  private patternLock?: TKComponentContent<TKPatternLockOption>;
  //手势密码配置
  private patternLockOption?: TKPatternLockOption;
  //获取进入后台的时间
  private enterBgDateTime?: Date;
  //锁屏时间
  private lockSeconds: number = 300;

  public constructor() {
    super();
    // 后台来的时候执行锁屏操作
    TKNotificationCenter.defaultCenter.addObserver(this, this.enterBackground,
      TKApplicationStateChangeListener.NOTE_TKAPPLICATION_BACKGROUND);
    TKNotificationCenter.defaultCenter.addObserver(this, this.enterForeground,
      TKApplicationStateChangeListener.NOTE_TKAPPLICATION_FOREGROUND);
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //用户账户
    let account: string = TKMapHelper.getString(param, "account");
    //用户默认头像
    let userDefaultImage: string = TKMapHelper.getString(param, "userDefaultImage");
    //用户头像
    let userImage: string = TKMapHelper.getString(param, "userImage", userDefaultImage);
    //最大输入错误次数
    let errorNum: number = TKMapHelper.getNumber(param, "errorNum", 5);
    //锁屏时间
    this.lockSeconds = TKMapHelper.getNumber(param, "lockSenconds", 300);
    //是否展示
    let isShow: boolean = TKMapHelper.getBoolean(param, "isShow", true);
    //是否可返回
    let isCanBack: boolean = TKMapHelper.getBoolean(param, "isCanBack", false);
    //是否关闭在认证以后
    let isCloseAfterVitify: boolean = TKMapHelper.getBoolean(param, "isCloseAfterVitify", true);
    //是否开启防截屏
    let isInterceptScreenshot: boolean = TKMapHelper.getBoolean(param, "isInterceptScreenshot", false);
    //弹出验证手势密码
    this.patternLockOption = {
      type: TKPatternLockType.Verify,
      userAccount: account,
      userImage: userImage,
      maxErrorNum: errorNum,
      isCanBack: isCanBack,
      isCloseAfterVerify: isCloseAfterVitify,
      isInterceptScreenshot: isInterceptScreenshot,
      onForgetPassword: () => {
        this.onForgetPassword();
      },
      onChangeAccount: () => {
        this.onChangeAccount();
      },
      onVerifyPassword: (result) => {
        this.onVerifyPassword(result);
      },
      onBackPassword: () => {
        this.onBackPassword();
      }
    };
    if (TKStringHelper.isNotBlank(TKPatternLock.getUserPassword(account)) && isShow) {
      this.patternLock = TKDialogHelper.createPatternLock(this.patternLockOption);
      this.patternLock.open();
    }
    return resultVO;
  }

  /**
   * 忘记密码
   */
  private onForgetPassword() {
    let param: Record<string, Object> = {
      "funcNo": "50262",
      "type": "0"
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   * 切换账号
   */
  private onChangeAccount() {
    let param: Record<string, Object> = {
      "funcNo": "50262",
      "type": "1"
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   * 校验密码
   * @param result
   */
  private onVerifyPassword(result: string) {
    if (result == "-1") {
      TKPatternLock.clearUserPassword();
    }
    let param: Record<string, Object> = {
      "funcNo": "50265",
      "flag": "3",
      "result": result
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   * 返回关闭
   */
  private onBackPassword() {
    let param: Record<string, Object> = {
      "funcNo": "50265",
      "flag": "4",
      "result": "0"
    }
    this.harmonyCallPluginCallBack(param);
  }

  /**
   * 进入后台
   */
  private enterBackground() {
    this.enterBgDateTime = new Date();
  }

  /**
   * 进入前台
   */
  private enterForeground() {
    if (TKStringHelper.isNotBlank(TKPatternLock.getUserPassword()) && this.enterBgDateTime) {
      let duration: number = new Date().getTime() - this.enterBgDateTime.getTime();
      if (duration >= (this.lockSeconds * 1000)) {
        if (this.patternLockOption) {
          if (this.patternLock && this.patternLock.isOpen) {
            if (this.patternLock.options?.type != TKPatternLockType.Verify) {
              this.patternLock.close();
            } else {
              return;
            }
          }
          this.patternLockOption.isCloseAfterVerify = true;
          this.patternLock = TKDialogHelper.createPatternLock(this.patternLockOption);
          this.patternLock.open();
        }
      }
    }
  }
}