import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 获取应用版本号
 */
export class TKPlugin50010 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //获取软件展示版本号
    let version: string = TKSystemHelper.getVersion();
    //获取软件下载的内部版本序号
    let versionSn: string = TKSystemHelper.getVersionCode();
    //原生应用版本号
    let nativeVersion: string = TKSystemHelper.getAppVersion();
    //原生应用内部版本序号
    let nativeVersionSn: string = TKSystemHelper.getAppVersionCode();
    let dataRow: Record<string, Object> = {
      "version": version,
      "versionSn": versionSn,
      "nativeVersion": nativeVersion,
      "nativeVersionSn": nativeVersionSn
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}