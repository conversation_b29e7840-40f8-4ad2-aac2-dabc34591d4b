import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 获取设备唯一码
 */
export class TKPlugin50022 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //获取设备唯一码
    let uuid: string = TKDeviceHelper.getDeviceUUID();
    let dataRow: Record<string, Object> = {
      "deviceToken": uuid
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}