import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  获取设备MAC地址
 */
export class TKPlugin50024 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //获取MAC
    let mac: string = TKDeviceHelper.getDeviceMac();
    let dataRow: Record<string, Object> = {
      "mac": mac
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}