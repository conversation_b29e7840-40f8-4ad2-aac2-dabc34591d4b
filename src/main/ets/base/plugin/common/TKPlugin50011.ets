import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *  获取应用名称
 */
export class TKPlugin50011 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //获取应用名称
    let appName: string = TKSystemHelper.getAppName();
    //包名
    let appIdentifier: string = TKSystemHelper.getAppIdentifier();

    let dataRow: Record<string, Object> = {
      "appName": appName,
      "appIdentifier": appIdentifier
    };
    resultVO.results = dataRow;
    return resultVO;
  }
}