import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKNotificationCenter } from '../../notification/TKNotificationCenter';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 *   H5发送原生广播
 */
export class TKPlugin50502 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    let notiName: string = TKMapHelper.getString(param, "notiName");
    let notiObject: Record<string, Object> = TKMapHelper.getJSON(param, "notiObject");
    let notiUserInfo: Record<string, Object> = TKMapHelper.getJSON(param, "notiUserInfo");
    if (TKStringHelper.isBlank(notiName)) {
      resultVO.errorNo = -5050201;
      resultVO.errorInfo = "通知名称不能为空!";
    } else {
      let notiExtObject: Record<string, Object> = notiObject;
      notiExtObject["webViewName"] = this.currentJSProxyController!.webControllerName;
      TKNotificationCenter.defaultCenter.postNotificationName(notiName, notiExtObject, notiUserInfo);
    }
    return resultVO;
  }
}