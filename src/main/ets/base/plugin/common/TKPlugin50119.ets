/**
 *  设置webView状态栏及其他扩展配置
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50119 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    let statusColor: string = TKMapHelper.getString(param, "color"); //颜色
    let statusStyle: string = TKMapHelper.getString(param, "style") //样式
    let iphoneXBottomColor: string = TKMapHelper.getString(param, "iphoneXBottomColor"); //iphonex底部颜色
    let isSupportPullDownRefresh: boolean = TKMapHelper.getBoolean(param, "isSupportPullDownRefresh") // 是否支持原生下拉刷新
    if (TKStringHelper.isBlank(statusColor)) {
      resultVO.errorNo = -5011901;
      resultVO.errorInfo = "状态栏颜色不能为空!";
    } else {
      let option: Record<string, Object> = {
        "statusColor": statusColor,
        "statusStyle": statusStyle,
        "bottomAreaBgColor": iphoneXBottomColor
      };
      this.onPluginEvent("50119", option);
    }
    return resultVO;
  }
}