import { TKNormalDialogOption } from '../../../components/dialog/TKNormalDialog';
import { TKProgressDialogOption } from '../../../components/dialog/TKProgressDialog';
import { TKBase64Helper } from '../../../util/crypto/TKBase64Helper';
import { TKFileHelper } from '../../../util/file/TKFileHelper';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKPermissionHelper } from '../../../util/system/TKPermissionHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKComponentContent, TKDialogHelper } from '../../../util/ui/TKDialogHelper';
import { TKImageHelper } from '../../../util/ui/TKImageHelper';
import { TKLoadInfoVO } from '../../mvc/model/TKLoadInfoVO';
import { TKReqParamVO } from '../../mvc/model/TKReqParamVO';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKImageCropperManager, TKImageCropperManagerDelegate } from '../../mvc/page/image/TKImageCropperManager';
import { TKBasePlugin } from '../TKBasePlugin';
import { buffer } from '@kit.ArkTS';

/**
 *   选择照片或者拍照，经过裁剪实现图片上传
 */
export class TKPlugin50273 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //服务器地址
    let serverAddr: string = TKMapHelper.getString(param, "serverAddr");
    //文件标示
    let fileName: string = TKMapHelper.getString(param, "fileName");
    //附加参数
    let jsonParam: Record<string, Object> = TKMapHelper.getJSON(param, "paramExt");
    //质量压缩率
    let compress: number = TKMapHelper.getNumber(param, "compress", 1) * 100;
    //压缩质量大小
    let compressSize: number = TKMapHelper.getNumber(param, "compressSize") * 1024;
    //宽高
    let width: number = TKMapHelper.getNumber(param, "width");
    let height: number = TKMapHelper.getNumber(param, "height");
    //标题文字颜色
    let titleColor: string = TKMapHelper.getString(param, "titleColor", "#ffffffff");
    //状态栏颜色
    let statusColor: string = TKMapHelper.getString(param, "statusColor");
    //状态栏样式
    let statusStyle: string = TKMapHelper.getString(param, "statusStyle");
    //加载模式(0:菊花，1：进度条)，默认0
    let loadingMode: string = TKMapHelper.getString(param, "loadingMode", "0");
    //剪切标示
    let cutFlag: boolean = TKMapHelper.getBoolean(param, "cutFlag", true);
    //摄像头模式
    let isFrontCamera: boolean = !(TKMapHelper.getString(param, "cameraFlag") == "1")
    //是否默认开启拍照闪光灯
    let cameraFlashMode: boolean = TKMapHelper.getBoolean(param, "cameraFlashMode");
    //选择模式(0:照片+拍照,1:照片,2:拍照)
    let mode: string = TKMapHelper.getString(param, "mode", "0");
    //是否使用身份证拍照界面(0:否，1:是)
    let isUseIdCardCamera: boolean = TKMapHelper.getBoolean(param, "isUseIdCardCamera");
    //是否自动保存拍照图片到相册
    let isAutoSavePhoto: boolean = TKMapHelper.getBoolean(param, "isAutoSavePhoto", false);
    //主颜色
    let mainColor: string = TKMapHelper.getString(param, "mainColor", "#FF1A9BFF");
    if (TKStringHelper.isNotBlank(serverAddr)) {
      if (TKStringHelper.isBlank(fileName)) {
        resultVO.errorNo = -5027302;
        resultVO.errorInfo = "文件标示不能为空";
        return resultVO;
      }
    }
    //弹出图片编辑组件
    TKImageCropperManager.shareInstance().isUseIdCardCamera = isUseIdCardCamera;
    TKImageCropperManager.shareInstance().isAutoSavePhoto = isAutoSavePhoto;
    TKImageCropperManager.shareInstance().isCutImage = cutFlag;
    TKImageCropperManager.shareInstance().isFrontCamera = isFrontCamera;
    TKImageCropperManager.shareInstance().cameraFlashMode = cameraFlashMode;
    TKImageCropperManager.shareInstance().mainColor = mainColor;
    TKImageCropperManager.shareInstance().showChoiceSheet(mode);
    TKImageCropperManager.shareInstance().delegate = {
      processCropperImages: async (imageUris: Array<string>) => {
        let imageUri: string = imageUris[0];
        let imageData: ArrayBuffer = buffer.from(TKFileHelper.readFile(imageUri)).buffer;
        if (!isAutoSavePhoto) {
          TKFileHelper.deleteFile(imageUri);
        }
        if (width > 0 && height > 0) {
          imageData = await TKImageHelper.scaleImageDataSize(imageData, width, height);
        }
        if (compressSize > 0) {
          imageData = await TKImageHelper.compressImageDataSize(imageData, compressSize);
          compress = 100;
        }
        if (compress != 100) {
          imageData = await TKImageHelper.compressImageDataQuality(imageData, compress);
        }
        let callBackFunc = (resultVO: TKResultVO) => {
          let jsParam: Record<string, Object> = (resultVO.results ?? {}) as Record<string, Object>;
          jsParam["funcNo"] = "50274";
          jsParam["error_no"] = resultVO.errorNo;
          jsParam["error_info"] = resultVO.errorInfo;
          jsParam["paramExt"] = jsonParam;
          jsParam["fileExtension"] = "jpeg";
          if (resultVO.errorNo == 0) {
            jsParam["base64Image"] = TKBase64Helper.stringWithBase64Encode(new Uint8Array(imageData));
          }
          this.harmonyCallPluginCallBack(jsParam);
        };
        if (TKStringHelper.isNotBlank(serverAddr)) {
          let reqParam: Record<string, Object> = jsonParam;
          reqParam[`${fileName}@@F`] = imageData;
          reqParam["file_extension"] = "jpeg";
          let reqParamVO: TKReqParamVO = this.commonService.createReqParamVO();
          reqParamVO.isUpload = true;
          reqParamVO.isReturnList = false;
          reqParamVO.url = serverAddr;
          reqParamVO.reqParam = reqParam;
          let progress: TKComponentContent<TKProgressDialogOption> | undefined = undefined;
          if (loadingMode == "0") {
            reqParamVO.isShowWait = true;
            reqParamVO.waitTip = "正在上传...";
          } else {
            progress = TKDialogHelper.createProgressDialog({
              tip: "正在上传...",
              angle: reqParamVO.waitAngle
            } as TKProgressDialogOption);
            reqParamVO.uploadBlock = (loadInfoVO: TKLoadInfoVO) => {
              progress?.reuse({
                value: (loadInfoVO.progress * 100)
              } as TKProgressDialogOption);
            }
          }
          this.commonService.serviceInvoke(reqParamVO, (resultVO) => {
            callBackFunc(resultVO);
            if (progress) {
              progress.close();
              progress = undefined;
            }
          });
        } else {
          callBackFunc(new TKResultVO());
        }
      },
      processCropperImageWithErrorNo: (errorNo: number, errorInfo: string) => {
        if (errorNo == -9 && TKSystemHelper.getConfig("system.isPluginBackspaceCallH5") == "0") {
          return;
        }
        let result: Record<string, Object> = {
          "funcNo": "50274",
          "error_no": (-5027300 + errorNo),
          "error_info": errorInfo,
          "paramExt": jsonParam
        }
        switch (errorNo) {
          case -8: {
            // 读取异常
            TKDialogHelper.showToast(errorInfo);
            break;
          }
          case -9: {
            //用户取消
            result["error_no"] = 0;
            break;
          }
          default: {
            // 没有权限
            TKDialogHelper.showAlertDialog({
              title: "温馨提示",
              message: errorInfo,
              confirmText: "去授权",
              cancelText: "暂不授权",
              confirm: () => {
                TKPermissionHelper.toAppSetting();
              }
            } as TKNormalDialogOption);
            break;
          }
        }
        this.harmonyCallPluginCallBack(result);
      }
    } as TKImageCropperManagerDelegate;
    return resultVO;
  }
}