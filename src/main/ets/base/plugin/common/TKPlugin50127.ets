/**
 *  是否开启App防截屏录屏
 */
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKWindowHelper } from '../../../util/ui/TKWindowHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

export class TKPlugin50127 extends TKBasePlugin {
  public serverInvoke(param: Record<string, Object>): TKResultVO {
    let resultVO: TKResultVO = new TKResultVO();
    //是否开启App防截屏录屏(0:否，1：是)
    let isInterceptScreenshot: boolean = TKMapHelper.getBoolean(param, "isInterceptScreenshot", false);
    TKWindowHelper.setWindowPrivacyMode(isInterceptScreenshot);
    return resultVO;
  }
}