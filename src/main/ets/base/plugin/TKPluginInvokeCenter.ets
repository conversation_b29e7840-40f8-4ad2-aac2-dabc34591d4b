/**
 *  插件引擎处理
 */
import { TKDataHelper } from '../../util/data/TKDataHelper';
import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKFileHelper } from '../../util/file/TKFileHelper';
import { TKXMLHelper } from '../../util/file/xml/TKXMLHelper';
import { TKLog } from '../../util/logger/TKLog';
import { TKMapHelper } from '../../util/map/TKMapHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../util/system/TKSystemHelper';
import { TKResultVO } from '../mvc/model/TKResultVO';
import { TKBasePlugin, TKPluginCallBackFunc } from './TKBasePlugin';
import * as TKPlugin from './TKPluginIndex';

/**
 * 调用插件的选项
 */
export interface TKPluginInvokeOption {
  //插件号
  funcNo: string,

  //参数
  param?: Record<string, Object>,

  //模块名
  moduleName?: string,

  //是否H5
  isH5?: boolean,

  //回调函数
  callBackFunc?: TKPluginCallBackFunc,

  //当前Nav子组件对象或者堆栈对象
  navComponentOrPathStack?: CustomComponent | NavPathStack;
}

/**
 * 调用插件的协议声明
 */
export interface TKPluginInvokeCenterDelegate {

  /**
   * 调用插件
   *
   * @param pluginInvokeOption
   * @returns
   */
  callPlugin(pluginInvokeOption: TKPluginInvokeOption): TKResultVO;
}

/**
 * 插件处理中心
 */
export class TKPluginInvokeCenter implements TKPluginInvokeCenterDelegate {
  /**
   * 插件类定义Map
   */
  private pluginClassMap: Map<string, FunctionConstructor> = new Map<string, FunctionConstructor>();
  /**
   *  js插件对象映射表，这个是appId_funcNo作为key
   */
  private pluginObjectMap: Map<string, TKBasePlugin> = new Map<string, TKBasePlugin>();
  /**
   *  js插件对象映射表,这个是根据funcNo作为key
   */
  private pluginObjectByFuncNoMap: Map<string, TKBasePlugin> = new Map<string, TKBasePlugin>();
  /**
   * 插件配置映射表
   */
  private pluginMap: Map<string, string> = new Map<string, string>();
  private static instance: TKPluginInvokeCenter | undefined = undefined;

  public constructor() {
    //加载插件配置
    let configFiles: string = TKSystemHelper.getConfig("system.pluginPath", "SystemPlugin.xml");
    this.reloadPluginConfig(configFiles);
    this.registerPlugin(TKPlugin);
  }

  public static shareInstance(): TKPluginInvokeCenter {
    if (!TKPluginInvokeCenter.instance) {
      TKPluginInvokeCenter.instance = new TKPluginInvokeCenter();
    }
    return TKPluginInvokeCenter.instance;
  }

  /**
   * 注册插件定义
   * @param plugins
   */
  public registerPlugin(plugins: Object) {
    Object.entries(plugins).forEach((e: Array<Object>, i: number) => {
      let key: string = e[0] as string;
      let plugin = e[1] as FunctionConstructor;
      this.pluginClassMap.set(key, plugin);
    });
  }

  /**
   *  获取缓存的插件对象
   *
   * @param funcNo 功能号
   *
   * @return 插件对象
   */
  public getCachePlugin(funcNo: string): TKBasePlugin | undefined {
    return this.pluginObjectByFuncNoMap.get(funcNo);
  }

  /**
   * 获取插件文件路径
   * @param funcPath
   * @returns
   */
  private getPluginPath(pluginPath: string): string {
    if (TKStringHelper.startsWith(pluginPath, "@bundle:")) {
      return pluginPath;
    }
    if (TKStringHelper.startsWith(pluginPath, "/")) {
      return pluginPath.substring(1, pluginPath.length);
    }
    let lastPluginPath = `thinkive/config/${TKSystemHelper.getEnvironment()}/plugin/${pluginPath}`;
    if (TKFileHelper.readFile(lastPluginPath).length <= 0) {
      lastPluginPath = `thinkive/config/default/plugin/${pluginPath}`;
    }
    return lastPluginPath;
  }

  /**
   *  重新载入配置文件
   *
   * @param pluginConfigPath 配置文件路径
   */
  public reloadPluginConfig(pluginConfigPath: string) {
    let files: Array<string> = TKStringHelper.split(pluginConfigPath, "|");
    for (let path of files) {
      let pluginPath: string = this.getPluginPath(path);
      let pluginsElem: Map<string, Object> = TKXMLHelper.readConfigXml(pluginPath);
      if (pluginsElem && pluginsElem.size > 0) {
        let appId: string = TKMapHelper.getString(pluginsElem, "appId");
        let pluginElems: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(pluginsElem, "children");
        if (pluginElems) {
          for (let pluginElem of pluginElems) {
            let funcNo: string = TKMapHelper.getString(pluginElem, "name");
            let value: string = TKMapHelper.getString(pluginElem, "value");
            let key: string = funcNo;
            if (TKStringHelper.isNotBlank(appId)) {
              key = `${appId}_${key}`;
            }
            this.pluginMap.set(key, value);
            this.pluginObjectMap.delete(key);
            this.pluginObjectByFuncNoMap.delete(funcNo);
          }
        }
      }
    }
  }


  /**
   *  调用插件
   *
   * @param pluginInvokeOption  调用插件选项
   *
   * @return
   */
  public callPlugin(pluginInvokeOption: TKPluginInvokeOption): TKResultVO {
    try {
      let funcNo = pluginInvokeOption.funcNo;
      let tempFuncNo = funcNo;
      let param = pluginInvokeOption.param ?? {};
      let moduleName = pluginInvokeOption.moduleName;
      let isH5 = pluginInvokeOption.isH5;
      let callBackFunc = pluginInvokeOption.callBackFunc;
      let navComponentOrPathStack = pluginInvokeOption.navComponentOrPathStack;
      if (TKStringHelper.isBlank(funcNo)) {
        funcNo = TKMapHelper.getString(param, "funcNo");
        tempFuncNo = funcNo;
      }
      let appId: string = TKMapHelper.getString(param, "_TKAppID");
      if (TKStringHelper.isNotBlank(funcNo) && TKStringHelper.isNotBlank(appId)) {
        funcNo = `${appId}_${funcNo}`;
      }
      if (TKStringHelper.isBlank(funcNo)) {
        TKLog.error("调用插件的功能号不能为空!");
        let resultVO: TKResultVO = new TKResultVO();
        resultVO.errorNo = -1;
        resultVO.errorInfo = "调用插件的功能号不能为空!";
        return resultVO;
      }
      //根据消息ID获插件
      let pluginName: string = TKMapHelper.getString(this.pluginMap, funcNo);
      if (TKStringHelper.isBlank(pluginName)) {
        funcNo = tempFuncNo;
        pluginName = TKMapHelper.getString(this.pluginMap, funcNo);
      }
      if (TKStringHelper.isBlank(pluginName)) {
        TKLog.warn(`插件[${funcNo}]没定义!`);
        pluginName = "TKPlugin" + funcNo;
      }
      let pluginClass: FunctionConstructor | undefined = this.pluginClassMap.get(pluginName);
      //如果存在实例插件
      if (pluginClass) {
        let plugin: TKBasePlugin | undefined = this.pluginObjectMap.get(funcNo);
        if (!plugin) {
          let instance = new pluginClass();
          if (instance instanceof TKBasePlugin) {
            plugin = instance as TKBasePlugin;
          }
          if (plugin && plugin.isCache) {
            this.pluginObjectMap.set(funcNo, plugin);
            this.pluginObjectByFuncNoMap.set(tempFuncNo, plugin);
          }
        }
        if (plugin) {
          if (TKStringHelper.isBlank(moduleName)) {
            moduleName = TKMapHelper.getString(param, "moduleName");
          }
          if (TKStringHelper.isNotBlank(moduleName)) {
            plugin.moduleName = moduleName!;
          }
          plugin.webControllerName = TKMapHelper.getString(param, "webControllerName", moduleName);
          let isTKSyncPlugin: string = TKMapHelper.getString(param, "isTKSyncPlugin");
          if (TKStringHelper.isNotBlank(isTKSyncPlugin)) {
            plugin.isSyncPlugin = (isTKSyncPlugin == "1");
          }
          plugin.isH5 = TKDataHelper.getBoolean(isH5);
          plugin.callBackFunc = callBackFunc;
          plugin.navComponentOrPathStack = navComponentOrPathStack;
          plugin.flowNo = TKMapHelper.getString(param, "flowNo");
          plugin.isUseJsCallBack = (TKMapHelper.getString(param, "isJsCallBack") == "1");
          if (funcNo != "50501") {
            TKLog.info(`调用插件(${funcNo})开始,入参为：${TKObjectHelper.toJsonStr(param)}`);
          }
          let pluginResultVO: TKResultVO = plugin.pluginInvoke(param);
          let resultVO: TKResultVO = new TKResultVO();
          resultVO.errorNo = pluginResultVO.errorNo;
          resultVO.errorInfo = pluginResultVO.errorInfo;
          resultVO.results = pluginResultVO.results;
          if (funcNo != "50501") {
            TKLog.info(`调用插件(${funcNo})结束,出参为：${resultVO.toJsonStr()}`);
          }
          return resultVO;
        }
      } else {
        TKLog.error(`插件[${pluginName}]对应的类不存在!`);
        let resultVO: TKResultVO = new TKResultVO();
        resultVO.errorNo = -3;
        resultVO.errorInfo = `插件[${pluginName}]对应的类不存在!`;
        return resultVO;
      }
    } catch (error) {
      TKLog.error(`插件中心调用插件业务异常:${error.message}`);
      let resultVO: TKResultVO = new TKResultVO();
      resultVO.errorNo = -4;
      resultVO.errorInfo = `插件中心调用插件业务异常:${error.message}`;
      return resultVO;
    }
    return new TKResultVO();
  }

  /**
   * 插件是否存在
   * @param pluginNo
   * @returns
   */
  public isPluginExist(pluginNo: string): boolean {
    let pluginName: string | undefined = this.pluginMap.get(pluginNo);
    if (TKStringHelper.isBlank(pluginName)) {
      pluginName = 'TKPlugin' + pluginNo;
    }
    if (this.pluginClassMap.get(pluginName!)) {
      return true;
    } else {
      return false;
    }
  }
}