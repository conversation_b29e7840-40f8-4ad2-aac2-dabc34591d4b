import { connection } from '@kit.NetworkKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKLog } from '../../../util/logger/TKLog';
import { TKNotificationCenter } from '../../notification/TKNotificationCenter';
import { TKObjectHelper } from '../../../util/data/TKObjectHelper';
import { TKNetHelper, TKNetworkType } from '../../../util/net/TKNetHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKDialogHelper } from '../../../util/ui/TKDialogHelper';

/**
 * 网络监听器
 */
export class TKNetworkListener {
  /**
   * 网络改变通知
   */
  public static readonly NOTE_NETWORK_CHANGE: string = "note_network_change";
  /**
   * 单例对象
   */
  private static instance: TKNetworkListener | undefined = undefined;
  /**
   * 当前网络连接状态
   */
  private _isNetAvailable: boolean = connection.hasDefaultNetSync();
  /**
   * 当前网络连接监听对象
   */
  private netCon: connection.NetConnection | undefined = undefined;

  private constructor() {
  }

  public static shareInstance(): TKNetworkListener {
    if (!TKNetworkListener.instance) {
      TKNetworkListener.instance = new TKNetworkListener();
    }
    return TKNetworkListener.instance;
  }

  /**
   * 开启网络监听
   */
  public startListener() {
    if (!this.netCon) {
      this.netCon = connection.createNetConnection();
      this.netCon.register((error: BusinessError, data) => {
        if (error) {
          TKLog.info('注册监听网络状态变化失败===>:' + TKObjectHelper.toJsonStr(error));
        }
      });
      // 设备从有网络到无网络状态会触发netLost事件
      this.netCon.on('netLost', (data: connection.NetHandle) => {
        TKLog.info('网络状态变化:监听[netLost]成功,data:' + TKObjectHelper.toJsonStr(data))
        this.updateInterfaceChange(false);
      });
      // 设备从无网络到有网络会触发netAvailable事件、netCapabilitiesChange事件和netConnectionPropertiesChange事件；
      // 设备从WiFi到蜂窝会触发netLost事件（WiFi丢失）之后触发 netAvaliable事件（蜂窝可用）；
      this.netCon.on('netAvailable', (data: connection.NetHandle) => {
        TKLog.info('网络状态变化:监听[netAvailable]成功,data:' + TKObjectHelper.toJsonStr(data))
        this.updateInterfaceChange(true);
      });
      // 网络不可用事件
      this.netCon.on('netUnavailable', () => {
        TKLog.info('网络状态变化:监听[netUnavailable]成功')
        this.updateInterfaceChange(false);
      });
    }
  }

  private updateInterfaceChange(isNetAvailable: boolean) {
    if (this._isNetAvailable != isNetAvailable) {
      this._isNetAvailable = isNetAvailable;
      if (isNetAvailable) {
        TKNetHelper.resetNetCacheInfo();
      } else {
        TKNetHelper.resetNetTypeCacheInfo();
      }
      if (TKSystemHelper.getConfig("networkChange.isShowNetChange", "1") != "0") {
        //对连接改变做出响应的处理动作
        let networkType = TKNetHelper.getNetworkType()
        //对连接改变做出响应的处理动作。
        if (networkType == TKNetworkType.Network_No) {
          TKDialogHelper.showToast("当前网络已断开");
        } else if (networkType == TKNetworkType.Network_WIFI) {
          TKDialogHelper.showToast("WIFI网络已连接");
        } else if (networkType == TKNetworkType.Network_2G || networkType == TKNetworkType.Network_3G ||
          networkType == TKNetworkType.Network_4G || networkType == TKNetworkType.Network_5G ||
          networkType == TKNetworkType.Network_CELLULAR) {
          TKDialogHelper.showToast("2G/3G/4G网络已连接");
        }
      }
      TKNotificationCenter.defaultCenter.postNotificationName(TKNetworkListener.NOTE_NETWORK_CHANGE, isNetAvailable);
    }
  }

  /**
   * 是否当前网络可连
   * @returns
   */
  public isNetAvailable(): boolean {
    return this._isNetAvailable;
  }

  /**
   * 关闭网络监听
   */
  public stopListener() {
    if (this.netCon) {
      this.netCon.unregister((error: BusinessError) => {
      });
    }
  }
}