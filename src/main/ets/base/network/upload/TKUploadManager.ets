/**
 * 断点续传管理器
 */
import { TKCacheManager } from '../../../util/cache/TKCacheManager';
import { TKMd5Helper } from '../../../util/crypto/TKMd5Helper';
import { TKDataHelper } from '../../../util/data/TKDataHelper';
import { TKFileHelper } from '../../../util/file/TKFileHelper';
import { TKLog } from '../../../util/logger/TKLog';
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKLoadInfoVO } from '../../mvc/model/TKLoadInfoVO';
import { TKReqParamVO } from '../../mvc/model/TKReqParamVO';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKWriteDataView } from '../../mvc/service/dao/socket/client/common/TKWriteDataView';
import { TKCommonService } from '../../mvc/service/TKCommonService';

export class TKUploadManager {
  private static readonly TKUploadCacheFileName: string = "TKUpload";
  private static readonly TKUploadBlockSize: number = (1024 * 1024 * 1.0);
  //授权key
  private appKey: string = "";
  private service: TKCommonService = new TKCommonService();
  private static instance: TKUploadManager | undefined = undefined;

  public static shareInstance(): TKUploadManager {
    if (!TKUploadManager.instance) {
      TKUploadManager.instance = new TKUploadManager();
    }
    return TKUploadManager.instance;
  }

  /**
   * 初始化上下文
   */
  public initContext(appKey: string) {
    this.appKey = appKey;
  }

  /**
   * 功能描述：根据文件绝对路径上传文件
   *
   * @param uploadUrl:           上传文件服务器地址
   * @param filePath：           文件本地存储路径
   * @param callBackFunc：       上传结果回调函数，入参为result，格式为：{code:错误号,msg:错误信息,data:”文件路径”}
   * @param uploadProgressFunc： 上传进度回调函数，入参为loadInfo,格式为：{bytesTotal:总共大小,bytesLoaded:已上传大小,progress:进度比例,例如0.1}
   */
  public async uploadFile(uploadUrl: string, filePath: string,
    callBackFunc: (result: Record<string, Object>) => void,
    uploadProgressFunc?: (progress: TKLoadInfoVO) => void) {
    if (TKFileHelper.isFileExists(filePath)) {
      let fileData: Uint8Array = TKFileHelper.readFile(filePath);
      let fileName: string = filePath.split(".").pop() as string;
      this.uploadFileData(uploadUrl, fileData, fileName, callBackFunc, uploadProgressFunc);
    } else {
      TKLog.error("上传文件路径不存在:" + filePath);
    }
  }

  /**
   * 功能描述：上传二进制文件流
   *
   * uploadUrl:    上传文件服务器地址
   * fileData：    二进制文件数据
   * fileName:      文件名称 test.jpeg
   * callBackFunc：上传结果回调函数，入参为result，格式为：{code:错误号,msg:错误信息,data:”文件路径”}
   * uploadProgressFunc： 上传进度回调函数，入参为loadInfo,格式为：{bytesTotal:总共大小,bytesLoaded:已上传大小,progress:进度比例,例如0.1}
   */
  public async uploadFileData(uploadUrl: string, fileData: Uint8Array, fileName: string,
    callBackFunc: (result: Record<string, Object>) => void, uploadProgressFunc?: (progress: TKLoadInfoVO) => void) {
    let fileMD5: string = await TKMd5Helper.stringWithMd5(fileData);
    let uploadInfo: Record<string, Object> | undefined =
      TKCacheManager.shareInstance().getFileCacheData(fileMD5, TKUploadManager.TKUploadCacheFileName);
    if (uploadInfo && Object.entries(uploadInfo).length > 0) {
      let sequenceNo: number = TKMapHelper.getNumber(uploadInfo, "sequenceNo");
      let totalBlock: number = TKMapHelper.getNumber(uploadInfo, "totalBlock");
      if (sequenceNo < 1 || totalBlock < 1) {
        uploadInfo = undefined;
      }
    }
    if (!uploadInfo || Object.entries(uploadInfo).length == 0) {
      uploadInfo = {};
      uploadInfo["fileMD5"] = fileMD5;
      uploadInfo["fileSize"] = fileData.length;
      uploadInfo["uploadSize"] = 0;
      uploadInfo["totalBlock"] = Math.ceil(fileData.length / TKUploadManager.TKUploadBlockSize);
      uploadInfo["sequenceNo"] = 1;
      TKCacheManager.shareInstance()
        .saveFileCacheData(fileMD5, uploadInfo, 0, false, TKUploadManager.TKUploadCacheFileName);
    }
    await this.uploadChunkFileData(uploadUrl, fileData, fileName, uploadInfo, callBackFunc, uploadProgressFunc);
  }

  private async uploadChunkFileData(uploadUrl: string, fileData: Uint8Array, fileName: string,
    uploadInfo: Record<string, Object>, callBackFunc: (result: Record<string, Object>) => void,
    uploadProgressFunc?: (progress: TKLoadInfoVO) => void) {
    let fileMD5: string = TKMapHelper.getString(uploadInfo, "fileMD5");
    let fileSize: number = TKMapHelper.getNumber(uploadInfo, "fileSize");
    let uploadSize: number = TKMapHelper.getNumber(uploadInfo, "uploadSize");
    let totalBlock: number = TKMapHelper.getNumber(uploadInfo, "totalBlock");
    let sequenceNo: number = TKMapHelper.getNumber(uploadInfo, "sequenceNo");
    let blockBegin: number = ((sequenceNo - 1) * TKUploadManager.TKUploadBlockSize);
    let blockSize: number =
      ((fileData.length - blockBegin) > TKUploadManager.TKUploadBlockSize) ? TKUploadManager.TKUploadBlockSize :
        (fileData.length - blockBegin);
    let blockData: Uint8Array = fileData.subarray(blockBegin, blockBegin + blockSize);
    let fileTokenStr: string =
      `"appKey=${this.appKey}fileName=${fileName}fileMD5=${fileMD5}totalBlock=${totalBlock}sequenceNo=${sequenceNo}blockData=`;
    let fileTokenData: Uint8Array = TKDataHelper.stringToUint8Array(fileTokenStr);
    let writeDV: TKWriteDataView = new TKWriteDataView(fileTokenData.length + blockData.length);
    writeDV.putBytes(fileTokenData);
    writeDV.putBytes(blockData);

    let reqParamVO: TKReqParamVO = this.service.createReqParamVO();
    reqParamVO.isUpload = true;
    reqParamVO.isShowWait = false;
    reqParamVO.isURLEncode = false;
    reqParamVO.isReturnList = false;
    reqParamVO.url = uploadUrl;
    let reqParam: Record<string, Object> = {
      "fileName": fileName,
      "fileMD5": fileMD5,
      "totalBlock": totalBlock,
      "sequenceNo": sequenceNo,
      "blockData@@F": blockData,
      "file_extension": fileName.split(".").pop() ?? "",
      "fileToken": await TKMd5Helper.stringWithMd5(new Uint8Array(writeDV.buffer))
    };
    reqParamVO.reqParam = reqParam;
    reqParamVO.uploadBlock = (loadInfo: TKLoadInfoVO) => {
      loadInfo.bytesTotal = (fileSize > loadInfo.bytesTotal) ? fileSize : loadInfo.bytesTotal;
      loadInfo.bytesLoaded = uploadSize + loadInfo.bytesLoaded;
      loadInfo.bytesLoaded = (loadInfo.bytesLoaded > loadInfo.bytesTotal) ? loadInfo.bytesTotal : loadInfo.bytesLoaded;
      if (uploadProgressFunc) {
        uploadProgressFunc(loadInfo);
      }
    }
    this.service.serviceInvoke(reqParamVO, (resultVO: TKResultVO) => {
      if (resultVO.errorNo == 0) {
        let data: Record<string, Object> = resultVO.results as Record<string, Object>;
        let nextSequenceNo: number = TKMapHelper.getNumber(data, "nextSequenceNo");
        let filePath: string = TKMapHelper.getString(data, "filePath");
        if (nextSequenceNo == -1) {
          TKCacheManager.shareInstance().deleteFileCacheData(fileMD5, TKUploadManager.TKUploadCacheFileName);
          let result: Record<string, Object> = {
            "code": resultVO.errorNo,
            "msg": resultVO.errorInfo,
            "data": filePath
          };
          if (uploadProgressFunc) {
            let loadInfo: TKLoadInfoVO = new TKLoadInfoVO();
            loadInfo.bytesTotal = fileSize;
            loadInfo.bytesLoaded = fileSize;
            uploadProgressFunc(loadInfo);
          }
          if (callBackFunc) {
            callBackFunc(result);
          }
        } else {
          uploadInfo["uploadSize"] = (((nextSequenceNo - 1) * TKUploadManager.TKUploadBlockSize) + blockData.length);
          uploadInfo["sequenceNo"] = nextSequenceNo;
          TKCacheManager.shareInstance()
            .saveFileCacheData(fileMD5, uploadInfo, 0, false, TKUploadManager.TKUploadCacheFileName);
          this.uploadChunkFileData(uploadUrl, fileData, fileName, uploadInfo, callBackFunc, uploadProgressFunc);
        }
      } else {
        let result: Record<string, Object> = {
          "code": resultVO.errorNo,
          "msg": resultVO.errorInfo
        };
        if (callBackFunc) {
          callBackFunc(result);
        }
      }
    });
  }
}