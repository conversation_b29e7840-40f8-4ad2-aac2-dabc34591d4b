/**
 * 下载管理器
 */
import request from '@ohos.request';
import { TKMd5Helper } from '../../../util/crypto/TKMd5Helper';
import { TKUUIDHelper } from '../../../util/crypto/TKUUIDHelper';
import { TKFileHelper } from '../../../util/file/TKFileHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import {
  TKDownLoadCallBack,
  TKDownLoadProgressVO,
  TKDownLoadRequestVO,
  TKDownLoadResultVO,
  TKDownLoadStatus
} from './domain/TKDownLoadBean';
import { TKContextHelper } from '../../../util/system/TKContextHelper';

export class TKDownLoadManager {
  private static instance: TKDownLoadManager | undefined = undefined;

  public static shareInstance(): TKDownLoadManager {
    if (!TKDownLoadManager.instance) {
      TKDownLoadManager.instance = new TKDownLoadManager();
    }
    return TKDownLoadManager.instance;
  }

  /**
   * 文件下载
   * @param request
   * @param downLoadCallBack
   */
  public async download(downLoadRequestVO: TKDownLoadRequestVO, downLoadCallBack: TKDownLoadCallBack) {
    downLoadRequestVO.sequence = downLoadRequestVO.sequence ?? TKUUIDHelper.uuid();
    downLoadRequestVO.fileName = downLoadRequestVO.fileName ?? await TKMd5Helper.stringWithMd5(downLoadRequestVO.url);
    downLoadRequestVO.header = downLoadRequestVO.header ?? {};
    let downloadPath: string = `${TKFileHelper.cacheDir()}/thinkive/download`;
    let downLoadResultVO: TKDownLoadResultVO = {
      sequence: downLoadRequestVO.sequence,
      errorInfo: '',
      url: downLoadRequestVO.url,
      header: downLoadRequestVO.header,
      fileName: downLoadRequestVO.fileName,
      filePath: `${downloadPath}/${downLoadRequestVO.fileName}`,
      progress: new TKDownLoadProgressVO()
    } as TKDownLoadResultVO;
    if (TKStringHelper.isBlank(downLoadResultVO.url)) {
      downLoadResultVO.errorInfo = '下载url不可为空';
      downLoadResultVO.status = TKDownLoadStatus.ERROR;
      this.executeDownloadCallBack(undefined, downLoadCallBack, downLoadResultVO);
      return;
    }
    TKFileHelper.createFileDir(downloadPath);
    TKFileHelper.deleteFile(downLoadResultVO.filePath);
    //开始回调
    downLoadResultVO.status = TKDownLoadStatus.PENDING;
    this.executeDownloadCallBack(undefined, downLoadCallBack, downLoadResultVO);
    try {
      let downloadTask: request.agent.Task = await request.agent.create(TKContextHelper.getCurrentContext(), {
        action: request.agent.Action.DOWNLOAD,
        url: downLoadResultVO.url,
        method: 'GET',
        header: downLoadResultVO.header,
        title: 'download',
        mode: downLoadRequestVO.backgroundMode ? request.agent.Mode.BACKGROUND : request.agent.Mode.FOREGROUND,
        retry: true,
        network: request.agent.Network.ANY,
        saveas: downLoadResultVO.filePath,
        overwrite: true
      } as request.agent.Config);
      downloadTask.on('progress', (progress: request.agent.Progress) => {
        // 下载中
        downLoadResultVO.status = TKDownLoadStatus.RUNNING;
        downLoadResultVO.progress.bytesLoaded = progress.processed;
        downLoadResultVO.progress.bytesTotal = progress.sizes[0];
        this.executeDownloadCallBack(downloadTask, downLoadCallBack, downLoadResultVO);
      });
      downloadTask.on('completed', (progress: request.agent.Progress) => {
        // 下载完成
        downLoadResultVO.status = TKDownLoadStatus.FINISHED;
        this.executeDownloadCallBack(downloadTask, downLoadCallBack, downLoadResultVO);
      });
      downloadTask.on('failed', (progress: request.agent.Progress) => {
        // 下载失败
        downLoadResultVO.status = TKDownLoadStatus.ERROR;
        downLoadResultVO.errorInfo = '下载失败';
        this.executeDownloadCallBack(downloadTask, downLoadCallBack, downLoadResultVO);
      });
      await downloadTask.start();
    } catch (err) {
      downLoadResultVO.errorInfo = err.message;
      downLoadResultVO.status = TKDownLoadStatus.ERROR;
      this.executeDownloadCallBack(undefined, downLoadCallBack, downLoadResultVO);
    }
  }

  /**
   * 执行下载回调
   * @param downloadTask
   * @param downLoadCallBack
   * @param downLoadResultVO
   */
  private async executeDownloadCallBack(downloadTask: request.agent.Task | undefined,
    downLoadCallBack: TKDownLoadCallBack,
    downLoadResultVO: TKDownLoadResultVO) {
    if (downloadTask &&
      (downLoadResultVO.status == TKDownLoadStatus.FINISHED || downLoadResultVO.status == TKDownLoadStatus.ERROR)) {
      downloadTask.off('progress');
      downloadTask.off('completed');
      downloadTask.off('failed');
      await request.agent.remove(downloadTask.tid);
    }
    if (downLoadCallBack) {
      downLoadCallBack(downLoadResultVO);
    }
  }
}