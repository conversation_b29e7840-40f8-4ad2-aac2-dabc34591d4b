/**
 * 下载状态
 */
export enum TKDownLoadStatus {
  PENDING = 'PENDING', // 开始
  RUNNING = 'RUNNING', // 加载中
  PAUSED = 'PAUSED', // 暂停
  CANCELED = 'CANCELED', // 取消
  FINISHED = 'FINISHED', // 完成
  ERROR = 'ERROR' // 异常
}

/**
 *  下载回调函数
 */
export type TKDownLoadCallBack = (downLoadResult: TKDownLoadResultVO) => void;

/**
 * 下载进度对象
 */
export class TKDownLoadProgressVO {
  /**
   *  总共的长度
   */
  public bytesTotal: number = 0;
  /**
   *  已经加载的长度
   */
  public bytesLoaded: number = 0;

  public get progress(): number {
    if (this.bytesTotal > 0) {
      return (this.bytesLoaded * 1.0) / (this.bytesTotal * 1.0);
    } else {
      return 0;
    }
  }
}

/**
 * 下载请求对象
 */
export interface TKDownLoadRequestVO {
  /**
   * 数据请求地址
   */
  url: string;

  /**
   * http请求头参数
   */
  header?: Record<string, Object>;

  /**
   * 请求流水号
   */
  sequence?: string;

  /**
   * 文件名称
   */
  fileName?: string;

  /**
   * 是否后台模式
   */
  backgroundMode?: boolean;
}

/**
 * 下载结果对象
 */
export interface TKDownLoadResultVO {
  /**
   * 下载流水号
   */
  sequence: string;

  /**
   * 下载错误信息
   */
  errorInfo: string;

  /**
   * 下载url
   */
  url: string;

  /**
   * http请求头参数
   */
  header: Record<string, Object>;

  /**
   * 文件名称
   */
  fileName: string;

  /**
   * 文件保存全路径
   */
  filePath: string;

  /**
   * 文件下载进度信息
   */
  progress: TKDownLoadProgressVO;

  /**
   * 下载状态
   */
  status: TKDownLoadStatus;
}