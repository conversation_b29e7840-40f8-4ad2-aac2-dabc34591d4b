import { TKProgressDialogOption } from '../../components/dialog/TKProgressDialog';
import { TKCacheManager } from '../../util/cache/TKCacheManager';
import { TKAesHelper } from '../../util/crypto/TKAesHelper';
import { TKPasswordGenerator } from '../../util/crypto/TKPasswordGenerator';
import { TKFileHelper } from '../../util/file/TKFileHelper';
import { TKLog } from '../../util/logger/TKLog';
import { TKMapHelper } from '../../util/map/TKMapHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../util/system/TKSystemHelper';
import { TKComponentContent, TKDialogHelper } from '../../util/ui/TKDialogHelper';
import { TKHttpRoomServerListener } from '../mvc/service/dao/http/client/gateway/TKHttpRoomServerListener';
import {
  TKDownLoadCallBack,
  TKDownLoadRequestVO,
  TKDownLoadResultVO,
  TKDownLoadStatus
} from '../network/download/domain/TKDownLoadBean';
import { TKDownLoadManager } from '../network/download/TKDownLoadManager';
import { TKNotificationCenter } from '../notification/TKNotificationCenter';
import { TKThemeManager } from '../theme/TKThemeManager';
import zlib from '@ohos.zlib';
// import { unzipToDirectory } from '@ohos/minizip';
// import { UnrarApi } from '@ohos/unrar';
import { TKContextHelper } from '../../util/system/TKContextHelper';
import { TKMd5Helper } from '../../util/crypto/TKMd5Helper';

/**
 * 更新模式
 */
export enum TKUpdateMode {
  Alert, //强制更新
  Confirm //非强制更新
}

/**
 *
 *  自定义UI代理
 */
export interface TKUpdateUIDelegate {
  /**
   *  自定义UI提示框
   * @param updateMode 更新模式
   * @param updateInfo 更新信息
   * @param updateBtnFunc 更新按钮执行函数
   *
   */
  showUpdateUIWindow: (updateMode: TKUpdateMode, updateInfo: Record<string, Object>, updateBtnFunc: () => void) => void;

  /*
   *  更新完成---注意：这个不是下载完成，下载完成并非更新完成，下载完成后，需要解压覆盖，全部动作完成后才触发更新完成回调
   */
  updateCompleted?: () => void;

  /**
   *  更新失败---注意：这个不仅仅是下载失败，除了下载失败以外，下载完成后解压失败，覆盖失败等都算更新失败
   */
  updateFailed?: () => void;
}

/**
 * 版本管理器
 */
export class TKVersionManager implements TKUpdateUIDelegate {
  //单例对象
  private static instance: TKVersionManager | undefined = undefined;
  //最近一次存储的app的版本号
  private static readonly CACHE_APP_VERSION: string = "TKAppVersion";
  //是否第一次装软件:软件升级重装，软件卸载重装,软件第一次装 等都算第一次
  public static readonly CACHE_ISFIRSTINSTALL: string = "TKIsFirstInstall";
  //App是否完成初始化
  public static readonly CACHE_APP_FINISH_INIT: string = "TKAppFinishInit";
  //更新完成通知
  private static readonly NOTE_UPDATE_SUCCESS: string = "note_update_success";
  //更新失败通知
  private static readonly NOTE_UPDATE_FAILED: string = "note_update_failed";
  //是否显示更新提示
  public isShowUpdateTip: boolean = true;
  //更新完成后是否重新加载webview
  public isReloadWebView: boolean = true;
  //解压密码
  public unzippwd: string = "";
  //载文件代理
  public downloadDelegate: TKDownLoadCallBack | undefined = undefined;
  //更新UI代理
  public updateUIDelegate: TKUpdateUIDelegate | undefined = undefined;
  // 当前要更新的内部升级版本序号
  private versionSn: string = "";
  //当前要更新的展示版本号
  private version: string = "";
  //当前要更新版本的MD5值
  private versionMd5: string = "";
  //提示语
  private tip: string = "";
  //进度条
  private progressDialog: TKComponentContent<TKProgressDialogOption> | undefined = undefined;

  public constructor() {
    this.updateUIDelegate = this;
  }

  public static shareInstance(): TKVersionManager {
    if (!TKVersionManager.instance) {
      TKVersionManager.instance = new TKVersionManager();
    }
    return TKVersionManager.instance;
  }

  /**
   *
   *  初始化框架的环境
   */
  public initContext() {
    //最近软件版本号
    let appVersion: string = TKCacheManager.shareInstance().getFileCacheData(TKVersionManager.CACHE_APP_VERSION) ?? "";
    //当前软件版本号
    let curVersion: string = `${TKSystemHelper.getAppVersion()}:${TKSystemHelper.getAppVersionCode()}`;
    //保存版本号
    TKCacheManager.shareInstance().saveFileCacheData(TKVersionManager.CACHE_APP_VERSION, curVersion);
    //判断版本号的变更
    if ((appVersion != curVersion) || (TKSystemHelper.getConfig("system.isDevelop") == "1")) {
      //刷新Configration.xml配置
      TKSystemHelper.refreshConfig();
      //清空Http本地缓存服务列表地址
      TKHttpRoomServerListener.clearCache();
      // //清空Socket本地缓存服务列表地址
      // [TKConfigUpdateManager clearCache];
      //清空主题本地缓存
      TKThemeManager.shareInstance().clearCache();
      // //清理全局配置本地缓存
      // [TKAppConfigManager clearCache];
      //是否第一次安装启动:是
      TKCacheManager.shareInstance().saveFileCacheData(TKVersionManager.CACHE_ISFIRSTINSTALL, "1");
      //是否完成了框架初始化:否
      TKCacheManager.shareInstance().saveFileCacheData(TKVersionManager.CACHE_APP_FINISH_INIT, "0");
      //请求版本次数缓存清空
      TKCacheManager.shareInstance().deleteFileCacheData(`cache_update_version_${TKSystemHelper.getVersionCode()}_num`);
      TKCacheManager.shareInstance()
        .deleteFileCacheData(`cache_update_version_${TKSystemHelper.getVersionCode()}_lastnum`);
      //清理www缓存文件
      let saveName: string = TKSystemHelper.getConfig("update.saveName", "www.zip");
      let www: string = TKFileHelper.fileDir() + "/thinkive/" + TKStringHelper.replace(saveName, ".zip", "");
      TKFileHelper.deleteFile(www);
    } else {
      //是否第一次安装启动:否
      TKCacheManager.shareInstance().saveFileCacheData(TKVersionManager.CACHE_ISFIRSTINSTALL, "0");
    }
    //是否第一次安装完成了初始化，第一次APP安装或者覆盖安装后，需要进行www的H5目录copy
    let isAppFinishInit: string = TKCacheManager.shareInstance()
      .getFileCacheData(TKVersionManager.CACHE_APP_FINISH_INIT) ?? "";
    if ((isAppFinishInit != "1") || (TKSystemHelper.getConfig("system.isDevelop") == "1")) {
      //目前全部强制为成功，因为鸿蒙自带目录copy功能，覆盖安装的时候会自动同步，以后有修改，再调整这块的逻辑
      if (TKSystemHelper.getConfig("webViewPool.htmlLoadMode") != "1") {
        TKCacheManager.shareInstance().saveFileCacheData(TKVersionManager.CACHE_APP_FINISH_INIT, "1");
      } else {
        TKCacheManager.shareInstance().saveFileCacheData(TKVersionManager.CACHE_APP_FINISH_INIT, "1");
      }
    }
    TKThemeManager.shareInstance().initThemeContext();
  }

  /**
   * 软件更新
   * @param url   更新地址
   * @param newVersion  最新版本
   * @param newVersionSn 最新版本序号
   * @param newVersionMd5 最新版本MD5值
   * @param isUpdateH5  是否更新的是H5
   * @param tip 更新提示语
   */
  public updateSoftware(url: string, newVersion: string, newVersionSn: string, newVersionMd5: string,
    isUpdateH5: boolean, tip: string = "") {
    if (TKStringHelper.isNotBlank(url) && TKStringHelper.isNotBlank(newVersion) &&
    TKStringHelper.isNotBlank(newVersionSn)) {
      TKLog.info(`前H5内部版本号:${newVersionSn},当前系统内部版本号:${TKSystemHelper.getVersionCode()}`);
      if (newVersionSn != TKSystemHelper.getVersionCode()) {
        this.versionSn = newVersionSn;
        this.version = newVersion;
        this.versionMd5 = newVersionMd5;
        if (!isUpdateH5) {
          TKSystemHelper.openAppURL(url);
        } else {
          let saveName: string = TKSystemHelper.getConfig("update.saveName", "www.zip");
          if (url.indexOf("?") > 0) {
            url = (url + "&_tkr=" + new Date().getTime());
          } else {
            url = (url + "?_tkr=" + new Date().getTime());
          }
          this.tip = tip;
          this.startDownload();
          TKDownLoadManager.shareInstance().download({
            url: url,
            fileName: saveName
          } as TKDownLoadRequestVO, (downLoadResultVO: TKDownLoadResultVO) => {
            if (downLoadResultVO.status == TKDownLoadStatus.RUNNING) {
              TKLog.info("正在下载H5更新包.......");
              if (this.progressDialog) {
                this.progressDialog.reuse({
                  value: (downLoadResultVO.progress.progress * 100)
                });
              }
            } else if (downLoadResultVO.status == TKDownLoadStatus.FINISHED) {
              let isAppFinishInit: string = TKCacheManager.shareInstance()
                .getFileCacheData(TKVersionManager.CACHE_APP_FINISH_INIT) ?? "";
              if (isAppFinishInit == "1") {
                this.updateH5();
              }
            } else if (downLoadResultVO.status == TKDownLoadStatus.ERROR) {
              this.showErrorTip();
            }
            if (this.downloadDelegate) {
              this.downloadDelegate(downLoadResultVO);
            }
          });
        }
      }
    }
  }

  /**
   *  开始下载
   */
  private startDownload() {
    TKLog.info("开始下载H5更新包.......");
    if (this.isShowUpdateTip) {
      if (TKStringHelper.isBlank(this.tip)) {
        this.tip = TKSystemHelper.getConfig("update.updateTip", "更新下载中...");
      }
      this.progressDialog = TKDialogHelper.createProgressDialog({ tip: this.tip });
      this.progressDialog.open();
    }
  }

  /**
   *  显示成功信息
   */
  private showSuccessTip() {
    if (this.isShowUpdateTip) {
      let successTip: string = TKSystemHelper.getConfig("update.successTip", "更新完成！");
      TKDialogHelper.showToast(successTip);
      if (this.progressDialog) {
        this.progressDialog.close();
        this.tip = "";
      }
    }
    if (this.isReloadWebView) {
      TKLog.info("通知各个模块的H5进行重载刷新操作.......");
      TKNotificationCenter.defaultCenter.postNotificationName(TKVersionManager.NOTE_UPDATE_SUCCESS);
    }
    this.updateUIDelegate?.updateCompleted?.();
  }

  /**
   *  显示错误信息
   */
  private showErrorTip(errorTip?: string) {
    if (this.isShowUpdateTip) {
      errorTip = errorTip ?? TKSystemHelper.getConfig("update.errorTip", "更新出错！");
      TKDialogHelper.showToast(errorTip);
      if (this.progressDialog) {
        this.progressDialog.close();
        this.tip = "";
      }
    }
    TKNotificationCenter.defaultCenter.postNotificationName(TKVersionManager.NOTE_UPDATE_FAILED);
    this.updateUIDelegate?.updateFailed?.();
  }

  /**
   * 文件解压
   * @param zipFile
   * @param unzipFile
   * @param password
   * @returns
   */
  private unzip(zipFile: string, unzipFile: string, password?: string): Promise<void> {
    // return new Promise<void>((resolve, reject) => {
    //   UnrarApi.RarFiles_Extract(zipFile, unzipFile, {
    //     callBackResult: (value) => {
    //       resolve();
    //     }
    //   }, password)
    // });
    // return unzipToDirectory(zipFile, unzipFile, password);
    TKFileHelper.createFileDir(unzipFile);
    return zlib.decompressFile(zipFile, unzipFile);
  }

  /**
   *  更新H5代码
   */
  private async updateH5() {
    TKLog.info("下载H5更新包完毕.......");
    //保存文件名
    let saveName: string = TKSystemHelper.getConfig("update.saveName", "www.zip");
    //下载文件目录
    let zipPath: string = `${TKFileHelper.cacheDir()}/thinkive/download`;
    //下载文件
    let zipFile: string = zipPath + "/" + saveName;
    //解压目录
    let unzipFile: string = TKStringHelper.replace(zipFile, ".zip", "");
    //解压是否需要密码
    let isEncrypt: string = TKSystemHelper.getConfig("update.isEncrypt");
    let password: string | undefined = TKSystemHelper.getConfig("update.password");
    if (TKStringHelper.isNotBlank(this.unzippwd)) {
      isEncrypt = "1";
      password = this.unzippwd;
    }
    if (isEncrypt == "1" && TKStringHelper.isNotBlank(password)) {
      let decryptPassword: string =
        TKAesHelper.stringWithAesDecryptSync(password, TKPasswordGenerator.shareInstance().generatorPassword());
      if (TKStringHelper.isNotBlank(decryptPassword)) {
        password = decryptPassword;
      } else {
        TKLog.warn("H5下载更新的Zip包密码不建议用明文!")
      }
    } else {
      password = undefined;
    }
    try {
      if (TKStringHelper.isNotBlank(this.versionMd5)) {
        let downloadMd5: string = await TKMd5Helper.stringWithMd5(TKFileHelper.readFile(zipFile));
        if (this.versionMd5 != downloadMd5) {
          TKLog.error("下载文件被篡改!");
          this.showErrorTip("更新出错,下载文件被非法篡改！");
          return;
        }
      }
      await this.unzip(zipFile, unzipFile, password);
      //移动文件
      let dirs: Array<string> = TKFileHelper.listFiles(unzipFile);
      if (dirs && dirs.length > 0) {
        TKLog.info("移动复制下载的H5更新包到安全沙箱File目录.......");
        //要移动的文件目录
        let moveFile: string = unzipFile;
        let www: string = TKStringHelper.replace(saveName, ".zip", "");
        for (let tempDir of dirs) {
          if (tempDir == www) {
            moveFile = (unzipFile + "/" + tempDir);
            break;
          }
        }
        //检测一下解压后的文件是否正确，如果内容是空的就直接返回，这个很奇怪，例如密码错误了，应该解压失败了，但是上面显示的确是解压成功，但是内容为空
        let checkFlag: boolean = false;
        let subFiles: Array<string> = TKFileHelper.listFiles(moveFile, { recursion: true });
        for (let subFile of subFiles) {
          let updateFilePath: string = (moveFile + subFile);
          if (!TKFileHelper.isDirectory(updateFilePath)) {
            let fileSize: number = TKFileHelper.fileSize(updateFilePath);
            checkFlag = (fileSize > 1);
            break;
          }
        }
        if (checkFlag) {
          //H5的WWW目录
          www = TKFileHelper.fileDir() + "/thinkive/" + www;
          let updateFlag: boolean = true;
          //遍历文件路径
          let allFiles: Array<string> = TKFileHelper.listFiles(moveFile, { recursion: true });
          for (let file of allFiles) {
            let updateFilePath: string = (moveFile + file);
            let localFilePath: string = (www + file);
            if (!TKFileHelper.isDirectory(updateFilePath)) {
              updateFlag = TKFileHelper.moveFile(updateFilePath, localFilePath);
              if (!updateFlag) {
                break;
              }
            }
          }
          TKLog.info("移动复制下载的H5更新包到Document目录完成.......");
          if (updateFlag) {
            TKLog.info("解压更新文件成功!");
            //删除文件
            TKFileHelper.deleteFile(unzipFile);
            TKFileHelper.deleteFile(zipFile);
            //更新内部版本序号
            TKSystemHelper.setConfig("update.versionSn", this.versionSn);
            //更新展示版本号
            TKSystemHelper.setConfig("update.version", this.version);
            //删除请求版本次数缓存
            let cacheUpdateNumKey: string = `cache_update_version_${this.versionSn}_num`;
            let cacheUpdateLastNumKey: string = `cache_update_version_${this.versionSn}_lastnum`;
            TKCacheManager.shareInstance().deleteFileCacheData(cacheUpdateNumKey);
            TKCacheManager.shareInstance().deleteFileCacheData(cacheUpdateLastNumKey);
            this.showSuccessTip();
            //提醒重启软件
            let isReboot: string = TKSystemHelper.getConfig("update.isReboot");
            if (isReboot == "1") {
              TKDialogHelper.showAlertDialog({
                title: "温馨提示",
                message: "软件完成更新，本应用需要重启, 确认后程序将会退出。",
                confirmText: "确认",
                confirm: () => {
                  TKContextHelper.getCurrentContext().getApplicationContext().killAllProcesses();
                }
              });
            }
          } else {
            TKLog.error("解压更新文件失败!");
            this.showErrorTip();
          }
        } else {
          TKLog.error("解压更新文件失败!");
          this.showErrorTip();
        }
      } else {
        TKLog.error("解压更新文件失败!");
        this.showErrorTip();
      }
    } catch (e) {
      TKLog.error("解压更新文件失败!");
      this.showErrorTip();
    }
  }

  /**
   *  自定义UI提示框
   * @param updateMode 更新模式
   * @param updateInfo 更新信息
   * @param updateBtnFunc 更新按钮执行函数
   *
   */
  public showUpdateUIWindow(updateMode: TKUpdateMode, updateInfo: Record<string, Object>, updateBtnFunc: () => void) {
    //升级描述
    let description: string = TKMapHelper.getString(updateInfo, "description");
    //是否H5
    let isH5: string = TKMapHelper.getString(updateInfo, "isH5");
    switch (updateMode) {
      case TKUpdateMode.Alert: {
        TKDialogHelper.showAlertDialog({
          title: "软件更新",
          message: description,
          confirmText: "下载",
          confirm: updateBtnFunc,
          isNoClose: isH5 != "1"
        });
        break;
      }
      case TKUpdateMode.Confirm: {
        TKDialogHelper.showAlertDialog({
          title: "软件更新",
          message: description,
          confirmText: "下载",
          confirm: updateBtnFunc,
          cancelText: "取消",
          cancel: () => {
          }
        });
        break;
      }
      default:
        break;
    }
  }

  /*
   *  更新完成---注意：这个不是下载完成，下载完成并非更新完成，下载完成后，需要解压覆盖，全部动作完成后才触发更新完成回调
   */
  public updateCompleted() {
  }

  /**
   *  更新失败---注意：这个不仅仅是下载失败，除了下载失败以外，下载完成后解压失败，覆盖失败等都算更新失败
   */
  public updateFailed() {
  }
}