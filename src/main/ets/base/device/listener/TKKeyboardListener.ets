import { TKTextInputKeyBoard } from '../../../components/keyboard/input/TKTextInputKeyBoard';
import { TKKeyBoard } from '../../../components/keyboard/view/TKKeyBoard';
import { TKKeyboardHelper } from '../../../util/ui/TKKeyboardHelper';
import { TKNotificationCenter } from '../../notification/TKNotificationCenter';

/**
 * 键盘监听器
 */
export class TKKeyboardListener {
  /**
   *  键盘高度改变通知
   */
  public static readonly NOTE_KEYBOARD_CHANGE: string = "note_keyboard_change";
  /**
   * 单例对象
   */
  private static instance: TKKeyboardListener | undefined = undefined;
  /**
   * 是否系统键盘在展示
   */
  public isSysKeyboardShow: boolean = false;
  /**
   * 当前的键盘
   */
  private _curCustomerKeyboard?: WeakRef<TKKeyBoard> = undefined;
  /**
   * 当前输入框
   */
  private _curTextInputKeyboard?: WeakRef<TKTextInputKeyBoard> = undefined;
  /**
   * 是否启动监听
   */
  private isRunning: boolean = false;

  private constructor() {
  }

  public static shareInstance(): TKKeyboardListener {
    if (!TKKeyboardListener.instance) {
      TKKeyboardListener.instance = new TKKeyboardListener();
    }
    return TKKeyboardListener.instance;
  }

  /**
   * 隐藏键盘
   */
  public hideKeyboard() {
    TKKeyboardHelper.hideKeyboard();
    if (!TKKeyboardListener.shareInstance().isSysKeyboardShow &&
    TKKeyboardListener.shareInstance().curCustomerKeyboard) {
      TKKeyboardListener.shareInstance().curCustomerKeyboard = undefined;
      TKKeyboardListener.shareInstance().postKeyboardChangeEvent(0);
    }
  }

  private keyboardChangeHandler: Callback<number> = (height: number) => {
    this.isSysKeyboardShow = height > 0;
    TKNotificationCenter.defaultCenter.postNotificationName(TKKeyboardListener.NOTE_KEYBOARD_CHANGE, height);
  }

  /**
   * 开启键盘监听
   */
  public startListener() {
    if (!this.isRunning) {
      this.isRunning = true;
      TKKeyboardHelper.onKeyboardListener(this.keyboardChangeHandler);
    }
  }

  /**
   * 关闭键盘监听
   */
  public stopListener() {
    if (this.isRunning) {
      this.isRunning = false;
      TKKeyboardHelper.offKeyboardListener(this.keyboardChangeHandler);
    }
  }

  /**
   * 发送键盘变化信息
   * @param height
   */
  public postKeyboardChangeEvent(height: number) {
    TKNotificationCenter.defaultCenter.postNotificationName(TKKeyboardListener.NOTE_KEYBOARD_CHANGE, height);
  }

  public set curCustomerKeyboard(keyboard: TKKeyBoard | undefined) {
    this._curCustomerKeyboard = keyboard ? new WeakRef(keyboard) : undefined;
  }

  public get curCustomerKeyboard(): TKKeyBoard | undefined {
    return this._curCustomerKeyboard?.deref();
  }

  public set curTextInputKeyboard(textInputKeyboard: TKTextInputKeyBoard | undefined) {
    this._curTextInputKeyboard = textInputKeyboard ? new WeakRef(textInputKeyboard) : undefined;
  }

  public get curTextInputKeyboard(): TKTextInputKeyBoard | undefined {
    return this._curTextInputKeyboard?.deref();
  }
}