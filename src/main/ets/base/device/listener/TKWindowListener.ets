import { TKWindowHelper } from '../../../util/ui/TKWindowHelper';
import { window } from '@kit.ArkUI';
import { TKNotificationCenter } from '../../notification/TKNotificationCenter';

/**
 * 窗口监听器
 */
export class TKWindowListener {
  /**
   * 窗口改变通知
   */
  public static readonly NOTE_WINDOW_CHANGE: string = "note_window_change";
  /**
   * 状态栏改变通知
   */
  public static readonly NOTE_STATUS_CHANGE: string = "note_status_change";
  /**
   * 单例对象
   */
  private static instance: TKWindowListener | undefined = undefined;
  /*
   * 当前状态栏的高度
   */
  private curStatusHeight?: number = undefined;
  /**
   * 是否启动监听
   */
  private isRunning: boolean = false;

  private constructor() {
    TKWindowHelper.getLastWindow().then((win) => {
      this.curStatusHeight = win.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM).topRect.height;
    });
  }

  public static shareInstance(): TKWindowListener {
    if (!TKWindowListener.instance) {
      TKWindowListener.instance = new TKWindowListener();
    }
    return TKWindowListener.instance;
  }

  private windowSizeChangeHandler: Callback<window.Size> = (size: window.Size) => {
    TKNotificationCenter.defaultCenter.postNotificationName(TKWindowListener.NOTE_WINDOW_CHANGE, size);
    TKWindowHelper.getLastWindow().then((win) => {
      let statusBarHeight =
        win.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM).topRect.height;
      if (statusBarHeight != this.curStatusHeight) {
        this.curStatusHeight = statusBarHeight;
        TKNotificationCenter.defaultCenter.postNotificationName(TKWindowListener.NOTE_STATUS_CHANGE, statusBarHeight);
      }
    });
  }

  /**
   * 开启窗口状态监听
   */
  public startListener() {
    if (!this.isRunning) {
      this.isRunning = true;
      TKWindowHelper.onWindowSizeChangeListener(this.windowSizeChangeHandler)
    }
  }

  /**
   * 关闭窗口状态监听
   */
  public stopListener() {
    if (this.isRunning) {
      this.isRunning = false;
      TKWindowHelper.offWindowSizeChangeListener(this.windowSizeChangeHandler);
    }
  }
}