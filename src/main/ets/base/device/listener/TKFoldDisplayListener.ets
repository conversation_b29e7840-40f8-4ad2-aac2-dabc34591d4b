import { display } from '@kit.ArkUI';
import { TKDeviceHelper } from '../../../util/dev/TKDeviceHelper';
import { TKNotificationCenter } from '../../notification/TKNotificationCenter';

/**
 * 折叠屏监听器
 */
export class TKFoldDisplayListener {
  /**
   * 折叠屏状态改变通知
   */
  public static readonly NOTE_FOLD_CHANGE: string = "note_fold_change";
  /**
   * 单例对象
   */
  private static instance: TKFoldDisplayListener | undefined = undefined;
  /**
   * 是否启动监听
   */
  private isRunning: boolean = false;

  private constructor() {
  }

  public static shareInstance(): TKFoldDisplayListener {
    if (!TKFoldDisplayListener.instance) {
      TKFoldDisplayListener.instance = new TKFoldDisplayListener();
    }
    return TKFoldDisplayListener.instance;
  }

  private foldStatusChangeHandler: Callback<display.FoldStatus> = (foldStatus: display.FoldStatus) => {
    TKNotificationCenter.defaultCenter.postNotificationName(TKFoldDisplayListener.NOTE_FOLD_CHANGE, foldStatus);
  }

  /**
   * 开启折叠屏监听
   */
  public startListener() {
    if (!this.isRunning) {
      this.isRunning = true;
      TKDeviceHelper.onFoldStatusChange(this.foldStatusChangeHandler);
    }
  }

  /**
   * 关闭折叠屏监听
   */
  public stopListener() {
    if (this.isRunning) {
      this.isRunning = false;
      TKDeviceHelper.offFoldStatusChange(this.foldStatusChangeHandler);
    }
  }

  /**
   * 获取可折叠设备的显示模式。
   * FoldDisplayMode:
   *   FOLD_DISPLAY_MODE_UNKNOWN  0  表示设备当前折叠显示模式未知。
   *   FOLD_DISPLAY_MODE_FULL  1  表示设备当前全屏显示。
   *   FOLD_DISPLAY_MODE_MAIN  2  表示设备当前主屏幕显示。
   *   FOLD_DISPLAY_MODE_SUB  3  表示设备当前子屏幕显示。
   *   FOLD_DISPLAY_MODE_COORDINATION  4  表示设备当前双屏协同显示。
   * @returns
   */
  public getFoldDisplayMode(): display.FoldDisplayMode {
    return display.getFoldDisplayMode();
  }

  /**
   * 获取可折叠设备的当前折叠状态。
   * FoldStatus:
   *   FOLD_STATUS_UNKNOWN  0  表示设备当前折叠状态未知。
   *   FOLD_STATUS_EXPANDED 1  表示设备当前折叠状态为完全展开。
   *   FOLD_STATUS_FOLDED  2  表示设备当前折叠状态为折叠。
   *   FOLD_STATUS_HALF_FOLDED  3  表示设备当前折叠状态为半折叠。半折叠指完全展开和折叠之间的状态。
   * @returns
   * @returns
   */
  public getFoldStatus(): display.FoldStatus {
    return display.getFoldStatus();
  }
}