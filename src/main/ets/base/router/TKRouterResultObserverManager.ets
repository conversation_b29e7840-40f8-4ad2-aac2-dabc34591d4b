import { TKStringHelper } from '../../util/string/TKStringHelper';

/**
 * 路由回调监听对象
 */
export interface TKRouterResultObserver {
  onResult: (data: Record<string, Object>) => void;
}

/**
 * 路由结果观察者管理器
 */
export class TKRouterResultObserverManager {
  /**
   * 定义路由跳转的观察者缓存
   */
  private routerResultObservers: Map<string, TKRouterResultObserver> = new Map<string, TKRouterResultObserver>();
  private static instance: TKRouterResultObserverManager | undefined = undefined;

  public static shareInstance(): TKRouterResultObserverManager {
    if (!TKRouterResultObserverManager.instance) {
      TKRouterResultObserverManager.instance = new TKRouterResultObserverManager();
    }
    return TKRouterResultObserverManager.instance;
  }

  /**
   * 添加流水号数据的观察者
   * @param flowNo
   * @param resultObserver
   */
  public register(flowNo: string, resultObserver: TKRouterResultObserver) {
    if (TKStringHelper.isNotBlank(flowNo) && resultObserver !== undefined) {
      this.routerResultObservers.set(flowNo, resultObserver);
    }
  }

  /**
   * 获取观察者
   * @param flowNo
   * @returns
   */
  public get(flowNo: string): TKRouterResultObserver | undefined {
    if (TKStringHelper.isNotBlank(flowNo)) {
      return this.routerResultObservers.get(flowNo);
    }
    return undefined;
  }

  /**
   * 移除流水号数据的观察者
   * @param flowNo
   */
  public remove(flowNo: string) {
    if (TKStringHelper.isNotBlank(flowNo)) {
      this.routerResultObservers.delete(flowNo);
    }
  }
}