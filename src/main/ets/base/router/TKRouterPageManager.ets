import { TKRouterPageInfo, TKRouterPageStyle } from './TKRouterPageInfo';
import { ArrayList } from '@kit.ArkTS';
import { router, uiObserver, window } from '@kit.ArkUI';
import { TKContextHelper } from '../../util/system/TKContextHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKUUIDHelper } from '../../util/crypto/TKUUIDHelper';
import { TKMapHelper } from '../../util/map/TKMapHelper';
import { TKBarName } from '../../util/ui/TKWindowHelper';
import { TKRouterResultObserverManager } from './TKRouterResultObserverManager';
import { TKObjectHelper } from '../../util/data/TKObjectHelper';

/**
 * 页面路由管理器
 */
export class TKRouterPageManager {
  private pageStack: ArrayList<TKRouterPageInfo> = new ArrayList<TKRouterPageInfo>();
  private static instance: TKRouterPageManager | undefined = undefined;
  private isRunning: boolean = false;

  public static shareInstance(): TKRouterPageManager {
    if (!TKRouterPageManager.instance) {
      TKRouterPageManager.instance = new TKRouterPageManager();
    }
    return TKRouterPageManager.instance;
  }

  public async startListener() {
    if (!this.isRunning) {
      this.isRunning = true;
      uiObserver.on('routerPageUpdate', TKContextHelper.getCurrentUIAbilityContext(),
        this.handleRouterPageInfoListener);
      uiObserver.on('navDestinationSwitch', TKContextHelper.getCurrentUIAbilityContext(),
        this.handleNavRouterPageInfoListener);
    }
  }

  public stopListener() {
    if (this.isRunning) {
      this.isRunning = false;
      uiObserver.off('routerPageUpdate', TKContextHelper.getCurrentUIAbilityContext(),
        this.handleRouterPageInfoListener);
      uiObserver.off('navDestinationSwitch', TKContextHelper.getCurrentUIAbilityContext(),
        this.handleNavRouterPageInfoListener);
    }
  }

  //监听页面显示隐藏事件
  private handleRouterPageInfoListener: Callback<uiObserver.RouterPageInfo> =
    (routerPageInfo: uiObserver.RouterPageInfo) => {
      if (routerPageInfo.state == uiObserver.RouterPageState.ABOUT_TO_APPEAR) {
        let curRouteState: router.RouterState = router.getState();
        let srcRouteState: router.RouterState | undefined =
          (curRouteState.index > 0) ? router.getStateByIndex(curRouteState.index - 1) : undefined;
        let window: window.Window = TKContextHelper.getCurrentMainWindow();

        let srcLayoutFullScreen: boolean =
          TKMapHelper.getBoolean((curRouteState.params ?? {}) as Record<string, Object>, "@srcLayoutFullScreen",
            TKMapHelper.getBoolean((srcRouteState?.params ?? {}) as Record<string, Object>, "@layoutFullScreen",
              window.getWindowProperties().isLayoutFullScreen));
        let srcSystemBarEnable: Array<TKBarName> =
          TKMapHelper.getObject((curRouteState.params ?? {}) as Record<string, Object>,
            "@srcSystemBarEnable", TKMapHelper.getObject((srcRouteState?.params ?? {}) as Record<string, Object>,
              "@systemBarEnable", [TKBarName.status, TKBarName.navigationIndicator])) as Array<TKBarName>;
        let srcBackgroundColor: string | undefined =
          TKMapHelper.getObject((curRouteState.params ?? {}) as Record<string, Object>, "@srcBackgroundColor",
            TKMapHelper.getObject((srcRouteState?.params ?? {}) as Record<string, Object>,
              "@backgroundColor")) as string;
        let srcStatusStyle: string | undefined =
          TKMapHelper.getObject((curRouteState.params ?? {}) as Record<string, Object>, "@srcStatusStyle",
            TKMapHelper.getObject((srcRouteState?.params ?? {}) as Record<string, Object>, "@statusStyle")) as string;
        let srcSystemBarProperties: window.SystemBarProperties =
          TKMapHelper.getObject((curRouteState.params ?? {}) as Record<string, Object>,
            "@srcSystemBarProperties", TKMapHelper.getObject((srcRouteState?.params ?? {}) as Record<string, Object>,
              "@systemBarProperties", window.getWindowSystemBarProperties())) as window.SystemBarProperties;

        let srcPageStyle: TKRouterPageStyle = {
          layoutFullScreen: srcLayoutFullScreen,
          systemBarEnable: srcSystemBarEnable,
          backgroundColor: srcBackgroundColor,
          systemBarProperties: srcSystemBarProperties,
          statusStyle: srcStatusStyle
        };

        let layoutFullScreen: Object | undefined =
          TKMapHelper.getObject((curRouteState.params ?? {}) as Record<string, Object>, "@layoutFullScreen");
        if (TKObjectHelper.nonNull(layoutFullScreen)) {
          layoutFullScreen =
            TKMapHelper.getBoolean((curRouteState.params ?? {}) as Record<string, Object>, "@layoutFullScreen");
        }
        let systemBarEnable: Array<TKBarName> | undefined =
          TKMapHelper.getObject((curRouteState.params ?? {}) as Record<string, Object>,
            "@systemBarEnable") as Array<TKBarName>;
        let backgroundColor: string | undefined =
          TKMapHelper.getObject((curRouteState.params ?? {}) as Record<string, Object>, "@backgroundColor") as string;
        let statusStyle: string | undefined =
          TKMapHelper.getObject((curRouteState.params ?? {}) as Record<string, Object>, "@statusStyle") as string;
        let systemBarProperties: window.SystemBarProperties | undefined =
          TKMapHelper.getObject((curRouteState.params ?? {}) as Record<string, Object>,
            "@systemBarProperties") as window.SystemBarProperties;

        let pageStyle: TKRouterPageStyle = {
          layoutFullScreen: layoutFullScreen as boolean,
          systemBarEnable: systemBarEnable,
          backgroundColor: backgroundColor,
          systemBarProperties: systemBarProperties,
          statusStyle: statusStyle
        };

        let tkRouterPageInfo: TKRouterPageInfo = {
          flowNo: TKMapHelper.getString(curRouteState.params as Record<string, Object>, "@flowNo", TKUUIDHelper.uuid()),
          url: curRouteState.path,
          name: curRouteState.name,
          pageId: routerPageInfo.pageId,
          params: curRouteState.params as Record<string, Object>,
          srcPageStyle: srcPageStyle,
          pageStyle: pageStyle
        };
        this.push(tkRouterPageInfo);
      } else if (routerPageInfo.state == uiObserver.RouterPageState.ABOUT_TO_DISAPPEAR) {
        let urlOrName: string = this.getRoutePageUrlOrName(routerPageInfo as Object as Record<string, Object>);
        let tkRouterPageInfo: TKRouterPageInfo | undefined = this.pop(urlOrName);
        if (tkRouterPageInfo) {
          TKRouterResultObserverManager.shareInstance().remove(tkRouterPageInfo.flowNo!);
        }
      }
    }
  //监听导航栏页面显示隐藏事件
  private handleNavRouterPageInfoListener: Callback<uiObserver.NavDestinationSwitchInfo> =
    (navDestinationSwitchInfo: uiObserver.NavDestinationSwitchInfo) => {
      let srcNavDestinationInfo: NavDestinationInfo = navDestinationSwitchInfo.from as NavDestinationInfo;
      let distNavDestinationInfo: NavDestinationInfo = navDestinationSwitchInfo.to as NavDestinationInfo;
      if (navDestinationSwitchInfo.operation == NavigationOperation.PUSH ||
        navDestinationSwitchInfo.operation == NavigationOperation.REPLACE) {
        let window: window.Window = TKContextHelper.getCurrentMainWindow();

        let srcLayoutFullScreen: boolean = navDestinationSwitchInfo.operation == NavigationOperation.PUSH ?
        TKMapHelper.getBoolean((distNavDestinationInfo.param ?? {}) as Record<string, Object>, "@srcLayoutFullScreen",
          TKMapHelper.getBoolean((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>, "@layoutFullScreen",
            window.getWindowProperties().isLayoutFullScreen)) :
        TKMapHelper.getBoolean((distNavDestinationInfo.param ?? {}) as Record<string, Object>, "@srcLayoutFullScreen",
          TKMapHelper.getBoolean((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>, "@srcLayoutFullScreen",
            window.getWindowProperties().isLayoutFullScreen));
        let srcSystemBarEnable: Array<TKBarName> = navDestinationSwitchInfo.operation == NavigationOperation.PUSH ?
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>,
            "@srcSystemBarEnable", TKMapHelper.getObject((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>,
              "@systemBarEnable", [TKBarName.status, TKBarName.navigationIndicator])) as Array<TKBarName> :
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>,
            "@srcSystemBarEnable", TKMapHelper.getObject((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>,
              "@srcSystemBarEnable", [TKBarName.status, TKBarName.navigationIndicator])) as Array<TKBarName>;
        let srcBackgroundColor: string | undefined = navDestinationSwitchInfo.operation == NavigationOperation.PUSH ?
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>, "@srcBackgroundColor",
            TKMapHelper.getObject((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>,
              "@backgroundColor")) as string :
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>, "@srcBackgroundColor",
            TKMapHelper.getObject((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>,
              "@srcBackgroundColor")) as string;
        let srcStatusStyle: string | undefined = navDestinationSwitchInfo.operation == NavigationOperation.PUSH ?
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>, "@srcStatusStyle",
            TKMapHelper.getObject((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>,
              "@statusStyle")) as string :
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>, "@srcStatusStyle",
            TKMapHelper.getObject((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>,
              "@srcStatusStyle")) as string;
        let srcSystemBarProperties: window.SystemBarProperties =
          navDestinationSwitchInfo.operation == NavigationOperation.PUSH ?
            TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>,
              "@srcSystemBarProperties",
              TKMapHelper.getObject((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>,
                "@systemBarProperties", window.getWindowSystemBarProperties())) as window.SystemBarProperties :
            TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>,
              "@srcSystemBarProperties",
              TKMapHelper.getObject((srcNavDestinationInfo?.param ?? {}) as Record<string, Object>,
                "@srcSystemBarProperties", window.getWindowSystemBarProperties())) as window.SystemBarProperties;

        let srcPageStyle: TKRouterPageStyle = {
          layoutFullScreen: srcLayoutFullScreen,
          systemBarEnable: srcSystemBarEnable,
          backgroundColor: srcBackgroundColor,
          systemBarProperties: srcSystemBarProperties,
          statusStyle: srcStatusStyle
        };

        let layoutFullScreen: Object | undefined =
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>, "@layoutFullScreen");
        if (TKObjectHelper.nonNull(layoutFullScreen)) {
          layoutFullScreen =
            TKMapHelper.getBoolean((distNavDestinationInfo.param ?? {}) as Record<string, Object>, "@layoutFullScreen");
        }
        let systemBarEnable: Array<TKBarName> | undefined =
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>,
            "@systemBarEnable") as Array<TKBarName>;
        let backgroundColor: string | undefined =
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>,
            "@backgroundColor") as string;
        let statusStyle: string | undefined =
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>,
            "@statusStyle") as string;
        let systemBarProperties: window.SystemBarProperties | undefined =
          TKMapHelper.getObject((distNavDestinationInfo.param ?? {}) as Record<string, Object>,
            "@systemBarProperties") as window.SystemBarProperties;

        let pageStyle: TKRouterPageStyle = {
          layoutFullScreen: layoutFullScreen as boolean,
          systemBarEnable: systemBarEnable,
          backgroundColor: backgroundColor,
          systemBarProperties: systemBarProperties,
          statusStyle: statusStyle
        };

        let tkRouterPageInfo: TKRouterPageInfo = {
          flowNo: TKMapHelper.getString(distNavDestinationInfo.param as Record<string, Object>, "@flowNo",
            TKUUIDHelper.uuid()),
          url: distNavDestinationInfo.name as string,
          name: distNavDestinationInfo.name as string,
          pageId: distNavDestinationInfo.navDestinationId,
          params: distNavDestinationInfo.param as Record<string, Object>,
          srcPageStyle: srcPageStyle,
          pageStyle: pageStyle
        };
        this.push(tkRouterPageInfo);
      } else if (navDestinationSwitchInfo.operation == NavigationOperation.POP) {
        let urlOrName: string = this.getRoutePageUrlOrName(srcNavDestinationInfo as Object as Record<string, Object>);
        let tkRouterPageInfo: TKRouterPageInfo | undefined = this.pop(urlOrName);
        if (tkRouterPageInfo) {
          TKRouterResultObserverManager.shareInstance().remove(tkRouterPageInfo.flowNo!);
        }
      }
    }

  /**
   * 获取指定路由页面的UrlOrName
   * @param routeInfo
   */
  public getRoutePageUrlOrName(routeInfo: Record<string, Object>): string {
    let params: Record<string, Object> = (routeInfo.params ?? routeInfo.param ?? {}) as Record<string, Object>;
    let urlOrName: string = TKMapHelper.getString(params, "@flowNo");
    if (TKStringHelper.isBlank(urlOrName)) {
      urlOrName = TKMapHelper.getString(routeInfo, "name");
    }
    if (TKStringHelper.isBlank(urlOrName)) {
      urlOrName = TKMapHelper.getString(routeInfo, "path");
    }
    return urlOrName;
  }

  /**
   * 压栈处理
   * @param routerPageInfo
   */
  public push(routerPageInfo: TKRouterPageInfo) {
    this.pageStack.add(routerPageInfo);
  }

  /**
   * 出栈处理
   * @param urlOrName
   * @returns
   */
  public pop(urlOrName?: string): TKRouterPageInfo | undefined {
    if (TKStringHelper.isNotBlank(urlOrName)) {
      for (let index = this.pageStack.length - 1; index >= 0; index--) {
        const routerPageInfo: TKRouterPageInfo = this.pageStack[index];
        if (routerPageInfo.flowNo == urlOrName || routerPageInfo.name == urlOrName || routerPageInfo.url == urlOrName) {
          return this.pageStack.removeByIndex(index);
        }
      }
    } else if (this.pageStack.length > 0) {
      return this.pageStack.removeByIndex(this.pageStack.length - 1);
    }
    return undefined;
  }

  /**
   * 获取栈信息
   * @param urlOrName
   * @param getIndex 偏移索引
   * @returns
   */
  public get(urlOrName?: string, offsetIndex?: number): TKRouterPageInfo | undefined {
    let pageIndex: number = -1;
    if (TKStringHelper.isNotBlank(urlOrName)) {
      for (let index = this.pageStack.length - 1; index >= 0; index--) {
        const routerPageInfo: TKRouterPageInfo = this.pageStack[index];
        if (routerPageInfo.flowNo == urlOrName || routerPageInfo.name == urlOrName || routerPageInfo.url == urlOrName) {
          pageIndex = index;
          break;
        }
      }
    } else {
      pageIndex = this.pageStack.length - 1;
    }
    if (offsetIndex) {
      pageIndex += offsetIndex;
    }
    if (pageIndex >= this.pageStack.length || pageIndex < 0) {
      return undefined;
    } else {
      return this.pageStack[pageIndex];
    }
  }
}