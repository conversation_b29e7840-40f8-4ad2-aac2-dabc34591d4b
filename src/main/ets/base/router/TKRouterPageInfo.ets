/**
 * 页面基础样式
 */
import { TKBarName } from '../../util/ui/TKWindowHelper';
import { router, window } from '@kit.ArkUI';

/*
* 路由样式
 */
export interface TKRouterPageStyle {
  /**
   * 窗口是否是全屏
   */
  layoutFullScreen?: boolean;

  /**
   * 窗口导航栏和状态栏是否显示
   */
  systemBarEnable?: Array<TKBarName>;

  /**
   * 窗口的背景颜色
   */
  backgroundColor?: string;

  /**
   * 系统状态栏风格，0:黑色，1:白色
   */
  statusStyle?: string;

  /**
   * 系统状态栏样式
   */
  systemBarProperties?: window.SystemBarProperties;
}

/**
 * 路由跳转参数
 */
export interface TKRouterPageInfo {
  /**
   * 流水号
   */
  flowNo?: string,

  /**
   * 路由path
   */
  url?: string,

  /**
   * 命名路由名
   */
  name?: string,

  /**
   * 页面唯一标识
   */
  pageId?: string;

  /**
   * 路由模式
   */
  mode?: router.RouterMode;

  /**
   * 路由参数
   */
  params?: Record<string, Object>;

  /**
   * 当前页面风格
   */
  pageStyle?: TKRouterPageStyle,

  /**
   * 前面页面风格
   */
  srcPageStyle?: TKRouterPageStyle;

  /**
   * 回调函数
   * @param data
   */
  onResult?: (data: Record<string, Object>) => void;

  /**
   * 当前Nav子组件对象或者堆栈对象
   */
  navComponentOrPathStack?: CustomComponent | NavPathStack;
}

/**
 * 路由返回选项
 */
export interface TKRouterBackOption {
  /**
   * 返回结果
   */
  result?: Record<string, Object>;

  /**
   *  是否路由返回
   */
  isRouterBack?: boolean;

  /**
   * 返回指定的url或者name
   */
  urlOrName?: string;

  /**
   * 当前Nav子组件对象或者堆栈对象
   */
  navComponentOrPathStack?: CustomComponent | NavPathStack;
}
