import { router, window } from '@kit.ArkUI';
import { TKArrayHelper } from '../../util/array/TKArrayHelper';
import { TKUUIDHelper } from '../../util/crypto/TKUUIDHelper';
import { TKObjectHelper } from '../../util/data/TKObjectHelper';
import { TKLog } from '../../util/logger/TKLog';
import { TKMapHelper } from '../../util/map/TKMapHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKContextHelper } from '../../util/system/TKContextHelper';
import { TKDialogHelper } from '../../util/ui/TKDialogHelper';
import { TKBarName, TKWindowHelper } from '../../util/ui/TKWindowHelper';
import { TKRouterBackOption, TKRouterPageInfo, TKRouterPageStyle } from './TKRouterPageInfo';
import * as TKNavRouteInfo from './TKNavRouterIndex';
import { TKRouterPageManager } from './TKRouterPageManager';
import { TKRouterResultObserver, TKRouterResultObserverManager } from './TKRouterResultObserverManager';
import { TKSystemHelper } from '../../util/system/TKSystemHelper';

/**
 * 路由跳转拦截器
 */
export interface TKRouterFilter {
  /**
   * 前拦截
   * @param routerPageInfo
   * @returns true 继续执行后续逻辑 false 中断逻辑
   */
  before?: (routerPageInfo: TKRouterPageInfo) => Promise<boolean>;

  /**
   * 后拦截
   * @param routerPageInfo
   * @returns true 继续执行后续逻辑 false 中断逻辑
   */
  after?: (routerPageInfo: TKRouterPageInfo) => Promise<boolean>;

  /**
   * 页面展示，目前绑定TKPageContainer组件isOnRouterPageShowOrHide属性
   * @param routerPageInfo
   * @returns
   */
  pageShow?: (pageName?: string, pageInfo?: Record<string, Object>) => Promise<boolean>;

  /**
   * 页面隐藏，目前绑定TKPageContainer组件isOnRouterPageShowOrHide属性
   * @param routerPageInfo
   * @returns
   */
  pageHide?: (pageName?: string, pageInfo?: Record<string, Object>) => Promise<boolean>;
}

/**
 * 导航路由对象
 */
export interface TKNavRouterInfo {
  /**
   * 名称
   */
  name: string;

  /**
   * 界面构造器
   */
  buildFunction: WrappedBuilder<[Object]>;
}

/**
 * 路由框架的页面跳转
 */
export namespace TKRouterHelper {

  //当前回退的页面
  let prevBackRoutePage: string = "";

  //当前Nav堆栈的名称
  let curNavPathStackName: string = "";

  //Nav堆栈Map
  let navPathStackMap: Map<string, NavPathStack> = new Map<string, NavPathStack>();

  //路由拦截器
  let routerFilters: Array<TKRouterFilter> = new Array();

  //是否可以退出app
  let isCanExitApp: boolean = false;

  //导航栏路由信息
  let navigationRouterMap: Map<string, TKNavRouterInfo> = new Map<string, TKNavRouterInfo>();

  /**
   * 注册导航栏路由信息
   */
  export function registerNavRouterObject(navRouter: Object) {
    Object.entries(navRouter).forEach((e: Array<Object>, i: number) => {
      let navRouterInfoFunc = e[1] as Function;
      registerNavRouterInfo(navRouterInfoFunc());
    });
  }

  /**
   * 注册导航栏路由信息
   * @param navRouterInfos
   */
  export function registerNavRouterInfo(navRouterInfo: TKNavRouterInfo) {
    navigationRouterMap.set(navRouterInfo.name, navRouterInfo);
  }

  /**
   * 获取注册导航栏路由Map
   * @returns
   */
  export function getNavigationRouterMap(): Map<string, TKNavRouterInfo> {
    return navigationRouterMap;
  }

  /**
   * 获取注册的路由拦截器
   * @returns
   */
  export function getRegisterRouterFilters(): Array<TKRouterFilter> {
    return routerFilters;
  }

  /**
   * 注册路由拦截器
   * @param messageFilter
   */
  export function onRegisterRouterFilter(routerFilter: TKRouterFilter) {
    if (!routerFilters.includes(routerFilter)) {
      routerFilters.push(routerFilter);
    }
  }

  /**
   *  卸载路由拦截器
   */
  export function unRegisterRouterFilter(routerFilter: TKRouterFilter) {
    TKArrayHelper.removeObjectInArray(routerFilters, routerFilter);
  }

  /**
   * 注册路由
   * @param navPathStackName 路由名称
   * @param navPathStack 路由堆栈
   */
  export function registerNavPathStack(navPathStackName: string, navPathStack: NavPathStack) {
    if (!navPathStackMap.has(navPathStackName)) {
      navPathStackMap.set(navPathStackName, navPathStack);
    }
    curNavPathStackName = navPathStackName;
  }

  /**
   * 卸载路由
   * @param navPathStackName 路由名称
   * @param navPathStack  路由堆栈
   */
  export function unRegisterNavPathStack(navPathStackName: string) {
    navPathStackMap.delete(navPathStackName);
  }

  /**
   * 设置当前路由名称
   * @param navPathStackName  路由名称
   */
  export function setCurNavPathStackName(navPathStackName: string) {
    curNavPathStackName = navPathStackName;
  }

  /**
   * 获取当前路由堆栈
   * @returns
   */
  export function getCurNavPathStack(navComponentOrPathStack?: CustomComponent | NavPathStack): NavPathStack | undefined {
    navComponentOrPathStack = getCurNavComponentOrPathStack(navComponentOrPathStack);
    if (navComponentOrPathStack instanceof NavPathStack) {
      return navComponentOrPathStack;
    } else {
      return navComponentOrPathStack?.queryNavigationInfo()?.pathStack;
    }
  }

  /**
   * 获取当前路由堆栈
   * @returns
   */
  function getCurNavComponentOrPathStack(navComponentOrPathStack?: CustomComponent | NavPathStack): CustomComponent | NavPathStack | undefined {
    return navPathStackMap.get(curNavPathStackName) ?? navComponentOrPathStack;
  }

  /**
   * 初始化参数
   * @param routerPageInfo
   */
  function buildRouterPageInfo(routerPageInfo: TKRouterPageInfo) {
    routerPageInfo.flowNo = TKUUIDHelper.uuid();
    routerPageInfo.mode = routerPageInfo.mode ?? router.RouterMode.Standard;
    routerPageInfo.params = routerPageInfo.params ?? {};
    routerPageInfo.params["@flowNo"] = routerPageInfo.flowNo!;
    routerPageInfo.navComponentOrPathStack = getCurNavComponentOrPathStack(routerPageInfo.navComponentOrPathStack);
    let window: window.Window = TKContextHelper.getCurrentMainWindow();

    let srcPageStyle: TKRouterPageStyle = {};
    srcPageStyle.layoutFullScreen =
      routerPageInfo?.srcPageStyle?.layoutFullScreen ?? window.getWindowProperties().isLayoutFullScreen;
    srcPageStyle.systemBarEnable =
      routerPageInfo?.srcPageStyle?.systemBarEnable ?? [TKBarName.status, TKBarName.navigationIndicator];
    srcPageStyle.backgroundColor = routerPageInfo?.srcPageStyle?.backgroundColor;
    srcPageStyle.systemBarProperties =
      routerPageInfo?.srcPageStyle?.systemBarProperties ?? window.getWindowSystemBarProperties();
    srcPageStyle.statusStyle = routerPageInfo?.srcPageStyle?.statusStyle;

    let pageStyle: TKRouterPageStyle = {};
    pageStyle.layoutFullScreen = routerPageInfo?.pageStyle?.layoutFullScreen ??
    TKMapHelper.getBoolean(routerPageInfo.params, "isLayoutFullScreen",
      window.getWindowProperties().isLayoutFullScreen);
    pageStyle.systemBarEnable =
      routerPageInfo?.pageStyle?.systemBarEnable ?? [TKBarName.status, TKBarName.navigationIndicator];
    pageStyle.backgroundColor = routerPageInfo?.pageStyle?.backgroundColor;
    pageStyle.systemBarProperties =
      routerPageInfo?.pageStyle?.systemBarProperties ?? window.getWindowSystemBarProperties();
    if (TKStringHelper.isBlank(routerPageInfo?.pageStyle?.systemBarProperties?.statusBarColor)) {
      let statusBarColor: string = TKMapHelper.getString(routerPageInfo.params, "titleBgColor",
        TKMapHelper.getString(routerPageInfo.params, "statusBarColor"));
      if (TKStringHelper.isNotBlank(statusBarColor)) {
        if (pageStyle.layoutFullScreen) {
          let windowSystemBarProperties: window.SystemBarProperties = window.getWindowSystemBarProperties();
          if (windowSystemBarProperties.statusBarColor &&
            windowSystemBarProperties.statusBarColor!.toUpperCase() != '#00FFFFFF') {
            pageStyle.systemBarProperties.statusBarColor = statusBarColor;
          }
        } else {
          pageStyle.systemBarProperties.statusBarColor = statusBarColor;
        }
      }
    }
    pageStyle.statusStyle = routerPageInfo?.pageStyle?.statusStyle ??
    TKMapHelper.getString(routerPageInfo.params, "statusStyle", pageStyle.statusStyle);
    if (TKStringHelper.isNotBlank(pageStyle.statusStyle)) {
      pageStyle.systemBarProperties.statusBarContentColor = (pageStyle.statusStyle == "0" ? "#FF000000" : "#FFFFFFFF");
    } else {
      pageStyle.systemBarProperties.statusBarContentColor =
        routerPageInfo?.pageStyle?.systemBarProperties?.statusBarContentColor ??
        TKMapHelper.getString(routerPageInfo.params, "statusBarContentColor",
          pageStyle.systemBarProperties.statusBarContentColor)
    }
    routerPageInfo.srcPageStyle = srcPageStyle;
    routerPageInfo.pageStyle = pageStyle;

    if (routerPageInfo.srcPageStyle && routerPageInfo.srcPageStyle.systemBarEnable) {
      routerPageInfo.params["@srcSystemBarEnable"] = routerPageInfo.srcPageStyle.systemBarEnable!;
    }
    if (routerPageInfo.srcPageStyle && routerPageInfo.srcPageStyle.backgroundColor) {
      routerPageInfo.params["@srcBackgroundColor"] = routerPageInfo.srcPageStyle.backgroundColor!;
    }
    if (routerPageInfo.srcPageStyle && TKStringHelper.isNotBlank(routerPageInfo.srcPageStyle.statusStyle)) {
      routerPageInfo.params["@srcStatusStyle"] = routerPageInfo.srcPageStyle.statusStyle!;
    }
    if (routerPageInfo.srcPageStyle && TKObjectHelper.nonNull(routerPageInfo.srcPageStyle.layoutFullScreen)) {
      routerPageInfo.params["@srcLayoutFullScreen"] = routerPageInfo.srcPageStyle.layoutFullScreen!;
    }
    if (routerPageInfo.srcPageStyle && routerPageInfo.srcPageStyle.systemBarProperties) {
      routerPageInfo.params["@srcSystemBarProperties"] = routerPageInfo.srcPageStyle.systemBarProperties!;
    }

    if (routerPageInfo.pageStyle && routerPageInfo.pageStyle.systemBarEnable) {
      routerPageInfo.params["@systemBarEnable"] = routerPageInfo.pageStyle.systemBarEnable!;
    }
    if (routerPageInfo.pageStyle && routerPageInfo.pageStyle.backgroundColor) {
      routerPageInfo.params["@backgroundColor"] = routerPageInfo.pageStyle.backgroundColor!;
    }
    if (routerPageInfo.pageStyle && TKStringHelper.isNotBlank(routerPageInfo.pageStyle.statusStyle)) {
      routerPageInfo.params["@statusStyle"] = routerPageInfo.pageStyle.statusStyle!;
    }
    if (routerPageInfo.pageStyle && TKObjectHelper.nonNull(routerPageInfo.pageStyle.layoutFullScreen)) {
      routerPageInfo.params["@layoutFullScreen"] = routerPageInfo.pageStyle.layoutFullScreen!;
    }
    if (routerPageInfo.pageStyle && routerPageInfo.pageStyle.systemBarProperties) {
      routerPageInfo.params["@systemBarProperties"] = routerPageInfo.pageStyle.systemBarProperties!;
    }

    if (routerPageInfo.pageStyle && TKObjectHelper.nonNull(routerPageInfo.pageStyle.layoutFullScreen)) {
      routerPageInfo.params["isLayoutFullScreen"] = routerPageInfo.pageStyle.layoutFullScreen!;
    }
    if (routerPageInfo.pageStyle && TKStringHelper.isNotBlank(routerPageInfo.pageStyle.statusStyle)) {
      routerPageInfo.params["statusStyle"] = routerPageInfo.pageStyle.statusStyle!;
    }

    if (routerPageInfo.pageStyle && routerPageInfo.pageStyle.systemBarProperties &&
    routerPageInfo.pageStyle.systemBarProperties.statusBarColor) {
      routerPageInfo.params["statusBarColor"] =
        routerPageInfo.params["statusBarColor"] ?? routerPageInfo.pageStyle.systemBarProperties.statusBarColor!;
      routerPageInfo.params["titleBgColor"] =
        routerPageInfo.params["titleBgColor"] ?? routerPageInfo.pageStyle.systemBarProperties.statusBarColor!;
    }

    if (routerPageInfo.pageStyle && routerPageInfo.pageStyle.systemBarProperties &&
    routerPageInfo.pageStyle.systemBarProperties.statusBarContentColor) {
      routerPageInfo.params["statusBarContentColor"] =
        routerPageInfo.pageStyle.systemBarProperties.statusBarContentColor;
      routerPageInfo.params["statusStyle"] =
        routerPageInfo.pageStyle.systemBarProperties.statusBarContentColor.toUpperCase() == "#FF000000" ? "0" : "1";
    }
  }

  /**
   * 重置路由样式
   * @param routerInfo
   * @returns
   */
  async function rebuildRoutePageStyle(routePageStyle: TKRouterPageStyle): Promise<void> {
    await TKWindowHelper.setStatusBar({
      layoutFullScreen: routePageStyle.layoutFullScreen,
      barNames: routePageStyle.systemBarEnable,
      barProperties: routePageStyle.systemBarProperties,
      winBgColor: routePageStyle.backgroundColor,
    }, TKContextHelper.getCurrentMainWindow());
  }

  /**
   * 更新当前路由样式
   */
  export function updateCurRoutePageStyle(pageStyle: TKRouterPageStyle,
    navComponentOrPathStack?: CustomComponent | NavPathStack) {
    let urlOrName: string | undefined =
      TKRouterPageManager.shareInstance()
        .getRoutePageUrlOrName(getRouteState(navComponentOrPathStack) as Object as Record<string, Object>);
    let routerInfo: TKRouterPageInfo | undefined = TKRouterPageManager.shareInstance().get(urlOrName);
    if (routerInfo) {
      routerInfo.pageStyle = pageStyle;
    }
  }

  /**
   * 刷新当前路由样式
   */
  export async function refreshCurRoutePageStyle(navComponentOrPathStack?: CustomComponent | NavPathStack) {
    let urlOrName: string | undefined =
      TKRouterPageManager.shareInstance()
        .getRoutePageUrlOrName(getRouteState(navComponentOrPathStack) as Object as Record<string, Object>);
    let routerInfo: TKRouterPageInfo | undefined = TKRouterPageManager.shareInstance().get(urlOrName);
    if (routerInfo && routerInfo.pageStyle) {
      await rebuildRoutePageStyle(routerInfo.pageStyle);
    }
  }

  /**
   * 处理单例模式下转移原页面的状态
   * @param routerPageInfo
   */
  function singleOffsetSrcPageStyle(routerPageInfo: TKRouterPageInfo, urlOrName: string) {
    let hisNextRouterPageInfo = TKRouterPageManager.shareInstance().get(urlOrName, 1);
    let hisRouterPageInfo = TKRouterPageManager.shareInstance().pop(urlOrName);
    if (hisRouterPageInfo) {
      // 将栈中要销毁的页面数据转移给目标下一页
      if (hisNextRouterPageInfo) {
        hisNextRouterPageInfo.srcPageStyle = hisRouterPageInfo.srcPageStyle;
      } else {
        // 如果自己调用自己时页需要转移一下
        routerPageInfo.srcPageStyle = hisRouterPageInfo.srcPageStyle;
      }
    }
  }

  /**
   * 路由Page API
   * @param routerPageInfo 路由信息
   * @returns
   */
  export function pushUrl(routerPageInfo: TKRouterPageInfo): Promise<void> {
    return urlRoute(routerPageInfo);
  }

  /**
   * 路由Page API
   * @param routerPageInfo 路由信息
   * @returns
   */
  export function replaceUrl(routerPageInfo: TKRouterPageInfo): Promise<void> {
    return urlRoute(routerPageInfo, true);
  }

  /**
   * 路由Page API
   * @param routerPageInfo
   * @returns
   */
  export function pushNamedRoute(routerPageInfo: TKRouterPageInfo): Promise<void> {
    return nameRoute(routerPageInfo);
  }

  /**
   * 路由Page API
   * @param routerPageInfo  路由信息
   * @param urlOrName  替换页面
   * @returns
   */
  export function replaceNamedRoute(routerPageInfo: TKRouterPageInfo, urlOrName?: string): Promise<void> {
    return nameRoute(routerPageInfo, true);
  }

  /**
   * 路由的核心代码
   * @param routerPageInfo 基本参数
   * @param isReplace 是否是 Replace 的命名路由
   * @returns
   */
  function urlRoute(routerPageInfo: TKRouterPageInfo, isReplace: boolean = false): Promise<void> {
    return urlOrNameRoute(routerPageInfo, isReplace, true);
  }

  /**
   * 路由的核心代码
   * @param routerPageInfo 基本参数
   * @param isReplace 是否是 Replace 的命名路由
   * @returns
   */
  function nameRoute(routerPageInfo: TKRouterPageInfo, isReplace: boolean = false): Promise<void> {
    return urlOrNameRoute(routerPageInfo, isReplace, false);
  }

  /**
   * 路由的核心代码
   * @param routerPageInfo 基本参数
   * @param isReplace 是否是 Replace 的命名路由
   * @param isUrlMode 是否是 url跳转模式
   * @returns
   */
  async function urlOrNameRoute(routerPageInfo: TKRouterPageInfo, isReplace: boolean = false,
    isUrlMode: boolean = true): Promise<void> {
    try {
      let isPass: boolean = true;
      for (let routerFilter of routerFilters) {
        if (routerFilter.before) {
          isPass = await routerFilter.before(routerPageInfo);
          if (!isPass) {
            break;
          }
        }
      }
      if (!isPass) {
        return;
      }
      // 初始化参数
      if (isUrlMode) {
        if (TKStringHelper.isBlank(routerPageInfo.url)) {
          const error: Error = new Error();
          error.message = '路由异常,url不可为空!'
          throw error;
        }
        //清空页面名称
        routerPageInfo.name = undefined;
      } else {
        if (TKStringHelper.isBlank(routerPageInfo.name)) {
          const error: Error = new Error();
          error.message = '路由异常,name不可为空!'
          throw error;
        }
        //清空页面URL
        routerPageInfo.url = undefined;
      }
      // 判断是否Replace模式
      if (isReplace) {
        let urlOrName: string | undefined =
          TKRouterPageManager.shareInstance()
            .getRoutePageUrlOrName(getRouteState(routerPageInfo.navComponentOrPathStack) as Object as Record<string, Object>);
        // replace判断回调函数的透传逻辑
        if (routerPageInfo.onResult === undefined) {
          let routerParam = getParams(routerPageInfo.navComponentOrPathStack) as Record<string, object>
          if (routerParam) {
            let flowNo: string = TKMapHelper.getString(routerParam, "@flowNo");
            if (TKStringHelper.isBlank(flowNo)) {
              flowNo = TKRouterPageManager.shareInstance().get(urlOrName)?.flowNo ?? "";
            }
            if (TKStringHelper.isNotBlank(flowNo)) {
              let resultObserver: TKRouterResultObserver | undefined =
                TKRouterResultObserverManager.shareInstance().get(flowNo);
              routerPageInfo.onResult = resultObserver?.onResult;
            }
          }
        }
        //  replace 后将 srcPageStyle属性转移给目标页
        let hisRouterPageInfo = TKRouterPageManager.shareInstance().pop(urlOrName);
        if (hisRouterPageInfo) {
          routerPageInfo.srcPageStyle = hisRouterPageInfo.srcPageStyle;
        }
        // 单例模式下，将历史的出栈销毁, 并要将srcPageStyle属性全部往栈顶转移一位
        if (routerPageInfo.mode == router.RouterMode.Single) {
          singleOffsetSrcPageStyle(routerPageInfo, routerPageInfo.url ?? routerPageInfo.name ?? "");
        }
      } else {
        // 路由成功后入栈
        if (routerPageInfo.mode == router.RouterMode.Single) {
          singleOffsetSrcPageStyle(routerPageInfo, routerPageInfo.url ?? routerPageInfo.name ?? "")
        }
      }
      // 初始化参数
      buildRouterPageInfo(routerPageInfo);
      if (routerPageInfo.onResult) {
        // 注册全局观察者
        TKRouterResultObserverManager.shareInstance()
          .register(routerPageInfo.flowNo!, { onResult: routerPageInfo.onResult } as TKRouterResultObserver);
      }
      let navPathStack = getCurNavPathStack(routerPageInfo.navComponentOrPathStack);
      if (isUrlMode) {
        TKLog.info(`[页面路由跳转URL:${routerPageInfo.flowNo}]---->routerType:${navPathStack ? "navPathStack" :
          "router"},isReplace:${isReplace},url:${routerPageInfo.url},params:${JSON.stringify(routerPageInfo.params)},mode:${routerPageInfo.mode}`);
        if (isReplace) {
          if (navPathStack) {
            await navPathStack.replacePathByName(routerPageInfo.url, routerPageInfo.params);
          } else {
            await router.replaceUrl({ url: routerPageInfo.url, params: routerPageInfo.params }, routerPageInfo.mode);
          }
        } else {
          if (navPathStack) {
            await navPathStack.pushPathByName(routerPageInfo.url, routerPageInfo.params);
          } else {
            await router.pushUrl({ url: routerPageInfo.url, params: routerPageInfo.params }, routerPageInfo.mode);
          }
        }
      } else {
        TKLog.info(`[页面路由跳转Named:${routerPageInfo.flowNo}]---->routerType:${navPathStack ? "navPathStack" :
          "router"},isReplace:${isReplace},name:${routerPageInfo.name},params:${JSON.stringify(routerPageInfo.params)},mode:${routerPageInfo.mode}`);
        if (isReplace) {
          if (navPathStack) {
            await navPathStack.replacePathByName(routerPageInfo.name, routerPageInfo.params);
          } else {
            await router.replaceNamedRoute({ name: routerPageInfo.name, params: routerPageInfo.params },
              routerPageInfo.mode);
          }
        } else {
          if (navPathStack) {
            await navPathStack.pushPathByName(routerPageInfo.name, routerPageInfo.params);
          } else {
            await router.pushNamedRoute({ name: routerPageInfo.name, params: routerPageInfo.params },
              routerPageInfo.mode);
          }
        }
      }
      if (routerPageInfo.pageStyle) {
        await rebuildRoutePageStyle(routerPageInfo.pageStyle);
      }
      setTimeout(async () => {
        for (let routerFilter of routerFilters) {
          if (routerFilter.after) {
            let isPass: boolean = await routerFilter.after(routerPageInfo);
            if (!isPass) {
              break;
            }
          }
        }
      }, 100);
    } catch (error) {
      TKLog.error(`urlOrNameRoute failed, code is ${error.code}, message is ${error.message}`);
    }
  }

  /**
   * 获取当前路由栈对象
   * @param componentOrPathStack
   * @returns
   */
  export function getRouteState(navComponentOrPathStack?: CustomComponent | NavPathStack): Record<string, Object> {
    navComponentOrPathStack = getCurNavComponentOrPathStack(navComponentOrPathStack);
    if (navComponentOrPathStack) {
      if (navComponentOrPathStack instanceof NavPathStack) {
        if (navComponentOrPathStack.size() > 0) {
          let navPathStackTopIndex: number = navComponentOrPathStack.size() - 1;
          return {
            "name": navComponentOrPathStack.getAllPathName()[navPathStackTopIndex] ?? "",
            "param": (navComponentOrPathStack.getParamByIndex(navPathStackTopIndex) ?? {}) as Record<string, Object>
          } as Record<string, Object>
        }
      } else {
        let navPathStack = navComponentOrPathStack.queryNavigationInfo()?.pathStack;
        let navDestinationInfo = navComponentOrPathStack.queryNavDestinationInfo();
        if (navPathStack && navDestinationInfo && navDestinationInfo.index > -1) {
          return navDestinationInfo as Object as Record<string, Object>;
        }
      }
    }
    return router.getState() as Object as Record<string, Object>;
  }

  /**
   * 获取当前路由栈长度
   * @param componentOrPathStack
   * @returns
   */
  export function getRouteStackLength(navComponentOrPathStack?: CustomComponent | NavPathStack): number {
    navComponentOrPathStack = getCurNavComponentOrPathStack(navComponentOrPathStack);
    if (navComponentOrPathStack) {
      if (navComponentOrPathStack instanceof NavPathStack) {
        return navComponentOrPathStack.size() + 1;
      } else {
        let navPathStack = navComponentOrPathStack.queryNavigationInfo()?.pathStack;
        let navDestinationInfo = navComponentOrPathStack.queryNavDestinationInfo();
        if (navPathStack && navDestinationInfo && navDestinationInfo.index > -1) {
          return navPathStack.size() + 1;
        }
      }
    }
    return Number(router.getLength());
  }

  /**
   * 当前路由栈是否到达栈顶
   * @param navComponentOrPathStack
   * @returns
   */
  export function isRouteStackTop(navComponentOrPathStack?: CustomComponent | NavPathStack): boolean {
    let stackSize: number = getRouteStackLength(navComponentOrPathStack);
    return (stackSize <= 1);
  }

  /**
   * 路由跳转新页面后回调数据结果
   * @param result
   * @param isRouterBack 是否内部直接back
   */
  export async function back(option: TKRouterBackOption = {}) {
    try {
      option.isRouterBack = option.isRouterBack ?? true;
      option.navComponentOrPathStack = getCurNavComponentOrPathStack(option.navComponentOrPathStack);
      if (TKSystemHelper.getConfig("system.isRouterBackExitAppTip") != "0") {
        if (isRouteStackTop(option.navComponentOrPathStack)) {
          if (!isCanExitApp) {
            isCanExitApp = true;
            TKDialogHelper.showSysToast("再按一次将退出程序到后台");
            TKLog.warn("[页面路由返回]---->已经到栈顶，忽略此次操作...");
            setTimeout(() => {
              isCanExitApp = false;
            }, 2000);
            return;
          }
        }
      }
      let urlOrName: string | undefined =
        TKRouterPageManager.shareInstance().getRoutePageUrlOrName(getRouteState(option.navComponentOrPathStack));
      if (prevBackRoutePage != urlOrName) {
        //预防重复返回的问题
        prevBackRoutePage = urlOrName;
        setTimeout(() => {
          prevBackRoutePage = "";
        }, 1000);
        // 路由成功后入栈
        TKLog.info(`[页面路由返回]---->result:${option.result ? JSON.stringify(option.result) :
          ""},isRouterBack:${option.isRouterBack},urlOrName:${urlOrName}`);
        let hisRouterInfo: TKRouterPageInfo | undefined = TKRouterPageManager.shareInstance().get(urlOrName);
        if (hisRouterInfo && hisRouterInfo.flowNo) {
          let resultObserver: TKRouterResultObserver | undefined =
            TKRouterResultObserverManager.shareInstance().get(hisRouterInfo.flowNo);
          if (option.result) {
            TKLog.info(`[页面路由返回]---->流水号:${hisRouterInfo.flowNo},是否存在回调函数:${(resultObserver &&
            resultObserver.onResult) ? true : false}`);
            (async () => {
              try {
                if (resultObserver && resultObserver.onResult) {
                  TKLog.info(`[页面路由返回]---->开始执行回调函数，参数为:${JSON.stringify(option.result)}`);
                  resultObserver.onResult(option.result!);
                }
              } catch (error) {
                TKLog.error(`[页面路由返回]---->执行回调函数失败, code is ${error.code}, message is ${error.message}`);
              }
            })();
          }
        } else {
          TKLog.error(`[页面路由返回]---->未找到相应的历史堆栈, urlOrName:${urlOrName}`);
        }
        if (option.isRouterBack) {
          let navPathStack = getCurNavPathStack(option.navComponentOrPathStack);
          TKLog.info(`[页面路由返回]---->routerType:${navPathStack ? "navPathStack" : "router"},result:${option.result ?
          JSON.stringify(option.result) : ""},urlOrName:${urlOrName}`);
          if (navPathStack) {
            if (TKStringHelper.isNotBlank(option.urlOrName)) {
              navPathStack.popToName(option.urlOrName!);
            } else {
              navPathStack.pop();
            }
          } else {
            if (TKStringHelper.isNotBlank(option.urlOrName)) {
              router.back({ url: option.urlOrName! })
            } else {
              router.back();
            }
          }
          // 延迟返回，等待上个页面状态的恢复
          setTimeout(async () => {
            if (hisRouterInfo && hisRouterInfo.srcPageStyle) {
              await rebuildRoutePageStyle(hisRouterInfo.srcPageStyle);
            }
          }, 10);
          setTimeout(async () => {
            for (let routerFilter of routerFilters) {
              if (routerFilter.after) {
                let isPass: boolean = await routerFilter.after(hisRouterInfo!);
                if (!isPass) {
                  break;
                }
              }
            }
          }, 100);
        }
      }
    } catch (error) {
      TKLog.error(`back failed, code is ${error.code}, message is ${error.message}`);
    }
  }

  /**
   * 获取当前路由参数
   */
  export function getParams(navComponentOrPathStack?: CustomComponent | NavPathStack): Record<string, Object> {
    navComponentOrPathStack = getCurNavComponentOrPathStack(navComponentOrPathStack);
    if (navComponentOrPathStack) {
      if (navComponentOrPathStack instanceof NavPathStack) {
        if (navComponentOrPathStack.size() > 0) {
          let navPathStackTopIndex: number = navComponentOrPathStack.size() - 1;
          return (navComponentOrPathStack.getParamByIndex(navPathStackTopIndex) ?? {}) as Record<string, Object>
        }
      } else {
        let navPathStack = navComponentOrPathStack.queryNavigationInfo()?.pathStack;
        let navDestinationInfo = navComponentOrPathStack.queryNavDestinationInfo();
        if (navPathStack && navDestinationInfo && navDestinationInfo.index > -1) {
          return (navDestinationInfo.param ?? {}) as Record<string, Object>;
        }
      }
    }
    return (router.getParams() ?? {}) as Record<string, Object>;
  }

}

//默认注册系统框架导航栏路由
TKRouterHelper.registerNavRouterObject(TKNavRouteInfo);