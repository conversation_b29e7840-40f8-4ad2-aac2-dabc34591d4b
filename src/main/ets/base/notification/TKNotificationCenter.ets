/**
 * 通知对象
 */
import { TKLog } from '../../util/logger/TKLog';
import { TKNotification } from './TKNotification';

/**
 * 消息处理器
 */
export type TKNotificationHandler = (notification: TKNotification) => void;

/**
 * 通知中心
 */
export class TKNotificationCenter {
  public static defaultCenter: TKNotificationCenter = new TKNotificationCenter();
  private listenerMap: Map<WeakRef<Object>, Map<TKNotification, TKNotificationHandler>> =
    new Map<WeakRef<Object>, Map<TKNotification, TKNotificationHandler>>();

  private constructor() {
  }

  /**
   * 获取监听Key
   * @param observer
   * @returns
   */
  private getObserverKey(observer: Object): WeakRef<Object> {
    let keys: Array<WeakRef<Object>> = Array.from(this.listenerMap.keys());
    let observerKey: WeakRef<Object> | undefined = undefined;
    for (let key of keys) {
      if (key.deref() == observer) {
        observerKey = key;
        break;
      }
    }
    return observerKey ?? new WeakRef(observer);
  }

  /**
   * 添加监听
   * @param observer 监听对象
   * @param handler  处理器
   * @param name     通知名称
   * @param obj      通知对象
   */
  public addObserver(observer: Object, handler: TKNotificationHandler, name: string, obj?: Object) {
    let observerKey: WeakRef<Object> = this.getObserverKey(observer);
    let observerListenerMap: Map<TKNotification, TKNotificationHandler> =
      this.listenerMap.get(observerKey) ?? new Map<TKNotification, TKNotificationHandler>();
    Array.from(observerListenerMap.keys()).forEach((noti, index) => {
      if (noti.name == name && noti.obj == obj) {
        observerListenerMap.delete(noti);
      }
    });
    let notification: TKNotification = new TKNotification(name, obj);
    handler = handler.bind(observer);
    let weakHandler = new WeakRef(handler);
    (observer as Record<string, Object>)[`TKNotificationHandler#${notification.name}`] = handler;
    observerListenerMap.set(notification, async (note) => {
      weakHandler.deref()?.(note);
    });
    this.listenerMap.set(observerKey, observerListenerMap);

  }

  /**
   * 批量添加监听
   * @param observer 监听对象
   * @param handler  处理器
   * @param notes    通知名称数组
   */
  public addObservers(observer: Object, handler: TKNotificationHandler, notes: Array<string>) {
    if (notes && notes.length > 0) {
      for (let note of notes) {
        this.addObserver(observer, handler, note);
      }
    }
  }

  /**
   * 发送通知
   * @param notification
   */
  public postNotificationName(name: string, obj?: Object, userInfo?: Record<string, Object>) {
    let notification: TKNotification = new TKNotification(name, obj, userInfo);
    this.listenerMap.forEach((observerListenerMap, observer) => {
      if (observer.deref()) {
        observerListenerMap.forEach((handler, noti) => {
          if (noti.name == notification.name) {
            if (noti.obj !== undefined && noti.obj !== null) {
              if (noti.obj == notification.obj) {
                try {
                  handler(notification);
                } catch (error) {
                  TKLog.error(`监听通知[${name}]处理异常 ~ code: ${error.code} -·- message: ${error.message}`);
                }
              }
            } else {
              try {
                handler(notification);
              } catch (error) {
                TKLog.error(`监听通知[${name}]处理异常 ~ code: ${error.code} -·- message: ${error.message}`);
              }
            }
          }
        })
      }
    });
  }

  /**
   * 移除监听
   * @param observer
   * @param name
   * @param obj
   */
  public removeObserver(observer: Object, name?: string, obj?: Object) {
    let observerKey: WeakRef<Object> = this.getObserverKey(observer);
    let observerListenerMap: Map<TKNotification, TKNotificationHandler> | undefined =
      this.listenerMap.get(observerKey);
    if (observerListenerMap) {
      if (name && name.length > 0) {
        Array.from(observerListenerMap.keys()).forEach((noti, index) => {
          if (obj !== undefined && obj !== null) {
            if (noti.name == name && noti.obj == obj) {
              observerListenerMap?.delete(noti);
            }
          } else {
            if (noti.name == name) {
              observerListenerMap?.delete(noti);
            }
          }
        });
        if (observerListenerMap.size == 0) {
          this.listenerMap.delete(observerKey);
        }
      } else {
        this.listenerMap.delete(observerKey);
      }
    }
  }
}