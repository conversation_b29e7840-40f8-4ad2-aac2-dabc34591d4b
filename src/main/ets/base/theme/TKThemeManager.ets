/**
 * 换肤中心
 */
import { TKCacheManager } from '../../util/cache/TKCacheManager';
import { TKDataHelper } from '../../util/data/TKDataHelper';
import { TKFileHelper } from '../../util/file/TKFileHelper';
import { TKMapHelper } from '../../util/map/TKMapHelper';
import { TKStringHelper } from '../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../util/system/TKSystemHelper';
import { TKCommonService } from '../mvc/service/TKCommonService';
import { TKNotificationCenter } from '../notification/TKNotificationCenter';
import { TKCSSRuleset } from './TKCSSRuleset';
import { TKContextHelper } from '../../util/system/TKContextHelper';
import { TKImageHelper } from '../../util/ui/TKImageHelper';

export class TKThemeManager {
  private static readonly CACHE_SYSTEM_THEME: string = "tk_system_theme";
  //改变主题的消息通知
  public static readonly NOTE_THEME_CHANGED: string = "note_theme_changed";
  //单例模式
  private static instance: TKThemeManager | undefined = undefined;
  //主题
  private _theme: string = "";
  private commonService: TKCommonService = new TKCommonService();
  //主题缓存
  private themeMap: Map<string, Map<string, TKCSSRuleset>> = new Map<string, Map<string, TKCSSRuleset>>();

  public static shareInstance(): TKThemeManager {
    if (!TKThemeManager.instance) {
      TKThemeManager.instance = new TKThemeManager();
    }
    return TKThemeManager.instance;
  }

  /**
   *  初始化Theme的环境
   */
  public initThemeContext() {
    let theme: string | undefined = TKCacheManager.shareInstance().getFileCacheData(TKThemeManager.CACHE_SYSTEM_THEME);
    if (TKStringHelper.isBlank(theme)) {
      theme = TKSystemHelper.getConfig("theme.currentTheme");
    } else {
      let cacheTheme: string = TKSystemHelper.getConfig(`theme.${theme}`);
      if (TKStringHelper.isBlank(cacheTheme)) {
        theme = TKSystemHelper.getConfig("theme.currentTheme");
      }
    }
    this.theme = theme!;
  }

  /**
   *  清理Theme的环境
   */
  public clearCache() {
    //清空主题
    if (TKSystemHelper.getConfig("theme.isSaveLastTheme") != "1") {
      TKCacheManager.shareInstance().deleteFileCacheData(TKThemeManager.CACHE_SYSTEM_THEME);
    }
  }

  /**
   *  设置当前主题
   * @param theme
   */
  public set theme(theme: string) {
    if (TKStringHelper.isNotBlank(theme) && theme != this._theme) {
      this._theme = theme;
      TKCacheManager.shareInstance().saveFileCacheData(TKThemeManager.CACHE_SYSTEM_THEME, this._theme);
      let themeConfig: Map<string, TKCSSRuleset> | undefined = this.themeMap.get(this._theme);
      if (!themeConfig) {
        this.parseThemeCSS(this._theme).then((themeConfig) => {
          this.themeMap.set(theme, themeConfig);
          //广播通知
          TKNotificationCenter.defaultCenter.postNotificationName(TKThemeManager.NOTE_THEME_CHANGED, this._theme);
        });
      } else {
        //广播通知
        TKNotificationCenter.defaultCenter.postNotificationName(TKThemeManager.NOTE_THEME_CHANGED, this._theme);
      }
    }
  }

  /**
   * 主题解析
   * @param theme
   * @returns
   */
  private async parseThemeCSS(theme: string): Promise<Map<string, TKCSSRuleset>> {
    let themeConfig: Map<string, TKCSSRuleset> = new  Map<string, TKCSSRuleset>();
    let themePathConfig: string = TKSystemHelper.getConfig(`theme.${theme}`);
    if (TKStringHelper.isNotBlank(themePathConfig) && !themePathConfig.includes(".css")) {
      themePathConfig = TKSystemHelper.getConfig(`theme.${themePathConfig}`);
    }
    if (TKStringHelper.isNotBlank(themePathConfig)) {
      let themePaths: Array<string> = themePathConfig.split("|");
      for (let tempThemePath of themePaths) {
        if (!tempThemePath.includes(".css")) {
          continue;
        }
        let basePath: string = "";
        let firstPath: string = tempThemePath.split(",")[0];
        let basePathIndex: number = firstPath.lastIndexOf("/");
        if (basePathIndex >= 0) {
          basePath = firstPath.substring(0, basePathIndex + 1);
        }
        let tempThemePaths: Array<string> = tempThemePath.split(",");
        for (let themePath of tempThemePaths) {
          themePath = themePath.startsWith(basePath) ? themePath : basePath + themePath;
          let moduleContext: Context | undefined = undefined;
          if (TKStringHelper.startsWith(themePath, "@bundle:")) {
            let resBundleModule: string =
              TKStringHelper.split(TKStringHelper.replace(themePath, "@bundle:", ""), ":")[0];
            moduleContext = await TKContextHelper.getCurHspModuleContext(resBundleModule);
          } else if (TKStringHelper.startsWith(themePath, "/")) {
            themePath = themePath.substring(1, themePath.length);
          } else {
            let lastThemePath: string = `thinkive/config/${TKSystemHelper.getEnvironment()}/theme/${themePath}`;
            if (TKFileHelper.readFile(lastThemePath).length <= 0) {
              lastThemePath = `thinkive/config/default/theme/${themePath}`;
            }
            themePath = lastThemePath;
          }
          let themeCss: string = TKDataHelper.uint8ArrayToString(TKFileHelper.readFile(themePath, moduleContext));
          if (TKStringHelper.isNotBlank(themeCss)) {
            // 匹配类定义的正则表达式
            const regex: RegExp = /\.([0-9a-zA-Z-_]+)\s*\{([^\}]+)\}/g;
            let match: RegExpExecArray | null = null;
            while ((match = regex.exec(themeCss)) !== null) {
              const className: string = TKStringHelper.trim(match[1]);
              const classProperties: string = TKStringHelper.trim(match[2]);
              // 解析类属性和值
              const classResult: TKCSSRuleset = new TKCSSRuleset();
              const classRegex: RegExp = /([0-9a-zA-Z-_]+)\s*:\s*([^;]+)/g;
              let classMatch: RegExpExecArray | null = null;
              while ((classMatch = classRegex.exec(classProperties)) !== null) {
                let property: string = TKStringHelper.trim(classMatch[1]);
                let value: string = TKStringHelper.trim(classMatch[2]);
                value = TKStringHelper.replace(value, "\"|'", "");
                switch (property) {
                  case "width": {
                    //宽度
                    classResult.width = TKDataHelper.stringToNumber(value, undefined);
                    break;
                  }
                  case "height": {
                    //高度
                    classResult.height = TKDataHelper.stringToNumber(value, undefined);
                    break;
                  }
                  case "padding": {
                    //内边距属性
                    if (TKStringHelper.startsWith(value, "[")) {
                      value = value.replace("[", "");
                      value = value.replace("]", "");
                      let paddings: Array<string> = value.split(",");
                      classResult.padding = {} as Padding;
                      if (paddings.length > 0) {
                        classResult.padding.left = TKDataHelper.stringToNumber(paddings[0], undefined);
                      }
                      if (paddings.length > 1) {
                        classResult.padding.top = TKDataHelper.stringToNumber(paddings[1], undefined);
                      }
                      if (paddings.length > 2) {
                        classResult.padding.right = TKDataHelper.stringToNumber(paddings[2], undefined);
                      }
                      if (paddings.length > 3) {
                        classResult.padding.bottom = TKDataHelper.stringToNumber(paddings[3], undefined);
                      }
                    } else {
                      classResult.padding = TKDataHelper.stringToNumber(value, undefined);
                    }
                    break;
                  }
                  case "margin": {
                    //外边距属性
                    if (TKStringHelper.startsWith(value, "[")) {
                      value = value.replace("[", "");
                      value = value.replace("]", "");
                      let margins: Array<string> = value.split(",");
                      classResult.margin = {} as Margin;
                      if (margins.length > 0) {
                        classResult.margin.left = TKDataHelper.stringToNumber(margins[0], undefined);
                      }
                      if (margins.length > 1) {
                        classResult.margin.top = TKDataHelper.stringToNumber(margins[1], undefined);
                      }
                      if (margins.length > 2) {
                        classResult.margin.right = TKDataHelper.stringToNumber(margins[2], undefined);
                      }
                      if (margins.length > 3) {
                        classResult.margin.bottom = TKDataHelper.stringToNumber(margins[3], undefined);
                      }
                    } else {
                      classResult.margin = TKDataHelper.stringToNumber(value, undefined);
                    }
                    break;
                  }
                  case "borderWidth": {
                    //边框宽度
                    classResult.borderWidth = TKDataHelper.stringToNumber(value, undefined);
                    break;
                  }
                  case "borderColor": {
                    //边框颜色
                    classResult.borderColor = value;
                    break;
                  }
                  case "borderRadius": {
                    //边框圆角
                    classResult.borderRadius = TKDataHelper.stringToNumber(value, undefined);
                    break;
                  }
                  case "backgroundColor": {
                    //背景色
                    classResult.backgroundColor = value;
                    break;
                  }
                  case "selectedBackgroundColor": {
                    //选中背景色
                    classResult.selectedBackgroundColor = value;
                    break;
                  }
                  case "highlightedBackgroundColor": {
                    //高亮背景色
                    classResult.highlightedBackgroundColor = value;
                    break;
                  }
                  case "disabledBackgroundColor": {
                    //禁用背景色
                    classResult.disabledBackgroundColor = value;
                    break;
                  }
                  case "opacity": {
                    //透明度
                    classResult.opacity = TKDataHelper.stringToNumber(value, undefined);
                    break;
                  }
                  case "fontSize": {
                    //字体大小
                    classResult.fontSize = TKDataHelper.stringToNumber(value, undefined);
                    break;
                  }
                  case "fontColor": {
                    //字体颜色
                    classResult.fontColor = value;
                    break;
                  }
                  case "fontWeight": {
                    //字体宽度
                    value = value.toLowerCase();
                    if (value == "lighter") {
                      classResult.fontWeight = FontWeight.Lighter;
                    } else if (value == "normal") {
                      classResult.fontWeight = FontWeight.Normal;
                    } else if (value == "regular") {
                      classResult.fontWeight = FontWeight.Regular;
                    } else if (value == "medium") {
                      classResult.fontWeight = FontWeight.Medium;
                    } else if (value == "bold") {
                      classResult.fontWeight = FontWeight.Bold;
                    } else if (value == "bolder") {
                      classResult.fontWeight = FontWeight.Bolder;
                    } else {
                      classResult.fontWeight = TKDataHelper.stringToNumber(value, undefined);
                    }
                    break;
                  }
                  case "fontStyle": {
                    //字体风格
                    value = value.toLowerCase();
                    if (value == "normal") {
                      classResult.fontStyle = FontStyle.Normal;
                    } else {
                      classResult.fontStyle = FontStyle.Italic;
                    }
                    break;
                  }
                  case "fontFamily": {
                    //字体类型
                    classResult.fontFamily = value;
                    break;
                  }
                  case "space": {
                    //间距
                    classResult.space = TKDataHelper.stringToNumber(value, undefined);
                    break;
                  }
                  case "color": {
                    //颜色
                    classResult.color = value;
                    break;
                  }
                  case "selectedColor": {
                    //选中的颜色
                    classResult.selectedColor = value;
                    break;
                  }
                  case "highlightedColor": {
                    //高亮颜色
                    classResult.highlightedColor = value;
                    break;
                  }
                  case "disabledColor": {
                    //禁用颜色
                    classResult.disabledColor = value;
                    break;
                  }
                  case "align": {
                    //对齐方式
                    value = value.toLowerCase();
                    if (value == "TopStart".toLowerCase()) {
                      classResult.align = Alignment.TopStart;
                    } else if (value == "top") {
                      classResult.align = Alignment.Top;
                    } else if (value == "TopEnd".toLowerCase()) {
                      classResult.align = Alignment.TopEnd;
                    } else if (value == "start") {
                      classResult.align = Alignment.Start;
                    } else if (value == "center") {
                      classResult.align = Alignment.Center;
                    } else if (value == "end") {
                      classResult.align = Alignment.End;
                    } else if (value == "BottomStart".toLowerCase()) {
                      classResult.align = Alignment.BottomStart;
                    } else if (value == "bottom") {
                      classResult.align = Alignment.Bottom;
                    } else if (value == "BottomEnd".toLowerCase()) {
                      classResult.align = Alignment.BottomEnd;
                    }
                    break;
                  }
                  case "horizontalAlign": {
                    //水平对齐
                    value = value.toLowerCase();
                    if (value == "start") {
                      classResult.horizontalAlign = HorizontalAlign.Start;
                    } else if (value == "center") {
                      classResult.horizontalAlign = HorizontalAlign.Center;
                    } else if (value == "end") {
                      classResult.horizontalAlign = HorizontalAlign.End;
                    }
                    break;
                  }
                  case "verticalAlign": {
                    //垂直对齐
                    value = value.toLowerCase();
                    if (value == "top") {
                      classResult.verticalAlign = VerticalAlign.Top;
                    } else if (value == "center") {
                      classResult.verticalAlign = VerticalAlign.Center;
                    } else if (value == "bottom") {
                      classResult.verticalAlign = VerticalAlign.Bottom;
                    }
                    break;
                  }
                  case "image": {
                    //图片
                    classResult.image = TKImageHelper.getImageResource(value);
                    break;
                  }
                  case "selectedImage": {
                    //选中图片
                    classResult.selectedImage = TKImageHelper.getImageResource(value);
                    break;
                  }
                  case "highlightedImage": {
                    //高亮图片
                    classResult.highlightedImage = TKImageHelper.getImageResource(value);
                    break;
                  }
                  case "disabledImage": {
                    //禁用图片
                    classResult.disabledImage = TKImageHelper.getImageResource(value);
                    break;
                  }
                }
              }
              themeConfig.set(className, classResult);
            }
          }
        }
      }
    }
    return themeConfig;
  }

  /**
   * 获取当前主题
   * @returns
   */
  public get theme() {
    return this._theme;
  }

  /**
   *  获取css配置的属性值
   * @param className 类名
   * @return 值
   */
  public getCssRulesetByClassName(className: string): TKCSSRuleset {
    let themeConfig: Map<string, TKCSSRuleset> =
      TKMapHelper.getObject(this.themeMap, this._theme, new Map<string, TKCSSRuleset>())!;
    return TKMapHelper.getObject(themeConfig, className, new TKCSSRuleset()) as TKCSSRuleset;
  }

  /**
   *  获取css配置
   * @return
   */
  public getCssRulesetMap(): Map<string, TKCSSRuleset> {
    let themeConfig: Map<string, TKCSSRuleset> =
      TKMapHelper.getObject(this.themeMap, this._theme, new Map<string, TKCSSRuleset>())!;
    return themeConfig;
  }
}