export class TKCSSRuleset {
  //宽度
  width?: Length;
  //高度
  height?: Length;
  //内边距属性
  padding?: Padding | Length;
  //外边距属性
  margin?: Margin | Length;
  //边框宽度
  borderWidth?: Length;
  //边框颜色
  borderColor?: ResourceColor;
  //边框圆角
  borderRadius?: Length;
  //背景色
  backgroundColor?: ResourceColor;
  //选中背景色
  selectedBackgroundColor?: ResourceColor;
  //高亮背景色
  highlightedBackgroundColor?: ResourceColor;
  //禁用背景色
  disabledBackgroundColor?: ResourceColor;
  //透明度
  opacity?: number;
  //字体大小
  fontSize?: Length;
  //字体颜色
  fontColor?: ResourceColor;
  //字体宽度
  fontWeight?: FontWeight | number | string;
  //字体风格
  fontStyle?: FontStyle;
  //字体类型
  fontFamily?: Resource | string;
  //间距
  space?: Length;
  //颜色
  color?: ResourceColor;
  //选中的颜色
  selectedColor?: ResourceColor;
  //高亮颜色
  highlightedColor?: ResourceColor;
  //禁用颜色
  disabledColor?: ResourceColor;
  //图片
  image?: Resource;
  //选中图片
  selectedImage?: Resource;
  //高亮图片
  highlightedImage?: Resource;
  //禁用图片
  disabledImage?: Resource;
  //对齐方式
  align?: Alignment;
  //水平对齐
  horizontalAlign?: HorizontalAlign;
  //垂直对齐
  verticalAlign?: VerticalAlign;
}