import { TKMapHelper } from '../../util/map/TKMapHelper';
import { TKNotification } from '../notification/TKNotification';
import { TKNotificationCenter } from '../notification/TKNotificationCenter';
import { TKCSSRuleset } from './TKCSSRuleset';
import { TKThemeManager } from './TKThemeManager';

/**
 * 主题属性
 */
@ObservedV2
export class TKThemeStyleAttribute {
  @Trace private style: Map<string, TKCSSRuleset> = TKThemeManager.shareInstance().getCssRulesetMap();

  public constructor() {
    this.onRegisterNotification();
  }

  /**
   *  通知列表
   *
   * @return
   */
  private listNotification(): Array<string> {
    let notes: Array<string> = new Array<string>();
    notes.push(TKThemeManager.NOTE_THEME_CHANGED);
    return notes;
  }

  /**
   *  注册消息
   */
  private onRegisterNotification() {
    let notes: Array<string> = this.listNotification();
    TKNotificationCenter.defaultCenter.addObservers(this, this.handleNotification, notes);
  }

  /**
   * 监听通知
   * @param note
   */
  private handleNotification(note: TKNotification) {
    if (note.name == TKThemeManager.NOTE_THEME_CHANGED) {
      this.style = TKThemeManager.shareInstance().getCssRulesetMap();
    }
  }

  /**
   *  获取css配置的属性值
   * @param className 类名
   * @return 值
   */
  public getCssRulesetByClassName(className: string): TKCSSRuleset {
    return TKMapHelper.getObject(this.style, className, new TKCSSRuleset()) as TKCSSRuleset;
  }
}
