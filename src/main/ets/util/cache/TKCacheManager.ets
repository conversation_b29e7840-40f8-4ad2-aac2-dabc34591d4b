import { TKAesHelper } from '../crypto/TKAesHelper';
import { TKPasswordGenerator } from '../crypto/TKPasswordGenerator';
import { TKDataHelper } from '../data/TKDataHelper';
import { TKObjectHelper } from '../data/TKObjectHelper';
import { TKLog } from '../logger/TKLog';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKCacheType, TKCacheVO } from './domain/TKCacheVO';
import { TKPreferences } from './TKPreferences';
import { TKSingleKVStore } from './TKSingleKVStore';

/**
 * 存储管理器
 */
export class TKCacheManager {
  //单例对象
  private static instance: TKCacheManager | undefined = undefined;
  //最大内存缓存的对象个数，超过就触发淘汰机制
  private maxMemCacheNum: number = 100;
  //最大内存缓存的对象大小，超过就出发淘汰机制
  private maxMemCacheSize: number = 100 * 1024 * 1024;
  //当前的内存大小
  private currentMemCacheSize: number = 0;
  //缓存记录key队列，按照先入先出的原则管理
  private memCacheKeys: Array<string> = new Array<string>();
  //内存缓存Map
  private memCacheMap: Map<string, Object> = new Map<string, Object>();
  //监控器
  private monitor: number | undefined = undefined;

  public static shareInstance(): TKCacheManager {
    if (!TKCacheManager.instance) {
      TKCacheManager.instance = new TKCacheManager();
    }
    return TKCacheManager.instance;
  }

  public setMaxMemCacheNum(maxNum: number) {
    this.maxMemCacheNum = maxNum;
  }

  public setMaxMemCacheSize(maxSize: number) {
    this.maxMemCacheSize = maxSize;
  }

  /**
   * AES加密key
   * @returns
   */
  private aesSystemKey(): string {
    return TKPasswordGenerator.shareInstance().generatorPassword();
  }

  /**
   *  启动监控
   */
  public startMonitor() {
    if (!this.monitor) {
      this.monitor = setInterval(() => {
        this.managerCacheObject();
      }, 60000);
    }
  }

  /**
   *  停止监控
   */
  public stopMonitor() {
    if (this.monitor) {
      clearInterval(this.monitor);
      this.monitor = undefined;
    }
  }

  /**
   *  管理内存对象
   */
  private async managerCacheObject() {
    try {
      //内存
      let memAllKeys: Array<string> = Array.from(this.memCacheKeys);
      if (memAllKeys && memAllKeys.length > 0) {
        for (let key of memAllKeys) {
          this.getMemCacheData<Object>(key);
        }
      }
      // //文件
      // let fileAllKeys: Array<string> | undefined = new TKPreferences().getData("TKCacheKeys", new Array<string>());
      // if (fileAllKeys && fileAllKeys.length > 0) {
      //   for (let key of fileAllKeys) {
      //     this.getFileCacheData<Object>(key);
      //   }
      // }
      // //数据库
      // let dbAllKeys: Array<string> | undefined = await new TKSingleKVStore().getData("TKCacheKeys", new Array<string>());
      // if (dbAllKeys && dbAllKeys.length > 0) {
      //   for (let key of dbAllKeys) {
      //     await this.getDBCacheData<Object>(key);
      //   }
      // }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理重新清理数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 内存对象缓存
   *
   * @param key 关键字
   * @param data 数据
   * @param timeOut  超时时间  默认是永久
   * @param isEncrypt 是否加密 默认不加密
   */
  public saveMemeCacheData(key: string, data: Object, timeOut: number = 0, isEncrypt: boolean = false) {
    this.saveCacheData(key, data, TKCacheType.Mem, timeOut, isEncrypt);
  }

  /**
   *  文件对象缓存
   *
   * @param key 关键字
   * @param data 数据
   * @param timeOut  超时时间  默认是永久
   * @param isEncrypt 是否加密 默认不加密
   * @param fileName 文件名称  默认是系统
   */
  public saveFileCacheData(key: string, data: Object, timeOut: number = 0, isEncrypt: boolean = false,
    fileName: string = "") {
    this.saveCacheData(key, data, TKCacheType.File, timeOut, isEncrypt, fileName);
  }

  /**
   *
   *  DB对象缓存
   *
   * @param key 关键字
   * @param data 数据
   * @param timeOut  超时时间  默认是永久
   * @param isEncrypt 是否加密 默认不加密
   * @param dbName 数据库名称  默认是系统
   */
  public saveDBCacheData(key: string, data: Object, timeOut: number = 0, isEncrypt: boolean = false,
    dbName: string = ""): Promise<void> {
    return this.saveCacheData(key, data, TKCacheType.DB, timeOut, isEncrypt, dbName) as Promise<void>;
  }

  /**
   * 通用对象缓存
   *
   * @param key 关键字
   * @param data 数据
   * @param cacheType 存储类型 默认是内存
   * @param timeOut  超时时间  默认是永久
   * @param isEncrypt 是否加密 默认不加密
   * @param fileName 文件名称  默认是系统
   */
  public saveCacheData(key: string, data: Object, cacheType: TKCacheType = TKCacheType.Mem, timeOut: number = 0,
    isEncrypt: boolean = false, fileName: string = ""): void | Promise<void> {
    try {
      switch (cacheType) {
        case TKCacheType.Mem_AutoUpdate: {
          cacheType = TKCacheType.Mem;
          break;
        }
        case TKCacheType.File_AutoUpdate: {
          cacheType = TKCacheType.File;
          break;
        }
        case TKCacheType.DB_AutoUpdate: {
          cacheType = TKCacheType.DB;
          break;
        }
        default: {
          break;
        }
      }
      if (data) {
        if (typeof data === 'string' && isEncrypt) {
          data = TKAesHelper.stringWithAesEncryptSync(data as string, this.aesSystemKey());
          data = `encrypt_aes:${data}`;
        }
        let cacheVO: TKCacheVO = new TKCacheVO();
        cacheVO.data = data;
        cacheVO.type = cacheType;
        cacheVO.cacheTime = timeOut;
        cacheVO.key = key;
        if (TKStringHelper.isNotBlank(fileName)) {
          cacheVO.fileName = fileName;
        }
        return this.saveCacheVO(cacheVO);
      } else {
        return this.deleteCacheData(key, cacheType);
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心保存数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   *  缓存对象
   *
   * @param cacheVO 对象
   */
  private saveCacheVO(cacheVO: TKCacheVO): void | Promise<void> {
    if (cacheVO) {
      cacheVO.beginTime = new Date().getTime();
      switch (cacheVO.type) {
        case TKCacheType.Mem: {
          this.saveMemCacheVO(cacheVO);
          break;
        }
        case TKCacheType.File: {
          this.saveFileCacheVO(cacheVO);
          break;
        }
        case TKCacheType.DB: {
          return this.saveDBCacheVO(cacheVO);
        }
        default: {
          break;
        }
      }
    }
  }

  /**
   * 设置内存存储
   * @param cacheVO
   */
  private saveMemCacheVO(cacheVO: TKCacheVO) {
    try {
      if (cacheVO.cacheTime > 0) {
        //先判断有没有超过最大数目或者是否超过内存阀值，有的话，就先删除
        while ((this.memCacheKeys.length > 0) &&
          ((this.memCacheKeys.length >= this.maxMemCacheNum) || (this.currentMemCacheSize >= this.maxMemCacheSize))) {
          let memCacheKey: string = this.memCacheKeys[0];
          this.deleteMemCacheData(memCacheKey);
        }
        this.deleteMemCacheData(cacheVO.key);
        //过滤之后，开始进行设置
        cacheVO.cacheSize = TKDataHelper.stringToUint8Array(TKObjectHelper.serialize(cacheVO.data)).length;
        this.memCacheKeys.push(cacheVO.key);
        this.currentMemCacheSize += cacheVO.cacheSize;
      }
      this.memCacheMap.set(cacheVO.key, cacheVO);
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心保存内存数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 设置文件存储
   * @param cacheVO
   */
  private saveFileCacheVO(cacheVO: TKCacheVO) {
    try {
      let preferences: TKPreferences = new TKPreferences(cacheVO.fileName);
      preferences.setData(cacheVO.key, cacheVO);
      if (cacheVO.cacheTime > 0) {
        if (TKStringHelper.isBlank(cacheVO.fileName)) {
          //同步Key
          let fileAllKeys: Array<string> = preferences.getData("TKCacheKeys", new Array<string>()) as Array<string>;
          if (fileAllKeys.indexOf(cacheVO.key) < 0) {
            fileAllKeys.push(cacheVO.key);
          }
          preferences.setData("TKCacheKeys", fileAllKeys);
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心保存文件数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 设置数据库存储
   * @param cacheVO
   */
  private async saveDBCacheVO(cacheVO: TKCacheVO): Promise<void> {
    try {
      let kvStore: TKSingleKVStore = new TKSingleKVStore(cacheVO.fileName);
      await kvStore.setData(cacheVO.key, cacheVO);
      if (cacheVO.cacheTime > 0) {
        if (TKStringHelper.isBlank(cacheVO.fileName)) {
          //同步Key
          let dbAllKeys: Array<string> = await kvStore.getData("TKCacheKeys", new Array<string>()) as Array<string>;
          if (dbAllKeys.indexOf(cacheVO.key) < 0) {
            dbAllKeys.push(cacheVO.key);
          }
          await kvStore.setData("TKCacheKeys", dbAllKeys);
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心保存DB数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   *  根据关键字获取内存缓存数据
   *
   * @param key 关键字
   *
   * @return 缓存数据
   */
  public getMemCacheData<T>(key: string): T | undefined {
    let data: T | undefined = undefined;
    try {
      let cacheVO: TKCacheVO | undefined = this.memCacheMap.get(key) as TKCacheVO | undefined;
      if (cacheVO) {
        data = this.getDataFromCacheVO(cacheVO);
        if (data === undefined) {
          this.deleteMemCacheData(key);
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心获取内存数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return data;
  }

  /**
   *  根据关键字获取文件缓存数据
   *
   * @param key 关键字
   * @param dbName 文件名称
   *
   * @return 缓存数据
   */
  public getFileCacheData<T>(key: string, fileName: string = ''): T | undefined {
    let data: T | undefined = undefined;
    try {
      let preferences: TKPreferences = new TKPreferences(fileName);
      let cacheVO: TKCacheVO | undefined = preferences.getData(key);
      if (cacheVO) {
        data = this.getDataFromCacheVO(cacheVO);
        if (data === undefined) {
          this.deleteFileCacheData(key, fileName);
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心获取文件数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return data;
  }

  /**
   *  根据关键字获取DB缓存数据
   *
   * @param key 关键字
   * @param dbName 数据库名称
   *
   * @return 缓存数据
   */
  public async getDBCacheData<T>(key: string, dbName: string = ''): Promise<T | undefined> {
    let data: T | undefined = undefined;
    try {
      let kvStore: TKSingleKVStore = new TKSingleKVStore(dbName);
      let cacheVO: TKCacheVO | undefined = await kvStore.getData(key);
      if (cacheVO) {
        data = this.getDataFromCacheVO(cacheVO);
        if (data === undefined) {
          await this.deleteDBCacheData(key, dbName);
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心获取DB数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return data;
  }

  /*
   *  获取缓存数据
   *
   *  @param key       关键字
   *  @param cacheType 类型
   *  @param fileName  文件名称
   *
   *  @return 缓存的数据
   */
  public getCacheData<T>(key: string, cacheType: TKCacheType = TKCacheType.Mem,
    fileName: string = ''): T | undefined | Promise<T | undefined> {
    let data: T | undefined | Promise<T | undefined> = undefined;
    try {
      switch (cacheType) {
        case TKCacheType.Mem_AutoUpdate: {
          cacheType = TKCacheType.Mem;
          break;
        }
        case TKCacheType.File_AutoUpdate: {
          cacheType = TKCacheType.File;
          break;
        }
        case TKCacheType.DB_AutoUpdate: {
          cacheType = TKCacheType.DB;
          break;
        }
        default: {
          break;
        }
      }
      switch (cacheType) {
        case TKCacheType.Mem: {
          data = this.getMemCacheData(key);
          break;
        }
        case TKCacheType.File: {
          data = this.getFileCacheData(key, fileName);
          break;
        }
        case TKCacheType.DB: {
          data = this.getDBCacheData(key, fileName);
          break;
        }
        default: {
          break;
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心获取数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return data;
  }

  /**
   *  获取数据，判断数据有效性
   *
   * @param cacheVO
   *
   * @return
   */
  private getDataFromCacheVO<T>(cacheVO: TKCacheVO): T | undefined {
    let data: T | undefined = undefined;
    let timeOut: number = cacheVO.cacheTime;
    let beginTime: number = cacheVO.beginTime;
    let nowTime: number = new Date().getTime();
    let interval: number = Math.ceil((nowTime - beginTime) / 1000.0);
    if (timeOut > 0) {
      if (interval <= timeOut) {
        data = cacheVO.data as T;
      }
    } else {
      data = cacheVO.data as T;
    }
    if (data !== undefined && typeof data === 'string' && TKStringHelper.startsWith(data as string, "encrypt_aes:")) {
      data = data.substring("encrypt_aes:".length, data.length) as T;
      data = TKAesHelper.stringWithAesDecryptSync(data as string, this.aesSystemKey()) as T;
    }
    return data;
  }

  /**
   *  删除缓存
   *
   * @param key       关键字
   * @param cacheType 类型
   * @param fileName  文件名称
   */
  public deleteCacheData(key: string, cacheType: TKCacheType = TKCacheType.Mem,
    fileName: string = ''): void | Promise<void> {
    try {
      switch (cacheType) {
        case TKCacheType.Mem_AutoUpdate: {
          cacheType = TKCacheType.Mem;
          break;
        }
        case TKCacheType.File_AutoUpdate: {
          cacheType = TKCacheType.File;
          break;
        }
        case TKCacheType.DB_AutoUpdate: {
          cacheType = TKCacheType.DB;
          break;
        }
        default: {
          break;
        }
      }
      switch (cacheType) {
        case TKCacheType.Mem: {
          this.deleteMemCacheData(key);
          break;
        }
        case TKCacheType.File: {
          this.deleteFileCacheData(key, fileName);
          break;
        }
        case TKCacheType.DB: {
          return this.deleteDBCacheData(key, fileName);
        }
        default: {
          break;
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心删除数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   *  根据关键字删除内存里面的缓存数据
   *
   * @param key 关键字
   */
  public deleteMemCacheData(key: string) {
    try {
      let cacheVO: TKCacheVO | undefined = this.memCacheMap.get(key) as TKCacheVO | undefined;
      if (cacheVO) {
        this.memCacheMap.delete(key);
        if (cacheVO.cacheTime > 0) {
          let index: number = this.memCacheKeys.indexOf(key);
          if (index >= 0) {
            this.memCacheKeys.splice(index, 1);
            this.currentMemCacheSize -= cacheVO.cacheSize;
          }
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心删除内存数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   *  根据关键字删除文件里面的缓存数据
   *
   * @param key 关键字
   * @param fileName  文件名称
   */
  public deleteFileCacheData(key: string, fileName: string = "") {
    try {
      let preferences: TKPreferences = new TKPreferences(fileName);
      let cacheVO: TKCacheVO | undefined = preferences.getData(key);
      if (cacheVO) {
        preferences.deleteData(key);
        if (cacheVO.cacheTime > 0) {
          //同步Key
          let fileAllKeys: Array<string> = preferences.getData("TKCacheKeys", new Array<string>()) as Array<string>;
          let index: number = fileAllKeys.indexOf(key);
          if (index >= 0) {
            fileAllKeys.splice(index, 1);
          }
          preferences.setData("TKCacheKeys", fileAllKeys);
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心删除文件数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   *  根据关键字删除内存里面的缓存数据
   *
   * @param key 关键字
   * @param dbName  数据库名称
   */
  public async deleteDBCacheData(key: string, dbName: string = ""): Promise<void> {
    try {
      let kvStore: TKSingleKVStore = new TKSingleKVStore(dbName);
      let cacheVO: TKCacheVO | undefined = await kvStore.getData(key);
      if (cacheVO) {
        await kvStore.deleteData(key);
        if (cacheVO.cacheTime > 0) {
          //同步Key
          let dbAllKeys: Array<string> = await kvStore.getData("TKCacheKeys", new Array<string>()) as Array<string>;
          let index: number = dbAllKeys.indexOf(key);
          if (index >= 0) {
            dbAllKeys.splice(index, 1);
          }
          await kvStore.setData("TKCacheKeys", dbAllKeys);
        }
      }
    } catch (error) {
      TKLog.error(`[TKCacheManager]缓存管理中心删除DB数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }
}