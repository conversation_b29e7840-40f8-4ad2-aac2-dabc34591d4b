import { distributedKVStore } from '@kit.ArkData';
import { TKObjectHelper } from '../data/TKObjectHelper';
import { TKLog } from '../logger/TKLog';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKContextHelper } from '../system/TKContextHelper';
import { TKSystemHelper } from '../system/TKSystemHelper';

export class TKSingleKVStore {
  private dbName: string | undefined = undefined;
  private kvManager: distributedKVStore.KVManager | undefined = undefined;
  private kvStore: distributedKVStore.SingleKVStore | undefined = undefined;

  public constructor(dbName: string = 'TKStoreKVDB') {
    try {
      this.dbName = TKStringHelper.isNotBlank(dbName) ? dbName : 'TKStoreKVDB';
      // 创建KVManager实例
      this.kvManager = distributedKVStore.createKVManager({
        context: TKContextHelper.getCurrentContext(),
        bundleName: TKSystemHelper.getAppIdentifier()
      } as distributedKVStore.KVManagerConfig) as distributedKVStore.KVManager;
    } catch (error) {
      TKLog.error(`[TKSingleKVStore]初始化异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取数据库名称
   * @returns
   */
  public getDBName(): string {
    return this.dbName as string;
  }

  /**
   * 创建数据库
   */
  private async buildKVStore(): Promise<void> {
    if (!this.kvStore) {
      return new Promise<void>((resolve, reject) => {
        if (this.kvManager) {
          this.kvManager.getKVStore<distributedKVStore.SingleKVStore>(this.dbName, {
            createIfMissing: true,
            encrypt: false,
            backup: false,
            autoSync: false,
            // kvStoreType不填时，默认创建多设备协同数据库
            kvStoreType: distributedKVStore.KVStoreType.SINGLE_VERSION,
            // 多设备协同数据库：kvStoreType: distributedKVStore.KVStoreType.DEVICE_COLLABORATION,
            securityLevel: distributedKVStore.SecurityLevel.S1
          } as distributedKVStore.Options, (error, store: distributedKVStore.SingleKVStore) => {
            if (error) {
              TKLog.error(`[TKSingleKVStore]创建数据库异常，code: ${error.code}， message: ${error.message}`);
              reject();
            } else {
              this.kvStore = store;
              resolve();
            }
          });
        }
      });
    }
  }

  /**
   * 缓存数据
   * @param key
   * @param data
   */
  public async setData<T>(key: string, data: T): Promise<void> {
    try {
      await this.buildKVStore();
      if (this.kvStore) {
        await this.kvStore.put(key, TKObjectHelper.serialize(data));
      }
    } catch (error) {
      TKLog.error(`[TKSingleKVStore]缓存数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取数据
   * @param key
   * @param defValue
   * @returns
   */
  public async getData<T>(key: string, defValue: T | undefined = undefined): Promise<T | undefined> {
    try {
      await this.buildKVStore();
      if (this.kvStore) {
        let dataStr: string = await this.kvStore.get(key) as string;
        let data: T | undefined = TKObjectHelper.deserialize<T>(dataStr) as T ?? defValue;
        return data;
      }
    } catch (error) {
      TKLog.error(`[TKSingleKVStore]获取数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return defValue;
  }

  /**
   * 删除缓存
   * @param key
   */
  public async deleteData(key: string): Promise<void> {
    try {
      await this.buildKVStore();
      if (this.kvStore) {
        await this.kvStore.delete(key);
      }
    } catch (error) {
      TKLog.error(`[TKSingleKVStore]删除缓存异常，code: ${error.code}， message: ${error.message}`);
    }
  }
}