import { TKDataHelper } from '../data/TKDataHelper';
import { TKURLRequestHelper, TKURLRequestVO, TKURLResponseVO } from '../net/TKURLRequestHelper';
import { TKImageHelper } from '../ui/TKImageHelper';
import image from '@ohos.multimedia.image';
import { TKFileHelper } from '../file/TKFileHelper';
import { TKMd5Helper } from '../crypto/TKMd5Helper';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKArrayHelper } from '../array/TKArrayHelper';

/**
 * 图片缓存加载回调
 */
export interface TKImageCacheLoadListener {
  //图片加载开始
  onLoadStart?: (src: string, image?: Resource | PixelMap) => void;

  //图片加载成功
  onLoadSuccess?: (src: string, image: Resource | PixelMap) => void;

  //图片加载失败
  onLoadFailed?: (src: string, image?: Resource | PixelMap, reason?: string) => void;
}

/**
 * 图片缓存参数
 */
@Observed
export class TKImageCacheOption {
  //加载图片地址
  loadSrc: string = "";
  //占位图片
  placeholderSrc?: ResourceStr;
  //失败图片
  errorholderSrc?: ResourceStr;
  //加载回调
  onLoadListener?: TKImageCacheLoadListener;
  //返回的图片对象
  image?: Resource | image.PixelMap;
}

/**
 * 图片存储管理器
 */
export class TKImageCacheManager {
  //单例对象
  private static instance: TKImageCacheManager | undefined = undefined;
  //最大内存缓存的对象个数，超过就触发淘汰机制
  private maxMemCacheNum: number = 100;
  //最大内存缓存的对象大小，超过就出发淘汰机制
  private maxMemCacheSize: number = 100 * 1024 * 1024;
  //当前图片的数量
  private currentMemCacheNum: number = 0;
  //当前的内存大小
  private currentMemCacheSize: number = 0;
  //缓存记录key队列，按照先入先出的原则管理
  private memCacheKeys: Array<string> = new Array<string>();
  //内存缓存Map
  private memCacheMap: Map<string, ArrayBuffer> = new Map<string, ArrayBuffer>();
  //是否开启内存
  private memCacheEnable: boolean = true;

  public static shareInstance(): TKImageCacheManager {
    if (!TKImageCacheManager.instance) {
      TKImageCacheManager.instance = new TKImageCacheManager();
    }
    return TKImageCacheManager.instance;
  }

  /**
   * 设置最大内存缓存数量
   * @param maxNum
   */
  public setMaxMemCacheNum(maxNum: number) {
    this.maxMemCacheNum = maxNum;
  }

  /**
   * 设置最大内存缓存大小
   * @param maxSize
   */
  public setMaxMemCacheSize(maxSize: number) {
    this.maxMemCacheSize = maxSize;
  }

  /**
   * 是否启用内存缓存
   * @param memCacheEnable
   */
  public setMemEnable(memCacheEnable: boolean) {
    this.memCacheEnable = memCacheEnable;
    if (!memCacheEnable) {
      this.memCacheMap.clear();
      TKArrayHelper.clearArray(this.memCacheKeys);
      this.currentMemCacheNum = 0;
      this.currentMemCacheSize = 0;
    }
  }

  /**
   * 保存图片内存缓存
   * @param imageLoadSrc
   * @param imageCacheData
   */
  private saveMemCache(imageLoadSrc: string, imageCacheData: ArrayBuffer) {
    if (this.memCacheEnable) {
      let imageCacheName: string = TKMd5Helper.stringWithMd5Sync(imageLoadSrc);
      if (!this.memCacheKeys.includes(imageCacheName)) {
        while (this.currentMemCacheNum > this.maxMemCacheNum ||
          this.currentMemCacheSize > this.maxMemCacheSize) {
          let deleteMemCacheKey: string = this.memCacheKeys[0];
          let deleteMemCacheSize: number = this.memCacheMap.get(deleteMemCacheKey)?.byteLength ?? 0;
          TKArrayHelper.removeObjectInArray(this.memCacheKeys, deleteMemCacheKey);
          this.memCacheMap.delete(deleteMemCacheKey);
          this.currentMemCacheNum--;
          this.currentMemCacheSize -= deleteMemCacheSize;
        }
        this.currentMemCacheNum++;
        this.currentMemCacheSize += imageCacheData.byteLength;
        this.memCacheKeys.push(imageCacheName);
        this.memCacheMap.set(imageCacheName, imageCacheData);
      }
    }
  }

  /**
   * 读取图片内存缓存
   * @param imageLoadSrc
   */
  private readMemCache(imageLoadSrc: string): ArrayBuffer | undefined {
    if (this.memCacheEnable) {
      let imageCacheName: string = TKMd5Helper.stringWithMd5Sync(imageLoadSrc);
      let imageCacheData: ArrayBuffer | undefined = this.memCacheMap.get(imageCacheName);
      return imageCacheData;
    }
    return undefined;
  }

  /**
   * 删除图片内存缓存
   * @param imageLoadSrc
   */
  private removeMemCache(imageLoadSrc: string) {
    if (this.memCacheEnable) {
      let imageCacheName: string = TKMd5Helper.stringWithMd5Sync(imageLoadSrc);
      if (this.memCacheKeys.includes(imageCacheName)) {
        let deleteMemCacheKey: string = imageCacheName;
        let deleteMemCacheSize: number = this.memCacheMap.get(deleteMemCacheKey)?.byteLength ?? 0;
        TKArrayHelper.removeObjectInArray(this.memCacheKeys, deleteMemCacheKey);
        this.memCacheMap.delete(deleteMemCacheKey);
        this.currentMemCacheNum--;
        this.currentMemCacheSize -= deleteMemCacheSize;
      }
    }
  }

  /**
   * 保存图片文件缓存
   * @param imageLoadSrc
   * @param imageCacheData
   */
  private saveFileCache(imageLoadSrc: string, imageCacheData: ArrayBuffer) {
    let imageCacheName: string = TKMd5Helper.stringWithMd5Sync(imageLoadSrc);
    let imageCachePath: string =
      `${TKFileHelper.cacheDir()}/thinkive/image/${imageCacheName}.jpeg`;
    TKFileHelper.writeFile(TKDataHelper.arrayBufferToUint8Array(imageCacheData), imageCachePath);
  }

  /**
   * 读取图片文件缓存
   * @param imageLoadSrc
   */
  private readFileCache(imageLoadSrc: string): ArrayBuffer | undefined {
    let imageCacheName: string = TKMd5Helper.stringWithMd5Sync(imageLoadSrc);
    let imageCachePath: string =
      `${TKFileHelper.cacheDir()}/thinkive/image/${imageCacheName}.jpeg`;
    let imageCacheData: ArrayBuffer | undefined =
      TKDataHelper.uint8ArrayToArrayBuffer(TKFileHelper.readFile(imageCachePath));
    return imageCacheData;
  }

  /**
   * 删除图片文件缓存
   * @param imageLoadSrc
   */
  private removeFileCache(imageLoadSrc: string) {
    let imageCacheName: string = TKMd5Helper.stringWithMd5Sync(imageLoadSrc);
    let imageCachePath: string =
      `${TKFileHelper.cacheDir()}/thinkive/image/${imageCacheName}.jpeg`;
    TKFileHelper.deleteFile(imageCachePath);
  }

  /**
   * 加载图片 $r(icon) $rawfile(icon.png) http://xxxx.png https:/xxxx.png
   */
  public async loadImage(option: TKImageCacheOption): Promise<void> {
    if (TKStringHelper.isNotBlank(option.loadSrc)) {
      if (option.loadSrc.startsWith("http://") || option.loadSrc.startsWith("https://")) {
        let imageCacheData: ArrayBuffer | undefined = this.readImageCache(option.loadSrc);
        if (imageCacheData && imageCacheData.byteLength > 0) {
          this.saveMemCache(option.loadSrc, imageCacheData);
          let image: image.PixelMap = await TKImageHelper.arrayBufferToPixelMap(imageCacheData) as image.PixelMap;
          option.image = image;
          option.onLoadListener?.onLoadSuccess?.(option.loadSrc, image);
        } else {
          if (option.placeholderSrc) {
            let image: Resource | undefined =
              (typeof option.placeholderSrc == "string") ? TKImageHelper.getImageResource(option.placeholderSrc) :
              option.placeholderSrc;
            option.image = image;
            option.onLoadListener?.onLoadStart?.(option.loadSrc, image);
          }
          TKURLRequestHelper.getRequest({ url: option.loadSrc } as TKURLRequestVO,
            async (responseVO: TKURLResponseVO) => {
              if (responseVO.error_no == 0 && responseVO.MIMEType.includes("image")) {
                imageCacheData = TKDataHelper.uint8ArrayToArrayBuffer(responseVO.result);
                this.saveImageCache(option.loadSrc, imageCacheData);
                let image: image.PixelMap = await TKImageHelper.arrayBufferToPixelMap(imageCacheData) as image.PixelMap;
                option.image = image;
                option.onLoadListener?.onLoadSuccess?.(option.loadSrc, image);
              } else {
                if (option.errorholderSrc) {
                  let image: Resource | undefined =
                    (typeof option.errorholderSrc == "string") ? TKImageHelper.getImageResource(option.errorholderSrc) :
                    option.errorholderSrc;
                  option.image = image;
                  option.onLoadListener?.onLoadFailed?.(option.loadSrc, image, responseVO.error_info);
                }
              }
            });
        }
      } else {
        let image: Resource = TKImageHelper.getImageResource(option.loadSrc);
        option.image = image;
        option.onLoadListener?.onLoadSuccess?.(option.loadSrc, image);
      }
    }
  }

  /**
   * 读取缓存图片
   * @param loadSrc
   * @returns
   */
  public readImageCache(loadSrc: string): ArrayBuffer | undefined {
    let imageCacheData: ArrayBuffer | undefined = this.readMemCache(loadSrc);
    if (!imageCacheData || imageCacheData.byteLength == 0) {
      imageCacheData = this.readFileCache(loadSrc);
    }
    if (imageCacheData && imageCacheData.byteLength > 0) {
      return imageCacheData;
    }
    return undefined;
  }

  /**
   * 保存图片缓存
   * @param loadSrc
   * @param cacheData
   */
  public saveImageCache(loadSrc: string, cacheData: ArrayBuffer) {
    this.saveMemCache(loadSrc, cacheData);
    this.saveFileCache(loadSrc, cacheData);
  }

  /**
   * 删除图片缓存
   * @param loadSrc
   */
  public removeImageCache(loadSrc: string) {
    this.removeMemCache(loadSrc);
    this.removeFileCache(loadSrc);
  }
}