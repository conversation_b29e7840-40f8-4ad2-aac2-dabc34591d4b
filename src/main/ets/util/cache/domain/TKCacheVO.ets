/**
 * 缓存类型
 */
export enum TKCacheType {
  Mem, //内存
  File, //文件
  DB, //数据库
  Mem_AutoUpdate, //内存，命中时候自动更新
  File_AutoUpdate, //文件，命中时候自动更新
  DB_AutoUpdate //数据库，命中时候自动更新
}

export class TKCacheVO {
  /**
   *  缓存类型
   */
  type: TKCacheType = TKCacheType.Mem;
  /**
   *  缓存Key
   */
  key: string = '';
  /**
   *  缓存数据对象
   */
  data: Object | undefined = undefined;
  /**
   *  缓存时间,单位秒，0代表永久缓存
   */
  cacheTime: number = 0;
  /**
   *  缓存对象创建时间
   */
  beginTime: number = 0;
  /**
   * 缓存大小
   */
  cacheSize: number = 0;
  /**
   * <AUTHOR> 2015-10-12 12:10:38
   *
   *  缓存文件名称
   */
  fileName: string = '';
}