import dataPreferences from '@ohos.data.preferences';
import { TKDataHelper } from '../data/TKDataHelper';
import { TKObjectHelper } from '../data/TKObjectHelper';
import { TKFileHelper } from '../file/TKFileHelper';
import { TKLog } from '../logger/TKLog';
import { TKMapHelper } from '../map/TKMapHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKContextHelper } from '../system/TKContextHelper';

export class TKPreferences {
  private fileName: string | undefined = undefined;
  private preferences: dataPreferences.Preferences | undefined = undefined;

  public constructor(fileName: string = 'TKPreferencesFile') {
    this.fileName = TKStringHelper.isNotBlank(fileName) ? fileName : 'TKPreferencesFile';
    this.preferences = dataPreferences.getPreferencesSync(TKContextHelper.getCurrentContext(), {
      name: this.fileName
    } as dataPreferences.Options);
  }

  /**
   * 获取文件名称
   * @returns
   */
  public getFileName(): string {
    return this.fileName as string;
  }

  /**
   * 缓存数据
   * @param key
   * @param data
   */
  public setData<T>(key: string, data: T) {
    try {
      if (this.preferences) {
        let value: string = TKObjectHelper.serialize(data) as string;
        if (value.length > 8000) {
          TKLog.warn(`[${this.fileName}]存储[${key}]的内容过大[${value.length}]，自动转为自定义文件存储`);
          let filePath: string = this.getLocalFilePath();
          let fileData: Record<string, Object | undefined> = this.getLocalFileData(filePath);
          fileData[key] = value;
          TKFileHelper.writeFile(JSON.stringify(fileData), filePath);
          this.preferences.deleteSync(key);
          this.preferences.flush();
        } else {
          this.preferences.putSync(key, value);
          this.preferences.flush();
        }
      }
    } catch (error) {
      TKLog.error(`[TKPreferences]缓存数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 本地存储路径
   * @returns
   */
  private getLocalFilePath(): string {
    let filePath: string = TKFileHelper.cacheDir() + "/" + this.fileName;
    return filePath;
  }

  /**
   * 本地存储数据
   * @returns
   */
  private getLocalFileData(filePath: string): Record<string, Object | undefined> {
    let fileContent: string = TKDataHelper.uint8ArrayToString(TKFileHelper.readFile(filePath));
    let fileData: Record<string, Object | undefined> =
      TKStringHelper.isNotBlank(fileContent) ? JSON.parse(fileContent) : {};
    return fileData;
  }

  /**
   * 获取数据
   * @param key
   * @param defValue
   * @returns
   */
  public getData<T>(key: string, defValue: T | undefined = undefined): T | undefined {
    try {
      if (this.preferences) {
        let dataStr: string = this.preferences.getSync(key, "") as string;
        if (TKStringHelper.isBlank(dataStr)) {
          let filePath: string = this.getLocalFilePath();
          let fileData: Record<string, Object | undefined> = this.getLocalFileData(filePath);
          dataStr = TKMapHelper.getString(fileData, key);
        }
        let data: T | undefined = TKObjectHelper.deserialize<T>(dataStr) as T ?? defValue;
        return data;
      }
      return defValue;
    } catch (error) {
      TKLog.error(`[TKPreferences]获取数据异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   * 删除数据
   * @param key
   */
  public deleteData(key: string) {
    try {
      if (this.preferences) {
        this.preferences.deleteSync(key);
        this.preferences.flush();
        let filePath: string = this.getLocalFilePath();
        let fileData: Record<string, Object | undefined> = this.getLocalFileData(filePath);
        if (Object.entries(fileData).length > 0) {
          fileData[key] = undefined;
          TKFileHelper.writeFile(JSON.stringify(fileData), filePath);
        }
      }
    } catch (error) {
      TKLog.error(`[TKPreferences]删除数据异常，code: ${error.code}， message: ${error.message}`);
    }
  }
}

