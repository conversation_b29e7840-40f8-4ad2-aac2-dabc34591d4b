/**
 * 核心资产存储管理
 */
import { TKLog } from '../logger/TKLog';
import { asset } from '@kit.AssetStoreKit';
import { TKDataHelper } from '../data/TKDataHelper';

export namespace TKAssetStore {

  /**
   * 保存数据
   * @param key
   * @param value
   * @param e10
   * @returns
   */
  export async function setData(key: string, value: string, e10: boolean = true): Promise<boolean> {
    try {
      if (!canIUse("SystemCapability.Security.Asset")) {
        TKLog.error(`[TKAssetStore]当前设备不支持该模块`);
        return false;
      }
      let attr: asset.AssetMap = new Map();
      attr.set(asset.Tag.ALIAS, TKDataHelper.stringToUint8Array(key));
      attr.set(asset.Tag.SECRET, TKDataHelper.stringToUint8Array(value));
      attr.set(asset.Tag.SYNC_TYPE, asset.SyncType.THIS_DEVICE);
      attr.set(asset.Tag.CONFLICT_RESOLUTION, asset.ConflictResolution.THROW_ERROR);
      if (e10) {
        attr.set(asset.Tag.IS_PERSISTENT, e10);
      }
      let result: boolean;
      if ((await hasKey(key))) {
        result = await updateAssetMap(attr, attr);
      } else {
        result = await setAssetMap(attr);
      }
      if (result) {
        AppStorage.setOrCreate(key, value);
      }
      return result;
    } catch (error) {
      TKLog.error(`[AssetStore]保存数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return false;
  }

  /**
   * 是否包含指定字段key
   * @param key
   * @returns
   */
  export async function hasKey(key: string): Promise<boolean> {
    try {
      if (!canIUse("SystemCapability.Security.Asset")) {
        TKLog.error(`[TKAssetStore]当前设备不支持该模块`);
        return false;
      }
      let query: asset.AssetMap = new Map();
      query.set(asset.Tag.ALIAS, TKDataHelper.stringToUint8Array(key));
      query.set(asset.Tag.RETURN_TYPE, asset.ReturnType.ALL);
      const result = await getAssetMap(query);
      if (!result) {
        return false;
      }
      if (result.length < 1) {
        return false;
      }
      return true;
    } catch (error) {
      TKLog.error(`[AssetStore]是否包含指定字段key判断异常，code: ${error.code}， message: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取数据
   * @param key
   * @returns
   */
  export async function getData(key: string): Promise<string | undefined> {
    try {
      if (!canIUse("SystemCapability.Security.Asset")) {
        TKLog.error(`[TKAssetStore]当前设备不支持该模块`);
        return undefined;
      }
      let query: asset.AssetMap = new Map();
      query.set(asset.Tag.ALIAS, TKDataHelper.stringToUint8Array(key));
      query.set(asset.Tag.RETURN_TYPE, asset.ReturnType.ALL);
      const a10 = await getAssetMap(query);
      if (!a10) {
        return undefined;
      }
      if (a10.length < 1) {
        return undefined;
      }
      let map: asset.AssetMap = a10[0]
      let b10 = map.get(asset.Tag.SECRET) as Uint8Array;
      if (b10) {
        let c10 = TKDataHelper.uint8ArrayToString(b10);
        return c10
      }
    } catch (error) {
      TKLog.error(`[AssetStore]获取数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return undefined;
  }

  /**
   * 删除数据
   * @param key
   * @returns
   */
  export async function removeData(key: string): Promise<boolean> {
    try {
      if (!canIUse("SystemCapability.Security.Asset")) {
        TKLog.error(`[TKAssetStore]当前设备不支持该模块`);
        return false;
      }
      let query: asset.AssetMap = new Map();
      query.set(asset.Tag.ALIAS, TKDataHelper.stringToUint8Array(key));
      let result = await removeAssetMap(query);
      if (result) {
        AppStorage.setOrCreate(key, '');
      }
      return result;
    } catch (error) {
      TKLog.error(`[AssetStore]删除数据异常，code: ${error.code}， message: ${error.message}`);
      return false;
    }
  }

  /**
   * 保存资产
   * @param attr
   * @returns
   */
  async function setAssetMap(attr: asset.AssetMap): Promise<boolean> {
    try {
      await asset.add(attr);
      return true;
    } catch (error) {
      TKLog.error(`[AssetStore]保存资产异常，code: ${error.code}， message: ${error.message}`);
      return false;
    }
  }

  /**
   * 删除资产
   * @param attr
   * @returns
   */
  async function removeAssetMap(attr: asset.AssetMap): Promise<boolean> {
    try {
      await asset.remove(attr);
      return true;
    } catch (error) {
      TKLog.error(`[AssetStore]删除资产异常，code: ${error.code}， message: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取资产
   * @param query
   * @returns
   */
  async function getAssetMap(query: asset.AssetMap): Promise<Array<asset.AssetMap>> {
    try {
      const z9 = await asset.query(query);
      return z9;
    } catch (error) {
      TKLog.error(`[AssetStore]获取资产异常，code: ${error.code}， message: ${error.message}`);
      return [];
    }
  }

  /**
   * 更新资产
   * @param query
   * @returns
   */
  async function updateAssetMap(query: asset.AssetMap, y9: asset.AssetMap): Promise<boolean> {
    try {
      await asset.update(query, y9);
      return true;
    } catch (error) {
      TKLog.error(`[AssetStore]更新资产异常，code: ${error.code}， message: ${error.message}`);
      return false;
    }
  }

}