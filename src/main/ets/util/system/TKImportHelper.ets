import { TKStringHelper } from '../string/TKStringHelper';
import { TKLog } from '../logger/TKLog';

/**
 * 思迪动态导入工具类
 * 用于动态导入模块并获取类实例
 */
export namespace TKImportHelper {
  /**
   * 动态导入模块
   *
   * @param importName 模块名称，如'numberauth_standard'
   * @returns 返回Promise，包含导入的模块
   */
  export async function importAndGetModule(importName: string): Promise<ESObject> {
    //TKLog.info(`[TKImportHelper]开始导入模块 ${importName}`);
    return new Promise<ESObject>(async (resolve) => {
      try {
        // 尝试导入模块
        const ns: ESObject = await import(`${importName}`);
        if (ns) {
          //TKLog.info(`[TKImportHelper]成功导入模块 ${importName}`);
          resolve(ns);
        } else {
          const errorMsg = `导入模块 ${importName} 失败，模块不存在`;
          TKLog.error(`[TKImportHelper]${errorMsg}`);
          resolve(undefined);
        }
      } catch (error) {
        let errorMsg = error instanceof Error ? error.message : JSON.stringify(error);
        errorMsg = `导入模块 ${importName} 异常: ${errorMsg}`;
        TKLog.error(`[TKImportHelper]${errorMsg}`);
        resolve(undefined);
      }
    });
  }

  /**
   * 获取类
   * @param moduleName 模块名称，可为空
   * @param className 类名称，可为空
   * @returns 返回Promise，包含类定义
   */
  export async function importAndGetClass(moduleName: string = "", className: string = ""): Promise<ESObject> {
    //TKLog.info(`[TKImportHelper]开始获取类，模块: ${moduleName || '无'}, 类名: ${className || '无'}`);
    return new Promise<ESObject>(async (resolve) => {
      try {
        // 如果提供了模块名，先尝试导入模块
        if (TKStringHelper.isNotBlank(moduleName)) {
          try {
            const moduleObj: ESObject = await importAndGetModule(moduleName);
            if (TKStringHelper.isBlank(className)) {
              // 如果没有提供类名，直接返回模块对象
              //TKLog.info(`[TKImportHelper]成功导入模块 ${moduleName}`);
              resolve(moduleObj);
              return;
            } else {
              // 如果提供了类名，尝试从模块中获取类
              let classObj: ESObject | undefined = moduleObj[className];
              if (classObj) {
                //TKLog.info(`[TKImportHelper]从模块 ${moduleName} 中找到了类 ${className}`);
                resolve(classObj);
                return;
              } else {
                //TKLog.warn(`[TKImportHelper]模块 ${moduleName} 中未找到类 ${className}，尝试直接导入类`);
              }
            }
          } catch (e) {
            const errorMsg = e instanceof Error ? e.message : JSON.stringify(e);
            //TKLog.warn(`[TKImportHelper]导入模块 ${moduleName} 失败: ${errorMsg}，尝试直接导入类`);
          }
        }
        // 如果模块中没有找到类或没有提供模块名，尝试直接导入类
        if (TKStringHelper.isNotBlank(className)) {
          try {
            const directClass: ESObject = await importAndGetModule(className);
            //TKLog.info(`[TKImportHelper]直接导入类 ${className} 成功`);
            resolve(directClass);
            return;
          } catch (e) {
            const errorMsg = e instanceof Error ? e.message : JSON.stringify(e);
            //TKLog.warn(`[TKImportHelper]直接导入类 ${className} 失败: ${errorMsg}`);
          }
        }
        // 所有尝试都失败
        const errorMsg = `未找到类: 模块=${moduleName || '无'}, 类名=${className || '无'}`;
        TKLog.error(`[TKImportHelper]${errorMsg}`);
        resolve(undefined);
      } catch (error) {
        let errorMsg = error instanceof Error ? error.message : JSON.stringify(error);
        errorMsg = `获取类异常: ${errorMsg}`;
        TKLog.error(`[TKImportHelper]${errorMsg}`);
        resolve(undefined);
      }
    });
  }

  /**
   * 从模块中获取类实例或创建实例
   * 尝试多种方式获取实例：
   * 1. 使用指定的初始化函数名
   * 2. 如果未指定或失败，尝试通过getInstance()获取单例实例
   * 3. 如果失败，尝试通过构造函数创建实例
   *
   * @param moduleName 模块名称，可为空
   * @param className 类名称，可为空
   * @param initFuncName 初始化函数名，可为空
   * @returns 返回Promise，包含实例对象
   */
  export async function importAndGetInstance<T>(moduleName: string = "", className: string = "",
    initFuncName: string = ""): Promise<T | undefined> {
    // TKLog.info(`[TKImportHelper]尝试获取实例，模块: ${moduleName || '无'}, 类名: ${className ||
    //   '无'}, 初始化函数: ${initFuncName || '无'}`);
    return new Promise<T | undefined>(async (resolve) => {
      try {
        // 找到类定义
        let classDef: ESObject | undefined = await importAndGetClass(moduleName, className);
        // 尝试创建实例
        // 方法1: 尝试使用指定的初始化函数
        if (TKStringHelper.isNotBlank(initFuncName) && classDef[initFuncName] &&
          typeof classDef[initFuncName] === 'function') {
          try {
            let instance: T = classDef[initFuncName]() as T;
            //TKLog.info(`[TKImportHelper]通过指定的初始化函数 ${initFuncName} 成功获取实例`);
            resolve(instance);
            return;
          } catch (e) {
            const errorMsg = e instanceof Error ? e.message : JSON.stringify(e);
            //TKLog.warn(`[TKImportHelper]通过指定的初始化函数 ${initFuncName} 获取实例失败: ${errorMsg}`);
          }
        }
        // 方法2: 尝试通过getInstance静态方法获取实例
        if (classDef['getInstance'] && typeof classDef['getInstance'] === 'function') {
          try {
            let instance: T = classDef['getInstance']() as T;
            //TKLog.info(`[TKImportHelper]通过getInstance方法成功获取实例`);
            resolve(instance);
            return;
          } catch (e) {
            const errorMsg = e instanceof Error ? e.message : JSON.stringify(e);
            //TKLog.warn(`[TKImportHelper]通过getInstance方法获取实例失败: ${errorMsg}`);
          }
        }
        // 方法3: 尝试使用构造函数创建实例
        if (typeof classDef === 'function') {
          try {
            let instance: T = new classDef() as T;
            //TKLog.info(`[TKImportHelper]通过构造函数成功创建实例`);
            resolve(instance);
            return;
          } catch (e) {
            const errorMsg = e instanceof Error ? e.message : JSON.stringify(e);
            //TKLog.warn(`[TKImportHelper]通过构造函数创建实例失败: ${errorMsg}`);
          }
        }
        // 所有方法都失败
        const errorMsg = `无法创建实例: 模块=${moduleName || '无'}, 类名=${className || '无'}`;
        TKLog.error(`[TKImportHelper]${errorMsg}`);
        resolve(undefined);
      } catch (error) {
        let errorMsg = error instanceof Error ? error.message : JSON.stringify(error);
        errorMsg = `获取实例异常: ${errorMsg}`;
        TKLog.error(`[TKImportHelper]${errorMsg}`);
        resolve(undefined);
      }
    });
  }

  /**
   * 检查模块是否可用
   * @param importName 模块名称
   * @returns 返回Promise，包含模块是否可用的结果
   */
  export async function isModuleAvailable(importName: string): Promise<boolean> {
    //TKLog.info(`[TKImportHelper]检查模块 ${importName} 是否可用`);
    let ns: ESObject = await importAndGetModule(importName);
    if (ns) {
      //TKLog.info(`[TKImportHelper]模块 ${importName} 可用`);
      return true;
    } else {
      //TKLog.info(`[TKImportHelper]模块 ${importName} 不可用`);
      return false;
    }
  }

  /**
   * 调用动态导入模块的静态方法
   * @param importName 模块名称
   * @param className 类名称
   * @param methodName 方法名称
   * @param args 方法参数
   * @returns 返回Promise，包含方法调用结果
   */
  export async function callStaticMethod(importName: string, className: string, methodName: string,
    ...args: Object[]): Promise<ESObject> {
    //TKLog.info(`[TKImportHelper]开始调用模块 ${importName} 中 ${className} 的静态方法 ${methodName}`);
    return new Promise<ESObject>(async (resolve) => {
      try {
        // 尝试导入模块
        const ns: ESObject = await importAndGetClass(importName, className);
        // 调用静态方法
        if (ns[methodName] && typeof ns[methodName] === 'function') {
          try {
            const result: ESObject = ns[methodName](...args);
            //TKLog.info(`[TKImportHelper]成功调用静态方法 ${methodName}`);
            resolve(result);
            return;
          } catch (e) {
            let errorMsg = e instanceof Error ? e.message : JSON.stringify(e);
            errorMsg = `调用静态方法 ${methodName} 异常: ${errorMsg}`;
            TKLog.error(`[TKImportHelper]${errorMsg}`);
            resolve(undefined);
            return;
          }
        }
        const errorMsg = `在模块 ${importName} 的类 ${className} 中未找到静态方法 ${methodName}`;
        TKLog.error(`[TKImportHelper]${errorMsg}`);
        resolve(undefined);
      } catch (error) {
        let errorMsg = error instanceof Error ? error.message : JSON.stringify(error);
        errorMsg = `导入模块 ${importName} 异常: ${errorMsg}`;
        TKLog.error(`[TKImportHelper]${errorMsg}`);
        resolve(undefined);
      }
    });
  }

  /**
   * 检查多个模块是否有可用的
   * @param importNames 模块名称数组或单个模块名称
   * @returns 返回Promise，如果有任一模块可用返回'1'，否则返回'0'
   */
  export async function modulesAvailable(importNames: Array<string> | string): Promise<string> {
    //TKLog.info(`[TKImportHelper]检查模块集合是否可用`);
    return new Promise<string>(async (resolve) => {
      let canHandle = false;
      if (typeof importNames === 'string') {
        // 单个模块检查
        let sdkIsExist = await isModuleAvailable(importNames);
        canHandle = sdkIsExist;
      } else {
        // 多个模块检查
        const promises = importNames.map(async (importName) => {
          let sdkIsExist = await isModuleAvailable(importName);
          canHandle = canHandle || sdkIsExist;
        });
        await Promise.all(promises);
      }
      resolve(canHandle ? '1' : '0');
    });
  }
}