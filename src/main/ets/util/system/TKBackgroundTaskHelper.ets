import { common, wantAgent } from '@kit.AbilityKit';
import backgroundTaskManager from '@ohos.resourceschedule.backgroundTaskManager';
import { TKLog } from '../logger/TKLog';
import { TKContextHelper } from './TKContextHelper';
import { BusinessError } from '@kit.BasicServicesKit';

/**
 * 后台任务管理器
 */
export namespace TKBackgroundTaskHelper {

  /**
   * 开启后台服务
   * @param bgMode
   * @param context
   */
  export function startBackgroundTask(bgMode?: backgroundTaskManager.BackgroundMode,
    context?: common.UIAbilityContext): void {
    try {
      bgMode = bgMode ?? backgroundTaskManager.BackgroundMode.AUDIO_PLAYBACK;
      context = context ?? TKContextHelper.getCurrentUIAbilityContext();
      let wantAgentInfo: wantAgent.WantAgentInfo = {
        wants: [
          {
            bundleName: context.abilityInfo.bundleName,
            abilityName: context.abilityInfo.name
          }
        ],
        operationType: wantAgent.OperationType.START_ABILITY,
        requestCode: 0,
        wantAgentFlags: [wantAgent.WantAgentFlags.UPDATE_PRESENT_FLAG]
      };
      wantAgent.getWantAgent(wantAgentInfo).then((wantAgentObj) => {
        backgroundTaskManager.startBackgroundRunning(context, bgMode, wantAgentObj).then(() => {
          TKLog.info('[TKBackgroundTaskHelper]开启后台服务成功');
        }).catch((err: BusinessError) => {
          TKLog.error(`[TKBackgroundTaskHelper]开启后台服务异常:  ${JSON.stringify(err)}`);
        });
      });
    } catch (error) {
      TKLog.error(`[TKBackgroundTaskHelper]开启后台服务异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 停止后台任务
   * @param context
   */
  export function stopBackgroundTask(context?: common.UIAbilityContext): void {
    context = context ?? TKContextHelper.getCurrentUIAbilityContext();
    backgroundTaskManager.stopBackgroundRunning(context).then(() => {
      TKLog.info('[TKBackgroundTaskHelper]关闭后台服务成功');
    }).catch((err: BusinessError) => {
      TKLog.error(`[TKBackgroundTaskHelper]关闭后台服务异常:  ${JSON.stringify(err)}`);
    });
  }
}