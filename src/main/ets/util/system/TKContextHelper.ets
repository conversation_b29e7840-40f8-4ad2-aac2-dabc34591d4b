/**
 * 上下文帮组类
 */
import { application, common, Context, UIAbility } from '@kit.AbilityKit';
import { window } from '@kit.ArkUI';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKNotificationCenter } from '../../base/notification/TKNotificationCenter';

export class TKMyAbilityLifecycleListener {
  public onAbilityCreate(ability: UIAbility): void {
  }

  public onWindowStageCreate(ability: UIAbility, windowStage: window.WindowStage): void {
  }

  public onWindowStageActive(ability: UIAbility, windowStage: window.WindowStage): void {
  }

  public onWindowStageInactive(ability: UIAbility, windowStage: window.WindowStage): void {
  }

  public onWindowStageDestroy(ability: UIAbility, windowStage: window.WindowStage): void {
  }

  public onAbilityDestroy(ability: UIAbility): void {
  }

  public onAbilityForeground(ability: UIAbility): void {
  }

  public onAbilityBackground(ability: UIAbility): void {
  }

  public onAbilityContinue(ability: UIAbility): void {
  }
}

/**
 * 前后台监听器
 */
export class TKApplicationStateChangeListener {
  /**
   * 进入前台
   */
  public static readonly NOTE_TKAPPLICATION_FOREGROUND: string = "TKApplicationDidBecomeActiveNotification";
  /**
   * 进入后台
   */
  public static readonly NOTE_TKAPPLICATION_BACKGROUND: string = "TKApplicationDidEnterBackgroundNotification";
  /**
   * 进入暂停
   */
  public static readonly NOTE_TKAPPLICATION_PAUSED: string = "TKApplicationDidBecomePausedNotification";
  public static isApplicationBackground: boolean = false;

  public onApplicationForeground(): void {
    TKApplicationStateChangeListener.isApplicationBackground = false;
    TKNotificationCenter.defaultCenter.postNotificationName(TKApplicationStateChangeListener.NOTE_TKAPPLICATION_FOREGROUND);
  }

  public onApplicationBackground(): void {
    TKApplicationStateChangeListener.isApplicationBackground = true;
    TKNotificationCenter.defaultCenter.postNotificationName(TKApplicationStateChangeListener.NOTE_TKAPPLICATION_BACKGROUND);
  }

  public onApplicationPaused(): void {
    TKNotificationCenter.defaultCenter.postNotificationName(TKApplicationStateChangeListener.NOTE_TKAPPLICATION_PAUSED);
  }
}


export namespace TKContextHelper {

  let _context: Context | undefined = undefined;
  let _hspModuleName: string | undefined = undefined;

  let _applicationStateChangeListener: TKApplicationStateChangeListener = new TKApplicationStateChangeListener();

  /**
   * 设置上下文
   * @param context
   */
  export function setCurrentContext(context?: Context, hspModuleName?: string) {
    _context = context;
    _hspModuleName = hspModuleName;
  }

  /**
   * 获取当前上下文
   * @param component
   * @returns
   */
  export function getCurrentContext(component?: Object): Context {
    let context: Context | undefined = undefined;
    if (component) {
      context = getContext(component);
    }
    if (!context) {
      context = _context;
    }
    if (!context) {
      context = getContext();
    }
    return context;
  }

  /**
   * 获取模块的上下文
   * @param hspModuleName 动态库模块名称
   * @returns
   */
  export async function getCurHspModuleContext(hspModuleName?: string): Promise<Context> {
    hspModuleName = hspModuleName ?? _hspModuleName;
    let context: Context = getCurrentContext();
    if (TKStringHelper.isNotBlank(hspModuleName)) {
      context = await application.createModuleContext(context, hspModuleName);
    }
    return context;
  }

  /**
   * 获取模块的上下文
   * @param hspModuleName 动态库模块名称
   * @returns
   */
  export function getCurHspModuleContextSync(hspModuleName?: string): Context {
    hspModuleName = hspModuleName ?? _hspModuleName;
    let context: Context = getCurrentContext();
    if (TKStringHelper.isNotBlank(hspModuleName)) {
      context = context.createModuleContext(hspModuleName);
    }
    return context;
  }

  /**
   * 获取当前UI能力上下文
   * @param component
   * @returns
   */
  export function getCurrentUIAbilityContext(component?: Object): common.UIAbilityContext {
    return getCurrentContext(component) as common.UIAbilityContext;
  }

  /**
   * 获取当前UI上下文
   * @param component
   * @returns
   */
  export function getCurrentUIContext(component?: Object): UIContext {
    return getCurrentMainWindow(component).getUIContext();
  }

  /**
   * 获取当前主window
   * @param component
   * @returns
   */
  export function getCurrentMainWindow(component?: Object): window.Window {
    return getCurrentUIAbilityContext(component).windowStage.getMainWindowSync();
  }

  //Window状态事件监听器
  let windowStageEventHandler: Callback<window.WindowStageEventType> = (event: window.WindowStageEventType) => {
    switch (event) {
      //到前台
      case window.WindowStageEventType.SHOWN: {
        _applicationStateChangeListener.onApplicationForeground();
        break;
      }
    //到后台
      case window.WindowStageEventType.HIDDEN: {
        _applicationStateChangeListener.onApplicationBackground();
        break;
      }
    //到前台
      case window.WindowStageEventType.RESUMED: {
        _applicationStateChangeListener.onApplicationForeground();
        break;
      }
    //到任务台
      case window.WindowStageEventType.PAUSED: {
        _applicationStateChangeListener.onApplicationPaused();
        break;
      }
    }
  };

  export function bindApplicationLifecycle() {
    let onWindowStageEvent = () => {
      getCurrentUIAbilityContext().windowStage.off('windowStageEvent', windowStageEventHandler);
      getCurrentUIAbilityContext().windowStage.on('windowStageEvent', windowStageEventHandler);
    }
    if (getCurrentUIAbilityContext().windowStage) {
      onWindowStageEvent();
    } else {
      setTimeout(() => {
        if (getCurrentUIAbilityContext().windowStage) {
          onWindowStageEvent();
        }
      }, 1000);
    }
    // getCurrentContext().getApplicationContext().on('applicationStateChange', _applicationStateChangeListener);
  }

  export function unbindApplicationLifecycle() {
    getCurrentUIAbilityContext().windowStage.off('windowStageEvent', windowStageEventHandler);
    //getCurrentContext().getApplicationContext().off('applicationStateChange', _applicationStateChangeListener);
  }
}