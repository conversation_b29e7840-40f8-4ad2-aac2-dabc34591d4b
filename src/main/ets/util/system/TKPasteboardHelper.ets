import { pasteboard } from '@kit.BasicServicesKit';
import { TKLog } from '../../util/logger/TKLog';
import { TKStringHelper } from '../string/TKStringHelper';

/**
 * 剪贴板工具类
 */
export namespace TKPasteboardHelper {

  /**
   * 获取系统剪贴板对象
   * @returns
   */
  function getSystemPasteboard(): pasteboard.SystemPasteboard {
    let systemPasteboard: pasteboard.SystemPasteboard = pasteboard.getSystemPasteboard();
    return systemPasteboard;
  }

  /**
   * 将纯文本数据写入系统剪贴板
   * @param data
   * @returns
   */
  export function copyDataText(text: string) {
    try {
      let pasteData: pasteboard.PasteData = pasteboard.createData(pasteboard.MIMETYPE_TEXT_PLAIN, text);
      return getSystemPasteboard().setDataSync(pasteData);
    } catch (error) {
      TKLog.error(`[TKPasteboardHelper]将纯文本数据写入系统剪贴板异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 将Htm数据写入系统剪贴板
   * @param data
   * @returns
   */
  export function copyDataHtml(html: string) {
    try {
      let pasteData: pasteboard.PasteData = pasteboard.createData(pasteboard.MIMETYPE_TEXT_HTML, html);
      getSystemPasteboard().setDataSync(pasteData);
    } catch (error) {
      TKLog.error(`[TKPasteboardHelper]将Htm数据写入系统剪贴板异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 将Uri数据写入系统剪贴板
   * @param data
   * @returns
   */
  export function copyDataURI(uri: string) {
    try {
      let pasteData: pasteboard.PasteData = pasteboard.createData(pasteboard.MIMETYPE_TEXT_URI, uri);
      getSystemPasteboard().setDataSync(pasteData);
    } catch (error) {
      TKLog.error(`[TKPasteboardHelper]将Uri数据写入系统剪贴板异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 读取系统剪贴板内容
   * @returns
   */
  export function readData(): pasteboard.PasteData {
    return getSystemPasteboard().getDataSync()
  }

  /**
   * 读取系统剪贴板里的字符串
   * @returns
   */
  export function readDataContent(): string {
    let text = '';
    try {
      if (hasData()) {
        let data = readData();
        text = data.getPrimaryText();
        if (TKStringHelper.isEmpty(text)) {
          text = data.getPrimaryHtml();
        }
        if (TKStringHelper.isEmpty(text)) {
          text = data.getPrimaryUri();
        }
        if (TKStringHelper.isEmpty(text)) {
          text = '';
        }
      }
    } catch (error) {
      TKLog.error(`[TKPasteboardHelper]读取系统剪贴板里的字符串异常，code: ${error.code}， message: ${error.message}`);
    }
    return text;
  }

  /**
   * 清空系统剪贴板内容，使用Promise异步回调。
   * @returns
   */
  export function clearData() {
    try {
      getSystemPasteboard().clearDataSync();
    } catch (error) {
      TKLog.error(`[TKPasteboardHelper]清空系统剪贴板内容异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 判断系统剪贴板中是否有内容，使用Promise异步回调。
   * @returns
   */
  export function hasData(): boolean {
    try {
      return getSystemPasteboard().hasDataSync();
    } catch (error) {
      TKLog.error(`[TKPasteboardHelper]判断系统剪贴板中是否有内容异常，code: ${error.code}， message: ${error.message}`);
      return false;
    }
  }
}