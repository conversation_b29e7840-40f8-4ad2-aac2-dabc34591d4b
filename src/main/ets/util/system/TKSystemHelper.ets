/**
 * 系统环境的帮组类
 */
import { bundleManager } from '@kit.AbilityKit';
import { TKCacheManager } from '../cache/TKCacheManager';
import { TKFileHelper } from '../file/TKFileHelper';
import { TKXMLHelper } from '../file/xml/TKXMLHelper';
import { TKLog } from '../logger/TKLog';
import { TKMapHelper } from '../map/TKMapHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKContextHelper } from './TKContextHelper';
import { BusinessError } from '@ohos.base';

export namespace TKSystemHelper {

  /**
   *  系统配置
   */
  let _sysConfig: Map<string, string> | undefined = undefined;

  /**
   *  运行环境
   */
  let _environment: string = "";

  /**
   * app环境缓存key
   */
  const CACHE_APP_ENVIRONMENT: string = "TKEnvironment";

  /**
   *  app配置缓存Key
   */
  const CACHE_APP_CONFIG: string = "TKConfiguration";

  /**
   * C接入的配置
   */
  const CACHE_BUS_CONFIG: string = "TKBusConfig";

  /**
   * Java接入的配置
   */
  const CACHE_SERVER_CONFIG: string = "TKServerConfig";

  /**
   * 读取配置
   */
  function config(): Map<string, string> {
    if (!_sysConfig) {
      _sysConfig = TKCacheManager.shareInstance().getFileCacheData(CACHE_APP_CONFIG, CACHE_APP_CONFIG);
    }
    if (!_sysConfig || _sysConfig.size == 0) {
      _sysConfig = TKFileHelper.readSystemConfig("Configuration.xml");
      TKCacheManager.shareInstance().saveFileCacheData(CACHE_APP_CONFIG, _sysConfig, 0, true, CACHE_APP_CONFIG);
    }
    return _sysConfig as Map<string, string>;
  }

  /**
   *  读取系统配置
   *
   * @param key key
   *
   * @return 配置value
   */
  export function getConfig(key: string, defaultValue: string = ""): string {
    return TKMapHelper.getString(config(), key, defaultValue);
  }


  /**
   *  修改系统配置
   *
   * @param key      key
   * @param value    value
   *
   * @return 成功标志
   */
  export function setConfig(key: string, value: string) {
    config().set(key, value);
    if (_sysConfig) {
      TKCacheManager.shareInstance().saveFileCacheData(CACHE_APP_CONFIG, _sysConfig, 0, true, CACHE_APP_CONFIG);
    }
  }

  /**
   *  刷新重载系统配置
   */
  export function refreshConfig() {
    //重新复制配置文件
    _sysConfig = undefined;
    TKCacheManager.shareInstance().deleteFileCacheData(CACHE_APP_CONFIG, CACHE_APP_CONFIG);
    config();
  }

  /**
   *  重载配置文件
   *
   * @param configPath 配置文件名称
   */
  export function reloadConfig(configPath: string) {
    config();
    let files: Array<string> = TKStringHelper.split(configPath, "|");
    if (files && files.length > 0) {
      files.forEach(file => {
        TKMapHelper.merge(_sysConfig, TKFileHelper.readSystemConfig(file))
      })
    }
  }

  /**
   *  获得升级展示版本号
   *
   * @return 获取升级展示版本号
   */
  export function getVersion(): string {
    let version: string = getConfig("update.version");
    if (TKStringHelper.isEmpty(version)) {
      version = getAppVersion();
      //初始软件更新展示版本号
      setConfig("update.version", version);
    }
    return version;
  }

  /**
   *  获得内部升级版本序号
   *
   * @return 获取内部升级版本序号
   */
  export function getVersionCode(): string {
    let versionCode: string = getConfig("update.versionSn");
    if (TKStringHelper.isBlank(versionCode)) {
      versionCode = getAppVersionCode();
      //初始软件更新版本号
      setConfig("update.versionSn", versionCode);
    }
    return versionCode;
  }

  /**
   *  获取系统应用版本号
   *
   * @return 系统应用版本号
   */
  export function getAppVersion(): string {
    try {
      let bundleInfo =
        bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
      return bundleInfo.versionName;
    } catch (error) {
      TKLog.error(`[TKSystemHelper]获取系统应用版本号异常，code: ${error.code}， message: ${error.message}`);
      return "";
    }
  }

  /**
   * <AUTHOR> 2015-05-18 14:05:25
   *
   *  获取系统应用内部Build版本序号
   *
   * @return 系统应用版本号
   */
  export function getAppVersionCode(): string {
    try {
      let bundleInfo =
        bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
      return bundleInfo.versionCode + '';
    } catch (error) {
      TKLog.error(`[TKSystemHelper]获取系统应用Build版本序号异常，code: ${error.code}， message: ${error.message}`);
      return "";
    }
  }

  /**
   *  获取应用名称
   *
   * @return 应用名称
   */
  export function getAppName(): string {
    let label = "";
    try {
      let bundleInfo =
        bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
      label = bundleInfo.appInfo.label;
      if (TKStringHelper.startsWith(label, '$string:')) {
        label = label.replace('$string:', '')
        label = TKContextHelper.getCurrentContext().resourceManager.getStringByNameSync(label)
      }
    } catch (error) {
      TKLog.error(`[TKSystemHelper]获取应用名称异常，code: ${error.code}， message: ${error.message}`);
    }
    return label;
  }

  /**
   *  获取应用展示名称
   *
   * @return 应用展示名称
   */
  export function getAppDisplayName(): string {
    let label = "";
    try {
      label = TKContextHelper.getCurrentUIAbilityContext().abilityInfo.label;
      if (TKStringHelper.startsWith(label, '$string:')) {
        label = label.replace('$string:', '')
        label = TKContextHelper.getCurrentContext().resourceManager.getStringByNameSync(label)
      }
    } catch (error) {
      TKLog.error(`[TKSystemHelper]获取应用展示名称异常，code: ${error.code}， message: ${error.message}`);
    }
    return label;
  }

  /**
   * <AUTHOR> 2014-12-23 16:12:06
   *
   *  获取应用唯一值
   *
   * @return 应用唯一值
   */
  export function getAppIdentifier(): string {
    try {
      let bundleInfo =
        bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
      return bundleInfo.name;
    } catch (error) {
      TKLog.error(`[TKSystemHelper]获取应用唯一标示异常，code: ${error.code}， message: ${error.message}`);
      return "";
    }
  }

  /**
   *  获取系统环境
   *
   * @return
   */
  export function getEnvironment(): string {
    if (TKStringHelper.isBlank(_environment)) {
      _environment = TKCacheManager.shareInstance().getFileCacheData(CACHE_APP_ENVIRONMENT) as string;
    }
    if (TKStringHelper.isBlank(_environment)) {
      let environmentConfig: Map<string, Object> = TKXMLHelper.readConfigXml(`thinkive/config/Environment.xml`);
      if (environmentConfig && environmentConfig.size > 0) {
        let environmentValue: string = TKMapHelper.getString(environmentConfig, "value");
        if (TKStringHelper.isNotBlank(environmentValue)) {
          _environment = environmentValue;
          TKCacheManager.shareInstance().saveFileCacheData(CACHE_APP_ENVIRONMENT, _environment);
        }
      }
    }
    if (TKStringHelper.isBlank(_environment)) {
      _environment = "prod";
    }
    return _environment;
  }


  /**
   *
   *  设置运行环境
   *
   * @param environment 运行环境
   *
   * @return
   */
  export function setEnvironment(environment: string) {
    if (TKStringHelper.isBlank(environment)) {
      environment = "prod";
    }
    if (_environment != environment) {
      _environment = environment;
      TKCacheManager.shareInstance().saveFileCacheData(CACHE_APP_ENVIRONMENT, _environment);
      TKCacheManager.shareInstance().deleteFileCacheData(CACHE_APP_CONFIG, CACHE_APP_CONFIG);
      TKCacheManager.shareInstance().deleteFileCacheData(CACHE_BUS_CONFIG, CACHE_BUS_CONFIG);
      TKCacheManager.shareInstance().deleteFileCacheData(CACHE_SERVER_CONFIG, CACHE_SERVER_CONFIG);
    }
  }

  /**
   * 打开指定软件
   * @param url
   * @param completion
   */
  export function openAppURL(url: string, completion?: (success: boolean) => void) {
    if (TKStringHelper.isNotBlank(url)) {
      let canOpen = bundleManager.canOpenLink(url);
      if (canOpen) {
        try {
          TKContextHelper.getCurrentUIAbilityContext().openLink(url, { appLinkingOnly: false }).then(() => {
            TKLog.info(`[TKSystemHelper]打开三方应用成功==>${url}`);
            if (completion) {
              completion(true);
            }
          }).catch((err: BusinessError) => {
            TKLog.info(`[TKSystemHelper]打开三方应用失败:${err.code},${err.message}==>${url}`);
            if (completion) {
              completion(false);
            }
          });
        } catch (e) {
          TKLog.info(`[TKSystemHelper]打开三方应用失败:${e.code},${e.message}==>${url}`);
          if (completion) {
            completion(false);
          }
        }
      } else {
        TKLog.info(`[TKSystemHelper]打开三方应用失败，未安装该应用==>${url}`);
        if (completion) {
          completion(false);
        }
      }
    } else {
      TKLog.info(`[TKSystemHelper]打开三方应用失败，Url不能为空==>${url}`);
      if (completion) {
        completion(false);
      }
    }
  }

  /**
   *  是否安装指定的软件
   * @param url
   */
  export function isInstallAppWithURL(url: string): boolean {
    if (TKStringHelper.isNotBlank(url)) {
      return bundleManager.canOpenLink(url);
    }
    return false;
  }
}