import abilityAccessCtrl from '@ohos.abilityAccessCtrl';
import { bundleManager, common, Want } from '@kit.AbilityKit';
import { TKLog } from '../logger/TKLog';
import { TKContextHelper } from './TKContextHelper';

/**
 * 权限枚举
 */
export enum TKPermission {
  APPROXIMATELY_LOCATION = "ohos.permission.APPROXIMATELY_LOCATION", // 模糊地理位置信息
  LOCATION = "ohos.permission.LOCATION", // 申请精确地理位置信息，需要先申请模糊位置权限
  MICROPHONE = "ohos.permission.MICROPHONE", // 麦克风
  CAMERA = "ohos.permission.CAMERA", // 相机
}

/**
 * 权限默认提示，跳转到设置页面
 */
export enum TKNoPermissionTip {
  LOCATION_TIP = "未开启应用定位权限，请进入'设置'-'应用和元服务'-'app'-中开启", // 申请精确地理位置信息，需要先申请模糊位置权限
  MICROPHONE_TIP = "未开启应用麦克风权限，请进入'设置'-'应用和元服务'-'app'-中开启", // 麦克风
  CAMERA_TIP = "未开启应用相机权限，请进入'设置'-'应用和元服务'-'app'-中开启", // 相机
}

/**
 * 授权结果
 */
export class TKPermissionsRequestResult {
  /**
   * 申请的权限列表
   */
  public permissions: Array<TKPermission> = [];
  /**
   * 已经授权的权限列表
   */
  public authorized: Array<TKPermission> = [];
  /**
   * 未授权的权限列表
   */
  public unauthorized: Array<TKPermission> = [];
  /**
   * 弹框授权的权限列表
   */
  public dialogShown: Array<TKPermission> = [];
  /**
   * 所有授权的状态结果列表
   */
  public authResults: Array<number> = [];
  /**
   * 弹框授权的权限列表
   */
  public dialogShownResults: Array<boolean> = [];

  public get isGrant(): boolean {
    return this.authorized.length == this.permissions.length && this.authorized.length > 0 &&
      this.permissions.length > 0;
  }
}

/**
 * 权限申请帮组类
 */
export namespace TKPermissionHelper {

  //应用设置页面
  const URI_INFO: string = 'application_info_entry'
  //移动网络设置页面
  const URI_NETWORK: string = "mobile_network_entry";
  //通知设置页面
  const URI_NOTIFICATION: string = "systemui_notification_settings";
  //蓝牙设置页面
  const URI_BLUETOOTH: string = "bluetooth_entry";
  //NFC设置页面
  const URI_NFC: string = "nfc_settings";
  let permissionFilter: ((permission: TKPermission, resolve: Function, reject?: Function) => void) | undefined =
    undefined;

  /**
   * 注册权限申请拦截函数
   * @param filter
   */
  export function registerPermissionFilter(filter: (permission: TKPermission, resolve: Function,
    reject?: Function) => void) {
    permissionFilter = filter;
  }

  /**
   * 权限检测是否授权
   */
  export function checkPermission(permission: TKPermission): abilityAccessCtrl.GrantStatus {
    let grantStatus: abilityAccessCtrl.GrantStatus = abilityAccessCtrl.GrantStatus.PERMISSION_DENIED;
    try {
      let bundleInfo: bundleManager.BundleInfo =
        bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION);
      // 获取应用程序的accessTokenID
      let tokenId: number = bundleInfo.appInfo.accessTokenId;
      // 校验应用是否被授予权限
      grantStatus = abilityAccessCtrl.createAtManager().checkAccessTokenSync(tokenId, permission);
    } catch (error) {
      TKLog.error(`[TKPermissionHelper]权限检测是否授权异常，code: ${error.code}， message: ${error.message}`);
    }
    return grantStatus;
  }

  /**
   * 批量权限检测是否授权
   */
  export function checkPermissions(permissions: Array<TKPermission>): Array<abilityAccessCtrl.GrantStatus> {
    let grantStatusArray: Array<abilityAccessCtrl.GrantStatus> = new Array<abilityAccessCtrl.GrantStatus>();
    if (permissions && permissions.length > 0) {
      for (let permission of permissions) {
        let grantStatus: abilityAccessCtrl.GrantStatus = checkPermission(permission);
        grantStatusArray.push(grantStatus);
      }
    }
    return grantStatusArray;
  }

  /**
   * 权限动态申请，并返回申请结果
   * @param permissions
   * @param context
   * @returns
   */
  export async function requestPermissionsFromUser(permissions: Array<TKPermission>,
    context?: common.UIAbilityContext): Promise<TKPermissionsRequestResult> {
    let permissionsRequestResult: TKPermissionsRequestResult = new TKPermissionsRequestResult();
    try {
      let abilityContext: common.UIAbilityContext =
        context ?? TKContextHelper.getCurrentContext() as common.UIAbilityContext;
      let authorized: Array<TKPermission> = new Array<TKPermission>();
      let unauthorized: Array<TKPermission> = new Array<TKPermission>();
      let dialogShown: Array<TKPermission> = new Array<TKPermission>();
      let authResults: Array<number> = checkPermissions(permissions);
      let dialogShownResults: Array<boolean> = new Array<boolean>();
      for (let i = 0; i < authResults.length; i++) {
        let permission = permissions[i];
        let authResult = authResults[i];
        if (authResult == abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
          authorized.push(permission);
        } else {
          unauthorized.push(permission);
        }
        dialogShownResults.push(false);
      }
      if (authorized.length < permissions.length) {
        for (let index = 0; index < unauthorized.length; index++) {
          const unauthorizedPermission = unauthorized[index];
          await executePermissionFilter(unauthorizedPermission);
          let requestResult = await abilityAccessCtrl.createAtManager()
            .requestPermissionsFromUser(abilityContext, [unauthorizedPermission]);
          for (let i = 0; i < requestResult.authResults.length; i++) {
            let permission: TKPermission = requestResult.permissions[i] as TKPermission;
            let authResult: number = requestResult.authResults[i];
            let dialogShownResult: boolean | undefined = requestResult.dialogShownResults?.[i];
            let index: number = permissions.indexOf(permission);
            authResults[index] = authResult;
            if (dialogShownResult) {
              dialogShownResults[index] = dialogShownResult;
              dialogShown.push(permission);
            }
          }
        }
        authorized.splice(0, authorized.length);
        unauthorized.splice(0, unauthorized.length);
        //保证授权顺序
        for (let i = 0; i < authResults.length; i++) {
          let permission = permissions[i];
          let authResult = authResults[i];
          if (authResult == abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
            authorized.push(permission);
          } else {
            unauthorized.push(permission);
          }
        }
      }
      permissionsRequestResult.permissions = permissions;
      permissionsRequestResult.authResults = authResults;
      permissionsRequestResult.dialogShownResults = dialogShownResults;
      permissionsRequestResult.authorized = authorized;
      permissionsRequestResult.unauthorized = unauthorized;
      permissionsRequestResult.dialogShown = dialogShown;
      // 授权成功
      return permissionsRequestResult;
    } catch (error) {
      TKLog.error(`[TKPermissionHelper]权限动态申请异常，code: ${error.code}， message: ${error.message}`);
      return permissionsRequestResult;
    }
  }

  /**
   * 权限动态申请，并返回申请结果
   * @param permission
   * @param context
   * @returns
   */
  export async function requestPermissionFromUser(permission: TKPermission,
    context?: common.UIAbilityContext): Promise<abilityAccessCtrl.GrantStatus> {
    let permissionsRequestResult: TKPermissionsRequestResult = await requestPermissionsFromUser([permission], context);
    return permissionsRequestResult.isGrant ? abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED :
    abilityAccessCtrl.GrantStatus.PERMISSION_DENIED;
  }

  /**
   *  执行权限申请拦截
   * @returns
   */
  function executePermissionFilter(permission: TKPermission): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (permissionFilter) {
        permissionFilter(permission, resolve, reject);
      } else {
        resolve();
      }
    });
  }

  /**
   * 跳转应用设置页面（调用此方法到设置，手动开启权限）
   */
  export function toAppSetting(uri: string = URI_INFO): Promise<void> {
    let want: Want = {
      bundleName: 'com.huawei.hmos.settings', //设置应用bundleName
      abilityName: 'com.huawei.hmos.settings.MainAbility', //设置应用abilityName
      uri: uri, //目标页面
      parameters: {
        bundleName: TKContextHelper.getCurrentUIAbilityContext().abilityInfo.bundleName, //拉起方应用包名
        pushParams: TKContextHelper.getCurrentUIAbilityContext().abilityInfo.bundleName, //拉起方应用包名
      }
    }
    return TKContextHelper.getCurrentUIAbilityContext().startAbility(want);
  }

  /**
   * 跳转移动网络设置页面
   */
  export function toNetworkSetting(): Promise<void> {
    return toAppSetting(URI_NETWORK);
  }

  /**
   * 跳转通知设置页面
   */
  export function toNotificationSetting(): Promise<void> {
    return toAppSetting(URI_NOTIFICATION);
  }

  /**
   * 跳转蓝牙设置页面
   */
  export function toBluetoothSetting(): Promise<void> {
    return toAppSetting(URI_BLUETOOTH);
  }

  /**
   * 跳转NFC设置页面
   */
  export function toNfcSetting(): Promise<void> {
    return toAppSetting(URI_NFC);
  }
}

// 鸿蒙权限列表字符串
// 'ohos.permission.ANSWER_CALL'
// 'ohos.permission.USE_BLUETOOTH'
// 'ohos.permission.DISCOVER_BLUETOOTH'
// 'ohos.permission.MANAGE_BLUETOOTH'
// 'ohos.permission.ACCESS_BLUETOOTH'
// 'ohos.permission.INTERNET'
// 'ohos.permission.MODIFY_AUDIO_SETTINGS'
// 'ohos.permission.ACCESS_NOTIFICATION_POLICY'
// 'ohos.permission.READ_CALENDAR'
// 'ohos.permission.READ_CALL_LOG'
// 'ohos.permission.READ_CELL_MESSAGES'
// 'ohos.permission.READ_CONTACTS'
// 'ohos.permission.GET_TELEPHONY_STATE'
// 'ohos.permission.GET_PHONE_NUMBERS'
// 'ohos.permission.READ_MESSAGES'
// 'ohos.permission.RECEIVE_MMS'
// 'ohos.permission.RECEIVE_SMS'
// 'ohos.permission.RECEIVE_WAP_MESSAGES'
// 'ohos.permission.MICROPHONE'
// 'ohos.permission.SEND_MESSAGES'
// 'ohos.permission.WRITE_CALENDAR'
// 'ohos.permission.WRITE_CALL_LOG'
// 'ohos.permission.WRITE_CONTACTS'
// 'ohos.permission.DISTRIBUTED_DATASYNC'
// 'ohos.permission.DISTRIBUTED_SOFTBUS_CENTER'
// 'ohos.permission.MANAGE_VOICEMAIL'
// 'ohos.permission.REQUIRE_FORM'
// 'ohos.permission.LOCATION_IN_BACKGROUND'
// 'ohos.permission.LOCATION'
// 'ohos.permission.APPROXIMATELY_LOCATION'
// 'ohos.permission.MEDIA_LOCATION'
// 'ohos.permission.GET_NETWORK_INFO'
// 'ohos.permission.PLACE_CALL'
// 'ohos.permission.CAMERA'
// 'ohos.permission.SET_NETWORK_INFO'
// 'ohos.permission.REMOVE_CACHE_FILES'
// 'ohos.permission.READ_MEDIA'
// 'ohos.permission.REBOOT'
// 'ohos.permission.RUNNING_LOCK'
// 'ohos.permission.WRITE_MEDIA'
// 'ohos.permission.SET_TIME'
// 'ohos.permission.SET_TIME_ZONE'
// 'ohos.permission.DOWNLOAD_SESSION_MANAGER'
// 'ohos.permission.COMMONEVENT_STICKY'
// 'ohos.permission.SYSTEM_FLOAT_WINDOW'
// 'ohos.permission.PRIVACY_WINDOW'
// 'ohos.permission.POWER_MANAGER'
// 'ohos.permission.REFRESH_USER_ACTION'
// 'ohos.permission.POWER_OPTIMIZATION'
// 'ohos.permission.REBOOT_RECOVERY'
// 'ohos.permission.MANAGE_LOCAL_ACCOUNTS'
// 'ohos.permission.INTERACT_ACROSS_LOCAL_ACCOUNTS'
// 'ohos.permission.VIBRATE'
// 'ohos.permission.ACTIVITY_MOTION'
// 'ohos.permission.READ_HEALTH_DATA'
// 'ohos.permission.CONNECT_IME_ABILITY'
// 'ohos.permission.CONNECT_SCREEN_SAVER_ABILITY'
// 'ohos.permission.READ_SCREEN_SAVER'
// 'ohos.permission.WRITE_SCREEN_SAVER'
// 'ohos.permission.SET_WALLPAPER'
// 'ohos.permission.GET_WALLPAPER'
// 'ohos.permission.CHANGE_ABILITY_ENABLED_STATE'
// 'ohos.permission.ACCESS_MISSIONS'
// 'ohos.permission.CLEAN_BACKGROUND_PROCESSES'
// 'ohos.permission.KEEP_BACKGROUND_RUNNING'
// 'ohos.permission.UPDATE_CONFIGURATION'
// 'ohos.permission.UPDATE_SYSTEM'
// 'ohos.permission.FACTORY_RESET'
// 'ohos.permission.UPDATE_MIGRATE'
// 'ohos.permission.GRANT_SENSITIVE_PERMISSIONS'
// 'ohos.permission.REVOKE_SENSITIVE_PERMISSIONS'
// 'ohos.permission.GET_SENSITIVE_PERMISSIONS'
// 'ohos.permission.INTERACT_ACROSS_LOCAL_ACCOUNTS_EXTENSION'
// 'ohos.permission.LISTEN_BUNDLE_CHANGE'
// 'ohos.permission.GET_BUNDLE_INFO'
// 'ohos.permission.ACCELEROMETER'
// 'ohos.permission.GYROSCOPE'
// 'ohos.permission.GET_BUNDLE_INFO_PRIVILEGED'
// 'ohos.permission.INSTALL_BUNDLE'
// 'ohos.permission.MANAGE_SHORTCUTS'
// 'ohos.permission.radio.ACCESS_FM_AM'
// 'ohos.permission.SET_TELEPHONY_STATE'
// 'ohos.permission.START_ABILIIES_FROM_BACKGROUND'
// 'ohos.permission.START_ABILITIES_FROM_BACKGROUND'
// 'ohos.permission.BUNDLE_ACTIVE_INFO'
// 'ohos.permission.START_INVISIBLE_ABILITY'
// 'ohos.permission.sec.ACCESS_UDID'
// 'ohos.permission.LAUNCH_DATA_PRIVACY_CENTER'
// 'ohos.permission.MANAGE_MEDIA_RESOURCES'
// 'ohos.permission.PUBLISH_AGENT_REMINDER'
// 'ohos.permission.CONTROL_TASK_SYNC_ANIMATOR'
// 'ohos.permission.INPUT_MONITORING'
// 'ohos.permission.MANAGE_MISSIONS'
// 'ohos.permission.NOTIFICATION_CONTROLLER'
// 'ohos.permission.CONNECTIVITY_INTERNAL'
// 'ohos.permission.MANAGE_NET_STRATEGY'
// 'ohos.permission.GET_NETWORK_STATS'
// 'ohos.permission.MANAGE_VPN'
// 'ohos.permission.SET_ABILITY_CONTROLLER'
// 'ohos.permission.USE_USER_IDM'
// 'ohos.permission.MANAGE_USER_IDM'
// 'ohos.permission.NETSYS_INTERNAL'
// 'ohos.permission.ACCESS_BIOMETRIC'
// 'ohos.permission.ACCESS_USER_AUTH_INTERNAL'
// 'ohos.permission.ACCESS_PIN_AUTH'
// 'ohos.permission.ACCESS_AUTH_RESPOOL'
// 'ohos.permission.ENFORCE_USER_IDM'
// 'ohos.permission.GET_RUNNING_INFO'
// 'ohos.permission.CLEAN_APPLICATION_DATA'
// 'ohos.permission.RUNNING_STATE_OBSERVER'
// 'ohos.permission.CAPTURE_SCREEN'
// 'ohos.permission.GET_WIFI_INFO'
// 'ohos.permission.GET_WIFI_INFO_INTERNAL'
// 'ohos.permission.SET_WIFI_INFO'
// 'ohos.permission.GET_WIFI_PEERS_MAC'
// 'ohos.permission.GET_WIFI_LOCAL_MAC'
// 'ohos.permission.GET_WIFI_CONFIG'
// 'ohos.permission.SET_WIFI_CONFIG'
// 'ohos.permission.MANAGE_WIFI_CONNECTION'
// 'ohos.permission.DUMP'
// 'ohos.permission.MANAGE_WIFI_HOTSPOT'
// 'ohos.permission.GET_ALL_APP_ACCOUNTS'
// 'ohos.permission.MANAGE_SECURE_SETTINGS'
// 'ohos.permission.READ_DFX_SYSEVENT'
// 'ohos.permission.READ_HIVIEW_SYSTEM'
// 'ohos.permission.WRITE_HIVIEW_SYSTEM'
// 'ohos.permission.MANAGE_ENTERPRISE_DEVICE_ADMIN'
// 'ohos.permission.SET_ENTERPRISE_INFO'
// 'ohos.permission.ACCESS_BUNDLE_DIR'
// 'ohos.permission.ENTERPRISE_SUBSCRIBE_MANAGED_EVENT'
// 'ohos.permission.ENTERPRISE_SET_DATETIME'
// 'ohos.permission.ENTERPRISE_GET_DEVICE_INFO'
// 'ohos.permission.ENTERPRISE_RESET_DEVICE'
// 'ohos.permission.ENTERPRISE_SET_WIFI'
// 'ohos.permission.ENTERPRISE_GET_NETWORK_INFO'
// 'ohos.permission.ENTERPRISE_SET_ACCOUNT_POLICY'
// 'ohos.permission.ENTERPRISE_SET_BUNDLE_INSTALL_POLICY'
// 'ohos.permission.ENTERPRISE_SET_NETWORK'
// 'ohos.permission.ENTERPRISE_MANAGE_SET_APP_RUNNING_POLICY'
// 'ohos.permission.ENTERPRISE_SET_SCREENOFF_TIME'
// 'ohos.permission.ENTERPRISE_GET_SETTINGS'
// 'ohos.permission.ENTERPRISE_INSTALL_BUNDLE'
// 'ohos.permission.ENTERPRISE_MANAGE_CERTIFICATE'
// 'ohos.permission.ENTERPRISE_RESTRICT_POLICY'
// 'ohos.permission.ENTERPRISE_MANAGE_USB'
// 'ohos.permission.ENTERPRISE_MANAGE_NETWORK'
// 'ohos.permission.ENTERPRISE_SET_BROWSER_POLICY'
// 'ohos.permission.NFC_TAG'
// 'ohos.permission.NFC_CARD_EMULATION'
// 'ohos.permission.PERMISSION_USED_STATS'
// 'ohos.permission.NOTIFICATION_AGENT_CONTROLLER'
// 'ohos.permission.MOUNT_UNMOUNT_MANAGER'
// 'ohos.permission.MOUNT_FORMAT_MANAGER'
// 'ohos.permission.STORAGE_MANAGER'
// 'ohos.permission.BACKUP'
// 'ohos.permission.CLOUDFILE_SYNC_MANAGER'
// 'ohos.permission.CLOUDFILE_SYNC'
// 'ohos.permission.FILE_ACCESS_MANAGER'
// 'ohos.permission.GET_DEFAULT_APPLICATION'
// 'ohos.permission.SET_DEFAULT_APPLICATION'
// 'ohos.permission.ACCESS_IDS'
// 'ohos.permission.MANAGE_DISPOSED_APP_STATUS'
// 'ohos.permission.ACCESS_DLP_FILE'
// 'ohos.permission.PROVISIONING_MESSAGE'
// 'ohos.permission.ACCESS_SYSTEM_SETTINGS'
// 'ohos.permission.READ_IMAGEVIDEO'
// 'ohos.permission.READ_AUDIO'
// 'ohos.permission.READ_DOCUMENT'
// 'ohos.permission.WRITE_IMAGEVIDEO'
// 'ohos.permission.WRITE_AUDIO'
// 'ohos.permission.WRITE_DOCUMENT'
// 'ohos.permission.ABILITY_BACKGROUND_COMMUNICATION'
// 'ohos.permission.securityguard.REPORT_SECURITY_INFO'
// 'ohos.permission.securityguard.REQUEST_SECURITY_MODEL_RESULT'
// 'ohos.permission.securityguard.REQUEST_SECURITY_EVENT_INFO'
// 'ohos.permission.ACCESS_CERT_MANAGER_INTERNAL'
// 'ohos.permission.ACCESS_CERT_MANAGER'
// 'ohos.permission.GET_LOCAL_ACCOUNTS'
// 'ohos.permission.MANAGE_DISTRIBUTED_ACCOUNTS'
// 'ohos.permission.GET_DISTRIBUTED_ACCOUNTS'
// 'ohos.permission.READ_ACCESSIBILITY_CONFIG'
// 'ohos.permission.WRITE_ACCESSIBILITY_CONFIG'
// 'ohos.permission.ACCESS_PUSH_SERVICE'
// 'ohos.permission.READ_APP_PUSH_DATA'
// 'ohos.permission.WRITE_APP_PUSH_DATA'
// 'ohos.permission.MANAGE_AUDIO_CONFIG'
// 'ohos.permission.MANAGE_CAMERA_CONFIG'
// 'ohos.permission.RECEIVER_STARTUP_COMPLETED'
// 'ohos.permission.READ_WHOLE_CALENDAR'
// 'ohos.permission.WRITE_WHOLE_CALENDAR'
// 'ohos.permission.ACCESS_SERVICE_DM'
// 'ohos.permission.RUN_ANY_CODE'
// 'ohos.permission.APP_TRACKING_CONSENT'
// 'ohos.permission.PUBLISH_SYSTEM_COMMON_EVENT'
// 'ohos.permission.ACCESS_SCREEN_LOCK_INNER'
// 'ohos.permission.PRINT'
// 'ohos.permission.MANAGE_PRINT_JOB'
// 'ohos.permission.CHANGE_OVERLAY_ENABLED_STATE'
// 'ohos.permission.CONNECT_CELLULAR_CALL_SERVICE'
// 'ohos.permission.CONNECT_IMS_SERVICE'
// 'ohos.permission.ACCESS_SENSING_WITH_ULTRASOUND'
// 'ohos.permission.PROXY_AUTHORIZATION_URI'
// 'ohos.permission.INSTALL_ENTERPRISE_BUNDLE'
// 'ohos.permission.GET_INSTALLED_BUNDLE_LIST'
// 'ohos.permission.ACCESS_CAST_ENGINE_MIRROR'
// 'ohos.permission.ACCESS_CAST_ENGINE_STREAM'
// 'ohos.permission.CLOUDDATA_CONFIG'
// 'ohos.permission.DEVICE_STANDBY_EXEMPTION'
// 'ohos.permission.RESTRICT_APPLICATION_ACTIVE'
// 'ohos.permission.MANAGE_SENSOR'
// 'ohos.permission.UPLOAD_SESSION_MANAGER'
// 'ohos.permission.PREPARE_APP_TERMINATE'
// 'ohos.permission.MANAGE_ECOLOGICAL_RULE'
// 'ohos.permission.GET_SCENE_CODE'
// 'ohos.permission.FILE_GUARD_MANAGER'
// 'ohos.permission.SET_FILE_GUARD_POLICY'
// 'ohos.permission.securityguard.SET_MODEL_STATE'
// 'ohos.permission.hsdr.HSDR_ACCESS'
// 'ohos.permission.SUPPORT_USER_AUTH'
// 'ohos.permission.CAPTURE_VOICE_DOWNLINK_AUDIO'
// 'ohos.permission.MANAGE_INTELLIGENT_VOICE'
// 'ohos.permission.INSTALL_ENTERPRISE_MDM_BUNDLE'
// 'ohos.permission.INSTALL_ENTERPRISE_NORMAL_BUNDLE'
// 'ohos.permission.INSTALL_SELF_BUNDLE'
// 'ohos.permission.OBSERVE_FORM_RUNNING'
// 'ohos.permission.MANAGE_DEVICE_AUTH_CRED'
// 'ohos.permission.UNINSTALL_BUNDLE'
// 'ohos.permission.RECOVER_BUNDLE'
// 'ohos.permission.GET_DOMAIN_ACCOUNTS'