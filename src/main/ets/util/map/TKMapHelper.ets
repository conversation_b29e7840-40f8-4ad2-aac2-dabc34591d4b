/**
 * Map操作帮组类
 */
import { TKObjectHelper } from '../data/TKObjectHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { HashMap, TreeMap } from '@kit.ArkTS';

export namespace TKMapHelper {

  /**
   * 获取对象类型数据
   * @param map
   * @param key
   * @returns
   */
  export function getObject<T>(map: Map<string, Object | undefined> | Record<string, Object | undefined>, key: string,
    defaultValue?: T): T {
    let result: Object | undefined = undefined;
    if (map && key) {
      result = map instanceof Map ? map.get(key) : map[key];
    }
    if (result !== undefined && result !== null) {
      return result as T;
    } else {
      return defaultValue as T;
    }
  }

  /**
   * 获取JSON类型数据
   * @param map
   * @param key
   * @param defaultValue
   * @returns
   */
  export function getJSON(map: Map<string, Object | undefined> | Record<string, Object | undefined>, key: string,
    defaultValue: Record<string, Object> = {}): Record<string, Object> {
    let result: Object | undefined = getObject(map, key);
    if (result !== undefined && result !== null) {
      if (typeof result === "string") {
        return JSON.parse(result);
      } else {
        return result as Record<string, Object>;
      }
    } else {
      return defaultValue;
    }
  }

  /**
   * 获取字符串类型数据
   * @param map
   * @param key
   * @param defaultValue
   * @returns
   */
  export function getString(map: Map<string, Object | undefined> | Record<string, Object | undefined>, key: string,
    defaultValue: string = ""): string {
    let result: Object | undefined = getObject(map, key);
    if (result !== undefined && result !== null && TKStringHelper.isNotEmpty(result.toString())) {
      return result.toString();
    } else {
      return defaultValue;
    }
  }

  /**
   * 获取数字类型数据
   * @param map
   * @param key
   * @param defaultValue
   * @returns
   */
  export function getNumber(map: Map<string, Object | undefined> | Record<string, Object | undefined>, key: string,
    defaultValue: number = 0): number {
    let result: Object | undefined = getObject(map, key);
    if (result !== undefined && result !== null) {
      return Number(result.toString());
    } else {
      return defaultValue;
    }
  }

  /**
   * 获取Boolean类型数据
   * @param map
   * @param key
   * @param defaultValue
   * @returns
   */
  export function getBoolean(map: Map<string, Object | undefined> | Record<string, Object | undefined>, key: string,
    defaultValue?: boolean): boolean {
    let result: Object | undefined = getObject(map, key);
    if (result !== undefined && result !== null) {
      result = result.toString().toLowerCase();
      return (result != "0" && result != "" && result != "false");
    } else {
      return defaultValue as boolean;
    }
  }

  /**
   * 保存对象类型数据
   * @param map
   * @param key
   * @param value
   */
  export function setObject<T>(map: T, key: string, value: Object | undefined) {
    if (map instanceof Map) {
      map.set(key, value);
    } else {
      (map as Record<string, Object | undefined>)[key] = value;
    }
  }

  /**
   * 删除对象类型数据
   * @param map
   * @param key
   */
  export function deleteObject<T>(map: T, key: string) {
    if (map instanceof Map) {
      map.delete(key);
    } else {
      (map as Record<string, Object | undefined>)[key] = undefined;
    }
  }

  /**
   *  Map对象合并，相当于putAll的方法
   * @param target  目标对象
   * @param source  来源对象
   * @param isDeep  是否深度
   */
  export function merge<T, S>(target: T, source: S, isDeep: boolean = false): T {
    if (source instanceof Map) {
      source.forEach((value: Object, key: string) => {
        const sourceKey: string = key;
        const sourceValue: Object = TKObjectHelper.clone(value);
        const targetValue: Object | undefined =
          getObject(target as Map<string, Object> | Record<string, Object>, sourceKey);
        if (isDeep) {
          if (targetValue === undefined || sourceValue === undefined) {
            setObject(target, sourceKey, sourceValue ?? targetValue);
          } else if (Array.isArray(sourceValue)) {
            setObject(target, sourceKey, sourceValue ?? targetValue);
          } else if (sourceValue instanceof Map && targetValue instanceof Map) {
            setObject(target, sourceKey, merge(targetValue, sourceValue, isDeep));
          } else {
            setObject(target, sourceKey, sourceValue ?? targetValue);
          }
        } else {
          setObject(target, sourceKey, sourceValue ?? targetValue);
        }
      });
    } else {
      Object.entries(source as Record<string, Object>).forEach((e, i) => {
        const sourceKey: string = e[0];
        const sourceValue: Object = TKObjectHelper.clone(e[1]);
        const targetValue: Object | undefined =
          getObject(target as Map<string, Object> | Record<string, Object>, sourceKey);
        if (isDeep) {
          if (targetValue === undefined || sourceValue === undefined) {
            setObject(target, sourceKey, sourceValue ?? targetValue);
          } else if (Array.isArray(sourceValue)) {
            setObject(target, sourceKey, sourceValue ?? targetValue);
          } else if (sourceValue instanceof Map && targetValue instanceof Map) {
            setObject(target, sourceKey, merge(targetValue, sourceValue, isDeep));
          } else {
            setObject(target, sourceKey, sourceValue ?? targetValue);
          }
        } else {
          setObject(target, sourceKey, sourceValue ?? targetValue);
        }
      });
    }
    return target;
  }

  /**
   * 是否包含Key
   * @param map
   * @param key
   * @returns
   */
  export function containKey<T>(map: T, key: string): boolean {
    let result: boolean = false;
    if ((map instanceof Map) || (map instanceof HashMap) || (map instanceof TreeMap)) {
      map.forEach((v: Object, k: string) => {
        if (key == k) {
          result = true;
        }
      });
    } else {
      result = Object.keys(map as Object).includes(key);
    }
    return result;
  }

  /**
   * 所有的keys
   * @param map
   * @returns
   */
  export function keys<T>(map: T): Array<string> {
    if ((map instanceof Map) || (map instanceof HashMap) || (map instanceof TreeMap)) {
      return Array.from(map.keys());
    } else {
      return Object.keys(map as Object);
    }
  }

  /**
   * 所有的values
   * @param map
   * @returns
   */
  export function values<T>(map: T): Array<Object> {
    if ((map instanceof Map) || (map instanceof HashMap) || (map instanceof TreeMap)) {
      return Array.from(map.values());
    } else {
      return Object.values(map as Object);
    }
  }
}