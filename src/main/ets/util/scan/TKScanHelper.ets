import { detectBarcode, generateBarcode, scanBarcode, scanCore } from '@kit.ScanKit';
import { image } from '@kit.ImageKit';
import { TKLog } from '../logger/TKLog';
import { TKContextHelper } from '../system/TKContextHelper';
import { TKPhotoSelectOptions, TKPickerHelper } from '../ui/TKPickerHelper';
import { photoAccessHelper } from '@kit.MediaLibraryKit';

export namespace TKScanHelper {

  /**
   * 调用默认界面扫码，使用Promise方式异步返回解码结果。
   * @param options
   *   scanTypes 设置扫码类型，默认扫码ALL（全部码类型）。
   *   enableMultiMode 是否开启多码识别，默认false。true：多码识别、false：单码识别。
   *   enableAlbum 是否开启相册，默认true。true-开启相册扫码、false-关闭相册扫码。
   * @returns 扫码结果
   */
  export async function startScanForResult(options?: scanBarcode.ScanOptions): Promise<string> {
    try {
      if (!options) {
        options = { scanTypes: [scanCore.ScanType.ALL], enableMultiMode: true, enableAlbum: true };
      }
      let scanResult: scanBarcode.ScanResult =
        await scanBarcode.startScanForResult(TKContextHelper.getCurrentContext(), options); //启动扫码，拉起扫码界面
      return scanResult.originalValue;
    } catch (error) {
      TKLog.error(`[TKScanHelper]打开扫描界面进行扫码异常，code: ${error.code}， message: ${error.message}`);
      return "";
    }
  }

  /**
   * 码图生成，使用Promise异步返回生成的码图。
   * @param content 码内容字符串
   * @param options 用于设置生成码图的参数:
   *     scanType  码类型。
   *     width 码图宽，单位：px。取值范围：[200, 4096]。
   *     height  码图高，单位：px。取值范围：[200, 4096]。
   *     margin  边距，单位：px，默认值为1，取值范围：[1, 10]。
   *     level 纠错水平，默认值为LEVEL_H。此参数只在生成QR码时有效。
   *     backgroundColor 生成码图背景颜色，HEX格式颜色，默认为白色（0xffffff）。
   *     pixelMapColor  生成码图颜色，HEX格式颜色，默认为黑色（0x000000）。
   * @returns
   */
  export function generateBarcodeImage(content: string,
    options?: generateBarcode.CreateOptions): Promise<image.PixelMap> {
    if (!options) {
      options = {
        scanType: scanCore.ScanType.QR_CODE,
        height: 800,
        width: 800,
        margin: 5
      }
    }
    return generateBarcode.createBarcode(content, options);
  }

  /**
   * 通过picker拉起图库并选择图片,并调用图片识码
   * @param options
   * @returns
   */
  export async function onPickerScanForResult(options?: scanBarcode.ScanOptions): Promise<string> {
    try {
      let imageUris: Array<string> = await TKPickerHelper.selectPhoto({
        maxSelectNumber: 1,
        isPhotoTakingSupported: false,
        MIMEType: photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE
      } as TKPhotoSelectOptions);
      if (imageUris && imageUris.length > 0) {
        return await onDetectBarCode(imageUris[0], options);
      }
    } catch (error) {
      TKLog.error(`[TKScanHelper]选择相册进行图片识码异常，code: ${error.code}， message: ${error.message}`);
    }
    return "";
  }

  /**
   * 调用图片识码，使用Promise方式异步返回识码结果。
   * @param uri 图片路径。
   * @param options
   *   scanTypes 设置扫码类型，默认扫码ALL（全部码类型）。
   *   enableMultiMode 是否开启多码识别，默认false。true：多码识别、false：单码识别。
   *   enableAlbum 是否开启相册，默认true。true-开启相册扫码、false-关闭相册扫码。
   * @returns ScanResult 扫码结果:
   *   scanType 码类型。
   *   originalValue 码识别内容结果。
   *   scanCodeRect 码识别位置信息。
   */
  export async function onDetectBarCode(imageURIOrData: string | ArrayBuffer,
    options?: scanBarcode.ScanOptions): Promise<string> {
    try {
      if (!options) {
        options = { scanTypes: [scanCore.ScanType.ALL], enableMultiMode: true, enableAlbum: true }
      }
      if (typeof imageURIOrData === "string") {
        let inputImage: detectBarcode.InputImage = { uri: imageURIOrData }
        let scanResults: Array<scanBarcode.ScanResult> = await detectBarcode.decode(inputImage, options);
        if (scanResults && scanResults.length > 0) {
          return scanResults[0].originalValue;
        }
      } else {
        let source: image.ImageSource = image.createImageSource(imageURIOrData as ArrayBuffer);
        let info: image.ImageInfo = await source.getImageInfo();
        let inputImage: detectBarcode.ByteImage = {
          byteBuffer: imageURIOrData as ArrayBuffer,
          width: info.size.width,
          height: info.size.height,
          format: detectBarcode.ImageFormat.NV21
        };
        let detectResult: detectBarcode.DetectResult = await detectBarcode.decodeImage(inputImage, options);
        let scanResults: Array<scanBarcode.ScanResult> = detectResult.scanResults;
        if (scanResults && scanResults.length > 0) {
          return scanResults[0].originalValue;
        }
      }
    } catch (error) {
      TKLog.error(`[TKScanHelper]调用图片识码异常，code: ${error.code}， message: ${error.message}`);
    }
    return "";
  }

  /**
   * 判断当前设备是否支持码能力
   * @returns
   */
  export function canIUseScan(): boolean {
    return canIUse('SystemCapability.Multimedia.Scan.ScanBarcode');
  }
}