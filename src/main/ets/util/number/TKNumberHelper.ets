/**
 * 数字相关帮组类
 */
import { TKStringHelper } from '../string/TKStringHelper';

/**
 进制格式
 */
export enum TKNumberRadix {
  /**
   * 二进制内存
   */
  BIN_MEM,

  /**
   * 二进制磁盘
   */
  BIN_DISK,

  /**
   * 通用十进制
   */
  DEC
}
;

export namespace TKNumberHelper {

  /**
   *  格式化小数 xxxxxx.xx,xxx,xxx.xx
   *
   * @param num         小数
   * @param digits      保留小数点
   * @param useGrouping 是否分组显示
   *
   * @return 格式化的字符串
   */
  export function formatNumber(num: number, digits: number, useGrouping: boolean = false): string {
    const options: Intl.NumberFormatOptions = {
      style: "decimal",
      useGrouping: useGrouping,
      minimumFractionDigits: digits,
      maximumFractionDigits: digits
    }
    return num.toLocaleString("zh-CN", options);
  }

  /**
   *  格式化小数 xxxx.xx% 例如0.01 = 1%
   *
   * @param num         小数
   * @param digits      保留小数点
   * @param useGrouping 是否分组显示
   *
   * @return 格式化的字符串
   */
  export function formatPerNumber(num: number, digits: number, useGrouping: boolean = false): string {
    const options: Intl.NumberFormatOptions = {
      style: "percent",
      useGrouping: useGrouping,
      minimumFractionDigits: digits,
      maximumFractionDigits: digits
    }
    return num.toLocaleString("zh-CN", options);
  }

  /**
   *  格式化金钱小数 ￥xxxx,xxx,xxx.xx
   *
   * @param num         小数
   * @param digits      保留小数点
   * @param useGrouping 是否分组显示
   *
   * @return 格式化的字符串
   */
  export function formatMoneyNumber(num: number, digits: number, useGrouping: boolean = false): string {
    const options: Intl.NumberFormatOptions = {
      style: "currency",
      currency: "CNY",
      useGrouping: useGrouping,
      minimumFractionDigits: digits,
      maximumFractionDigits: digits
    }
    return num.toLocaleString("zh-CN", options);
  }

  /**
   *  转换格式化的数字字符串为数字 xxx,xxx.xx
   *
   * @param numberStr 数字字符串
   *
   * @return 数字
   */
  export function parseNumberStr(numberStr: string): number {
    numberStr = TKStringHelper.replace(numberStr, ",", "");
    return Number(numberStr);
  }

  /**
   *  转换格式化的百分号字符串为数据 例如1% = 0.01
   *
   * @param numberStr 百分号字符串
   *
   * @return 数字
   */
  export function parsePerNumberStr(numberStr: string): number {
    numberStr = TKStringHelper.replace(numberStr, ",|%", "");
    return Number(numberStr) / 100;
  }

  /**
   *  转换格式化的金钱字符串为数字 ￥xxxx,xxx,xxx.xx
   *
   * @param numberStr 数字字符串
   *
   * @return 数字
   */
  export function parseMoneyNumberStr(numberStr: string): number {
    numberStr = TKStringHelper.replace(numberStr, ",|¥", "");
    return Number(numberStr);
  }

  /**
   * 获取指定数字随机的结果
   * @param num
   * @returns
   */
  export function getRandomNum(num: number): number {
    let index: number = 0;
    if (num > 0) {
      index = Math.floor(Math.random() * num);
      if (index == num) {
        index--;
      }
    }
    return index;
  }

  /**
   * 格式化小数点进行进制转换描述
   * @param num   小数
   * @param digits 保留小数点
   * @param radix 机制模式
   * @returns
   */
  export function formatNumberRadix(num: number, digits: number, radix: TKNumberRadix): string {
    let unit: string = "";
    let value: number = num;
    if (radix == TKNumberRadix.BIN_MEM || radix == TKNumberRadix.BIN_DISK) {
      let K: number = (radix == TKNumberRadix.BIN_MEM) ? 1024 : 1000;
      let M: number = K * ((radix == TKNumberRadix.BIN_MEM) ? 1024 : 1000);
      let G: number = M * ((radix == TKNumberRadix.BIN_MEM) ? 1024 : 1000);
      let T: number = G * ((radix == TKNumberRadix.BIN_MEM) ? 1024 : 1000);
      if (value >= T) {
        value = value / T;
        unit = "T";
      } else if (value >= G) {
        value = value / G;
        unit = "G";
      } else if (value >= M) {
        value = value / M;
        unit = "M";
      } else if (value >= K) {
        value = value / K;
        unit = "K";
      }
    } else if (radix == TKNumberRadix.DEC) {
      let W: number = 10000;
      let Y: number = W * 10000;
      if (value >= Y) {
        value = value / Y;
        unit = "亿";
      } else if (value >= W) {
        value = value / W;
        unit = "万";
      }
    }
    const options: Intl.NumberFormatOptions = {
      style: "decimal",
      useGrouping: false,
      minimumFractionDigits: digits,
      maximumFractionDigits: digits
    }
    return value.toLocaleString("zh-CN", options) + unit;
  }
}