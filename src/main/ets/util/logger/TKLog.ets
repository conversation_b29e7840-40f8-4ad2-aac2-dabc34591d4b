import { hilog } from '@kit.PerformanceAnalysisKit';
import { TKFileHelper } from '../file/TKFileHelper';
import { util } from '@kit.ArkTS';
import { TKDateHelper } from '../date/TKDateHelper';
import { TKDataHelper } from '../data/TKDataHelper';
import { TKStringHelper } from '../string/TKStringHelper';

export namespace TKLog {
  const domain: number = 0xffff;
  const tag: string = "Thinkive";
  let level: number = 0;
  let type: string = "console";

  /**
   *  设置日志级别
   * @param logLevel
   */
  export function setLogLevel(logLevel: string) {
    if (logLevel) {
      if (logLevel == 'debug') {
        level = 1;
      } else if (logLevel == 'info') {
        level = 2;
      } else if (logLevel == 'warn') {
        level = 3;
      } else if (logLevel == 'error') {
        level = 4;
      } else if (logLevel == 'off') {
        level = 5;
      } else {
        level = 0;
      }
    }
  }

  /**
   *  设置日志级别
   * @param logLevel
   */
  export function setLogType(logType: string) {
    if (TKStringHelper.isBlank(logType)) {
      logType = "console";
    }
    type = logType;
  }

  export function debug(format: string, ...args: Object[]) {
    if (level <= 1) {
      if (hilog.isLoggable(domain, tag, hilog.LogLevel.DEBUG)) {
        if (type.includes("console")) {
          hilog.debug(domain, tag, format, args);
        }
        if (type.includes("file")) {
          writeLog("DEBUG", format, args);
        }
      }
    }
  }

  export function info(format: string, ...args: Object[]) {
    if (level <= 2) {
      if (hilog.isLoggable(domain, tag, hilog.LogLevel.INFO)) {
        if (type.includes("console")) {
          hilog.info(domain, tag, format, args);
        }
        if (type.includes("file")) {
          writeLog("INFO", format, args);
        }
      }
    }
  }

  export function warn(format: string, ...args: Object[]) {
    if (level <= 3) {
      if (hilog.isLoggable(domain, tag, hilog.LogLevel.WARN)) {
        if (type.includes("console")) {
          hilog.warn(domain, tag, format, args);
        }
        if (type.includes("file")) {
          writeLog("WARN", format, args);
        }
      }
    }
  }

  export function error(format: string, ...args: Object[]) {
    if (level <= 4) {
      if (hilog.isLoggable(domain, tag, hilog.LogLevel.ERROR)) {
        if (type.includes("console")) {
          hilog.error(domain, tag, format, args);
        }
        if (type.includes("file")) {
          writeLog("ERROR", format, args);
        }
      }
    }
  }

  /**
   * 清理日志
   */
  export function clearLogFile() {
    const logPath: string = `${TKFileHelper.tempDir()}/thinkive/log/temp.log`;
    TKFileHelper.deleteFile(logPath);
  }

  /**
   * 写日志
   * @param level
   * @param format
   * @param args
   */
  export function writeLog(level: string, format: string, ...args: Object[]) {
    const logPath: string = `${TKFileHelper.tempDir()}/thinkive/log/temp.log`;
    let content: string =
      `${TKDateHelper.formatDate(new Date(), "YYYY-MM-DD HH:mm:ss:SSS")} [${level}] ${util.format(format, args)}\n`;
    TKFileHelper.writeFile(content, logPath, true);
  }

  /**
   * 读取日志
   */
  export function readLogFile(): string {
    const logPath: string = `${TKFileHelper.tempDir()}/thinkive/log/temp.log`;
    return TKDataHelper.uint8ArrayToString(TKFileHelper.readFile(logPath));
  }

}
