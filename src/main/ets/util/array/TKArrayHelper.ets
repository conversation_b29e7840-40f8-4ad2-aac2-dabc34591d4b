export namespace TKArrayHelper {
  /**
   * 删除数组中指定的元素集合
   * @param sourceArray
   * @param deleteArray
   */
  export function removeObjectsInArray(sourceArray: Array<Object>, deleteArray: Array<Object> | Set<Object>) {
    if (sourceArray !== undefined && deleteArray !== undefined) {
      deleteArray.forEach((value: Object) => {
        removeObjectInArray(sourceArray, value);
      });
    }
  }

  /**
   * 删除数组中指定的元素
   * @param sourceArray
   * @param deleteObject
   */
  export function removeObjectInArray(sourceArray: Array<Object>, deleteObject: Object) {
    if (sourceArray !== undefined && deleteObject !== undefined) {
      let index: number = sourceArray.indexOf(deleteObject);
      if (index >= 0) {
        sourceArray.splice(index, 1);
      }
    }
  }

  /**
   * 清空数组
   * @param sourceArray
   */
  export function clearArray(sourceArray: Array<Object>) {
    if (sourceArray !== undefined) {
      sourceArray.splice(0, sourceArray.length);
    }
  }
}