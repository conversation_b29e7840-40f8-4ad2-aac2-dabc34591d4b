/**
 * 输入法键盘工具类
 */
import { TKLog } from '../logger/TKLog';
import { TKContextHelper } from '../system/TKContextHelper';
import { TKWindowHelper } from './TKWindowHelper';

export namespace TKKeyboardHelper {

  /**
   * 隐藏键盘
   */
  export function hideKeyboard() {
    try {
      TKContextHelper.getCurrentUIContext().getFocusController().clearFocus();
    } catch (error) {
      TKLog.error(`[TKKeyboardHelper]隐藏键盘异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 订阅输入法软键盘显示或隐藏事件
   * @param callBack 返回boolen，true-键盘显示、false-键盘隐藏
   */
  export function onKeyboardListener(callback: Callback<number>) {
    try {
      TKWindowHelper.getLastWindow().then((win) => {
        win.off("keyboardHeightChange", callback);
        win.on('keyboardHeightChange', callback);
      });
    } catch (error) {
      TKLog.error(`[TKKeyboardHelper]开启订阅键盘显示隐藏事件异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 取消订阅输入法软键盘显示或隐藏事件
   */
  export function offKeyboardListener(callback?: Callback<number>) {
    try {
      TKWindowHelper.getLastWindow().then((win) => {
        if (callback) {
          win.off('keyboardHeightChange', callback);
        } else {
          win.off('keyboardHeightChange');
        }
      });
    } catch (error) {
      TKLog.error(`[TKKeyboardHelper]取消订阅键盘显示隐藏事件异常，code: ${error.code}， message: ${error.message}`);
    }
  }

}