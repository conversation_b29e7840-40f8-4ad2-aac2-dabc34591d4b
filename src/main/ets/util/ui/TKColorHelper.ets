import { TKStringHelper } from '../string/TKStringHelper';
import { common2D } from '@kit.ArkGraphics2D';

/**
 * 颜色相关工具类
 */
export namespace TKColorHelper {

  /**
   * 16进制颜色转矩阵
   * @param color 16进制颜色，例如#FFFFFFFF  ARGB格式
   * @returns
   */
  export function colorToMatrix(color: string): number[] {
    color = TKStringHelper.replace(color, "#|0x", "");
    let alpha: number = 255;
    let red: number = 255;
    let green: number = 255;
    let blue: number = 255;
    if (color.length == 8) {
      alpha = parseInt(color.substring(0, 2), 16);
      red = parseInt(color.substring(2, 4), 16);
      green = parseInt(color.substring(4, 6), 16);
      blue = parseInt(color.substring(6, 8), 16);
    } else {
      if (color.length >= 2) {
        red = parseInt(color.substring(0, 2), 16);
      }
      if (color.length >= 4) {
        green = parseInt(color.substring(2, 4), 16);
      }
      if (color.length >= 6) {
        blue = parseInt(color.substring(4, 6), 16);
      }
    }
    const normalizedR: number = red / 255;
    const normalizedG: number = green / 255;
    const normalizedB: number = blue / 255;
    const normalizedA: number = alpha / 255;
    return [normalizedR, normalizedG, normalizedB, normalizedA];
  }

  /**
   * 16进制颜色转ARGB
   * @param color 16进制颜色，例如#FFFFFFFF  ARGB格式
   * @returns
   */
  export function colorToARGB(color: string): common2D.Color {
    color = TKStringHelper.replace(color, "#|0x", "");
    let alpha: number = 255;
    let red: number = 255;
    let green: number = 255;
    let blue: number = 255;
    if (color.length == 8) {
      alpha = parseInt(color.substring(0, 2), 16);
      red = parseInt(color.substring(2, 4), 16);
      green = parseInt(color.substring(4, 6), 16);
      blue = parseInt(color.substring(6, 8), 16);
    } else {
      if (color.length >= 2) {
        red = parseInt(color.substring(0, 2), 16);
      }
      if (color.length >= 4) {
        green = parseInt(color.substring(2, 4), 16);
      }
      if (color.length >= 6) {
        blue = parseInt(color.substring(4, 6), 16);
      }
    }
    let argb: common2D.Color = {
      alpha: alpha,
      red: red,
      green: green,
      blue: blue
    }
    return argb;
  }
}