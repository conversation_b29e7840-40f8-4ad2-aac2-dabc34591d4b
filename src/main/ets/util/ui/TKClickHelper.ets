/**
 * 节流、防抖 工具类（用于点击事件，防止按钮被重复点击）
 */
export namespace TKClickHelper {

  //节流
  const throttleTimerMap: Map<Function, number> = new Map<Function, number>();

  //防抖
  const debounceTimerMap: Map<Function, number> = new Map<Function, number>();

  /**
   * 节流：在一定时间内，只触发一次
   * @param func 要执行的回调函数
   * @param wait = 800 延时的时间 毫秒
   * @param immediate = true 是否立即执行
   */
  export function throttle(func: () => void, wait: number = 800, immediate: boolean = true) {
    if (immediate) {
      if (throttleTimerMap.get(func) === undefined) {
        typeof func === 'function' && func();
        let throttleTimer = setTimeout(() => {
          throttleTimerMap.delete(func);
          clearTimeout(throttleTimer);
        }, wait)
        throttleTimerMap.set(func, throttleTimer);
      }
    } else if (throttleTimerMap.get(func) === undefined) {
      let throttleTimer = setTimeout(() => {
        typeof func === 'function' && func();
        throttleTimerMap.delete(func);
        clearTimeout(throttleTimer);
      }, wait)
      throttleTimerMap.set(func, throttleTimer);
    }
  }

  /**
   * 防抖：一定时间内，只有最后一次操作，再过wait毫秒后才执行函数
   * @param func 要执行的函数
   * @param wait 延时的时间
   */
  export function debounce(func: () => void, wait: number = 800) {
    if (debounceTimerMap.get(func) !== undefined) { //清除定时器
      clearTimeout(debounceTimerMap.get(func));
    }
    let debounceTimer = setTimeout(() => {
      typeof func === 'function' && func();
      clearTimeout(debounceTimer);
    }, wait)
    debounceTimerMap.set(func, debounceTimer);
  }

}