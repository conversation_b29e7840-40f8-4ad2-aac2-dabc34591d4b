import window from '@ohos.window';
import { TKLog } from '../logger/TKLog';
import { TKContextHelper } from '../system/TKContextHelper';
import { display, KeyboardAvoidMode } from '@kit.ArkUI';
import { resourceManager } from '@kit.LocalizationKit';
import { TKStringHelper } from '../string/TKStringHelper';

export enum TKBarName {
  status = 'status', // 状态栏
  navigation = 'navigation', // 导航条
  navigationIndicator = 'navigationIndicator' // 底部导航条
}

/**
 * 窗口入参对象
 */
export interface TKWinOptions {
  /**
   * 窗口名称 默认subWindow
   */
  name?: string;

  /**
   * 窗口类型 默认TYPE_DIALOG
   */
  windowType?: window.WindowType;

  /**
   *窗口要显示的路由  如:pages/Welcome需要在main_pages.json中声明
   */
  contentRouterPath?: string;

  /**
   *窗口要显示的路由名称
   */
  contentRouterName?: string;

  /**
   * 窗口背景颜色,默认#33606266
   */
  bgColor?: string;
}

/**
 * 窗口状态栏入参对象
 */
export interface TKWinStatusBarOptions {
  //窗口的布局是否为沉浸式布局（该沉浸式布局状态栏、导航栏仍然显示）。true表示沉浸式布局；false表示非沉浸式布局。
  layoutFullScreen?: boolean;

  //设置窗口全屏模式时状态栏、导航栏或底部导航条是否显示  [TKBarName.status, TKBarName.navigationIndicator]
  barNames?: Array<TKBarName>;

  //状态栏、导航栏的属性
  //   statusBarColor 状态栏背景颜色，为十六进制RGB或ARGB颜色，不区分大小写，例如#00FF00或#FF00FF00
  //   statusBarContentColor 状态栏文字颜色。当设置此属性后， isStatusBarLightIcon属性设置无效
  //   isStatusBarLightIcon 状态栏图标是否为高亮状态。true表示高亮；false表示不高亮。默认值：false
  //   navigationBarColor 导航栏背景颜色，为十六进制RGB或ARGB颜色，不区分大小写，例如#00FF00或#FF00FF00
  //   navigationBarContentColor  导航栏文字颜色。当设置此属性后， isNavigationBarLightIcon属性设置无效
  //   isNavigationBarLightIcon 导航栏图标是否为高亮状态。true表示高亮；false表示不高亮。默认值：false
  barProperties ?: window.SystemBarProperties;

  //设置窗口的背景颜色 #FF00FF00
  winBgColor?: string;
}

/**
 * 窗口处理帮组类
 */
export namespace TKWindowHelper {
  //状态栏高度
  let statusBarHeight: number = 0;
  //导航栏高度
  let navigationBarHeight: number = 0;
  //最近的窗口
  let lastWindow: window.Window | undefined = undefined;

  /**
   * 获取主window
   * @returns
   */
  export function getMainWindow(): window.Window {
    return TKContextHelper.getCurrentMainWindow();
  }

  /**
   * 根据参数创建窗口
   */
  export async function createWindow(options: TKWinOptions): Promise<window.Window | undefined> {
    try {
      options.name = options.name ?? 'subWindow';
      options.windowType = options.windowType ?? window.WindowType.TYPE_DIALOG;
      options.bgColor = options.bgColor ?? '#33606266';
      //创建窗口
      let windowClass = await window.createWindow({
        name: options.name,
        windowType: options.windowType,
        ctx: TKContextHelper.getCurrentContext()
      });
      //将窗口缓存
      if (TKStringHelper.isNotBlank(options.contentRouterPath)) {
        await windowClass.setUIContent(options.contentRouterPath);
      } else if (TKStringHelper.isNotBlank(options.contentRouterName)) {
        await windowClass.loadContentByName(options.contentRouterName);
      }
      //获取屏幕四大角
      let d = display.getDefaultDisplaySync();
      //设置窗口大小
      await windowClass.resize(d.width, d.height);
      // 设置窗口背景颜色
      windowClass.setWindowBackgroundColor(options.bgColor);
      //显示窗口
      await windowClass.showWindow();
      return windowClass;
    } catch (error) {
      TKLog.error(`[TKWindowHelper]创建窗口异常，code: ${error.code}， message: ${error.message}`);
    }
    return undefined;
  }

  /**
   * 获取当前窗口
   * @param context
   * @returns
   */
  export async function getLastWindow(context?: Context, isUseCache: boolean = true): Promise<window.Window> {
    try {
      if (isUseCache && lastWindow) {
        return lastWindow;
      }
      context = context ?? TKContextHelper.getCurrentContext();
      // 操作窗口
      let windowClass: window.Window | undefined = await window.getLastWindow(context) ?? getMainWindow();
      // 设置常亮
      if (windowClass) {
        lastWindow = windowClass;
      }
      return windowClass;
    } catch (error) {
      TKLog.error(`[TKWindowHelper]获取当前窗口异常，code: ${error.code}， message: ${error.message}`);
      return getMainWindow();
    }
  }

  /**
   * 获取当前窗口的属性
   * @param windowClass
   * @returns
   */
  export async function getWindowProperties(windowClass?: window.Window): Promise<window.WindowProperties> {
    windowClass = windowClass ?? await getLastWindow();
    return windowClass.getWindowProperties();
  }

  /**
   * 同步获取当前窗口的属性
   * @param windowClass
   * @returns
   */
  export function getWindowPropertiesSync(windowClass?: window.Window): window.WindowProperties {
    windowClass = windowClass ?? getMainWindow();
    return windowClass.getWindowProperties();
  }

  /**
   * 获取当前窗口Bar的属性
   * @param windowClass
   * @returns
   */
  export async function getWindowSystemBarProperties(windowClass?: window.Window): Promise<window.SystemBarProperties> {
    windowClass = windowClass ?? await getLastWindow();
    return windowClass.getWindowSystemBarProperties();
  }

  /**
   * 同步获取当前窗口Bar的属性
   * @param windowClass
   * @returns
   */
  export function getWindowSystemBarPropertiesSync(windowClass?: window.Window): window.SystemBarProperties {
    windowClass = windowClass ?? getMainWindow();
    return windowClass.getWindowSystemBarProperties();
  }

  /**
   * 设置当前窗口的属性
   * @param windowClass
   * @returns
   */
  export async function setWindowSystemBarProperties(windowProperties: window.SystemBarProperties,
    windowClass?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      await windowClass.setWindowSystemBarProperties(windowProperties);
    } catch (error) {
      TKLog.error(`[TKWindowHelper]设置当前窗口状态栏导航栏属性异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取虚拟键盘抬起时的页面避让模式（OFFSET-上抬模式、RESIZE-压缩模式）。
   */
  export async function getKeyboardAvoidMode(windowClass?: window.Window): Promise<KeyboardAvoidMode> {
    windowClass = windowClass ?? await getLastWindow();
    return windowClass.getUIContext().getKeyboardAvoidMode();
  }

  /**
   * 同步获取虚拟键盘抬起时的页面避让模式（OFFSET-上抬模式、RESIZE-压缩模式）。
   */
  export function getKeyboardAvoidModeSync(windowClass?: window.Window): KeyboardAvoidMode {
    windowClass = windowClass ?? getMainWindow();
    return windowClass.getUIContext().getKeyboardAvoidMode();
  }

  /**
   * 设置虚拟键盘弹出时，页面的避让模式。
   * @param value （OFFSET-上抬模式、RESIZE-压缩模式）
   * @param windowClass
   */
  export async function setKeyboardAvoidMode(value: KeyboardAvoidMode, windowClass?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      windowClass.getUIContext().setKeyboardAvoidMode(value);
    } catch (error) {
      TKLog.error(`[TKWindowHelper]异步设置虚拟键盘弹出页面的避让模式异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 同步设置虚拟键盘弹出时，页面的避让模式。
   * @param value （OFFSET-上抬模式、RESIZE-压缩模式）
   * @param windowClass
   */
  export function setKeyboardAvoidModeSync(value: KeyboardAvoidMode, windowClass?: window.Window): void {
    try {
      windowClass = windowClass ?? getMainWindow();
      windowClass.getUIContext().setKeyboardAvoidMode(value);
    } catch (error) {
      TKLog.error(`[TKWindowHelper]同步设置虚拟键盘弹出页面的避让模式异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取屏幕常亮状态
   * @param windowClass
   * @returns
   */
  export async function getWindowKeepScreenOn(windowClass?: window.Window): Promise<boolean> {
    let windowProperties: window.WindowProperties = await getWindowProperties(windowClass);
    return windowProperties.isKeepScreenOn;
  }

  /**
   * 同步获取屏幕常亮状态
   * @param windowClass
   * @returns
   */
  export function getWindowKeepScreenOnSync(windowClass?: window.Window): boolean {
    let windowProperties: window.WindowProperties = getWindowPropertiesSync(windowClass);
    return windowProperties.isKeepScreenOn;
  }

  /**
   * 设置屏幕常亮
   * @param isKeepScreenOn
   * @param windowClass
   * @returns
   */
  export async function setWindowKeepScreenOn(isKeepScreenOn: boolean, windowClass?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      await windowClass.setWindowKeepScreenOn(isKeepScreenOn);
    } catch (error) {
      TKLog.error(`[TKWindowHelper]${isKeepScreenOn == true ? '设置' :
        '关闭'}窗口常亮异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取屏幕亮度
   * @param windowClass
   * @returns
   */
  export async function getWindowBrightness(windowClass?: window.Window): Promise<number> {
    let windowProperties: window.WindowProperties = await getWindowProperties(windowClass);
    return windowProperties.brightness;
  }

  /**
   * 同步获取屏幕亮度
   * @param windowClass
   * @returns
   */
  export function getWindowBrightnessSync(windowClass?: window.Window): number {
    let windowProperties: window.WindowProperties = getWindowPropertiesSync(windowClass);
    return windowProperties.brightness;
  }

  /**
   * 设置屏幕亮度
   * @param brightness
   * @param windowClass
   * @returns
   */
  export async function setWindowBrightness(brightness: number, windowClass?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      await windowClass.setWindowBrightness(brightness);
    } catch (error) {
      TKLog.error(`[TKWindowHelper]设置屏幕亮度异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取窗口是否为隐私模式。设置为隐私模式的窗口，窗口内容将无法被截屏或录屏。
   * @param windowClass
   * @returns
   */
  export async function getWindowPrivacyMode(windowClass?: window.Window): Promise<boolean> {
    let windowProperties: window.WindowProperties = await getWindowProperties(windowClass);
    return windowProperties.isPrivacyMode;
  }

  /**
   * 同步获取窗口是否为隐私模式。设置为隐私模式的窗口，窗口内容将无法被截屏或录屏。
   * @param windowClass
   * @returns
   */
  export function getWindowPrivacyModeSync(windowClass?: window.Window): boolean {
    let windowProperties: window.WindowProperties = getWindowPropertiesSync(windowClass);
    return windowProperties.isPrivacyMode;
  }

  /**
   * 设置窗口是否为隐私模式。设置为隐私模式的窗口，窗口内容将无法被截屏或录屏。
   * @param isPrivacyMode
   * @param windowClass
   * @returns
   */
  export async function setWindowPrivacyMode(isPrivacyMode: boolean, windowClass?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      await windowClass.setWindowPrivacyMode(isPrivacyMode);
    } catch (error) {
      TKLog.error(`[TKWindowHelper]设置窗口隐私模式异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 设置窗口旋转
   * @param orientation
   *   UNSPECIFIED  0  表示未定义方向模式，由系统判定。
   *   PORTRAIT  1  表示竖屏显示模式。
   *   LANDSCAPE  2  表示横屏显示模式。
   *   PORTRAIT_INVERTED  3  表示反向竖屏显示模式。
   *   LANDSCAPE_INVERTED  4  表示反向横屏显示模式。
   *   AUTO_ROTATION  5  表示传感器自动旋转模式。
   *   AUTO_ROTATION_PORTRAIT  6  表示传感器自动竖向旋转模式。
   *   AUTO_ROTATION_LANDSCAPE  7  表示传感器自动横向旋转模式。
   *   AUTO_ROTATION_RESTRICTED  8  表示受开关控制的自动旋转模式。
   *   AUTO_ROTATION_PORTRAIT_RESTRICTED  9  表示受开关控制的自动竖向旋转模式。
   *   AUTO_ROTATION_LANDSCAPE_RESTRICTED  10  表示受开关控制的自动横向旋转模式。
   *   LOCKED  11  表示锁定模式。
   * @param windowClass
   * @returns
   */
  export async function setPreferredOrientation(orientation: window.Orientation,
    windowClass?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      await windowClass.setPreferredOrientation(orientation);
    } catch (error) {
      TKLog.error(`[TKWindowHelper]设置窗口旋转方向[${orientation}]异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 设备当前是否以竖屏方式显示（竖屏）
   */
  export function isPortrait(): boolean {
    let configuration: resourceManager.Configuration = TKContextHelper.getCurrentContext()
      .resourceManager
      .getConfigurationSync();
    return configuration.direction == resourceManager.Direction.DIRECTION_VERTICAL;
  }

  /**
   * 设备当前是否以横屏方式显示（横屏）
   */
  export function isLandscape(): boolean {
    let configuration: resourceManager.Configuration = TKContextHelper.getCurrentContext()
      .resourceManager
      .getConfigurationSync();
    return configuration.direction == resourceManager.Direction.DIRECTION_HORIZONTAL;
  }

  /**
   * 设置窗口的背景色。Stage模型下，该接口需要在loadContent()或setUIContent()调用生效后使用。
   * @param color 需要设置的背景色，为十六进制RGB或ARGB颜色，不区分大小写，例如#00FF00或#FF00FF00
   * @param windowClass
   * @returns
   */
  export async function setWindowBackgroundColor(color: string, windowClass ?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      if (color.startsWith("#")) {
        windowClass.setWindowBackgroundColor(color);
      }
    } catch (error) {
      TKLog.error(`[TKWindowHelper]异步设置窗口背景颜色异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 同步设置窗口的背景色。Stage模型下，该接口需要在loadContent()或setUIContent()调用生效后使用。
   * @param color 需要设置的背景色，为十六进制RGB或ARGB颜色，不区分大小写，例如#00FF00或#FF00FF00
   * @param windowClass
   * @returns
   */
  export function setWindowBackgroundColorSync(color: string, windowClass ?: window.Window): void {
    try {
      windowClass = windowClass ?? getMainWindow();
      if (color.startsWith("#")) {
        windowClass.setWindowBackgroundColor(color);
      }
    } catch (error) {
      TKLog.error(`[TKWindowHelper]同步设置窗口背景颜色异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取点击时是否支持切换焦点窗口，使用Promise异步回调。
   * @param windowClass
   * @returns
   */
  export async function getWindowFocusable(windowClass ?: window.Window): Promise<boolean> {
    let windowProperties: window.WindowProperties = await getWindowProperties(windowClass);
    return windowProperties.focusable;
  }

  /**
   * 同步获取点击时是否支持切换焦点窗口
   * @param windowClass
   * @returns
   */
  export function getWindowFocusableSync(windowClass ?: window.Window): boolean {
    let windowProperties: window.WindowProperties = getWindowPropertiesSync(windowClass);
    return windowProperties.focusable;
  }

  /**
   * 设置点击时是否支持切换焦点窗口，使用Promise异步回调。
   * @param isFocusable 点击时是否支持切换焦点窗口。true表示支持；false表示不支持。
   * @param windowClass
   * @returns
   */
  export async function setWindowFocusable(isFocusable: boolean, windowClass ?: window.Window): Promise<void> {
    windowClass = windowClass ?? await getLastWindow();
    return await windowClass.setWindowFocusable(isFocusable);
  }

  /**
   * 获取窗口是否为可触状态，使用Promise异步回调。
   * @param windowClass
   * @returns
   */
  export async function getWindowTouchable(windowClass ?: window.Window): Promise<boolean> {
    let windowProperties: window.WindowProperties = await getWindowProperties(windowClass);
    return windowProperties.touchable;
  }

  /**
   * 同步获取窗口是否为可触状态
   * @param windowClass
   * @returns
   */
  export function getWindowTouchableSync(windowClass ?: window.Window): boolean {
    let windowProperties: window.WindowProperties = getWindowPropertiesSync(windowClass);
    return windowProperties.touchable;
  }

  /**
   * 设置窗口是否为可触状态，使用Promise异步回调。
   * @param isTouchable 窗口是否为可触状态。true表示可触；false表示不可触。
   * @param windowClass
   * @returns
   */
  export async function setWindowTouchable(isTouchable: boolean, windowClass?: window.Window): Promise<void> {
    windowClass = windowClass ?? await getLastWindow();
    return await windowClass.setWindowTouchable(isTouchable);
  }

  /**
   * 设置沉浸式状态栏
   * @param options
   * @param windowClass
   */
  export async function setStatusBar(options: TKWinStatusBarOptions, windowClass ?: window.Window): Promise<void> {
    try {
      if (options.layoutFullScreen !== undefined) {
        await setWindowLayoutFullScreen(options.layoutFullScreen, windowClass);
      }
      if (TKStringHelper.isNotBlank(options.winBgColor)) {
        await setWindowBackgroundColor(options.winBgColor!, windowClass);
      }
      if (options.barNames !== undefined) {
        await setWindowSystemBarEnable(options.barNames, windowClass);
      }
      if (options.barProperties !== undefined) {
        await setWindowSystemBarProperties(options.barProperties);
      }
    } catch (error) {
      TKLog.error(`[TKWindowHelper]设置窗口状态栏相关功能异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 设置窗口导航栏和状态栏是否显示
   * @param barNames
   * @param windowClass
   * @returns
   */
  export async function setWindowSystemBarEnable(barNames: Array<TKBarName>,
    windowClass?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      let lastBarNames: Array<TKBarName> = Array.from(barNames);
      let index: number = lastBarNames.indexOf(TKBarName.navigationIndicator);
      let navigationIndicatorEnable: boolean = index >= 0;
      if (index >= 0) {
        lastBarNames.splice(index, 1);
      }
      await windowClass.setWindowSystemBarEnable(lastBarNames as Array<'status' | 'navigation'>);
      await windowClass.setSpecificSystemBarEnabled("navigationIndicator", navigationIndicatorEnable);
    } catch (error) {
      TKLog.error(`[TKWindowHelper]设置${JSON.stringify(barNames)}显示异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 设置窗口横屏竖屏
   * @param enable
   * @param windowClass
   * @returns
   */
  export async function enableLandscapeMultiWindow(enable: boolean, windowClass?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      if (enable) {
        await windowClass.enableLandscapeMultiWindow();
      } else {
        await windowClass.disableLandscapeMultiWindow();
      }
    } catch (error) {
      TKLog.error(`[TKWindowHelper]设置窗口横屏竖屏异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取沉浸式状态栏状态
   * @param windowClass
   * @returns
   */
  export async function getWindowLayoutFullScreen(windowClass?: window.Window): Promise<boolean> {
    let windowProperties: window.WindowProperties = await getWindowProperties(windowClass);
    return windowProperties.isLayoutFullScreen;
  }

  /**
   * 同步获取沉浸式状态栏状态
   * @param windowClass
   * @returns
   */
  export function getWindowLayoutFullScreenSync(windowClass?: window.Window): boolean {
    let windowProperties: window.WindowProperties = getWindowPropertiesSync(windowClass);
    return windowProperties.isLayoutFullScreen;
  }

  /**
   * 沉浸式状态栏设置
   * @param isLayoutFullScreen
   * @param windowClass
   * @returns
   */
  export async function setWindowLayoutFullScreen(isLayoutFullScreen: boolean,
    windowClass?: window.Window): Promise<void> {
    try {
      windowClass = windowClass ?? await getLastWindow();
      if (windowClass.getWindowProperties().isLayoutFullScreen != isLayoutFullScreen) {
        await windowClass.setWindowLayoutFullScreen(isLayoutFullScreen);
      }
    } catch (error) {
      TKLog.error(`[TKWindowHelper]${isLayoutFullScreen == true ? '开启' :
        '关闭'}沉浸式状态栏异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取状态栏的高度
   * @param windowClass
   * @returns
   */
  export async function getStatusBarHeight(windowClass?: window.Window): Promise<number> {
    if (statusBarHeight == 0) {
      windowClass = windowClass ?? await getLastWindow();
      statusBarHeight = windowClass.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM).topRect.height;
    }
    if (statusBarHeight == 0) {
      return vp2px(39);
    } else {
      return statusBarHeight;
    }
  }

  /**
   * 同步获取状态栏的高度
   * @param windowClass
   * @returns
   */
  export function getStatusBarHeightSync(windowClass?: window.Window): number {
    if (statusBarHeight == 0) {
      windowClass = windowClass ?? getMainWindow();
      statusBarHeight = windowClass.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM).topRect.height;
    }
    if (statusBarHeight == 0) {
      return vp2px(39);
    } else {
      return statusBarHeight;
    }
  }

  /**
   * 获取底部导航栏安全区的高度
   * @param windowClass
   * @returns
   */
  export async function getNavigationBottomBarHeight(windowClass?: window.Window): Promise<number> {
    if (navigationBarHeight == 0) {
      windowClass = windowClass ?? await getLastWindow();
      navigationBarHeight = windowClass.getWindowAvoidArea(window.AvoidAreaType.TYPE_NAVIGATION_INDICATOR)
        .bottomRect
        .height;
    }
    if (navigationBarHeight == 0) {
      return vp2px(28);
    } else {
      return navigationBarHeight;
    }
  }

  /**
   * 同步获取导航栏的高度
   * @param windowClass
   * @returns
   */
  export function getNavigationBottomBarHeightSync(windowClass?: window.Window): number {
    if (navigationBarHeight == 0) {
      windowClass = windowClass ?? getMainWindow();
      navigationBarHeight = windowClass.getWindowAvoidArea(window.AvoidAreaType.TYPE_NAVIGATION_INDICATOR)
        .bottomRect
        .height;
    }
    if (navigationBarHeight == 0) {
      return vp2px(28);
    } else {
      return navigationBarHeight;
    }
  }

  /**
   * 监听窗口旋转
   * @param callback
   * @param windowClass
   */
  export async function onWindowSizeChangeListener(callback: Callback<window.Size>,
    windowClass?: window.Window) {
    try {
      windowClass = windowClass ?? await getLastWindow();
      windowClass.off('windowSizeChange', callback);
      windowClass.on('windowSizeChange', callback);
    } catch (error) {
      TKLog.error(`[TKWindowHelper]开启监听窗口旋转异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 取消监听窗口旋转
   * @param callback
   * @param windowClass
   */
  export async function offWindowSizeChangeListener(callback?: Callback<window.Size>, windowClass?: window.Window) {
    try {
      windowClass = windowClass ?? await getLastWindow();
      if (callback) {
        windowClass.off('windowSizeChange', callback);
      } else {
        windowClass.off('windowSizeChange');
      }
    } catch (error) {
      TKLog.error(`[TKWindowHelper]取消监听窗口旋转异常，code: ${error.code}， message: ${error.message}`);
    }
  }

}
