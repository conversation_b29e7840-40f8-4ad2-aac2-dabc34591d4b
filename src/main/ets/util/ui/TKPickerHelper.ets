/**
 * 拍照、文件(文件、图片、视频、音频)选择和保存,工具类
 */
import { camera, cameraPicker } from '@kit.CameraKit';
import { TKLog } from '../logger/TKLog';
import { TKContextHelper } from '../system/TKContextHelper';
import { photoAccessHelper } from '@kit.MediaLibraryKit';
import { picker } from '@kit.CoreFileKit';
import { dataSharePredicates } from '@kit.ArkData';
import { TKBase64Helper } from '../crypto/TKBase64Helper';
import { buffer } from '@kit.ArkTS';
import { TKFileHelper } from '../file/TKFileHelper';
import { TKUUIDHelper } from '../crypto/TKUUIDHelper';
import fs from '@ohos.file.fs';

const TK_PHOTO_DEFAULT_SELECT_NUMBER: number = 9; //数量

export namespace TKPickerHelper {

  /**
   * 调用系统相机，拍照、录视频
   * @param options
   * @returns
   */
  export async function openCamera(options?: TKCameraOptions): Promise<string> {
    try {
      options = options ?? new TKCameraOptions();
      if (!options.mediaTypes || options.mediaTypes.length == 0) {
        options.mediaTypes = [cameraPicker.PickerMediaType.PHOTO, cameraPicker.PickerMediaType.VIDEO];
      }
      let pickerProfile: cameraPicker.PickerProfile = {
        cameraPosition: options.cameraPosition ? options.cameraPosition : camera.CameraPosition.CAMERA_POSITION_BACK,
        videoDuration: options.videoDuration,
        saveUri: options.saveUri
      };

      let pickerResult: cameraPicker.PickerResult =
        await cameraPicker.pick(TKContextHelper.getCurrentContext(), options.mediaTypes, pickerProfile);
      return pickerResult?.resultUri ?? "";
    } catch (error) {
      TKLog.error(`[TKPickerHelper]打开系统相机异常，code: ${error.code}， message: ${error.message}`);
    }
    return "";
  }

  /**
   * 通过选择模式拉起photoPicker界面，用户可以选择一个或多个图片/视频。
   * @param options
   * @returns
   */
  export async function selectPhoto(options?: TKPhotoSelectOptions): Promise<Array<string>> {
    try {
      options = options ?? new TKPhotoSelectOptions();
      //可选择的媒体文件类型，若无此参数，则默认为图片和视频类型。
      if (!options.MIMEType) {
        options.MIMEType = photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE;
      }
      //选择媒体文件数量的最大值,默认9
      if (!options.maxSelectNumber) {
        options.maxSelectNumber = TK_PHOTO_DEFAULT_SELECT_NUMBER;
      }
      //支持拍照。
      if (options.isPhotoTakingSupported == undefined) {
        options.isPhotoTakingSupported = true;
      }
      //支持编辑照片。
      if (options.isEditSupported == undefined) {
        options.isEditSupported = true;
      }
      //支持搜查照片。
      if (options.isSearchSupported == undefined) {
        options.isSearchSupported = true;
      }
      let photoSelectOptions: photoAccessHelper.PhotoSelectOptions = {
        MIMEType: options.MIMEType,
        maxSelectNumber: options.maxSelectNumber,
        isPhotoTakingSupported: options.isPhotoTakingSupported,
        isEditSupported: options.isEditSupported,
        isSearchSupported: options.isSearchSupported,
        recommendationOptions: options.recommendationOptions,
        preselectedUris: options.preselectedUris
      }
      let photoPicker = new photoAccessHelper.PhotoViewPicker();
      let photoSelectResult: photoAccessHelper.PhotoSelectResult = await photoPicker.select(photoSelectOptions);
      return photoSelectResult?.photoUris ?? [];
    } catch (error) {
      TKLog.error(`[TKPickerHelper]打开系统相册异常，code: ${error.code}， message: ${error.message}`);
      return [];
    }
  }

  /**
   * 保存图片到相册
   *
   * @param image
   */
  export async function savePhotoToGallery(image: string | ArrayBuffer | Uint8Array): Promise<boolean> {
    let helper = photoAccessHelper.getPhotoAccessHelper(TKContextHelper.getCurrentContext());
    try {
      TKLog.debug("[TKPickerHelper]开始保存图片");
      image = image instanceof Uint8Array ? buffer.from(image).buffer : image;
      image = (typeof image == "string") ? buffer.from(TKBase64Helper.dataWithBase64Decode(image)).buffer : image;
      // let uri: string = await helper.createAsset(photoAccessHelper.PhotoType.IMAGE, 'jpg');
      // // 使用uri打开文件，可以持续写入内容，写入过程不受时间限制
      // let file: fs.File = fs.openSync(uri, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
      // // 写到媒体库文件中
      // fs.writeSync(file.fd, image);
      // fs.closeSync(file.fd);
      let imageName: string = TKUUIDHelper.uuid();
      let imagePath: string = `${TKFileHelper.tempDir()}/photo/${imageName}.jpg`;
      TKFileHelper.writeFile(new Uint8Array(image), imagePath);
      let srcFileUri: string = imagePath;
      let srcFileUris: Array<string> = [srcFileUri];
      let photoCreationConfigs: Array<photoAccessHelper.PhotoCreationConfig> = [
        {
          title: `${imageName}`,
          fileNameExtension: 'jpg',
          photoType: photoAccessHelper.PhotoType.IMAGE,
          subtype: photoAccessHelper.PhotoSubtype.DEFAULT
        }
      ];
      let desFileUris: Array<string> = await helper.showAssetsCreationDialog(srcFileUris, photoCreationConfigs);
      let desFileUri: string = desFileUris[0];
      // 将来源于应用沙箱的照片内容写入媒体库的目标uri
      let srcFile = fs.openSync(srcFileUri, fs.OpenMode.READ_ONLY);
      let desFile = fs.openSync(desFileUri, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
      fs.copyFileSync(srcFile.fd, desFile.fd);
      fs.closeSync(srcFile);
      fs.closeSync(desFile);
      TKFileHelper.deleteFile(srcFileUri);
      TKLog.info(`[TKPickerHelper]保存图片到相册成功，地址是${desFileUri}`)
      return true;
    } catch (error) {
      TKLog.error(`[TKPickerHelper]保存图片到相册异常，code: ${error.code}， message: ${error.message}`);
      return false;
    }
  }

  /**
   * 通过保存模式拉起photoPicker进行保存图片或视频资源的文件名，若无此参数，则默认需要用户自行输入
   *
   * @param newFileNames
   */
  export async function savePhoto(newFileNames?: Array<string>): Promise<Array<string>> {
    try {
      let photoPicker = new picker.PhotoViewPicker();
      if (newFileNames == undefined || newFileNames == null || newFileNames.length == 0) {
        let photoSaveResult = await photoPicker.save();
        return photoSaveResult ?? [];
      } else {
        let photoSaveOptions = new picker.PhotoSaveOptions();
        photoSaveOptions.newFileNames = newFileNames;
        let photoSaveResult = await photoPicker.save(photoSaveOptions)
        return photoSaveResult ?? [];
      }
    } catch (error) {
      TKLog.error(`[TKPickerHelper]保存选择照片异常，code: ${error.code}， message: ${error.message}`);
      return [];
    }
  }

  /**
   * 通过选择模式拉起documentPicker界面，用户可以选择一个或多个文件。
   * @param options
   * @returns
   */
  export async function selectDocument(options?: TKDocumentSelectOptions): Promise<Array<string>> {
    try {
      options = options ?? new TKDocumentSelectOptions();
      //选择媒体文件数量的最大值,默认9
      if (!options.maxSelectNumber) {
        options.maxSelectNumber = TK_PHOTO_DEFAULT_SELECT_NUMBER;
      }
      //支持选择的资源类型,默认文件
      if (!options.selectMode) {
        options.selectMode = 0;
      }
      let documentSelectOptions: picker.DocumentSelectOptions = {
        maxSelectNumber: options.maxSelectNumber,
        defaultFilePathUri: options.defaultFilePathUri,
        fileSuffixFilters: options.fileSuffixFilters,
        selectMode: options.selectMode
      }
      let documentPicker = new picker.DocumentViewPicker();
      return await documentPicker.select(documentSelectOptions) ?? [];
    } catch (error) {
      TKLog.error(`[TKPickerHelper]打开文件选择器异常，code: ${error.code}， message: ${error.message}`);
      return [];
    }
  }

  /**
   * 通过保存模式拉起documentPicker界面，用户可以保存一个或多个文件。
   * @param options
   * @returns
   */
  export async function saveDocument(newFileNames?: Array<string>): Promise<Array<string>> {
    try {
      let documentPicker = new picker.DocumentViewPicker();
      if (newFileNames == undefined || newFileNames == null || newFileNames.length == 0) {
        return await documentPicker.save() ?? [];
      } else {
        let documentSaveOptions = new picker.DocumentSaveOptions();
        documentSaveOptions.newFileNames = newFileNames;
        return await documentPicker.save(documentSaveOptions) ?? [];
      }
    } catch (error) {
      TKLog.error(`[TKPickerHelper]保存文件异常，code: ${error.code}， message: ${error.message}`);
      return [];
    }
  }

  /**
   * 通过选择模式拉起audioPicker界面（目前拉起的是documentPicker，audioPicker在规划中），用户可以选择一个或多个音频文件。
   * @returns
   */
  export async function selectAudio(): Promise<Array<string>> {
    try {
      let audioSelectOptions = new picker.AudioSelectOptions();
      let audioPicker = new picker.AudioViewPicker();
      return await audioPicker.select(audioSelectOptions) ?? [];
    } catch (error) {
      TKLog.error(`[TKPickerHelper]打开音频选择器异常，code: ${error.code}， message: ${error.message}`);
      return [];
    }
  }

  /**
   * 通过保存模式拉起audioPicker界面（目前拉起的是documentPicker，audioPicker在规划中），用户可以保存一个或多个音频文件。
   * @param newFileNames
   * @returns
   */
  export async function saveAudio(newFileNames?: Array<string>): Promise<Array<string>> {
    try {
      let audioPicker: picker.AudioViewPicker = new picker.AudioViewPicker();
      if (newFileNames == undefined || newFileNames == null || newFileNames.length == 0) {
        return await audioPicker.save() ?? [];
      } else {
        let audioSaveOptions = new picker.AudioSaveOptions();
        audioSaveOptions.newFileNames = newFileNames;
        return await audioPicker.save(audioSaveOptions) ?? [];
      }
    } catch (error) {
      TKLog.error(`[TKPickerHelper]保存音频文件异常，code: ${error.code}， message: ${error.message}`);
      return [];
    }
  }

  /**
   * 获取对应uri的PhotoAsset对象,用于读取文件信息。
   * @param uri 文件uri
   *    PhotoAsset - photoAccessHelper.PhotoKeys:
   *      URI  'uri'  文件uri。
   *      PHOTO_TYPE  'media_type'  媒体文件类型。
   *      DISPLAY_NAME  'display_name'  显示名字。
   *      SIZE  'size'  文件大小。
   *      DURATION  'duration'  持续时间（单位：毫秒）。
   *      WIDTH  'width'  图片宽度（单位：像素）。
   *      HEIGHT  'height'  图片高度（单位：像素）。
   *      DATE_TAKEN  'date_taken'  拍摄日期（文件拍照时间距1970年1月1日的秒数值）。
   *      ORIENTATION  'orientation'  图片文件的方向。
   *      TITLE  'title'  文件标题。
   *      getThumbnail 获取缩略图
   * @returns
   */
  export async function getPhotoAsset(uri: string): Promise<photoAccessHelper.PhotoAsset | undefined> {
    try {
      let phAccessHelper = photoAccessHelper.getPhotoAccessHelper(TKContextHelper.getCurrentContext());
      let predicates: dataSharePredicates.DataSharePredicates = new dataSharePredicates.DataSharePredicates();
      // 配置查询条件，使用PhotoViewPicker选择图片返回的uri进行查询
      predicates.equalTo('uri', uri);
      let fetchOption: photoAccessHelper.FetchOptions = {
        fetchColumns: [],
        predicates: predicates
      };
      let fetchResult: photoAccessHelper.FetchResult<photoAccessHelper.PhotoAsset> =
        await phAccessHelper.getAssets(fetchOption);
      // 得到uri对应的PhotoAsset对象，读取文件的部分信息
      return await fetchResult.getFirstObject();
    } catch (error) {
      TKLog.error(`[TKPickerHelper]根据[${uri}]获取相册对象异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }
}

/**
 * 相机参数类
 */
export class TKCameraOptions {
  mediaTypes: Array<cameraPicker.PickerMediaType> =
    [cameraPicker.PickerMediaType.PHOTO, cameraPicker.PickerMediaType.VIDEO]; //媒体类型。
  cameraPosition: camera.CameraPosition = camera.CameraPosition.CAMERA_POSITION_BACK; //相机的位置。
  saveUri?: string; //保存配置信息的uri。
  videoDuration?: number; //录制的最大时长。
}

/**
 * 相册选择参数类
 */
export class TKPhotoSelectOptions {
  MIMEType?: photoAccessHelper.PhotoViewMIMETypes =
    photoAccessHelper.PhotoViewMIMETypes.IMAGE_TYPE; //可选择的媒体文件类型，若无此参数，则默认为图片和视频类型。
  maxSelectNumber?: number = TK_PHOTO_DEFAULT_SELECT_NUMBER; //选择媒体文件数量的最大值(默认值为50，最大值为500)。
  isPhotoTakingSupported?: boolean = true; //支持拍照。
  isEditSupported?: boolean = true; //支持编辑照片。
  isSearchSupported?: boolean = true; //支持搜索。
  recommendationOptions?: photoAccessHelper.RecommendationOptions; //支持照片推荐。
  preselectedUris?: Array<string>; //预选择图片的uri数据。
}

/**
 * 文档选择参数类
 */
export class TKDocumentSelectOptions {
  maxSelectNumber?: number = TK_PHOTO_DEFAULT_SELECT_NUMBER; //选择文件最大个数，上限500，有效值范围1-500（选择目录仅支持特定设备，且目录选择的最大个数为1）
  defaultFilePathUri?: string; //指定选择的文件或者目录路径
  fileSuffixFilters?: Array<string>; //选择文件的后缀类型，若选择项存在多个后缀名，则每一个后缀名之间用英文逗号进行分隔
  selectMode?: picker.DocumentSelectMode =
    0; //支持选择的资源类型，比如：文件、文件夹和二者混合，仅支持特定设备。系统能力： SystemCapability.FileManagement.UserFileService.FolderSelection
}