import { buildTKNormalDialog, TKNormalDialogOption } from '../../components/dialog/TKNormalDialog';
import { ComponentContent, promptAction } from '@kit.ArkUI';
import { TKContextHelper } from '../system/TKContextHelper';
import { buildTKMenuDialog, TKMenuDialogOption } from '../../components/dialog/TKMenuDialog';
import { TKWindowHelper } from './TKWindowHelper';
import { buildTKLoadingDialog, TKLoadingDialogOption } from '../../components/dialog/TKLoadingDialog';
import { buildTKProgressDialog, TKProgressDialogOption } from '../../components/dialog/TKProgressDialog';
import { buildTKPatternLock, TKPatternLockOption } from '../../components/patternlock/TKPatternLock';
import { TKObjectHelper } from '../data/TKObjectHelper';
import { buildTKBlurDialog, TKBlurDialogOption } from '../../components/dialog/TKBlurDialog';
import { buildTKToastDialog, TKToastDialogOption } from '../../components/dialog/TKToastDialog';
import { buildTKToastTipDialog, TKToastTipDialogOption } from '../../components/dialog/TKToastTipDialog';
import { buildTKKeyBoard, TKKeyBoardOption } from '../../components/keyboard/view/TKKeyBoard';
import { TKKeyBoardEventDelegate } from '../../components/keyboard/delegate/TKKeyBoardEventDelegate';
import { TKLog } from '../../util/logger/TKLog';


/**
 * Toast参数类
 */
export class TKSysToastOptions {
  duration?: number = 2000; //显示时长(1500ms-10000ms)
  bottom?: string | number = '80vp'; //距离屏幕底部的位置
  showMode?: number = 0; //是否显示在应用之上
  backgroundColor?: ResourceColor = "#DF3F3F3F";
  textColor?: ResourceColor = Color.White;
  backgroundBlurStyle?: BlurStyle = BlurStyle.NONE;
}

/**
 * 自定义Action
 */
export class TKDismissDialogAction<T extends Object> implements DismissDialogAction {
  private componentContent: TKComponentContent<T>;
  private dismissDialogAction: DismissDialogAction;

  public constructor(componentContent: TKComponentContent<T>, dismissDialogAction: DismissDialogAction) {
    this.componentContent = componentContent;
    this.dismissDialogAction = dismissDialogAction;
  }

  public dismiss(): void {
    this.componentContent.close();
  }

  public get reason(): DismissReason {
    return this.dismissDialogAction.reason;
  }
}

/**
 * 弹层自定义
 */
export class TKComponentContent<T extends Object> extends ComponentContent<T> {
  private operTime: Date = new Date();
  public isOpen: boolean = false;
  public options?: T;

  public constructor(builder: WrappedBuilder<[T]>, args: T) {
    super(TKContextHelper.getCurrentUIContext(), builder, args);
    this.options = args;
  }

  public open(options: promptAction.BaseDialogOptions = {} as promptAction.BaseDialogOptions) {
    try {
      this.operTime = new Date();
      this.isOpen = true;
      options = options ?? {} as promptAction.BaseDialogOptions;
      options.alignment = options.alignment ?? DialogAlignment.Center;
      options.autoCancel = options.autoCancel ?? false;
      options.isModal = options.isModal ?? true;
      options.maskColor = options.maskColor ?? Color.Transparent;
      options.keyboardAvoidMode = options.keyboardAvoidMode ?? KeyboardAvoidMode.NONE;
      let onWillDismiss = options.onWillDismiss;
      options.onWillDismiss = (dismissDialogAction: DismissDialogAction) => {
        let tkDismissDialogAction: TKDismissDialogAction<T> = new TKDismissDialogAction(this, dismissDialogAction);
        if (onWillDismiss) {
          onWillDismiss(tkDismissDialogAction);
        } else {
          if (tkDismissDialogAction.reason == DismissReason.TOUCH_OUTSIDE) {
            if (options.autoCancel) {
              tkDismissDialogAction.dismiss();
            }
          } else if (tkDismissDialogAction.reason == DismissReason.CLOSE_BUTTON) {
            tkDismissDialogAction.dismiss();
          }
        }
      };
      TKContextHelper.getCurrentUIContext().getPromptAction().openCustomDialog(this, options);
    } catch (error) {
      TKLog.error(`[TKComponentContent]弹出窗口异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  public update(args: T): void {
    this.reuse(args);
  }

  public reuse(args: T): void {
    try {
      this.operTime = new Date();
      this.options = args;
      super.reuse(args as Object);
    } catch (error) {
      TKLog.error(`[TKComponentContent]更新窗口异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  public destroy() {
    try {
      super.reuse({ "isDisappear": true });
    } catch (error) {
      TKLog.error(`[TKComponentContent]销毁窗口异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  public close() {
    try {
      let curTime = new Date();
      TKContextHelper.getCurrentUIContext().getPromptAction().closeCustomDialog(this);
      let t = setTimeout(() => {
        if (curTime.getTime() > this.operTime.getTime()) {
          this.destroy();
        }
        clearTimeout(t);
      }, 400);
      this.isOpen = false;
      this.options = undefined;
    } catch (error) {
      TKLog.error(`[TKComponentContent]关闭窗口异常，code: ${error.code}， message: ${error.message}`);
    }
  }
}

export namespace TKDialogHelper {

  const dialogMap: Map<Function, TKComponentContent<Object>> =
    new Map<Function, TKComponentContent<Object>>();

  /**
   * 通用弹窗处理
   * @param options
   * @param isCache 是否单例缓存弹框，默认是true
   * @param isFirstPriority 单例缓存模式下是否First优先，First优先的话，后面调用在前面弹框没有关闭时是不会进行覆盖的，默认是false
   * @returns
   */
  export function createCommonDialog<T extends Object>(builder: WrappedBuilder<[T]>,
    options?: T, isCache?: boolean, isFirstPriority?: boolean): TKComponentContent<T> {
    isCache = isCache ?? true;
    isFirstPriority = isFirstPriority ?? false;
    if (isCache) {
      let dialogContent: TKComponentContent<Object> | undefined =
        dialogMap.get(builder.builder);
      if (!dialogContent) {
        dialogContent =
          new TKComponentContent(builder, options ?? {} as Record<string, Object> as Object as T);
        dialogMap.set(builder.builder, dialogContent);
      } else {
        if (!dialogContent.isOpen || !isFirstPriority) {
          dialogContent.reuse(options ?? {} as Record<string, Object>);
        }
      }
      return dialogContent as TKComponentContent<T>;
    } else {
      let dialogContent: TKComponentContent<T> =
        new TKComponentContent(builder, options ?? {} as Record<string, Object> as Object as T);
      return dialogContent;
    }
  }

  /**
   * 弹层loading
   * @param options
   * @returns
   */
  export function createLoadingDialog(options: TKLoadingDialogOption = {}): TKComponentContent<TKLoadingDialogOption> {
    let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKLoadingDialog) as WrappedBuilder<[Object]>;
    let dialogContent: TKComponentContent<TKLoadingDialogOption> =
      createCommonDialog<TKLoadingDialogOption>(builder, options);
    return dialogContent;
  }

  /**
   * 弹层进度条
   * @param options
   * @returns
   */
  export function createProgressDialog(options: TKProgressDialogOption = {
  }): TKComponentContent<TKProgressDialogOption> {
    let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKProgressDialog) as WrappedBuilder<[Object]>;
    let dialogContent: TKComponentContent<TKProgressDialogOption> =
      createCommonDialog<TKProgressDialogOption>(builder, options);
    return dialogContent;
  }

  /**
   * 显示弹框
   * @param options
   */
  export function showAlertDialog(options: TKNormalDialogOption): TKComponentContent<TKNormalDialogOption> {
    let dialogContent: TKComponentContent<TKNormalDialogOption> | undefined = undefined;
    let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKNormalDialog) as WrappedBuilder<[Object]>;
    options = TKObjectHelper.fixDefault(options, TKNormalDialogOption);
    options.close = () => {
      dialogContent?.close();
      dialogContent = undefined;
    }
    dialogContent = createCommonDialog<TKNormalDialogOption>(builder, options);
    dialogContent.open({
      maskColor: ''
    });
    return dialogContent!;
  }

  /**
   * 列表选择弹窗
   * @param options
   */
  export function showActionSheet(options: TKMenuDialogOption): TKComponentContent<TKMenuDialogOption> {
    let dialogContent: TKComponentContent<TKMenuDialogOption> | undefined = undefined;
    let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKMenuDialog) as WrappedBuilder<[Object]>;
    options = TKObjectHelper.fixDefault(options, TKMenuDialogOption);
    options.close = () => {
      dialogContent?.close();
      dialogContent = undefined;
    }
    dialogContent = createCommonDialog<TKMenuDialogOption>(builder, options);
    dialogContent.open({
      autoCancel: true,
      alignment: DialogAlignment.Bottom,
      maskColor: '',
      offset: { dx: 0, dy: -px2vp(TKWindowHelper.getNavigationBottomBarHeightSync()) },
    })
    return dialogContent!;
  }

  /**
   * 弹出系统土司,默认时长为2s,距离底部默认为80vp
   * @param message 提示消息
   * @param options (显示时长、距离屏幕底部的位置、是否显示在应用之上)
   */
  export function showSysToast(message: string | Resource, options?: TKSysToastOptions) {
    if ((typeof message === 'string' && message.length > 0) || message) {
      options = options ?? new TKSysToastOptions();
      promptAction.showToast({
        message: message,
        duration: options.duration,
        bottom: options.bottom,
        showMode: options.showMode,
        backgroundColor: options.backgroundColor,
        textColor: options.textColor,
        backgroundBlurStyle: options.backgroundBlurStyle,
      })
    }
  }

  /**
   * 弹出土司,默认时长为2s,距离底部默认为80vp
   * @param message 提示消息
   * @param options (显示时长、距离屏幕底部的位置、是否显示在应用之上)
   */
  export function showToast(message: string | Resource, options: TKToastDialogOption = {}) {
    if ((typeof message === 'string' && message.length > 0) || message) {
      options = TKObjectHelper.fixDefault(options, TKToastDialogOption);
      options.message = message;
      options.fontColor = options.fontColor ?? Color.White;
      options.bottom = options.bottom ?? "80vp"
      let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKToastDialog) as WrappedBuilder<[Object]>;
      let dialogContent: TKComponentContent<TKToastDialogOption> =
        createCommonDialog<TKToastDialogOption>(builder, options);
      dialogContent.open({
        isModal: false,
        alignment: DialogAlignment.Bottom,
        offset: { dx: 0, dy: `-${options.bottom}` },
      })
      let toastId = setTimeout(() => {
        dialogContent.close();
        clearTimeout(toastId);
      }, options.duration);
    }
  }

  /**
   * 弹出带图标的吐司,默认时长为2s，居中展示
   * @param message 提示消息
   * @param options
   */
  export function showTipToast(message: string | Resource, options: TKToastTipDialogOption = {}) {
    if ((typeof message === 'string' && message.length > 0) || message) {
      options = TKObjectHelper.fixDefault(options, TKToastTipDialogOption);
      options.message = message;
      options.imageRes = options.imageRes ?? $r("app.media.tk_framework_tip_ok");
      let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKToastTipDialog) as WrappedBuilder<[Object]>;
      let dialogContent: TKComponentContent<TKToastTipDialogOption> =
        createCommonDialog<TKToastTipDialogOption>(builder, options);
      dialogContent.open({
        isModal: false,
      });
      let toastId = setTimeout(() => {
        dialogContent.close();
        clearTimeout(toastId);
      }, options.duration);
    }
  }

  /**
   * 弹出带图标的成功吐司,默认时长为2s，居中展示
   * @param message 提示消息
   * @param options
   */
  export function showSuccessToast(message: string | Resource, options: TKToastTipDialogOption = {}) {
    options = TKObjectHelper.fixDefault(options, TKToastTipDialogOption);
    options.imageRes = options.imageRes ?? $r("app.media.tk_framework_tip_ok");
    options.fontColor = options.fontColor ?? Color.White;
    showTipToast(message, options);
  }

  /**
   * 弹出带图标的信息吐司,默认时长为2s，居中展示
   * @param message 提示消息
   * @param options
   */
  export function showInfoToast(message: string | Resource, options: TKToastTipDialogOption = {}) {
    options = TKObjectHelper.fixDefault(options, TKToastTipDialogOption);
    options.imageRes = options.imageRes ?? $r("app.media.tk_framework_tip_info");
    options.fontColor = options.fontColor ?? Color.White;
    showTipToast(message, options);
  }

  /**
   * 弹出带图标的错误吐司,默认时长为2s，居中展示
   * @param message 提示消息
   * @param options
   */
  export function showErrorToast(message: string | Resource, options: TKToastTipDialogOption = {}) {
    options = TKObjectHelper.fixDefault(options, TKToastTipDialogOption);
    options.imageRes = options.imageRes ?? $r("app.media.tk_framework_tip_warn");
    options.fontColor = options.fontColor ?? Color.Red;
    showTipToast(message, options);
  }

  /**
   * 模糊背景
   * @param options
   * @returns
   */
  export function createBlurBackground(options: TKBlurDialogOption = {}): TKComponentContent<TKBlurDialogOption> {
    let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKBlurDialog) as WrappedBuilder<[Object]>;
    let dialogContent: TKComponentContent<TKBlurDialogOption> =
      createCommonDialog<TKBlurDialogOption>(builder, options);
    return dialogContent;
  }

  /**
   * 创建手势密码
   * @param options
   */
  export function createPatternLock(options: TKPatternLockOption): TKComponentContent<TKPatternLockOption> {
    let dialogContent: TKComponentContent<TKPatternLockOption> | undefined = undefined;
    let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKPatternLock) as WrappedBuilder<[Object]>;
    options = TKObjectHelper.fixDefault(options, TKPatternLockOption);
    options.close = () => {
      dialogContent?.close();
      dialogContent = undefined;
    }
    dialogContent = createCommonDialog<TKPatternLockOption>(builder, options);
    return dialogContent!;
  }

  /**
   * 打开自定义键盘
   * @param options
   * @returns
   */
  export function showKeyBoard(options: TKKeyBoardOption): TKComponentContent<TKKeyBoardOption> {
    let dialogContent: TKComponentContent<TKKeyBoardOption> | undefined = undefined;
    let builder: WrappedBuilder<[Object]> = wrapBuilder(buildTKKeyBoard) as WrappedBuilder<[Object]>;
    options = TKObjectHelper.fixDefault(options, TKKeyBoardOption);
    options.delegate = options.delegate ?? {} as TKKeyBoardEventDelegate;
    let hideKeyBoard = options.delegate?.hideKeyBoard;
    options.delegate.hideKeyBoard = () => {
      dialogContent?.close();
      dialogContent = undefined;
      if (hideKeyBoard) {
        hideKeyBoard();
      }
    }
    dialogContent = createCommonDialog<TKKeyBoardOption>(builder, options);
    dialogContent.open({
      isModal: false,
      autoCancel: true,
      alignment: DialogAlignment.Bottom,
      transition: TransitionEffect.OPACITY.animation({ duration: 200 }).combine(TransitionEffect.translate({
        y: 260
      })),
      onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
        dismissDialogAction.dismiss();
      }
    })
    return dialogContent!;
  }

  /**
   * 关闭自定义键盘
   */
  export function closeKeyBoard() {
    let dialogContent: TKComponentContent<Object> | undefined =
      dialogMap.get(buildTKKeyBoard);
    if (dialogContent && dialogContent.isOpen) {
      dialogContent.close();
    }
  }
}