import { TKFileHelper } from '../file/TKFileHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKSystemHelper } from '../system/TKSystemHelper';
import { buffer } from '@kit.ArkTS';
import { TKDataHelper } from '../data/TKDataHelper';

/**
 * Web浏览器帮组类
 */
export namespace TKWebHelper {
  /**
   * 获取本地的地址
   * @param url
   * @returns
   */
  export function getLocalFilePath(url: string): string {
    let saveName: string = TKSystemHelper.getConfig("update.saveName", "www.zip");
    let www: string = TKStringHelper.replace(saveName, ".zip", "");
    let resFile: string = url.split("?")[0];
    resFile = resFile.replace(getLocalDomain(), "");
    resFile = resFile.split("#")[0];
    let lastResFile: string = "";
    if (TKSystemHelper.getConfig("webViewPool.htmlLoadMode") == "1") {
      lastResFile = TKFileHelper.resourceDir() + "/thinkive/" + www + resFile;
    } else {
      lastResFile = TKFileHelper.fileDir() + "/thinkive/" + www + resFile;
      if (!TKFileHelper.isFileExists(lastResFile)) {
        lastResFile = TKFileHelper.resourceDir() + "/thinkive/" + www + resFile;
      }
    }
    return lastResFile;
  }

  /**
   * 获取本地文件数据
   * @param filePath
   * @returns
   */
  export function getLocalFileData(filePath: string, mimeType: string): string | ArrayBuffer {
    if (isTextMIMEType(mimeType)) {
      return TKDataHelper.uint8ArrayToString(TKFileHelper.readFile(filePath));
    } else {
      return buffer.from(TKFileHelper.readFile(filePath)).buffer;
    }
  }

  /**
   * 获取本地域名
   * @returns
   */
  export function getLocalDomain(): string {
    let localDomain: string =
      TKSystemHelper.getConfig("webViewPool.localDomain", "http://www.thinkivelocalharmony.com");
    if (localDomain.endsWith("/")) {
      localDomain = localDomain.substring(0, localDomain.length - 1);
    }
    return localDomain;
  }

  /**
   * mimeType类型定义
   */
  const TKMIMETypeMap: Record<string, string> = {
    "apk": "application/vnd.android.package-archive",
    "html": "text/html",
    "htm": "text/html",
    "shtml": "text/html",
    "css": "text/css",
    "xml": "text/xml",
    "gif": "image/gif",
    "jpeg": "image/jpeg",
    "jpg": "image/jpeg",
    "js": "application/javascript",
    "atom": "application/atom+xml",
    "rss": "application/rss+xml",
    "mml": "text/mathml",
    "txt": "text/plain",
    "jad": "text/vnd.sun.j2me.app-descriptor",
    "wml": "text/vnd.wap.wml",
    "htc": "text/x-component",
    "png": "image/png",
    "svg": "image/svg+xml",
    "svgz": "image/svg+xml",
    "tif": "image/tiff",
    "tiff": "image/tiff",
    "wbmp": "image/vnd.wap.wbmp",
    "webp": "image/webp",
    "ico": "image/x-icon",
    "jng": "image/x-jng",
    "bmp": "image/x-ms-bmp",
    "woff": "font/woff",
    "woff2": "font/woff2",
    "jar": "application/java-archive",
    "war": "application/java-archive",
    "ear": "application/java-archive",
    "json": "application/json",
    "hqx": "application/mac-binhex40",
    "doc": "application/msword",
    "pdf": "application/pdf",
    "ps": "application/postscript",
    "eps": "application/postscript",
    "ai": "application/postscript",
    "rtf": "application/rtf",
    "m3u8": "application/vnd.apple.mpegurl",
    "kml": "application/vnd.google-earth.kml+xml",
    "kmz": "application/vnd.google-earth.kmz",
    "xls": "application/vnd.ms-excel",
    "eot": "application/vnd.ms-fontobject",
    "ppt": "application/vnd.ms-powerpoint",
    "odg": "application/vnd.oasis.opendocument.graphics",
    "odp": "application/vnd.oasis.opendocument.presentation",
    "ods": "application/vnd.oasis.opendocument.spreadsheet",
    "odt": "application/vnd.oasis.opendocument.text",
    "pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "wmlc": "application/vnd.wap.wmlc",
    "7z": "application/x-7z-compressed",
    "cco": "application/x-cocoa",
    "jardiff": "application/x-java-archive-diff",
    "jnlp": "application/x-java-jnlp-file",
    "run": "application/x-makeself",
    "pl": "application/x-perl",
    "pm": "application/x-perl",
    "prc": "application/x-pilot",
    "pdb": "application/x-pilot",
    "rar": "application/x-rar-compressed",
    "rpm": "application/x-redhat-package-manager",
    "sea": "application/x-sea",
    "swf": "application/x-shockwave-flash",
    "sit": "application/x-stuffit",
    "tcl": "application/x-tcl",
    "tk": "application/x-tcl",
    "der": "application/x-x509-ca-cert",
    "pem": "application/x-x509-ca-cert",
    "crt": "application/x-x509-ca-cert",
    "xpi": "application/x-xpinstall",
    "xhtml": "application/xhtml+xml",
    "xspf": "application/xspf+xml",
    "zip": "application/zip",
    "bin": "application/octet-stream",
    "exe": "application/octet-stream",
    "dll": "application/octet-stream",
    "deb": "application/octet-stream",
    "dmg": "application/octet-stream",
    "iso": "application/octet-stream",
    "img": "application/octet-stream",
    "msi": "application/octet-stream",
    "msp": "application/octet-stream",
    "mid": "audio/midi",
    "midi": "audio/midi",
    "kar": "audio/midi",
    "mp3": "audio/mpeg",
    "ogg": "audio/ogg",
    "m4a": "audio/x-m4a",
    "ra": "audio/x-realaudio",
    "3gpp": "video/3gpp",
    "3gp": "video/3gpp",
    "ts": "video/mp2t",
    "mp4": "video/mp4",
    "mpeg": "video/mpeg",
    "mpg": "video/mpeg",
    "mov": "video/quicktime",
    "webm": "video/webm",
    "flv": "video/x-flv",
    "m4v": "video/x-m4v",
    "mng": "video/x-mng",
    "asx": "video/x-ms-asf",
    "asf": "video/x-ms-asf",
    "wmv": "video/x-ms-wmv",
    "avi": "video/x-msvideo"
  }

  /**
   * 获取路径后缀，得到文件类型
   */
  export function getUrlPathSuffix(url: string) {
    url = url.split("?")[0];
    let paths: Array<string> = url.split("/");
    let path: string = paths[paths.length-1];
    let suffixes: Array<string> = path.split(".");
    if (suffixes.length >= 2) {
      let suffix: string = suffixes[suffixes.length-1];
      suffix = suffix.split("#")[0];
      return suffix;
    }
    return "";
  }

  /**
   * 获取文件MIME类型
   * @param suffix
   * @returns
   */
  export function getMIMEType(suffix: string): string {
    return TKMIMETypeMap[suffix.toLowerCase()] ?? "application/octet-stream";
  }

  /**
   * 判断是否文本文件
   * @param mimeType
   * @returns
   */
  export function isTextMIMEType(mimeType: string): boolean {
    let textMIMETypes: Array<string> = new Array<string>();
    textMIMETypes.push("application/x-javascript");
    textMIMETypes.push("application/javascript");
    textMIMETypes.push("application/json");
    textMIMETypes.push("application/xhtml+xml");
    textMIMETypes.push("application/xspf+xml");
    textMIMETypes.push("application/atom+xml");
    textMIMETypes.push("application/rss+xml");
    mimeType = mimeType.toLowerCase();
    if (textMIMETypes.includes(mimeType) || mimeType.startsWith("text/")) {
      return true;
    } else {
      return false;
    }
  }
}