/**
 * 图片相关工具类
 */
import { image } from '@kit.ImageKit';
import { TKBase64Helper } from '../crypto/TKBase64Helper';
import { buffer } from '@kit.ArkTS';
import { TKLog } from '../logger/TKLog';
import { TKFileHelper } from '../file/TKFileHelper';
import { resourceManager } from '@kit.LocalizationKit';
import { TKContextHelper } from '../system/TKContextHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKDeviceHelper } from '../dev/TKDeviceHelper';
import { TKSystemHelper } from '../system/TKSystemHelper';

export namespace TKImageHelper {

  /**
   * 缩放图片大小
   * @param imageData
   * @param width
   * @param height
   * @returns
   */
  export async function scaleImageDataSize(imageData: ArrayBuffer, width: number,
    height: number): Promise<ArrayBuffer> {
    try {
      let source: image.ImageSource = image.createImageSource(imageData);
      let info: image.ImageInfo = await source.getImageInfo();
      let pixelMap: PixelMap = await source.createPixelMap();
      if (width > 0 && height > 0 && info.size.width > width || info.size.height > height) {
        let scaleNum: number = 1;
        let heightRatio: number = height / info.size.height;
        let widthRatio: number = width / info.size.width;
        scaleNum = heightRatio < widthRatio ? widthRatio : heightRatio;
        await pixelMap.scale(scaleNum, scaleNum);
      }
      let packOpts: image.PackingOption = { format: "image/jpeg", quality: 100 };
      const arrayBuffer: ArrayBuffer = await packingFromPixelMap(pixelMap, packOpts);
      return arrayBuffer;
    } catch (error) {
      TKLog.error(`[TKImageHelper]缩放图片大小异常，code: ${error.code}， message: ${error.message}`);
      return imageData;
    }
  }

  /**
   * 压缩图片质量
   * @param imageData
   * @param quality
   * @returns
   */
  export async function compressImageDataQuality(imageData: ArrayBuffer, quality: number): Promise<ArrayBuffer> {
    try {
      let source: image.ImageSource = image.createImageSource(imageData);
      let pixelMap: PixelMap = await source.createPixelMap();
      let packOpts: image.PackingOption = { format: "image/jpeg", quality: quality };
      const arrayBuffer: ArrayBuffer = await packingFromPixelMap(pixelMap, packOpts);
      return arrayBuffer;
    } catch (error) {
      TKLog.error(`[TKImageHelper]压缩图片质量异常，code: ${error.code}， message: ${error.message}`);
      return imageData;
    }
  }

  /**
   * 压缩图片大小
   * @param imageData
   * @param maxSize
   * @returns
   */
  export async function compressImageDataSize(imageData: ArrayBuffer, maxSize: number): Promise<ArrayBuffer> {
    try {
      //原图大小超过范围，先进行“压处理”，这里 压缩比 采用二分法进行处理，6次二分后的最小压缩比是0.015625，已经够小了
      let max: number = 100;
      let min: number = 0;
      let quality: number = 100;
      let data: ArrayBuffer = imageData;
      //首先判断原图大小是否在要求内，如果满足要求则不进行压缩，over
      if (data.byteLength < maxSize) {
        return data;
      }
      for (let i = 0; i < 6; i++) {
        quality = (max + min) / 2;
        data = await compressImageDataQuality(data, quality);
        if (data.byteLength < maxSize * 0.9) {
          min = quality;
        } else if (data.byteLength > maxSize) {
          max = quality;
        } else {
          break;
        }
      }
      //判断“压处理”的结果是否符合要求，符合要求就over
      if (data.byteLength < maxSize) {
        return data;
      }
      //缩处理，直接用大小的比例作为缩处理的比例进行处理，因为有取整处理，所以一般是需要两次处理
      let lastDataLength: number = 0;
      for (let i = 0; i < 6; i++) {
        if (data.byteLength > maxSize && data.byteLength != lastDataLength) {
          lastDataLength = data.byteLength;
          //获取处理后的尺寸
          let scaleNum: number = Math.sqrt(maxSize / data.byteLength);
          let source: image.ImageSource = image.createImageSource(data);
          let pixelMap: PixelMap = await source.createPixelMap();
          await pixelMap.scale(scaleNum, scaleNum);
          let packOpts: image.PackingOption = { format: "image/jpeg", quality: quality };
          data = await packingFromPixelMap(pixelMap, packOpts);
        } else {
          break;
        }
      }
      return data;
    } catch (error) {
      TKLog.error(`[TKImageHelper]压缩图片大小异常，code: ${error.code}， message: ${error.message}`);
      return imageData;
    }
  }

  /**
   * 图片base64字符串转PixelMap
   * @param base64 图片base64字符串
   * @returns
   */
  export async function base64ToPixelMap(base64: string): Promise<image.PixelMap | undefined> {
    try {
      //将原始图片base64字符串转变为通过base64字符串
      const reg: RegExp = new RegExp('data:image/\\w+;base64,');
      const base64Str: string = base64.startsWith("data:image") ? base64.replace(reg, '') : base64;
      //将通用base64字符串转变为arrayBuffer
      let arrayBuffer: ArrayBuffer = buffer.from(TKBase64Helper.dataWithBase64Decode(base64Str)).buffer;
      //将arrayBuffer转变为pixelMap
      let imageSource: image.ImageSource = image.createImageSource(arrayBuffer);
      let opts: image.DecodingOptions = { editable: false };
      //注意：这里return的是Promise，因此使用时需要在业务侧拿到最终的PixelMap
      return await imageSource.createPixelMap(opts);
    } catch (error) {
      TKLog.error(`[TKImageHelper]图片base64字符串转PixelMap异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   * PixelMap转图片base64字符串
   * @param pixelMap
   * @param format 目标格式,默认png,当前只支持jpg、webp和png。当传入的格式与文件格式不匹配，可能会导致生成错误的Base64字符串。
   * @returns
   */
  export async function pixelMapToBase64Str(pixelMap: image.PixelMap, format: string = 'image/jpeg'): Promise<string> {
    try {
      let packOpts: image.PackingOption = { format: format, quality: 100 };
      const arrayBuffer: ArrayBuffer = await packingFromPixelMap(pixelMap, packOpts);
      let base64Str: string = TKBase64Helper.stringWithBase64Encode(new Uint8Array(arrayBuffer));
      let headStr: string = `data:${format};base64,`;
      if (!base64Str.startsWith(headStr)) {
        base64Str = headStr + base64Str;
      }
      return base64Str;
    } catch (error) {
      TKLog.error(`[TKImageHelper]PixelMap转图片base64字符串异常，code: ${error.code}， message: ${error.message}`);
      return '';
    }
  }

  /**
   * 二进制转PixelMap
   * @param ArrayBuffer
   * @returns
   */
  export async function arrayBufferToPixelMap(arrayBuffer: ArrayBuffer): Promise<image.PixelMap | undefined> {
    try {
      //将arrayBuffer转变为pixelMap
      let imageSource: image.ImageSource = image.createImageSource(arrayBuffer);
      let opts: image.DecodingOptions = { editable: false };
      //注意：这里return的是Promise，因此使用时需要在业务侧拿到最终的PixelMap
      return await imageSource.createPixelMap(opts);
    } catch (error) {
      TKLog.error(`[TKImageHelper]二进制转PixelMap异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   * PixelMap转二进制
   * @param pixelMap
   * @param format 目标格式,默认png,当前只支持jpg、webp和png
   * @returns
   */
  export async function pixelMapToArrayBuffer(pixelMap: image.PixelMap,
    format: string = 'image/jpeg'): Promise<ArrayBuffer> {
    try {
      let packOpts: image.PackingOption = { format: format, quality: 100 };
      const arrayBuffer: ArrayBuffer = await packingFromPixelMap(pixelMap, packOpts);
      return arrayBuffer;
    } catch (error) {
      TKLog.error(`[TKImageHelper]PixelMap转二进制异常，code: ${error.code}， message: ${error.message}`);
      return new ArrayBuffer(0);
    }
  }

  /**
   * 保存pixelMap到本地
   * @param pixelMap PixelMap
   * @param path 文件夹路径
   * @param name 文件名
   * @param format 目标格式。默认png。当前只支持jpg（image/jpeg）、webp和png（image/png）。
   * @returns
   */
  export async function savePixelMap(pixelMap: image.PixelMap, path: string, name: string,
    format: string = 'image/jpeg'): Promise<string> {
    try {
      if (!TKFileHelper.isFileExists(path)) {
        TKFileHelper.createFileDir(path); //如果文件夹不存在就创建
      }
      let filePath: string = path + "/" + name;
      let file = TKFileHelper.openFile(filePath);
      let packOpts: image.PackingOption = { format: format, quality: 100 };
      await packToFileFromPixelMap(pixelMap, file.fd, packOpts);
      TKFileHelper.closeFile(file.fd);
      return filePath;
    } catch (error) {
      TKLog.error(`[TKImageHelper]保存pixelMap到本地异常，code: ${error.code}， message: ${error.message}`);
      return '';
    }
  }

  /**
   * 保存ImageSource到本地
   * @param pixelMap PixelMap
   * @param path 文件夹路径
   * @param name 文件名
   * @param format 目标格式。默认png。当前只支持jpg（image/jpeg）、webp和png（image/png）。
   * @returns
   */
  export async function saveImageSource(source: image.ImageSource, path: string, name: string,
    format: string = 'image/jpeg'): Promise<string> {
    try {
      if (!TKFileHelper.isFileExists(path)) {
        TKFileHelper.createFileDir(path); //如果文件夹不存在就创建
      }
      let filePath: string = path + "/" + name;
      let file = TKFileHelper.openFile(filePath);
      let packOpts: image.PackingOption = { format: format, quality: 100 };
      await packToFileFromImageSource(source, file.fd, packOpts);
      TKFileHelper.closeFile(file.fd);
      return filePath;
    } catch (error) {
      TKLog.error(`[TKImageHelper]保存ImageSource到本地异常，code: ${error.code}， message: ${error.message}`);
      return '';
    }
  }

  /**
   * 创建图片源实例
   * @param src（联合类型: string、number、ArrayBuffer、resourceManager.RawFileDescriptor）
   *   path string 图片路径，当前仅支持应用沙箱路径。当前支持格式有：.jpg .png .gif .bmp .webp RAW SVG10+ .ico11+。
   *   fd  number 文件描述符fd。
   *   buf  ArrayBuffer  图像缓冲区数组。
   *   rawfile resourceManager.RawFileDescriptor 图像资源文件的RawFileDescriptor。
   * options SourceOptions  图片属性，包括图片像素密度、像素格式和图片尺寸。
   *   sourceDensity  number ImageSource的密度。
   *   sourcePixelFormat  PixelMapFormat 图片像素格式。
   *   sourceSize  Size 图像像素大小。
   * @returns
   */
  export function createImageSource(src: string | number | ArrayBuffer | resourceManager.RawFileDescriptor,
    options?: image.SourceOptions): image.ImageSource {
    if (typeof src === 'string') {
      if (options) {
        return image.createImageSource(src, options);
      } else {
        return image.createImageSource(src);
      }
    } else if (typeof src === 'number') {
      if (options) {
        return image.createImageSource(src, options);
      } else {
        return image.createImageSource(src);
      }
    } else if (src instanceof ArrayBuffer) {
      if (options) {
        return image.createImageSource(src, options);
      } else {
        return image.createImageSource(src);
      }
    } else {
      if (options) {
        return image.createImageSource(src, options);
      } else {
        return image.createImageSource(src);
      }
    }
  }

  /**
   * 以增量的方式创建图片源实例
   * @param buf ArrayBuffer  增量数据
   * @param options SourceOptions  图片属性，包括图片像素密度、像素格式和图片尺寸。
   *   sourceDensity  number ImageSource的密度。
   *   sourcePixelFormat  PixelMapFormat 图片像素格式。
   *   sourceSize  Size 图像像素大小。
   * @returns
   */
  export function createIncrementalSource(buf: ArrayBuffer, options?: image.SourceOptions): image.ImageSource {
    if (options) {
      return image.CreateIncrementalSource(buf, options);
    } else {
      return image.CreateIncrementalSource(buf);
    }
  }

  /**
   * 图片压缩或重新打包，使用Promise形式返回结果。
   * @param source PixelMap-打包的PixelMap资源。
   * @param options 设置打包参数:
   *   format 目标格式。当前只支持jpg（image/jpeg）、webp 和 png（image/png）。
   *   quality JPEG编码中设定输出图片质量的参数，取值范围为0-100。
   *   bufferSize 接收编码数据的缓冲区大小，单位为Byte。默认为10MB。bufferSize需大于编码后图片大小。
   * @returns
   */
  export function packingFromPixelMap(source: image.PixelMap, options: image.PackingOption): Promise<ArrayBuffer> {
    const imagePacker: image.ImagePacker = image.createImagePacker();
    return imagePacker.packing(source, options).finally(() => {
      imagePacker.release(); //释放
    });
  }

  /**
   * 图片压缩或重新打包，使用Promise形式返回结果。
   * @param source ImageSource-打包的图片源。
   * @param options 设置打包参数:
   *   format 目标格式。当前只支持jpg（image/jpeg）、webp 和 png（image/png）。
   *   quality JPEG编码中设定输出图片质量的参数，取值范围为0-100。
   *   bufferSize 接收编码数据的缓冲区大小，单位为Byte。默认为10MB。bufferSize需大于编码后图片大小。
   * @returns
   */
  export function packingFromImageSource(source: image.ImageSource,
    options: image.PackingOption): Promise<ArrayBuffer> {
    const imagePacker: image.ImagePacker = image.createImagePacker();
    return imagePacker.packing(source as image.ImageSource, options).finally(() => {
      imagePacker.release(); //释放
    });
  }

  /**
   * 将PixelMap图片源编码后直接打包进文件。
   * @param source PixelMap-打包的PixelMap资源。
   * @param fd 文件描述符。
   * @param option 设置打包参数:
   *   format 目标格式。当前只支持jpg（image/jpeg）、webp 和 png（image/png）。
   *   quality JPEG编码中设定输出图片质量的参数，取值范围为0-100。
   *   bufferSize 接收编码数据的缓冲区大小，单位为Byte。默认为10MB。bufferSize需大于编码后图片大小。
   * @returns
   */
  export function packToFileFromPixelMap(source: image.PixelMap, fd: number,
    options: image.PackingOption): Promise<void> {
    const imagePacker: image.ImagePacker = image.createImagePacker();
    return imagePacker.packToFile(source, fd, options).finally(() => {
      imagePacker.release(); //释放
    });
  }

  /**
   * 将ImageSource图片源编码后直接打包进文件。
   * @param source ImageSource-打包的图片源。
   * @param fd 文件描述符。
   * @param option 设置打包参数:
   *   format 目标格式。当前只支持jpg（image/jpeg）、webp 和 png（image/png）。
   *   quality JPEG编码中设定输出图片质量的参数，取值范围为0-100。
   *   bufferSize 接收编码数据的缓冲区大小，单位为Byte。默认为10MB。bufferSize需大于编码后图片大小。
   * @returns
   */
  export function packToFileFromImageSource(source: image.ImageSource, fd: number,
    options: image.PackingOption): Promise<void> {
    const imagePacker: image.ImagePacker = image.createImagePacker();
    return imagePacker.packToFile(source, fd, options).finally(() => {
      imagePacker.release(); //释放
    });
  }

  /**
   * 用户获取resource目录下的media中的图片PixelMap
   * @param resource 例如：$r("app.media.icon")
   * @returns
   */
  export async function getPixelMapFromResource(resource: ResourceStr): Promise<image.PixelMap> {
    let unit8Array: Uint8Array = new Uint8Array();
    if (typeof resource == 'string') {
      unit8Array = TKFileHelper.readFile(resource);
    } else {
      if (resource.type == 30000 || resource.id == 0) {
        //$rawfile()写法的 resource
        unit8Array =
          await (await TKContextHelper.getCurHspModuleContext()).resourceManager.getRawFileContent(resource.params?.[0]);
      } else {
        //$r()写法的resource
        let resName: string = resource.params && resource.params.length > 0 ? resource.params[0] : "";
        if (TKStringHelper.isNotBlank(resName)) {
          unit8Array = await getMediaByName(resName);
        } else {
          unit8Array = await (await TKContextHelper.getCurHspModuleContext()).resourceManager.getMediaContent(resource);
        }
      }
    }
    let imageSource = image.createImageSource(unit8Array.buffer.slice(0, unit8Array.buffer.byteLength));
    let createPixelMap: image.PixelMap = await imageSource.createPixelMap({
      desiredPixelFormat: image.PixelMapFormat.RGBA_8888
    });
    await imageSource.release();
    return createPixelMap;
  }

  /**
   * 获取指定资源的GIF图片
   * @param resource
   * @returns
   */
  export async function getPixmapListFromResource(resource: ResourceStr): Promise<Array<image.PixelMap>> {
    let unit8Array: Uint8Array = new Uint8Array();
    if (typeof resource == 'string') {
      unit8Array = TKFileHelper.readFile(resource);
    } else {
      if (resource.type == 30000 || resource.id == 0) {
        //$rawfile()写法的 resource
        unit8Array =
          await (await TKContextHelper.getCurHspModuleContext()).resourceManager.getRawFileContent(resource.params?.[0]);
      } else {
        //$r()写法的resource
        let resName: string = resource.params && resource.params.length > 0 ? resource.params[0] : "";
        if (TKStringHelper.isNotBlank(resName)) {
          unit8Array = await getMediaByName(resName);
        } else {
          unit8Array = await (await TKContextHelper.getCurHspModuleContext()).resourceManager.getMediaContent(resource);
        }
      }
    }
    let imageSource = image.createImageSource(unit8Array.buffer.slice(0, unit8Array.buffer.byteLength));
    let createPixelMap: Array<image.PixelMap> = await imageSource.createPixelMapList({
      desiredPixelFormat: image.PixelMapFormat.RGBA_8888
    });
    await imageSource.release();
    return createPixelMap;
  }

  /**
   * 获取$r的资源内容
   * @param resName
   * @returns
   */
  export async function getMediaByName(resName: string): Promise<Uint8Array> {
    resName = resName.replaceAll("app.media.", "");
    return new Promise<Uint8Array>((resolve, reject) => {
      TKContextHelper.getCurHspModuleContext().then((context) => {
        context.resourceManager.getMediaByName(resName, (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        });
      });
    });
  }

  /**
   * 获取图片资源
   * @param value $r(icon) $rawfile(icon.png)
   * @returns
   */
  export function getImageResource(value: string): Resource {
    let imageResource: Resource | undefined = undefined;
    if (value.startsWith("$r(")) {
      value = value.substring("$r(".length, value.length - 1).trim();
      let resBundleModule: string = "";
      let resPath: string = value;
      if (TKStringHelper.startsWith(resPath, "@bundle:")) {
        let resPaths: Array<string> = TKStringHelper.split(TKStringHelper.replace(resPath, "@bundle:", ""), ":");
        resBundleModule = resPaths[0];
        resPath = resPaths[1];
      }
      let imagePath: string = resPath.split(".")[0];
      imageResource = $r(`app.media.${imagePath}`);
      if (TKStringHelper.isNotBlank(resBundleModule)) {
        imageResource.bundleName = TKSystemHelper.getAppIdentifier();
        imageResource.moduleName = resBundleModule;
      }
    } else if (value.startsWith("$rawfile(")) {
      value = value.substring("$rawfile(".length, value.length - 1).trim();
      let resBundleModule: string = "";
      let resPath: string = value;
      if (TKStringHelper.startsWith(resPath, "@bundle:")) {
        let resPaths: Array<string> = TKStringHelper.split(TKStringHelper.replace(resPath, "@bundle:", ""), ":");
        resBundleModule = resPaths[0];
        resPath = resPaths[1];
      }
      if (resPath.indexOf(".") < 0) {
        resPath += ".png";
      }
      let imageDir: string = TKDeviceHelper.getScreenDensityImageDir();
      let imagePath: string | undefined = undefined;
      if (TKStringHelper.isNotBlank(imageDir)) {
        imagePath = `thinkive/res/image/${imageDir}/${resPath}`;
        imageResource = $rawfile(imagePath);
        if (TKStringHelper.isNotBlank(resBundleModule)) {
          imageResource.bundleName = TKSystemHelper.getAppIdentifier();
          imageResource.moduleName = resBundleModule;
          imagePath = `@bundle:${resBundleModule}:${imagePath}`;
        }
      }
      if (!imageResource || TKFileHelper.readFile(imagePath!).length <= 0) {
        imagePath = `thinkive/res/image/${resPath}`;
        imageResource = $rawfile(imagePath);
        if (TKStringHelper.isNotBlank(resBundleModule)) {
          imageResource.bundleName = TKSystemHelper.getAppIdentifier();
          imageResource.moduleName = resBundleModule;
        }
      }
    }
    return imageResource as Resource;
  }

}