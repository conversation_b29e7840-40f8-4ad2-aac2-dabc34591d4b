import { image } from '@kit.ImageKit';
import { componentSnapshot, window } from '@kit.ArkUI';
import { TKWindowHelper } from './TKWindowHelper';

/**
 * 截屏工具类
 */
export namespace TKSnapshotHelper {

  /**
   * 获取已加载的组件的截图，传入组件的组件id，找到对应组件进行截图。通过Promise返回结果。
   * @param id 目标组件的组件标识,组件的唯一标识id
   * @returns
   */
  export function snapshotComponent(id: string): Promise<image.PixelMap> {
    return componentSnapshot.get(id);
  }

  /**
   * 在应用后台渲染CustomBuilder自定义组件，并输出其截图。通过Promise返回结果并支持获取离屏组件绘制区域坐标和大小。
   * @param builder 自定义组件构建函数。
   * @returns
   */
  export function snapshotBuilder(builder: CustomBuilder): Promise<image.PixelMap> {
    return componentSnapshot.createFromBuilder(builder);
  }

  /**
   * 获取窗口截图，使用Promise异步回调。
   * @param windowClass
   * @returns
   */
  export async function snapshotWindow(windowClass?: window.Window): Promise<image.PixelMap> {
    windowClass = windowClass ?? await TKWindowHelper.getLastWindow();
    return await windowClass.snapshot();
  }

}