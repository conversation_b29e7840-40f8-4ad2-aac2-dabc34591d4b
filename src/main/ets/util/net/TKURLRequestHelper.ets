import { TKCookieManager } from '../../base/mvc/service/dao/http/client/cookie/TKCookieManager';
import { TKDataHelper } from '../data/TKDataHelper';
import { TKObjectHelper } from '../data/TKObjectHelper';
import { TKLog } from '../logger/TKLog';
import { TKMapHelper } from '../map/TKMapHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKNetHelper } from './TKNetHelper';
import { buffer } from '@kit.ArkTS';
import { rcp } from '@kit.RemoteCommunicationKit';
import { BusinessError } from '@kit.BasicServicesKit';

/**
 * 请求内容格式
 */
export enum TKURLContentType {
  /**
   *  默认自动适配模式
   */
  NONE,

  /**
   *  application/x-www-form-urlencoded 类型
   */
  WWW_FORM,

  /**
   *  multipart/form-data 类型
   */
  FORM_DATA,

  /**
   *  application/json 类型
   */
  JSON
}
;

/**
 * 请求对象
 */
export class TKURLRequestVO {
  /**
   *  请求的地址
   */
  url: string = "";
  /**
   * 是否进行URL编码
   */
  isEncodeUrl?: boolean = true;
  /**
   *  是否保持原始的参数格式
   */
  isKeepOriginalParam?: boolean = false;
  /**
   * 请求入参，文件的key以@@F结尾
   */
  param?: Record<string, Object> = {};
  /**
   * 请求头
   */
  header?: Record<string, Object> = {};
  /**
   * 请求超时时间
   */
  timeout?: number = 60;
  /**
   * 请求方法，不需要传递，会自动填充
   */
  method?: string;
  /**
   * 请求内容格式
   */
  contentType?: TKURLContentType = TKURLContentType.NONE;
}

/**
 * 响应对象
 */
export interface TKURLResponseVO {
  /**
   * 响应类型
   */
  MIMEType: string;

  /**
   * 响应头
   */
  header: Record<string, Object>;

  /**
   * 响应状态码
   */
  statusCode: number;

  /**
   * 错误类型：0-业务异常，1-网络异常
   */
  error_type: number;

  /**
   * 错误号
   */
  error_no: number;

  /**
   * 错误信息
   */
  error_info: string;

  /**
   * 返回的原始数据
   */
  result: Uint8Array;
}

/**
 * 上传下载进度对象
 */
export class TKURLProgressVO {
  /**
   *  总共的长度
   */
  public bytesTotal: number = 0;
  /**
   *  已经加载的长度
   */
  public bytesLoaded: number = 0;

  public get progress(): number {
    if (this.bytesTotal > 0) {
      return (this.bytesLoaded * 1.0) / (this.bytesTotal * 1.0);
    } else {
      return 0;
    }
  }
}

/**
 * 请求帮组类
 */
export namespace TKURLRequestHelper {

  /**
   * 根据url获取jsessionid
   */
  function getJessionIdByUrl(url: string): string {
    let jsessionid: string = "";
    let jsessionidAry: Array<string> = TKStringHelper.split(url, ";jsessionid=");
    if (jsessionidAry && jsessionidAry.length == 2) {
      jsessionid = TKStringHelper.split(jsessionidAry[1], "?")[0];
    }
    return jsessionid;
  }

  /**
   * 根据url获取jsessionidKey
   */
  function getJessionIdKeyByUrl(url: string): string {
    let jsessionidKey: string = "";
    let urlParam: Map<string, Object> = TKDataHelper.urlToFormatMap(url);
    if (urlParam && urlParam.size > 0) {
      jsessionidKey = TKMapHelper.getString(urlParam, "TKJsessionIdKey");
    }
    if (TKStringHelper.isBlank(jsessionidKey)) {
      jsessionidKey = "JSESSIONID";
    }
    return jsessionidKey;
  }

  /**
   *  获取请求的头
   *
   * @ReqParamVo reqParamVo
   *
   * @return
   */
  function getRequestHeader(requestVO: TKURLRequestVO): Record<string, Object> {
    let url: string = requestVO.url;
    let header: Record<string, Object> = {};
    if (requestVO.header) {
      TKObjectHelper.assign(header, requestVO.header);
    }
    //处理URL上面的;jsessionid
    let jsessionidKey: string = getJessionIdKeyByUrl(url);
    let jsessionid: string = getJessionIdByUrl(url);
    let domain: string = TKNetHelper.getSchemeHostPortByURL(url);
    if (TKStringHelper.isNotBlank(jsessionid)) {
      TKCookieManager.shareInstance().setCookieItem(domain, jsessionidKey, jsessionid);
    }
    let cookie: string = TKCookieManager.shareInstance().getCookieStr(domain);
    if (TKStringHelper.isNotBlank(cookie)) {
      header["Cookie"] = cookie;
    }
    if (TKMapHelper.getString(header, "Content-Type").indexOf("application/json") >= 0) {
      requestVO.contentType = TKURLContentType.JSON;
    }
    return header;
  }

  /**
   * 获取请求参数
   * @param requestVO
   * @returns
   */
  function getRequestParam(requestVO: TKURLRequestVO): Record<string, Object> {
    let param: Record<string, Object> = {};
    if (requestVO.param) {
      //设置请求入参
      Object.entries(requestVO.param).forEach((e, i) => {
        let key: string = e[0] as string;
        let value: Object = e[1] as Object;
        if (requestVO.isKeepOriginalParam) {
          param[key] = value;
        } else {
          if (!TKStringHelper.endsWith(key, "@@F")) {
            value = value ?? "";
            if (requestVO.isEncodeUrl) {
              param[key] = encodeURIComponent(value.toString())
            } else {
              param[key] = value.toString();
            }
          }
        }
      });
    }
    return param;
  }

  /**
   *  详细信息调试
   *
   * @param url   请求url
   * @param param 参数
   */
  function debugRequestParam(url: string, header: Object, param: Object) {
    TKLog.debug(`调试请求串:\nURL===>${url}\nHeader===>${TKObjectHelper.toJsonStr(header)}\nParam===>${TKObjectHelper.toJsonStr(param)}`);
  }

  /**
   * 构建回调对象
   * @param response
   * @param data
   * @param error
   * @returns
   */
  function buildHttpResponseResult(response: rcp.Response, error?: BusinessError<rcp.Response>): TKURLResponseVO {
    let result: TKURLResponseVO = {} as TKURLResponseVO;
    if (error) {
      result.error_type = 1;
      result.error_no = -1;
      result.error_info = error.message;
    } else {
      let contentType: string | Array<string> = response.headers?.['content-type'] ?? "";
      if (Array.isArray(contentType)) {
        let temp: Array<string> = contentType as Array<string>;
        contentType = temp.length > 0 ? temp[0] : "";
      }
      let MIMEType: string = TKStringHelper.split(contentType, ";")[0].trim();
      let statusCode: number = response.statusCode;
      let data: ArrayBuffer = response.body ?? new ArrayBuffer(0);
      result.MIMEType = MIMEType;
      result.header = response.headers as Record<string, Object>;
      result.statusCode = statusCode;
      result.error_type = 0;
      result.error_no = 0;
      result.error_info = "";
      result.result = new Uint8Array(data);
    }
    return result;
  }

  /**
   * 执行回调函数
   * @param completionHandler
   * @param result
   */
  function safeExcuteHandleResultBlock<T>(completionHandler: (responseVO: T) => void, result: T) {
    if (completionHandler) {
      try {
        completionHandler(result);
      } catch (error) {
        TKLog.error("处理请求业务回调异常:" + error.message);
      }
    }
  }

  /**
   * 功能描述：get请求
   * @param requestVO          请求对象
   * @param completionHandler  请求完成回调函数
   */
  export function getRequest(requestVO: TKURLRequestVO, completionHandler: (responseVO: TKURLResponseVO) => void) {
    //请求参数
    requestVO = TKObjectHelper.fixDefault(requestVO, TKURLRequestVO);
    let url: string = requestVO.isEncodeUrl ? encodeURI(requestVO.url) : requestVO.url;
    let parameters: Record<string, Object> = getRequestParam(requestVO);
    let headers: Record<string, Object> = getRequestHeader(requestVO);
    let method: string = "GET";
    let configuration: rcp.Configuration = {
      transfer: {
        timeout: {
          connectMs: requestVO.timeout! > 0 ? requestVO.timeout! * 1000 : Number.MAX_VALUE,
          transferMs: requestVO.timeout! > 0 ? requestVO.timeout! * 1000 : Number.MAX_VALUE
        }
      }
    } as rcp.Configuration;
    let queryStr: string = TKDataHelper.mapToFormatUrl(parameters);
    if (TKStringHelper.isNotBlank(queryStr)) {
      url = url.includes("?") ? `${url}&${queryStr}` : `${url}?${queryStr}`
    }

    debugRequestParam(url, headers, {} as Record<string, Object>);

    let reqSource: rcp.Request =
      new rcp.Request(url, method, headers as rcp.RequestHeaders, undefined, undefined, undefined, configuration);
    const reqSession = rcp.createSession();
    reqSession.fetch(reqSource).then((response) => {
      let result: TKURLResponseVO = buildHttpResponseResult(response);
      safeExcuteHandleResultBlock(completionHandler, result);
    }).catch((error: BusinessError<rcp.Response>) => {
      let result: TKURLResponseVO = buildHttpResponseResult({} as rcp.Response, error);
      safeExcuteHandleResultBlock(completionHandler, result);
    });
  }

  /**
   * 功能描述：post请求
   * @param requestVO          请求对象
   * @param completionHandler  请求完成回调函数
   */
  export function postRequest(requestVO: TKURLRequestVO, completionHandler: (responseVO: TKURLResponseVO) => void) {
    //请求参数
    requestVO = TKObjectHelper.fixDefault(requestVO, TKURLRequestVO);
    let url: string = requestVO.isEncodeUrl ? encodeURI(requestVO.url) : requestVO.url;
    let parameters: Object = getRequestParam(requestVO);
    let headers: Record<string, Object> = getRequestHeader(requestVO);
    let method: string = "POST";
    let configuration: rcp.Configuration = {
      transfer: {
        timeout: {
          connectMs: requestVO.timeout! > 0 ? requestVO.timeout! * 1000 : Number.MAX_VALUE,
          transferMs: requestVO.timeout! > 0 ? requestVO.timeout! * 1000 : Number.MAX_VALUE
        }
      }
    } as rcp.Configuration;

    debugRequestParam(url, headers, parameters);

    if (requestVO.contentType == TKURLContentType.JSON) {
      if (TKStringHelper.isBlank(TKMapHelper.getString(headers, "Content-Type"))) {
        headers["Content-Type"] = "application/json";
      }
      parameters = JSON.stringify(parameters);
    } else {
      if (TKStringHelper.isBlank(TKMapHelper.getString(headers, "Content-Type"))) {
        headers["Content-Type"] = "application/x-www-form-urlencoded";
      }
      parameters = new rcp.Form(parameters as rcp.FormFields);
    }

    let reqSource: rcp.Request =
      new rcp.Request(url, method, headers as rcp.RequestHeaders, parameters, undefined, undefined, configuration);
    const reqSession = rcp.createSession();
    reqSession.fetch(reqSource).then((response) => {
      let result: TKURLResponseVO = buildHttpResponseResult(response);
      safeExcuteHandleResultBlock(completionHandler, result);
    }).catch((error: BusinessError<rcp.Response>) => {
      let result: TKURLResponseVO = buildHttpResponseResult({} as rcp.Response, error);
      safeExcuteHandleResultBlock(completionHandler, result);
    });
  }

  /**
   * 功能描述：文件上传请求
   * @param requestVO          请求对象
   * @param completionHandler  请求完成回调函数
   */
  export function uploadRequest(requestVO: TKURLRequestVO, completionHandler: (responseVO: TKURLResponseVO) => void,
    uploadProgressHandler?: (progressVO: TKURLProgressVO) => void) {
    requestVO = TKObjectHelper.fixDefault(requestVO, TKURLRequestVO);
    //请求参数
    let url: string = requestVO.isEncodeUrl ? encodeURI(requestVO.url) : requestVO.url
    let parameters: Record<string, Object> = getRequestParam(requestVO);
    let headers: Record<string, Object> = getRequestHeader(requestVO);
    if (TKStringHelper.isBlank(TKMapHelper.getString(headers, "Content-Type"))) {
      headers["Content-Type"] = "multipart/form-data";
    }
    let method: string = "POST";
    let configuration: rcp.Configuration = {
      transfer: {
        timeout: {
          connectMs: requestVO.timeout! > 0 ? requestVO.timeout! * 1000 : Number.MAX_VALUE,
          transferMs: requestVO.timeout! > 0 ? requestVO.timeout! * 1000 : Number.MAX_VALUE
        }
      },
      tracing: {
        httpEventsHandler: {
          onUploadProgress: (totalSize: number, transferredSize: number) => {
            let progressVO: TKURLProgressVO = new TKURLProgressVO();
            progressVO.bytesTotal = totalSize;
            progressVO.bytesLoaded = transferredSize;
            if (uploadProgressHandler) {
              safeExcuteHandleResultBlock<TKURLProgressVO>(uploadProgressHandler, progressVO);
            }
          }
        }
      }
    } as rcp.Configuration;

    let formData: rcp.MultipartFormFields = {};
    Object.entries(parameters).forEach((e, i) => {
      let key: string = e[0] as string;
      let value: Object = e[1] as Object;
      formData[key] = value as rcp.MultipartFormFieldValue;
    });
    //上传文件不走加密签名
    let reqParam: Record<string, Object | undefined> = requestVO.param!;
    Object.entries(reqParam).forEach((e, i) => {
      //设置请求入参
      let key: string = e[0] as string;
      let value: Object = e[1] as Object;
      //文件上传处理
      if (TKStringHelper.endsWith(key, "@@F")) {
        let file_extension: string | undefined = TKMapHelper.getString(reqParam, "file_extension");
        if (TKStringHelper.isBlank(file_extension)) {
          file_extension = "jpeg";
        }
        let key1: string = key.substring(0, key.length - 3);
        let fileName: string = `${key1}.${file_extension}`;
        if (Array.isArray(value)) {
          let fileDataArray: Array<Object> = value as Array<Object>;
          let formFiles: Array<rcp.FormFieldFileValue> = new Array<rcp.FormFieldFileValue>();
          for (let i = 0; i < fileDataArray.length; i++) {
            let fileData: Object = fileDataArray[i];
            fileName = `${key1}${i}.${file_extension}`;
            if (typeof fileData === "string") {
              let filePath: string = fileData;
              filePath = filePath.replace("file:///", "/");
              formFiles.push({
                contentOrPath: filePath
              });
            } else {
              if (fileData && (fileData instanceof Uint8Array || fileData instanceof ArrayBuffer) &&
                fileData.byteLength > 0) {
                formFiles.push({
                  contentType: "multipart/form-data",
                  remoteFileName: fileName,
                  contentOrPath: {
                    content: buffer.from(fileData).buffer
                  }
                });
              }
            }
          }
          formData[key1] = formFiles;
        } else {
          let fileData: Object = value;
          if (typeof fileData === "string") {
            let filePath: string = fileData;
            filePath = filePath.replace("file:///", "/");
            formData[key1] = {
              contentOrPath: filePath
            } as rcp.FormFieldFileValue;
          } else {
            if (fileData && (fileData instanceof Uint8Array || fileData instanceof ArrayBuffer) &&
              fileData.byteLength > 0) {
              formData[key1] = {
                contentType: "multipart/form-data",
                remoteFileName: fileName,
                contentOrPath: {
                  content: buffer.from(fileData).buffer
                }
              } as rcp.FormFieldFileValue;
            }
          }
        }
      }
    });

    let reqSource: rcp.Request =
      new rcp.Request(url, method, headers as rcp.RequestHeaders, new rcp.MultipartForm(formData), undefined, undefined,
        configuration);
    const reqSession = rcp.createSession();
    reqSession.fetch(reqSource).then((response) => {
      let result: TKURLResponseVO = buildHttpResponseResult(response);
      safeExcuteHandleResultBlock(completionHandler, result);
    }).catch((error: BusinessError<rcp.Response>) => {
      let result: TKURLResponseVO = buildHttpResponseResult({} as rcp.Response, error);
      safeExcuteHandleResultBlock(completionHandler, result);
    });
  }
}