/**
 *  移动网络运营商
 */
import { TKStringHelper } from '../string/TKStringHelper';
import { connection } from '@kit.NetworkKit';
import { wifiManager } from '@kit.ConnectivityKit';
import { radio } from '@kit.TelephonyKit';
import { TKLog } from '../logger/TKLog';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKDeviceInfoDelegate } from '../dev/TKDeviceHelper';
import { TKSystemHelper } from '../system/TKSystemHelper';
import { TKNetworkListener } from '../../base/network/listener/TKNetworkListener';

/**
 *  网络类型
 */
export enum TKNetworkType {
  /**
   *  无网络
   */
  Network_No = 0,
  /**
   *  2G
   */
  Network_2G = 1,
  /**
   *  3G
   */
  Network_3G = 2,
  /**
   *  4G
   */
  Network_4G = 3,
  /**
   *  wifi
   */
  Network_WIFI = 4,
  /**
   *  CELLULAR
   */
  Network_CELLULAR = 5,
  /**
   *  5G
   */
  Network_5G = 6,
  /**
   *  未知
   */
  Network_UnDefined = 10
}

/**
 *  网络对象
 */
export class TKNetworkInfo {
  /**
   * 网卡名称
   */
  public interfaceName: string = "";
  /**
   * 网络名称
   */
  public networkName: string = "";
  /**
   *  ipv4本地地址
   */
  public ipv4LocalAddress: string = "";
  /**
   *  ipv4子网掩码地址
   */
  public ipv4NetmaskAddress: string = "";
  /**
   *  ipv4广播地址
   */
  public ipv4BroadcastAddress: string = "";
  /**
   *  ipv4路由地址
   */
  public ipv4RouterAddress: string = "";
  /**
   *  ipv4Dns地址
   */
  public ipv4DnsAddress: string = "";
  /**
   *  ipv6本地地址
   */
  public ipv6LocalAddress: string = "";
  /**
   *  ipv6子网掩码地址
   */
  public ipv6NetmaskAddress: string = "";
  /**
   *  ipv6广播地址
   */
  public ipv6BroadcastAddress: string = "";
  /**
   *  ipv6路由地址
   */
  public ipv6RouterAddress: string = "";
  /**
   *  ipv6Dns地址
   */
  public ipv6DnsAddress: string = "";
}

/**
 * 设备网络操作帮组类
 */
export namespace TKNetHelper {
  //当前网络代理
  let curHttpProxy: string = "";
  //设备代理
  let deviceInfoDelegate: TKDeviceInfoDelegate | undefined = undefined;
  //网络供应商名称
  let phoneOperatorName: string = "";
  //网络类型
  let networkType: number = Number.MIN_VALUE;
  //手机IMSI
  let phoneIMSI: string = "";
  //本地内网IP
  let localIp: string = "";
  //蜂窝网络
  let NET_CELLULAR: string = "rmnet0";
  //WIFI网络
  let NET_WIFI: string = "wlan0";
  //WIFI网络
  let NET_WIFI1: string = "eth0";

  /**
   设置代理类
   */
  export function setTKDeviceInfoDelegate(delegate: TKDeviceInfoDelegate) {
    deviceInfoDelegate = delegate;
  }

  /**
   * 检查默认数据网络是否被激活
   */
  export function isNetAvailable(): boolean {
    return connection.hasDefaultNetSync();
  }

  /**
   * 判断当前网络是否是Wi-Fi，否则是移动流量热点网络。
   */
  export function hasNetWiFi(): boolean {
    return getNetworkType() == TKNetworkType.Network_WIFI;
  }

  /**
   * 获取网络类型
   */
  export function getNetworkType(): TKNetworkType {
    try {
      if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
        return TKNetworkType.Network_UnDefined;
      }
      if (networkType != Number.MIN_VALUE) {
        return networkType;
      }
      if (!TKNetworkListener.shareInstance().isNetAvailable()) {
        networkType = TKNetworkType.Network_No;
      } else {
        networkType = TKNetworkType.Network_CELLULAR;
        if (wifiManager.isWifiActive() && wifiManager.isConnected()) {
          networkType = TKNetworkType.Network_WIFI;
        } else {
          let net: connection.NetHandle = connection.getDefaultNetSync();
          let netCapabilities: connection.NetCapabilities = connection.getNetCapabilitiesSync(net);
          let netBearType: connection.NetBearType = netCapabilities.bearerTypes[0];
          switch (netBearType) {
            case connection.NetBearType.BEARER_CELLULAR: {
              let signalInformations = radio.getSignalInformationSync(0);
              if (!signalInformations || signalInformations.length == 0) {
                signalInformations = radio.getSignalInformationSync(1);
              }
              if (signalInformations && signalInformations.length > 0) {
                let signalType = signalInformations[0].signalType;
                switch (signalType) {
                  case radio.NetworkType.NETWORK_TYPE_GSM:
                    networkType = TKNetworkType.Network_2G;
                    break;
                  case radio.NetworkType.NETWORK_TYPE_WCDMA:
                    networkType = TKNetworkType.Network_3G;
                    break;
                  case radio.NetworkType.NETWORK_TYPE_LTE:
                    networkType = TKNetworkType.Network_4G;
                    break;
                  case radio.NetworkType.NETWORK_TYPE_NR:
                    networkType = TKNetworkType.Network_5G;
                    break;
                  default:
                    networkType = TKNetworkType.Network_CELLULAR;
                    break;
                }
              } else {
                networkType = TKNetworkType.Network_CELLULAR;
              }
              break;
            }
            case connection.NetBearType.BEARER_WIFI: {
              networkType = TKNetworkType.Network_WIFI;
              break;
            }
            default: {
              networkType = TKNetworkType.Network_UnDefined;
              break;
            }
          }
        }
      }
      return networkType;
    } catch (error) {
      TKLog.error(`[TKNetHelper]同步获取网络类型异常，code: ${error.code}， message: ${error.message}`);
      return TKNetworkType.Network_CELLULAR;
    }
  }

  /**
   * 获取网络类型,异步模式
   */
  export async function getNetworkTypeAsync(): Promise<TKNetworkType> {
    try {
      if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
        return TKNetworkType.Network_UnDefined;
      }
      if (networkType != Number.MIN_VALUE) {
        return networkType;
      }
      if (!TKNetworkListener.shareInstance().isNetAvailable()) {
        networkType = TKNetworkType.Network_No;
      } else {
        networkType = TKNetworkType.Network_CELLULAR;
        if (wifiManager.isWifiActive() && wifiManager.isConnected()) {
          networkType = TKNetworkType.Network_WIFI;
        } else {
          let net: connection.NetHandle = connection.getDefaultNetSync();
          let netCapabilities: connection.NetCapabilities = connection.getNetCapabilitiesSync(net);
          let netBearType: connection.NetBearType = netCapabilities.bearerTypes[0];
          switch (netBearType) {
            case connection.NetBearType.BEARER_CELLULAR: {
              let radioTech = await radio.getRadioTech(0);
              switch (radioTech.psRadioTech) {
                case radio.RadioTechnology.RADIO_TECHNOLOGY_GSM:
                  networkType = TKNetworkType.Network_2G;
                  break;
                case radio.RadioTechnology.RADIO_TECHNOLOGY_WCDMA:
                  networkType = TKNetworkType.Network_3G;
                  break;
                case radio.RadioTechnology.RADIO_TECHNOLOGY_LTE:
                  networkType = TKNetworkType.Network_4G;
                  break;
                case radio.RadioTechnology.RADIO_TECHNOLOGY_NR:
                  networkType = TKNetworkType.Network_5G;
                  break;
                default:
                  networkType = TKNetworkType.Network_CELLULAR;
                  break;
              }
              break;
            }
            case connection.NetBearType.BEARER_WIFI: {
              networkType = TKNetworkType.Network_WIFI;
              break;
            }
            default: {
              networkType = TKNetworkType.Network_UnDefined;
              break;
            }
          }
        }
      }
      return networkType;
    } catch (error) {
      TKLog.error(`[TKNetHelper]异步获取网络类型异常，code: ${error.code}， message: ${error.message}`);
      return TKNetworkType.Network_CELLULAR;
    }
  }

  /**
   * 获取网络类型名称
   */
  export function getNetworkTypeInfo(): string {
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return "";
    }
    let networkType: TKNetworkType = getNetworkType();
    return getNetworkTypeInfoInner(networkType);
  }

  /**
   * 获取网络类型名称,异步模式
   */
  export async function getNetworkTypeInfoAsync(): Promise<string> {
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return "";
    }
    let networkType: TKNetworkType = await getNetworkTypeAsync();
    return getNetworkTypeInfoInner(networkType);
  }

  /**
   * 获取网络类型名称
   * @param networkType
   * @returns
   */
  function getNetworkTypeInfoInner(networkType: TKNetworkType): string {
    let networkTypeInfo: string = "未知";
    switch (networkType) {
      case TKNetworkType.Network_No:
        networkTypeInfo = "无网络";
        break;
      case TKNetworkType.Network_WIFI:
        networkTypeInfo = "WIFI";
        break;
      case TKNetworkType.Network_2G:
        networkTypeInfo = "2G";
        break;
      case TKNetworkType.Network_3G:
        networkTypeInfo = "3G";
        break;
      case TKNetworkType.Network_4G:
        networkTypeInfo = "4G";
        break;
      case TKNetworkType.Network_5G:
        networkTypeInfo = "5G";
        break;
      case TKNetworkType.Network_CELLULAR:
        networkTypeInfo = "移动蜂窝网络";
        break;
      default:
        break;
    }
    return networkTypeInfo;
  }

  /**
   * 获取连接的WIFI名称
   */
  export async function getWiFiName(): Promise<string> {
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return "";
    }
    try {
      if (wifiManager.isWifiActive() && wifiManager.isConnected()) {
        let wifiLinkedInfo: wifiManager.WifiLinkedInfo = await wifiManager.getLinkedInfo();
        return wifiLinkedInfo.ssid;
      }
    } catch (error) {
      TKLog.error(`[TKNetHelper]获取连接的WIFI名称异常，code: ${error.code}， message: ${error.message}`);
    }
    return "";
  }

  /**
   *  根据域名获取ip
   * @param strHostName
   */
  export async function getIPAddressByHostName(hostName: string): Promise<string> {
    try {
      hostName = getHostByURL(hostName);
      let address: connection.NetAddress = await connection.getDefaultNetSync()?.getAddressByName(hostName);
      return address ? address.address : "";
    } catch (error) {
      TKLog.error(`[TKNetHelper]根据域名解析IP异常，code: ${error.code}， message: ${error.message}`);
      return "";
    }
  }

  /**
   *  根据URL获取IP，根据域名获取IP列表
   *
   * @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json或者wwww.baidu.com等格式都支持,如果是IP就直接返回IP
   *
   * @return 域名对应的IP地址
   */
  export async function getDNSIPV4AddressByHostName(hostName: string): Promise<Array<string>> {
    let results: Array<string> = new Array<string>();
    try {
      hostName = getHostByURL(hostName);
      let addresses: Array<connection.NetAddress> = await connection.getDefaultNetSync().getAddressesByName(hostName);
      if (addresses && addresses.length > 0) {
        for (let address of addresses) {
          if (address.family != 2) {
            if (!results.includes(address.address)) {
              results.push(address.address);
            }
          }
        }
      }
    } catch (error) {
      TKLog.error(`[TKNetHelper]根据域名解析IPV4地址异常，code: ${error.code}， message: ${error.message}`);
    }
    return results;
  }

  /**
   *  根据URL获取IP，根据域名获取IP列表
   *
   * @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json或者wwww.baidu.com等格式都支持,如果是IP就直接返回IP
   *
   * @return 域名对应的IP地址
   */
  export async function getDNSIPV6AddressByHostName(hostName: string): Promise<Array<string>> {
    let results: Array<string> = new Array<string>();
    try {
      hostName = getHostByURL(hostName);
      let addresses: Array<connection.NetAddress> = await connection.getDefaultNetSync().getAddressesByName(hostName);
      if (addresses && addresses.length > 0) {
        for (let address of addresses) {
          if (address.family == 2) {
            if (!results.includes(address.address)) {
              results.push(address.address);
            }
          }
        }
      }
    } catch (error) {
      TKLog.error(`[TKNetHelper]根据域名解析IPV6地址异常，code: ${error.code}， message: ${error.message}`);
    }
    return results;
  }

  /**
   * 获取出口所有的DNS服务地址
   * @returns
   */
  export function getOutPutDNSServers(): Array<string> {
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return [];
    }
    let dnses: Array<string> = new Array<string>();
    let addressMap: Map<string, TKNetworkInfo> = getIPAddressMap();
    if (addressMap && addressMap.size > 0) {
      addressMap.forEach((value, key) => {
        if (TKStringHelper.isNotBlank(value.ipv4DnsAddress) && !dnses.includes(value.ipv4DnsAddress)) {
          dnses.push(value.ipv4DnsAddress);
        }
        if (TKStringHelper.isNotBlank(value.ipv6DnsAddress) && !dnses.includes(value.ipv6DnsAddress)) {
          dnses.push(value.ipv6DnsAddress);
        }
      });
    }
    return dnses;
  }

  /**
   *  获取手机设备的移动蜂窝网络信息
   */
  export function getCellNetworkInfo(): TKNetworkInfo | undefined {
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return undefined;
    }
    let networkInfo: TKNetworkInfo | undefined = getIPAddressMap().get(NET_CELLULAR);
    if (networkInfo) {
      networkInfo.networkName = `${getPhoneOperatorName()}(${getNetworkTypeInfo()})`
    }
    return networkInfo;
  }

  /**
   *  获取手机设备的移动WIFI网络信息
   */
  export function getWifiNetworkInfo(): TKNetworkInfo | undefined {
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return undefined;
    }
    let networkInfo: TKNetworkInfo | undefined = getIPAddressMap().get(NET_WIFI);
    if (!networkInfo) {
      networkInfo = getIPAddressMap().get(NET_WIFI1);
    }
    return networkInfo;
  }

  /**
   * 获取本地相关网络地址信息
   * @returns
   */
  function getIPAddressMap(): Map<string, TKNetworkInfo> {
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return new Map<string, TKNetworkInfo>();
    }
    let addressMap: Map<string, TKNetworkInfo> = new Map<string, TKNetworkInfo>();
    try {
      // let ipV4Info: wifiManager.IpInfo = wifiManager.getIpInfo();
      // let ipV6Info: wifiManager.Ipv6Info = wifiManager.getIpv6Info();
      // if ((ipV4Info && ipV4Info.ipAddress > 0) || (ipV6Info && TKStringHelper.isNotBlank(ipV6Info.linkIpv6Address))) {
      //   //处理wifi
      //   let wifiNetworkInfo: TKNetworkInfo = new TKNetworkInfo();
      //   wifiNetworkInfo.interfaceName = "wifi";
      //   if (ipV4Info && ipV4Info.ipAddress > 0) {
      //     wifiNetworkInfo.ipv4LocalAddress = convertIpV4Address(ipV4Info.ipAddress);
      //     wifiNetworkInfo.ipv4NetmaskAddress = convertIpV4Address(ipV4Info.netmask);
      //     wifiNetworkInfo.ipv4RouterAddress = convertIpV4Address(ipV4Info.gateway);
      //     wifiNetworkInfo.ipv4DnsAddress =
      //       convertIpV4Address(ipV4Info.primaryDns > 0 ? ipV4Info.primaryDns : ipV4Info.secondDns);
      //   }
      //   if (ipV6Info && TKStringHelper.isNotBlank(ipV6Info.linkIpv6Address) &&
      //     !ipV6Info.linkIpv6Address.startsWith("fe80")) {
      //     wifiNetworkInfo.ipv6LocalAddress = ipV6Info.linkIpv6Address;
      //     wifiNetworkInfo.ipv6NetmaskAddress = ipV6Info.netmask
      //     wifiNetworkInfo.ipv6RouterAddress = ipV6Info.gateway
      //     wifiNetworkInfo.ipv6DnsAddress =
      //       TKStringHelper.isNotBlank(ipV6Info.primaryDNS) ? ipV6Info.primaryDNS : ipV6Info.secondDNS;
      //   }
      //   addressMap.set(wifiNetworkInfo.interfaceName, wifiNetworkInfo);
      // }
      let nets: Array<connection.NetHandle> = connection.getAllNetsSync();
      if (nets && nets.length > 0) {
        for (let net of nets) {
          let netCapabilities: connection.ConnectionProperties = connection.getConnectionPropertiesSync(net);
          if (netCapabilities) {
            let cellNetworkInfo: TKNetworkInfo = new TKNetworkInfo();
            cellNetworkInfo.interfaceName = netCapabilities.interfaceName;
            if (netCapabilities.linkAddresses && netCapabilities.linkAddresses.length > 0) {
              for (let linkAddress of netCapabilities.linkAddresses) {
                if (linkAddress.address && TKStringHelper.isNotBlank(linkAddress.address.address) &&
                  !linkAddress.address.address.startsWith("fe80")) {
                  //iPV4
                  if (linkAddress.address.family != 2) {
                    cellNetworkInfo.ipv4LocalAddress = linkAddress.address.address;
                  } else {
                    cellNetworkInfo.ipv6LocalAddress = linkAddress.address.address;
                  }
                }
              }
            }
            if (netCapabilities.routes && netCapabilities.routes.length > 0) {
              for (let route of netCapabilities.routes) {
                if (route.gateway && route.hasGateway && TKStringHelper.isNotBlank(route.gateway.address) &&
                  !route.gateway.address.startsWith("fe80")) {
                  //iPV4
                  if (route.gateway.family != 2) {
                    cellNetworkInfo.ipv4RouterAddress = route.gateway.address;
                  } else {
                    cellNetworkInfo.ipv6RouterAddress = route.gateway.address;
                  }
                }
                if (route.destination && route.destination.address &&
                TKStringHelper.isNotBlank(route.destination.address.address) &&
                  !route.destination.address.address.startsWith("fe80")) {
                  //iPV4
                  if (route.destination.address.family != 2) {
                    cellNetworkInfo.ipv4BroadcastAddress = route.destination.address.address;
                  } else {
                    cellNetworkInfo.ipv6BroadcastAddress = route.destination.address.address;
                  }
                }
              }
            }
            if (netCapabilities.dnses && netCapabilities.dnses.length > 0) {
              for (let dns of netCapabilities.dnses) {
                if (TKStringHelper.isNotBlank(dns.address) && !dns.address.startsWith("fe80"))
                //iPV4
                {
                  if (dns.family != 2) {
                    cellNetworkInfo.ipv4DnsAddress = dns.address;
                  } else {
                    cellNetworkInfo.ipv6DnsAddress = dns.address;
                  }
                }
              }
            }
            addressMap.set(cellNetworkInfo.interfaceName, cellNetworkInfo);
          }
        }
      }
    } catch (error) {
      TKLog.error(`[TKNetHelper]获取本地所有网络连接信息异常，code: ${error.code}， message: ${error.message}`);
    }
    return addressMap;
  }

  /**
   * 转换ipv4地址
   * @param ipAddress
   * @returns
   */
  export function convertIpV4Address(ipAddress: number): string {
    return (ipAddress >>> 24) + "." + (ipAddress >> 16 & 0xFF) + "." + (ipAddress >> 8 & 0xFF) + "." +
      (ipAddress & 0xFF);
  }

  /**
   * 获取当前设备的IP地址
   */
  export function getLocalIP(): string {
    try {
      if (deviceInfoDelegate && deviceInfoDelegate.getLocalIP) {
        return deviceInfoDelegate.getLocalIP();
      }
      if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
        return "";
      }
      if (TKStringHelper.isNotBlank(localIp)) {
        return localIp;
      }
      let searchArray: Array<string> = [NET_WIFI, NET_CELLULAR, NET_WIFI1];
      let addressMap: Map<string, TKNetworkInfo> = getIPAddressMap();
      let localIPV4: string = "";
      let localIPV6: string = "";
      for (let key of searchArray) {
        let networkInfo = addressMap.get(key);
        if (networkInfo) {
          if (TKStringHelper.isBlank(localIPV4)) {
            localIPV4 = networkInfo.ipv4LocalAddress;
          }
          if (TKStringHelper.isBlank(localIPV6)) {
            localIPV6 = networkInfo.ipv6LocalAddress;
          }
          if (TKStringHelper.isNotBlank(localIPV4)) {
            break;
          }
        }
      }
      localIp = TKStringHelper.isNotBlank(localIPV4) ? localIPV4 : localIPV6;
      return localIp;
    } catch (error) {
      TKLog.error(`[TKNetHelper]获取本地IP异常，code: ${error.code}， message: ${error.message}`);
      return "";
    }
  }

  /**
   * 获取IP
   */
  export function getIP() {
    return "";
  }

  /**
   * 获取当前网络代理
   */
  export async function fetchHttpProxy(): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      connection.getDefaultHttpProxy((error: BusinessError, httpProxy: connection.HttpProxy) => {
        if (httpProxy) {
          curHttpProxy = httpProxy.host;
        } else {
          curHttpProxy = "";
        }
        resolve(curHttpProxy);
      });
    });
  }

  /**
   * 获取当前网络代理同步API
   */
  export function fetchHttpProxySync(): string {
    fetchHttpProxy();
    return curHttpProxy;
  }

  /**
   * 获取运营商名称
   */
  export function getPhoneOperatorName(): string {
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return "未定义的";
    }
    if (TKStringHelper.isNotBlank(phoneOperatorName)) {
      return phoneOperatorName;
    }
    phoneOperatorName = "未定义的";
    try {
      phoneOperatorName = radio.getOperatorNameSync(0);
      if (TKStringHelper.isBlank(phoneOperatorName)) {
        phoneOperatorName = radio.getOperatorNameSync(1);
      }
    } catch (error) {
      TKLog.error(`[TKNetHelper]获取运营商名称异常，code: ${error.code}， message: ${error.message}`);
    }
    return phoneOperatorName;
  }

  /**
   * 获取运营商名称
   */
  export function getPhoneOperator(): string {
    //设备网络运营商
    let phoneOperator: string = getPhoneOperatorName();
    let deviceOperator: string = "4";
    if (TKStringHelper.isNotBlank(phoneOperator)) {
      if (phoneOperator.indexOf("移动")) {
        deviceOperator = "0";
      } else if (phoneOperator.indexOf("联通")) {
        deviceOperator = "1";
      } else if (phoneOperator.indexOf("电信")) {
        deviceOperator = "2";
      } else if (phoneOperator.indexOf("铁通")) {
        deviceOperator = "3";
      }
    }
    return deviceOperator;
  }

  /**
   *  根据URL获取协议+HOST+端口
   *
   * @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json
   *
   * @return http://www.baidu.com:8080
   */
  export function getSchemeHostPortByURL(url: string): string {
    if (TKStringHelper.isNotBlank(url)) {
      let temp: Array<string> = TKStringHelper.split(url, "://")
      if (temp && temp.length == 2) {
        let scheme: string = temp[0];
        let host: string = temp[1];
        host = TKStringHelper.split(host, "/")[0];
        url = `${scheme}://${host}`;
      }
    }
    return url;
  }

  /**
   *  根据URL获取HOST+端口
   *
   * @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json
   *
   * @return www.baidu.com:8080
   */
  export function getHostPortByURL(url: string): string {
    let hostPort: string = "";
    if (TKStringHelper.isNotBlank(url)) {
      let temp: Array<string> = TKStringHelper.split(url, "://")
      hostPort = (temp.length == 2) ? temp[1] : temp[0];
      hostPort = TKStringHelper.split(hostPort, "/")[0];
    }
    return hostPort;
  }

  /**
   *  根据URL获取主机名称
   *
   * @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json
   *
   * @return www.baidu.com
   */
  export function getHostByURL(url: string): string {
    if (TKStringHelper.isNotBlank(url)) {
      url = formatAddress(url)[0];
    }
    return url;
  }

  /**
   *  根据URL获取Scheme协议
   *
   * @param url 例如:http://wwww.baidu.com:8080或者http://wwww.baidu.com:8080/servlet/json
   *
   * @return http
   */
  export function getSchemeByURL(url: string): string {
    let protectHeader: string = "";
    if (TKStringHelper.isNotBlank(url)) {
      let temp: Array<string> = TKStringHelper.split(url, "://")
      if (temp && temp.length == 2) {
        protectHeader = temp[0];
      }
    }
    return protectHeader;
  }

  /**
   *  是否IPV6的地址
   *
   * @return
   */
  export function isIPV6URL(url: string): boolean {
    let host: string = getHostByURL(url);
    return host.indexOf(":") > -1;
  }

  /**
   * 是否域名
   * @param url
   * @returns
   */
  export function isDomainURL(url: string): boolean {
    let host: string = getHostByURL(url);
    return host.endsWith(".com") || host.endsWith(".net") || host.endsWith(".cn") || host.endsWith(".co");
  }

  /**
   *  格式化地址，兼容ipv6的处理
   */
  export function formatAddress(address: string): Array<string> {
    let temp: Array<string> = new Array<string>();
    if (TKStringHelper.isNotBlank(address)) {
      let addressTemp: Array<string> = TKStringHelper.split(address, "://")
      address = (addressTemp.length == 2) ? addressTemp[1] : addressTemp[0];
      address = TKStringHelper.split(address, "/")[0];
      let index: number = address.indexOf("]");
      //判断是不是ipv6的地址格式
      if (index > 1 && TKStringHelper.startsWith(address, "[")) {
        let ip: string = address.substring(1, index);
        temp.push(ip);
        if (address.length > (index + 2)) {
          let last: string = address.substring(index + 2, address.length);
          temp.push(...TKStringHelper.split(last, ":"));
        }
      } else {
        temp = TKStringHelper.split(address, ":");
      }
    }
    return temp;
  }

  /**
   *  获取运营商信息(IMSI)
   *
   * @return 获取运营商信息(IMSI)
   */
  export function getPhoneIMSI() {
    if (deviceInfoDelegate && deviceInfoDelegate.getPhoneIMSI) {
      return deviceInfoDelegate.getPhoneIMSI();
    }
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return "";
    }
    if (TKStringHelper.isNotBlank(phoneIMSI)) {
      return phoneIMSI;
    }
    return phoneIMSI;
  }

  /**
   * 重置网络缓存信息
   */
  export function resetNetCacheInfo() {
    //网络供应商
    phoneOperatorName = "";
    getPhoneOperatorName();
    //网络类型
    networkType = Number.MIN_VALUE;
    //重新刷新网络类型
    getNetworkType();
    //手机IMSI
    phoneIMSI = "";
    getPhoneIMSI();
    //本地内网IP
    localIp = "";
    getLocalIP();
  }

  /**
   * 重置网络类型缓存信息
   */
  export function resetNetTypeCacheInfo() {
    //网络类型
    networkType = Number.MIN_VALUE;
  }

}