/**
 * 格式化判断工具类
 */
import { TKDateHelper } from '../date/TKDateHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import url from '@ohos.url';

export enum TKFormatOption {
  /**
   *  不为空
   */
  NotEmpty,
  /**
   *  日期
   */
  Date,
  /**
   *  日期时间
   */
  DateTime,
  /**
   *  字母数字
   */
  AlphaNumeric,
  /**
   *  电子邮件
   */
  Email,
  /**
   *  货币
   */
  Money,
  /**
   *  数字
   */
  Numeric,
  /**
   *  浮点数
   */
  NumberFloat,
  /**
   *  手机号
   */
  Mobile,
  /**
   *  电话
   */
  Phone,
  /**
   *  固话
   */
  Tel,
  /**
   *  邮政编码
   */
  PostalCode,
  /**
   *  网址
   */
  URL,
  /**
   *  身份证
   */
  CardID,
  /**
   *  股票代码
   */
  Stock,
  /**
   *  强交易密码
   */
  StrongTradePwd,
  /**
   *  强登录密码
   */
  StrongLoginPwd,
  /**
   *  中文，英文，字母或_
   */
  CnAndEnNumeric,
  /**
   *  英文，字母或_
   */
  EnNumeric,
  /**
   *  中文
   */
  Chinese
}

/**
 * 密码强度
 */
export enum TKPasswordStrongLevel {
  Low,
  Mid,
  High
}

/**
 * 格式化帮组类
 */
export namespace TKFormatHelper {


  /**
   *  判断字符串是否满足正则表达式
   *
   * @param regex 正则表达式
   * @param str   要校验的字符串
   *
   * @return YES,NO
   */
  export function isFormatRegex(regex: string, str: string): boolean {
    if (TKStringHelper.isNotBlank(regex) && TKStringHelper.isNotBlank(str)) {
      let predicate: RegExp = new RegExp(regex);
      if (predicate) {
        return predicate.test(str);
      }
      return false;
    }
    return false;
  }

  /**
   *  判断字符串是否为日期，正确格式为YYYY-MM-DD或者YYYY/MM/DD
   *
   * @param str 要校验的字符串
   *
   * @return YES,NO
   */
  export function isDate(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let dateRegex: string = "^(\\d{4})(-|/)(\\d{2})\\2(\\d{2})$";
    let flag: boolean = isFormatRegex(dateRegex, str);
    if (flag) {
      let temp: Array<string> = TKStringHelper.split(str, "-|/", true);
      let year: string = temp[0];
      let month: string = temp[1];
      let day: string = temp[2];
      // 注意月份需要减去 1，因为 JavaScript 中的月份是从 0 开始的
      let date: Date = new Date(Number(year), Number(month) - 1, Number(day));
      let newStr: string = TKDateHelper.formatDate(date, "YYYYMMDD")
      let oldStr: string = TKStringHelper.replace(str, "-|/", "");
      flag = (oldStr == newStr);
    }
    return flag;
  }

  /**
   *  判断是否为长时间，例如 YYYY-MM-DD HH:mm:ss或者YYYY/MM/DD HH:mm:ss
   *
   * @param str 要校验的字符串
   *
   * @return YES,NO
   */
  export function isDateTime(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let dateRegex: string = "^(\\d{4})(-|/)(\\d{2})\\2(\\d{2}) (\\d{2}):(\\d{2}):(\\d{2})$";
    let flag: boolean = isFormatRegex(dateRegex, str);
    if (flag) {
      let temp: Array<string> = TKStringHelper.split(str, "-|/| |:", true);
      let year: string = temp[0]; //年
      let month: string = temp[1]; //月
      let day: string = temp[2]; //日
      let hour: string = temp[3]; //时
      let minute: string = temp[4]; //分
      let second: string = temp[5]; //分

      // 注意月份需要减去 1，因为 JavaScript 中的月份是从 0 开始的
      let date: Date =
        new Date(Number(year), Number(month) - 1, Number(day), Number(hour), Number(minute), Number(second));
      let newStr: string = TKDateHelper.formatDate(date, "YYYYMMDDHHmmss")
      let oldStr: string = TKStringHelper.replace(str, "-|/| |:", "");
      flag = (oldStr == newStr);
    }
    return flag;
  }

  /**
   *  判断字符串是否为字母或数字
   *
   * @param str 要校验的字符串
   *
   * @return YES,NO
   */
  export function isAlphaNumber(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^[A-Za-z0-9]+$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否为中文，英文，字母或_
   *
   * @param str 要校验的字符串
   *
   * @return YES,NO
   */
  export function isCnAndEnNumeric(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^[_0-9a-zA-Z\\u4e00-\\u9fa5]+$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否为英文，字母或_
   *
   * @param str 要校验的字母串
   *
   * @return YES,NO
   */
  export function isEnNumeric(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^[_0-9a-zA-Z]+$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否为中文
   *
   * @param str 要校验的字符串
   *
   * @return YES,NO
   */
  export function isChinese(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^[\\u4e00-\\u9fa5]+$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否包含中文
   *
   * @param str 要校验的字符串
   *
   * @return YES,NO
   */
  export function isIncludeChinese(str: string): boolean {
    for (let i = 0; i < str.length; i++) {
      let a: number = str.charCodeAt(i);
      if (a > 0x4e00 && a < 0x9fff) {
        return true;
      }
    }
    return false;
  }

  /**
   *  判断是否是邮箱
   *
   * @param str 要校验的字符串
   *
   * @return YES,NO
   */
  export function isEmail(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否是货币
   *
   * @param str 要校验的字符串
   *
   * @return YES,NO
   */
  export function isMoney(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^[+-]?\\d+(,\\d{3})*(\\.\\d+)?$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否为数字
   *
   * @param str 要检验的字符串
   *
   * @return YES,NO
   */
  export function isNumeric(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^[0-9]*$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否是浮点数
   *
   * @param str 要检验的字符串
   *
   * @return YES,NO
   */
  export function isNumberFloat(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^\\d+(\\.\\d+)?$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否是手机号
   *
   * @param str 要检验的字符串
   *
   * @return YES,NO
   */
  export function isMobile(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^1[0-9]{10}$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否是电话，包含手机和固话
   *
   * @param str 要检验的字符串
   *
   * @return YES,NO
   */
  export function isPhone(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "(^\\(\\d{3,5}\\)\\d{6,8}(-\\d{2,8})?$)|(^\\d+-\\d+$)|(^(1)[0-9]{10}$)";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否为固话
   *
   * @param str 要检查的字符串
   *
   * @return YES,NO
   */
  export function isTel(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^(([0\\+]\\d{2,3}-)?(0\\d{2,3})-)(\\d{7,8})(-(\\d{3,}))?$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否是邮政编码
   *
   * @param str 要检查的字符串
   *
   * @return YES,NO
   */
  export function isPostalCode(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^\\d{6}$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否为合法的URL
   *
   * @param str 要检查的字符串
   *
   * @return YES，NO
   */
  export function isURL(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    try {
      url.URL.parseURL(str);
      return true;
    } catch {
      return false;
    }
  }

  /**
   *  判断是否为身份证
   *
   * @param str 要检查的字符串
   *
   * @return YES,NO
   */
  export function isCardID(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    //身份证省份区域开头
    let dic: Record<string, Object> = {
      "11": "北京",
      "12": "天津",
      "13": "河北",
      "14": "山西",
      "15": "内蒙古",
      "21": "辽宁",
      "22": "吉林",
      "23": "黑龙江",
      "31": "上海",
      "32": "江苏",
      "33": "浙江",
      "34": "安徽",
      "35": "福建",
      "36": "江西",
      "37": "山东",
      "41": "河南",
      "42": "湖北",
      "43": "湖南",
      "44": "广东",
      "45": "广西",
      "46": "海南",
      "50": "重庆",
      "51": "四川",
      "52": "贵州",
      "53": "云南",
      "54": "西藏",
      "61": "陕西",
      "62": "甘肃",
      "63": "青海",
      "64": "宁夏",
      "65": "新疆",
      "71": "台湾",
      "81": "香港",
      "82": "澳门",
      "91": "国外"
    };
    //校验长度，类型,身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    let regexStr: string = "(^\\d{15}$)|(^\\d{17}(\\d|X))$";
    if (!isFormatRegex(regexStr, str)) {
      return false;
    }
    //检查省份
    let province: string = str.substring(0, 2);
    if (TKStringHelper.isBlank(dic[province] as string)) {
      return false;
    }
    //校验生日
    let len: number = str.length;
    //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
    if (len == 15) {
      let year: number = 1900 + Number(str.substring(6, 8)); //年
      let month: number = Number(str.substring(8, 10)); //月
      let day: number = Number(str.substring(10, 12)); //日
      //获取日期字符串进行有效性的比较
      let cardDate: Date = new Date(year, month - 1, day);
      let cardDateStr: string = TKDateHelper.formatDate(cardDate, "YYYYMMDD");
      let oldDateStr: string = "19" + str.substring(6, 12);
      //年月日是否合理
      if (cardDateStr != oldDateStr) {
        return false;
      }
      //当前日期
      let now: Date = new Date();
      let nowYear: number = now.getFullYear();
      //判断年份的范围（3岁到100岁之间)
      let time: number = nowYear - year;
      if (!(time >= 3 && time <= 100)) {
        return false;
      }
    }
    //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
    if (len == 18) {
      let year: number = Number(str.substring(6, 10)); //年
      let month: number = Number(str.substring(10, 12)); //月
      let day: number = Number(str.substring(12, 14)); //日
      //获取日期字符串进行有效性的比较
      let cardDate: Date = new Date(year, month - 1, day);
      let cardDateStr: string = TKDateHelper.formatDate(cardDate, "YYYYMMDD");
      let oldDateStr: string = str.substring(6, 14); //原日期
      //年月日是否合理
      if (cardDateStr != oldDateStr) {
        return false;
      }
      //当前日期
      let now: Date = new Date();
      let nowYear: number = now.getFullYear();
      //判断年份的范围（3岁到100岁之间)
      let time: number = nowYear - year;
      if (!(time >= 3 && time <= 100)) {
        return false;
      }
    }
    //15位转18位
    if (str.length == 15) {
      let arrInt: Array<number> = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      let arrCh: Array<string> = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
      let cardTemp: number = 0;
      str = str.substring(0, 6) + "19" + str.substring(6, str.length - 6);
      for (let i = 0; i < 17; i++) {
        cardTemp += Number(str.substring(i, i + 1)) * arrInt[i];
      }
      str += arrCh[cardTemp % 11];
    }
    //检验位的检测
    if (str.length == 18) {
      let arrInt: Array<number> = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      let arrCh: Array<string> = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
      let cardTemp: number = 0;
      for (let i = 0; i < 17; i++) {
        cardTemp += Number(str.substring(i, i + 1)) * arrInt[i];
      }
      let val: string = arrCh[cardTemp % 11];
      if (val != str.charAt(17)) {
        return false;
      }
    }
    return true;
  }

  /**
   *  判断是否六位交易密码
   *
   * @param str 要检测的字符串
   *
   * @return YES,NO
   */
  export function isTradePassword(str: string): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    let regexStr: string = "^[0-9]{6}$";
    return isFormatRegex(regexStr, str);
  }

  /**
   *  判断是否6位强交易密码
   *
   * @param str 要检查的密码字符串
   * @param filters 要过滤数组,数组中的每一项都不能包含当前的密码串，否则认为密码为弱密码，比如可以用身份证，手机等校验

   * @return YES,NO
   */
  export function isStrongTradePassword(str: string, filters?: Array<string>): boolean {
    if (TKStringHelper.isBlank(str)) {
      return false;
    }
    //不是6位数字
    let regex: string = "^\\d{6}$";
    if (!isFormatRegex(regex, str)) {
      return false;
    }
    //3个一样
    regex = "([\\d])\\1{2}";
    if (isFormatRegex(regex, str)) {
      return false;
    }
    //顺增
    let strValue: string = TKStringHelper.replace(str, "\\d", (searchSubValue: string, pos: number): string => {
      return (Number(searchSubValue) - pos).toString();
    });
    regex = "^(\\d)\\1+$";
    if (isFormatRegex(regex, strValue)) {
      return false;
    }
    //顺减
    strValue = TKStringHelper.replace(str, "\\d", (searchSubValue: string, pos: number): string => {
      return (Number(searchSubValue) + pos).toString();
    });
    regex = "^(\\d)\\1+$";
    if (isFormatRegex(regex, strValue)) {
      return false;
    }
    let temp: string = "";
    for (let i = 0; i < str.length; i++) {
      let c: string = str.substring(i, i + 1);
      if (!temp.includes(c)) {
        temp += c;
      }
    }
    //超过2个数字组合
    if (temp.length <= 2) {
      return false;
    }
    let flag: boolean = true;
    let map: Record<string, Object> = {};
    for (let i = 0; i < str.length; i++) {
      let k: string = str.substring(i, i + 1);
      let v: string | undefined = map[k] as string;
      if (TKStringHelper.isBlank(v)) {
        v = "0";
      }
      map[k] = Number(v) + 1;
      if ((Number(v) + 1) >= (str.length / 2)) {
        flag = false;
      }
    }
    if (!flag) {
      return false;
    }
    if (filters && filters.length > 0) {
      for (let i = 0; i < filters.length; i++) {
        let filter: string = filters[i];
        if (filter.includes(str)) {
          return false;
        }
      }
    }
    return true;
  }

  /**
   *  判断是否强登录密码 0:弱密码，1:中密码，2:强密码
   *
   * @param str 要检查的密码字符串

   * @return
   */
  export function checkPasswordStrength(str: string): TKPasswordStrongLevel {
    if (TKStringHelper.isBlank(str) || str.length < 8) {
      return TKPasswordStrongLevel.Low;
    }

    let rules: Array<Record<string, Object>> = [
      { "regex": "\\d+", "weight": "2" },
      { "regex": "[a-z]+", "weight": "4" },
      { "regex": "[A-Z]+", "weight": "8" },
      { "regex": "[~!@#\\$%^&*\\(\\)\\{\\};,.\\?\\/'\"]", "weight": "16" },
    ];
    let score: number = 0;
    for (let j = rules.length - 1; j >= 0; j--) {
      let rule: Record<string, Object> = rules[j];
      let regex: string = rule["regex"] as string;
      let weight: number = Number(rule["weight"]);
      if (isFormatRegex(regex, str)) {
        score |= weight;
      }
    }
    if (score <= 10) {
      return TKPasswordStrongLevel.Low;
    } else if (score <= 20) {
      return TKPasswordStrongLevel.Mid;
    } else {
      return TKPasswordStrongLevel.High;
    }
  }

  /**
   *  校验字符串格式
   *
   * @param srcStr      要校验的字符串
   * @param description 描述
   * @param option      校验格式
   * @param filter      过滤字符串数组，用于密码强弱校验，过滤数组,数组中的每一项都不能包含当前的密码串，否则认为密码为弱密码，比如可以用身份证，手机等校验
   *
   * @return 校验结果
   */
  export function checkValid(srcStr: string, description: string, option: TKFormatOption,
    filter?: Array<string>): string {
    let strMsg: string = "";
    switch (option) {
      case TKFormatOption.NotEmpty: {
        // 不许空值
        if (TKStringHelper.isBlank(srcStr)) {
          strMsg = `${description}不能为空！\n`;
        }
        break;
      }
      case TKFormatOption.Date: {
        //日期
        if (!isDate(srcStr)) {
          strMsg = `${description}格式错误，请输入正确的日期格式！\n`;
        }
        break;
      }
      case TKFormatOption.DateTime: {
        //日期时间
        if (!isDateTime(srcStr)) {
          strMsg = `${description}格式错误，请输入正确的日期时间格式！\n`;
        }
        break;
      }
      case TKFormatOption.AlphaNumeric: {
        //字母数字
        if (!isAlphaNumber(srcStr)) {
          strMsg = `${description}格式错误，请输入字母或数字！\n`;
        }
        break;
      }
      case TKFormatOption.Email: {
        // 电子邮件
        if (!isEmail(srcStr)) {
          strMsg = `${description}格式错误，请输入正确的邮件格式！\n`;
        }
        break;
      }
      case TKFormatOption.Money: {
        //货币
        if (!isMoney(srcStr)) {
          strMsg = `${description}格式错误，请输入正确的货币格式！`;
        }
        break;
      }
      case TKFormatOption.Numeric: {
        //数字
        if (!isNumeric(srcStr)) {
          strMsg = `${description}格式错误，请输入数字！\n`;
        }
        break;
      }
      case TKFormatOption.NumberFloat: {
        //浮点数
        if (!isNumberFloat(srcStr)) {
          strMsg = `${description}格式错误，请输入浮点数！\n`;
        }
        break;
      }
      case TKFormatOption.Mobile: {
        // 手机号码
        if (!isMobile(srcStr)) {
          strMsg = `${description}格式错误，请输入正确的手机号码！\n`;
        }
        break;
      }
      case TKFormatOption.Phone: {
        // 电话
        if (!isPhone(srcStr)) {
          strMsg = `${description}格式错误，请输入正确的电话格式！\n`;
        }
        break;
      }
      case TKFormatOption.Tel: {
        // 固话
        if (!isTel(srcStr)) {
          strMsg = `${description}格式错误，请输入正确的电话格式！\n`;
        }
        break;
      }
      case TKFormatOption.PostalCode: {
        // 邮政编码
        if (!isPostalCode(srcStr)) {
          strMsg = `${description}格式错误，请输入6位数字！\n`;
        }
        break;
      }
      case TKFormatOption.URL: {
        //URL
        if (!isURL(srcStr)) {
          strMsg = `${description}格式错误，请输入正确的URL格式！\n`;
        }
        break;
      }
      case TKFormatOption.CardID: {
        //身份证号
        if (!isCardID(srcStr)) {
          strMsg = `${description}格式错误，请输入正确的身份证号码！\n`;
        }
        break;
      }
      case TKFormatOption.Stock: {
        //股票代码
        if (!isNumeric(srcStr) || (srcStr.length != 6)) {
          strMsg = `${description}格式错误，请输入6位数字！\n`;
        }
        break;
      }
      case TKFormatOption.StrongTradePwd: {
        //强密码校验
        if (!isTradePassword(srcStr)) {
          strMsg = `${description}格式错误，请输入6位数字！\n`;
        } else {
          if (!isStrongTradePassword(srcStr, filter)) {
            strMsg = `${description}强度太弱,请修改！\n`;
          }
        }
        break;
      }
      case TKFormatOption.StrongLoginPwd: {
        //强密码校验
        if (checkPasswordStrength(srcStr) == TKPasswordStrongLevel.Low) {
          strMsg = `${description}强度太弱,请修改！\n\n`;
        }
        break;
      }
      case TKFormatOption.CnAndEnNumeric: {
        //中文，英文，字母或
        if (!isCnAndEnNumeric(srcStr)) {
          strMsg = `${description}格式错误，请输入中文，英文，字母或_！\n`;
        }
        break;
      }
      case TKFormatOption.Chinese: {
        //中文
        if (!isChinese(srcStr)) {
          strMsg = `${description}格式错误，请输入中文！\n`;
        }
        break;
      }
      case TKFormatOption.EnNumeric: {
        //英文，字母或_
        if (!isEnNumeric(srcStr)) {
          strMsg = `${description}格式错误，请输入英文，字母或_！\n`;
        }
        break;
      }
      default: {
        //其他
        strMsg = `${description}类型不能识别！\n`;
        break;
      }
    }
    return strMsg;
  }

}