/**
 * 计算帮组类
 */
export namespace TKCalculatorHelper {
  // 定义常量
  const MAX_NUMBER: number = Number.MAX_SAFE_INTEGER;
  const MIN_NUMBER: number = Number.MIN_SAFE_INTEGER;

  /**
   * 检查数字是否在安全范围内
   */
  function checkNumberRange(num: number): void {
    if (num > MAX_NUMBER || num < MIN_NUMBER) {
      throw new Error('数字超出计算范围');
    }
  }

  /**
   *  验证数字格式
   */
  function isValidNumber(numStr: string): boolean {
    // 匹配科学计数法和普通数字
    const numberPattern = /^-?\d*\.?\d+(?:[eE][\-\+]?\d+)?$/;
    return numberPattern.test(numStr);
  }

  /**
   * 获取运算优先级
   */
  function getPriority(op: string): number {
    switch (op) {
      case '+':
      case '-':
        return 1;
      case '*':
      case '/':
        return 2;
      default:
        return 0;
    }
  }

  /**
   * 算式计算
   */
  function calculate(num1: number, num2: number, operator: string): number {
    let result: number;
    try {
      switch (operator) {
        case '+':
          result = num1 + num2;
          break;
        case '-':
          result = num1 - num2;
          break;
        case '*':
          result = num1 * num2;
          break;
        case '/':
          if (num2 === 0) {
            throw new Error('除数不能为0');
          }
          result = num1 / num2;
          break;
        default:
          throw new Error('不支持的运算符');
      }
      checkNumberRange(result);
      return formatNumber(result);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('计算过程出错');
    }
  }

  /**
   * 格式化数字
   */
  function formatNumber(num: number): number {
    // 处理科学计数法的情况
    if (Math.abs(num) < 1e-10) {
      return 0;
    }
    return parseFloat(num.toFixed(10));
  }

  /**
   * 处理表达式
   */
  function preprocessExpression(expression: string): string {
    if (!expression.trim()) {
      throw new Error('表达式不能为空');
    }
    let expr = expression;
    // 检查括号内是否为空
    if (/\(\s*\)/.test(expr)) {
      throw new Error('括号内不能为空');
    }
    // 处理连续的加减号
    while (expr.includes('++') || expr.includes('--') || expr.includes('+-') || expr.includes('-+')) {
      expr = expr.replace(/\+\+/g, '+')
        .replace(/--/g, '+')
        .replace(/\+-/g, '-')
        .replace(/-\+/g, '-');
    }
    // 处理表达式开头的负号
    expr = expr.replace(/^-/, '0-');
    // 处理括号后的负号
    expr = expr.replace(/\(-/g, '(0-');
    // 处理操作符后的负号
    expr = expr.replace(/([+\-*\/])-/g, '$10-');
    // 检查非法字符
    if (/[^0-9+\-*/().\sEe]/.test(expr)) {
      throw new Error('表达式包含非法字符');
    }
    // 检查括号匹配
    let bracketCount = 0;
    for (const char of expr) {
      if (char === '(') {
        bracketCount++;
      }
      if (char === ')') {
        bracketCount--;
      }
      if (bracketCount < 0) {
        throw new Error('括号不匹配');
      }
    }
    if (bracketCount !== 0) {
      throw new Error('括号不匹配');
    }
    // 移除空格
    return expr.replace(/\s/g, '');
  }

  /**
   * 处理运算符
   * @param numbers
   * @param operators
   */
  function processOperation(numbers: number[], operators: string[]): void {
    if (numbers.length < 2 || operators.length < 1) {
      throw new Error('表达式格式错误');
    }
    const operator = operators.pop()!;
    const num2 = numbers.pop()!;
    const num1 = numbers.pop()!;

    const result = calculate(num1, num2, operator);
    numbers.push(result);
  }

  /**
   * 执行表达式，并返回计算结果
   * @param expression
   * @returns
   */
  export function evaluate(expression: string): number {
    const processedExpr = preprocessExpression(expression);
    const numbers: number[] = [];
    const operators: string[] = [];
    let i = 0;
    while (i < processedExpr.length) {
      const currentChar = processedExpr[i];
      // 处理数字（包括科学计数法）
      if ((currentChar >= '0' && currentChar <= '9') || currentChar === '.') {
        let numStr = '';
        // 处理普通数字和小数点
        while (i < processedExpr.length) {
          const char = processedExpr[i];
          // 如果是数字或第一个小数点
          if ((char >= '0' && char <= '9') ||
            (char === '.' && !numStr.includes('.'))) {
            numStr += char;
            i++;
            continue;
          }
          // 处理科学计数法
          if ((char === 'e' || char === 'E') && numStr.length > 0) {
            numStr += char;
            i++;
            // 处理科学计数法后的正负号
            if (i < processedExpr.length && (processedExpr[i] === '+' || processedExpr[i] === '-')) {
              numStr += processedExpr[i];
              i++;
            }
            // 继续读取科学计数法的指数部分
            while (i < processedExpr.length && processedExpr[i] >= '0' && processedExpr[i] <= '9') {
              numStr += processedExpr[i];
              i++;
            }
            break;
          }
          break;
        }
        if (!isValidNumber(numStr)) {
          throw new Error(`无效的数字格式: ${numStr}`);
        }
        const num = parseFloat(numStr);
        checkNumberRange(num);
        numbers.push(num);
        continue;
      }
      if (currentChar === '(') {
        operators.push(currentChar);
      } else if (currentChar === ')') {
        while (operators.length > 0 && operators[operators.length - 1] !== '(') {
          processOperation(numbers, operators);
        }
        if (operators.length === 0) {
          throw new Error('括号不匹配');
        }
        operators.pop();
      } else if (['+', '-', '*', '/'].includes(currentChar)) {
        while (operators.length > 0 &&
          operators[operators.length - 1] !== '(' &&
          getPriority(operators[operators.length - 1]) >= getPriority(currentChar)) {
          processOperation(numbers, operators);
        }
        operators.push(currentChar);
      }
      i++;
    }
    while (operators.length > 0) {
      if (operators[operators.length - 1] === '(') {
        throw new Error('括号不匹配');
      }
      processOperation(numbers, operators);
    }
    if (numbers.length !== 1) {
      throw new Error('表达式格式错误');
    }
    return numbers[0];
  }

}