import { TKLog } from '../logger/TKLog';

export type TKTimerHandler = (userInfo?: Object) => void;

/**
 * 定时任务状态
 */
export enum TKTimerState {
  None,
  Start,
  Pause,
  Stop
}

export class TKTimer {
  private interval: number = 0;
  private handler: TKTimerHandler | undefined = undefined;
  private userInfo: Object | undefined = undefined;
  private state: TKTimerState = TKTimerState.None;
  private timer: number | undefined = undefined;

  public constructor(interval: number, target: Object, handler: TKTimerHandler, userInfo?: Object) {
    this.interval = interval;
    this.handler = handler.bind(target);
    this.userInfo = userInfo;
  }

  /**
   * 开始
   */
  public start() {
    this.state = TKTimerState.Start;
    if (!this.timer) {
      this.timer = setInterval(() => {
        if (this.state == TKTimerState.Start) {
          if (this.handler) {
            try {
              this.handler(this.userInfo);
            } catch (error) {
              TKLog.error(`定时任务处理异常 ~ code: ${error.code} -·- message: ${error.message}`);
            }
          }
        }
      }, this.interval);
    }
  }

  /**
   * 暂停
   */
  public pause() {
    this.state = TKTimerState.Pause;
  }

  /**
   * 恢复
   */
  public resume() {
    this.state = TKTimerState.Start;
  }

  /**
   * 结束
   */
  public stop() {
    this.state = TKTimerState.Stop;
    if (this.timer !== undefined) {
      clearInterval(this.timer);
      this.timer = undefined;
    }
    this.handler = undefined;
    this.userInfo = undefined;
  }
}
