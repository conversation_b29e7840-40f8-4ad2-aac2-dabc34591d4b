/**
 * 通用对象操作帮组类
 */
import { HashMap, HashSet, TreeMap, TreeSet } from '@kit.ArkTS';
import { TKLog } from '../logger/TKLog';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKDataHelper } from './TKDataHelper';
import { TKMapHelper } from '../map/TKMapHelper';

export type TKJson = Record<string, Object> | Array<Record<string, Object>> | undefined;

export namespace TKObjectHelper {

  /**
   * 修复对象的默认属性
   * @param obj  对象值
   * @param TClass 对象类
   * @returns
   */
  export function fixDefault<T>(targetObj: T, TClass: Function, sourceObj?: T): T {
    let initObject: T = TKObjectHelper.assign(new (TClass as FunctionConstructor)() as Object as T, targetObj);
    targetObj = TKObjectHelper.assign(targetObj, initObject);
    targetObj = TKObjectHelper.assign(targetObj, sourceObj as T);
    return targetObj;
  }

  /**
   * 对象赋值拷贝
   * @param target  目标对象
   * @param source  来源对象
   * @param isDeep  是否深度
   * @param isOnlyOverwrite  是否仅仅进行覆盖逻辑，如果是true那就意味着target的key只做覆盖，不做合并
   */
  export function assign<T>(target: T, source: T, isDeep: boolean = false, isOnlyOverwrite: boolean = false): T {
    if (source === undefined || source === null || target === undefined || target === null) {
      target = target ?? source as T;
      return target;
    } else if (Array.isArray(source)) {
      target = source as T;
      return target;
    } else if ((source instanceof Set) || (source instanceof HashSet) || (source instanceof TreeSet)) {
      target = source as T;
      return target;
    } else if ((source instanceof Map) || (source instanceof HashMap) || (source instanceof TreeMap)) {
      source.forEach((value: Object, key: string) => {
        const sourceKey: string = key;
        const sourceValue: Object = value;
        if (!isOnlyOverwrite || TKMapHelper.containKey(target, sourceKey)) {
          if ((target instanceof Map) || (target instanceof HashMap) || (target instanceof TreeMap)) {
            const targetValue: Object =
              (target as Map<string, Object> | HashMap<string, Object> | TreeMap<string, Object>).get(sourceKey) as Object;
            if (isDeep) {
              (target as Map<string, Object> | HashMap<string, Object> | TreeMap<string, Object>).set(sourceKey,
                assign(targetValue, sourceValue, isDeep));
            } else {
              (target as Map<string, Object> | HashMap<string, Object> | TreeMap<string, Object>).set(sourceKey,
                sourceValue ?? targetValue);
            }
          } else {
            const targetValue: Object = (target as Record<string, Object>)[sourceKey] as Object;
            if (isDeep) {
              (target as Record<string, Object>)[sourceKey] = assign(targetValue, sourceValue, isDeep);
            } else {
              (target as Record<string, Object>)[sourceKey] = sourceValue ?? targetValue;
            }
          }
        }
      });
      return target;
    } else if (!(source instanceof Uint8Array) && typeof source === 'object') {
      Object.entries(source as Record<string, Object>).forEach((e) => {
        const sourceKey: string = e[0];
        const sourceValue: Object = e[1];
        if (!isOnlyOverwrite || TKMapHelper.containKey(target, sourceKey)) {
          if ((target instanceof Map) || (target instanceof HashMap) || (target instanceof TreeMap)) {
            const targetValue: Object =
              (target as Map<string, Object> | HashMap<string, Object> | TreeMap<string, Object>).get(sourceKey) as Object;
            if (isDeep) {
              (target as Map<string, Object> | HashMap<string, Object> | TreeMap<string, Object>).set(sourceKey,
                assign(targetValue, sourceValue, isDeep));
            } else {
              (target as Map<string, Object> | HashMap<string, Object> | TreeMap<string, Object>).set(sourceKey,
                sourceValue ?? targetValue);
            }
          } else {
            const targetValue: Object = (target as Record<string, Object>)[sourceKey] as Object;
            if (isDeep) {
              (target as Record<string, Object>)[sourceKey] = assign(targetValue, sourceValue, isDeep);
            } else {
              (target as Record<string, Object>)[sourceKey] = sourceValue ?? targetValue;
            }
          }
        }
      });
      return target;
    } else {
      target = source as T;
      return target;
    }
  }

  /**
   * 对象赋值拷贝
   * @param source  来源对象
   * @param isDeep  是否深度
   */
  export function copy<T>(source: T, isDeep: boolean = false): T {
    let target: T = undefined as T;
    if (typeof source === 'object') {
      let TClass: FunctionConstructor | undefined =
        (source as Object).constructor ? (source as Object).constructor as FunctionConstructor : undefined;
      target = (TClass ? new TClass() as Object as Record<string, Object> : {} as Record<string, Object>) as T;
    }
    return assign(target, source, isDeep);
  }

  /**
   * 对象转换格式化
   * @param obj
   * @returns
   */
  function convertSerializeObject(obj: Object, isSerialize: boolean = true): Object {
    if (obj === null || obj === undefined) {
      return obj;
    } else if (Array.isArray(obj)) {
      const jsonArray: Array<Object> = obj.map((item: Object) => convertSerializeObject(item, isSerialize));
      return jsonArray;
    } else if ((obj instanceof Set) || (obj instanceof HashSet) || (obj instanceof TreeSet)) {
      let jsonArray: Array<Object> = new Array<Object>();
      obj.forEach((value: Object) => {
        jsonArray.push(convertSerializeObject(value, isSerialize));
      });
      return isSerialize ? {
        '@dataType': (obj instanceof Set) ? 'Set' : ((obj instanceof HashSet) ? 'HashSet' : 'TreeSet'),
        '@value': jsonArray
      } as Record<string, Object> : jsonArray;
    } else if ((obj instanceof Map) || (obj instanceof HashMap) || (obj instanceof TreeMap)) {
      const json: Record<string, Object> = {};
      obj.forEach((value: Object, key: string) => {
        json[key] = convertSerializeObject(value, isSerialize);
      });
      return isSerialize ? {
        '@dataType': (obj instanceof Map) ? 'Map' : ((obj instanceof HashMap) ? 'HashMap' : 'TreeMap'),
        '@value': json
      } as Record<string, Object> : json;
    } else if (!(obj instanceof Uint8Array) && typeof obj === 'object') {
      const json: Record<string, Object> = {};
      Object.entries(obj as Record<string, Object>).forEach((e) => {
        const key: string = e[0];
        const value: Object = e[1];
        json[key] = convertSerializeObject(value, isSerialize);
      });
      return json;
    } else {
      return obj;
    }
  }

  /**
   * 对象转换格式化
   * @param obj
   * @returns
   */
  function convertDeserializeObject(obj: Object): Object {
    if (obj === null || obj === undefined) {
      return obj;
    } else if (Array.isArray(obj)) {
      const objArray: Array<Object> = obj.map((item: Object) => convertDeserializeObject(item));
      return objArray;
    } else if (!(obj instanceof Uint8Array) && typeof obj === 'object') {
      let dataType: string = (obj as Record<string, Object>)["@dataType"] as string;
      if ((dataType === "Set") || (dataType === "HashSet") || (dataType === "TreeSet")) {
        const set = (dataType === "Set") ? new Set<Object>() :
          ((dataType === "HashSet") ? new HashSet<Object>() : new TreeSet<Object>());
        ((obj as Record<string, Object>)["@value"] as Array<Object>).forEach(value => {
          set.add(convertDeserializeObject(value));
        })
        return set;
      } else if ((dataType === "Map") || (dataType === "HashMap") || (dataType === "TreeMap")) {
        const map = (dataType === "Map") ? new Map<string, Object>() :
          ((dataType === "HashMap") ? new HashMap<string, Object>() : new TreeMap<string, Object>());
        Object.entries((obj as Record<string, Object>)["@value"] as Record<string, Object>).forEach((e) => {
          const key: string = e[0];
          const value: Object = e[1];
          map.set(key, convertDeserializeObject(value));
        });
        return map;
      } else {
        let json: Record<string, Object> = {};
        Object.entries(obj as Record<string, Object>).forEach((e) => {
          const key: string = e[0];
          const value: Object = e[1];
          json[key] = convertDeserializeObject(value);
        });
        return json;
      }
    }
    return obj;
  }

  /**
   * 对象序列化
   * @param obj  对象
   * @param isHumpToUnderLine 是否驼峰转下划线
   * @returns
   */
  export function serialize<T>(obj: T, isHumpToUnderLine: boolean = false): string | undefined {
    try {
      if (obj !== null && obj !== undefined) {
        let json: Object = convertSerializeObject(obj);
        if (isHumpToUnderLine) {
          if (Array.isArray(json)) {
            json = json.map((item: Object) => TKDataHelper.humpObjToUnderLineObj(item));
          } else {
            json = TKDataHelper.humpObjToUnderLineObj(json as object);
          }
        }
        return JSON.stringify(json);
      }
    } catch (error) {
      TKLog.error(`[TKObjectHelper]对象序列化异常，code: ${error.code}， message: ${error.message}`);
    }
    return undefined;
  }

  /**
   * 对象反序列化
   * @param jsonString
   * @param isUnderLineToHump 是否下划线转驼峰
   * @returns
   */
  export function deserialize<T>(jsonString: string | undefined, TClass?: Function,
    isUnderLineToHump: boolean = false): T | Array<T> | undefined {
    try {
      if (TKStringHelper.isNotBlank(jsonString)) {
        let json: Object = JSON.parse(jsonString as string) as Object;
        let deserializeObject: Object = convertDeserializeObject(json) as Object;
        return toObject(deserializeObject, TClass, isUnderLineToHump);
      }
    } catch (error) {
      TKLog.error(`[TKObjectHelper]对象反序列化异常，code: ${error.code}， message: ${error.message}`);
    }
    return undefined;
  }

  /**
   * 通用对象转json
   * @param obj
   * @returns
   */
  export function toJson<T>(obj: T, isHumpToUnderLine: boolean = false): TKJson {
    let json: TKJson = undefined;
    try {
      if (obj !== null && obj !== undefined) {
        let lastObj: Object = obj as Object;
        if (typeof obj === "string" && ((obj.trim().startsWith("{") && obj.trim().endsWith("}")) ||
          (obj.trim().startsWith("[") && obj.trim().endsWith("]")))) {
          lastObj = JSON.parse(obj);
        }
        json = convertSerializeObject(lastObj, false) as TKJson;
        if (isHumpToUnderLine) {
          if (Array.isArray(json)) {
            json = json.map((item: Object) => TKDataHelper.humpObjToUnderLineObj(item)) as TKJson;
          } else {
            json = TKDataHelper.humpObjToUnderLineObj(json as object) as TKJson;
          }
        }
      }
    } catch (error) {
      TKLog.error(`[TKObjectHelper]对象转换JSON异常，code: ${error.code}， message: ${error.message}`);
    }
    return json;
  }

  /**
   * 通用对象转jsonStr
   * @param obj
   * @returns
   */
  export function toJsonStr<T>(obj: T): string {
    let json: TKJson = toJson(obj);
    return (json !== undefined && json !== null) ? JSON.stringify(json) : "";
  }

  /**
   * 对象复制
   * @param obj
   * @returns
   */
  export function clone<T>(obj: T): T {
    let source: Object = obj as Object;
    if (source === null || source === undefined) {
      return source as T;
    } else if (Array.isArray(source)) {
      return source.map((value: Object) => clone(value)) as T;
    } else if ((source instanceof Set) || (source instanceof HashSet) || (source instanceof TreeSet)) {
      let target = (source instanceof Set) ? new Set<Object>() :
        ((source instanceof HashSet) ? new HashSet<Object>() : new TreeSet<Object>())
      source.forEach((value: Object) => {
        target.add(clone(value));
      });
      return target as T;
    } else if ((source instanceof Map) || (source instanceof HashMap) || (source instanceof TreeMap)) {
      let target = (source instanceof Map) ? new Map<string, Object>() :
        ((source instanceof HashMap) ? new HashMap<string, Object>() : new TreeMap<string, Object>())
      source.forEach((value: Object, key: string) => {
        target.set(key, clone(value));
      });
      return target as T;
    } else if (!(source instanceof Uint8Array) && typeof source === 'object') {
      let TClass: FunctionConstructor | undefined =
        source.constructor ? source.constructor as FunctionConstructor : undefined;
      let target: Record<string, Object> = TClass ? new TClass() as Object as Record<string, Object> : {};
      Object.entries(source as Record<string, Object>).forEach((e) => {
        const key: string = e[0];
        const value: Object = e[1];
        if (value instanceof WeakRef) {
          target[key] = value;
        } else {
          target[key] = clone(value);
        }
      });
      return target as T;
    } else {
      return source as T;
    }
  }

  /**
   * JSON转对象
   * @param json
   * @param TClass
   * @param isUnderLineToHump 是否下划线转驼峰
   * @param isOnlyOverwrite  是否仅仅进行覆盖逻辑，如果是true那就意味着target的key只做覆盖，不做合并
   * @returns
   */
  export function toObject<T>(json: Object, TClass?: Function,
    isUnderLineToHump: boolean = false, isOnlyOverwrite: boolean = false): T | Array<T> | undefined {
    try {
      let jsonObj: Object | null | undefined = (typeof json === "string") ? JSON.parse(json) : json as Object;
      if (jsonObj !== null && jsonObj !== undefined) {
        if (isUnderLineToHump) {
          if (Array.isArray(jsonObj)) {
            jsonObj = jsonObj.map((item: Object) => TKDataHelper.underLineObjToHumpObj(item));
          } else {
            jsonObj = TKDataHelper.underLineObjToHumpObj(jsonObj);
          }
        }
      }
      if (Array.isArray(jsonObj)) {
        let instanceArray: Array<T> = new Array<T>();
        if (jsonObj !== null && jsonObj !== undefined) {
          jsonObj.forEach((value: Object) => {
            if (TClass) {
              let instance: T = new (TClass as FunctionConstructor)() as T;
              value = formatJsonObj(value);
              instance = assign<T>(instance, value as T, true, isOnlyOverwrite) as T;
              instanceArray.push(instance);
            } else {
              instanceArray.push(value as T);
            }
          });
        }
        return instanceArray;
      } else {
        if (TClass) {
          let instance: T = new (TClass as FunctionConstructor)() as T;
          if (jsonObj !== null && jsonObj !== undefined) {
            jsonObj = formatJsonObj(jsonObj);
            instance = assign<T>(instance, jsonObj as T, true, isOnlyOverwrite) as T;
          }
          return instance;
        } else {
          return jsonObj as T;
        }
      }
    } catch (error) {
      TKLog.error(`[TKObjectHelper]JSON转对象异常，code: ${error.code}， message: ${error.message}`);
    }
    return undefined;
  }

  /**
   * 格式化json对象，去掉系统拼接的冗余参数
   * @param jsonObj
   * @returns
   */
  function formatJsonObj(jsonObj: Object): Object {
    let keepKeys: Array<string> =
      ["@srcSystemBarEnable", "@systemBarEnable", "@srcBackgroundColor", "@backgroundColor", "@srcStatusStyle",
        "@statusStyle", "@srcLayoutFullScreen", "@layoutFullScreen", "@srcSystemBarProperties", "@systemBarProperties"];
    let formatJson: Record<string, Object> = {};
    if (jsonObj !== null && jsonObj !== undefined) {
      Object.entries(jsonObj as Record<string, Object>).forEach((e) => {
        const key: string = e[0];
        const value: Object = e[1];
        if (!keepKeys.includes(key)) {
          formatJson[key] = value;
        }
      });
    } else {
      formatJson = jsonObj as Record<string, Object>;
    }
    return formatJson;
  }

  /**
   * 是否为空对象
   * @param obj
   */
  export function isNull<T>(obj: T): boolean {
    return obj === undefined || obj === null;
  }

  /**
   * 是否不为空对象
   * @param obj
   */
  export function nonNull<T>(obj: T): boolean {
    return obj !== undefined && obj !== null;
  }
}