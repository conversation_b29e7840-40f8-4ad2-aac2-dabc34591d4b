/**
 * 数据转换帮组类
 */
import { buffer, HashMap, TreeMap, util } from '@kit.ArkTS';
import { TKLog } from '../logger/TKLog';
import { TKStringHelper } from '../string/TKStringHelper';

export namespace TKDataHelper {

  export const CHAR_GBK: string = "gbk";

  export const CHAR_UTF8: string = "utf-8";

  /**
   * Map转JSON对象
   * @param map
   * @returns
   */
  export function mapToJson(map: Map<string, Object>): Record<string, Object> {
    const json: Record<string, Object> = {};
    try {
      map.forEach((value, key) => {
        if (value instanceof Map) {
          json[key] = mapToJson(value);
        } else if (Array.isArray(value)) {
          json[key] = (value as Array<Object>).map(item => {
            if (item instanceof Map) {
              return mapToJson(item);
            } else {
              return item;
            }
          });
        } else {
          json[key] = value;
        }
      });
    } catch (error) {
      TKLog.error(`[TKDataHelper]Map转JSON对象异常，code: ${error.code}， message: ${error.message}`);
    }
    return json;
  }

  /**
   * Map转JSON字符串
   * @param map
   * @returns
   */
  export function mapToJsonString(map: Map<string, Object>): string {
    const json: Record<string, Object> = mapToJson(map);
    return JSON.stringify(json);
  }

  /**
   * JSON对象或者字符串转Map对象
   * @param json
   * @returns
   */
  export function jsonToMap(json: Record<string, Object> | string): Map<string, Object> {
    const map: Map<string, Object> = new Map<string, Object>();
    if (json) {
      try {
        let data: Record<string, Object> =
          (typeof json === "string") ? JSON.parse(json as string) as Record<string, Object> :
            json as Record<string, Object>;
        Object.entries(data).forEach((e, i) => {
          const key = e[0];
          const value = e[1];
          if (Array.isArray(value)) {
            const newArray = (value as Array<Object>).map((item) => {
              if (!(item instanceof Uint8Array) && typeof item === 'object' && item !== null && item !== undefined) {
                return jsonToMap(item as Record<string, Object>);
              }
              return item;
            });
            map.set(key, newArray);
          } else if (!(value instanceof Uint8Array) && typeof value === 'object' && value !== null &&
            value !== undefined) {
            map.set(key, jsonToMap(value as Record<string, Object>));
          } else {
            map.set(key, value);
          }
        });
      } catch (error) {
        TKLog.error(`[TKDataHelper]JSON对象或者字符串转Map对象异常，code: ${error.code}， message: ${error.message}`);
      }
    }
    return map;
  }

  /**
   * 字符串转数字
   * @param str
   * @param defValue
   * @returns
   */
  export function stringToNumber(str: string, defValue: number = 0): number {
    let result: number = defValue;
    try {
      if (str) {
        result = Number(str);
      }
    } catch (error) {
      TKLog.error(`[TKDataHelper]字符串转数字异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * 字符串转 Uint8Array
   * @param str
   * @returns
   */
  export function stringToUint8Array(str: string | undefined | null, encoding: string = "utf-8"): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    if (str) {
      try {
        result = new util.TextEncoder(encoding).encodeInto(str) ?? new Uint8Array();
      } catch (error) {
        TKLog.error(`[TKDataHelper]字符串转Uint8Array异常，code: ${error.code}， message: ${error.message}`);
      }
    }
    return result;
  }

  /**
   * Uint8Array 转字符串
   * @param str
   * @returns
   */
  export function uint8ArrayToString(uint8Array: Uint8Array | ArrayBuffer | undefined | null,
    encoding: string = "utf-8"): string {
    let result: string = "";
    if (uint8Array) {
      uint8Array = (uint8Array instanceof Uint8Array) ? uint8Array : new Uint8Array(uint8Array);
      try {
        result = util.TextDecoder.create(encoding).decodeWithStream(uint8Array as Uint8Array);
      } catch (error) {
        TKLog.error(`[TKDataHelper]Uint8Array转字符串异常，code: ${error.code}， message: ${error.message}`);
      }
    }
    return result;
  }

  /**
   * Uint8Array 转ArrayBuffer
   * @param str
   * @returns
   */
  export function arrayBufferToUint8Array(arrayBuffer: ArrayBuffer): Uint8Array {
    if (arrayBuffer && arrayBuffer.byteLength > 0) {
      return new Uint8Array(arrayBuffer);
    }
    return new Uint8Array();
  }

  /**
   * Uint8Array 转ArrayBuffer
   * @param str
   * @returns
   */
  export function uint8ArrayToArrayBuffer(uint8Array: Uint8Array | Array<number>): ArrayBuffer {
    if (uint8Array && uint8Array.length > 0) {
      return buffer.from(new Uint8Array(uint8Array)).buffer;
    }
    return new ArrayBuffer(0);
  }

  /**
   *  数据字典转字符串
   *
   * @param paramMap    要转换的数据字典
   * @param split0      第一层分割符号   ,
   * @param split1      第二层分隔符    =
   *
   * @return 转换后的字符串 name=liubao,age=20,lover=xiaozhu
   */
  export function mapToFormatString(paramMap: Map<string, Object> | Record<string, Object>, split0: string,
    split1: string): string {
    if (paramMap) {
      if (paramMap instanceof Map) {
        return Array.from(paramMap.entries()).map(entry => entry.join(split1)).join(split0);
      } else {
        return Array.from(Object.entries(paramMap)).map(entry => entry.join(split1)).join(split0);
      }
    }
    return "";
  }

  /**
   *  字符串转成数据字典
   *
   * @param str    要转换的字符串 例如name=liubao,age=20,lover=xiaozhu
   * @param split0 第一层分割符号  ,
   * @param split1 第二层分隔符    =
   *
   * @return 转换后的数据字典
   */
  export function stringToFormatMap(str: string, split0: string, split1: string): Map<string, Object> {
    let result: Map<string, Object> = new Map<string, Object>();
    if (TKStringHelper.isNotBlank(str)) {
      str.split(split0).forEach((key, index) => {
        let temp: Array<string> = key.split(split1);
        result.set(temp[0], temp[1]);
      });
    }
    return result;
  }

  /**
   *  数据字典转url参数字符串
   *
   * @param dic    要转换的数据字典
   *
   * @return 转换后的url参数字符串
   */
  export function mapToFormatUrl(paramMap: Map<string, Object> | Record<string, Object>): string {
    return mapToFormatString(paramMap, "&", "=");
  }

  /**
   *  url参数字符串转成数据字典
   *
   * @param url    要转换的url地址
   *
   * @return 转换后的数据字典
   */
  export function urlToFormatMap(url: string): Map<string, Object> {
    if (TKStringHelper.isNotBlank(url)) {
      let temp: Array<string> = url.split("?");
      if (temp.length > 1) {
        return stringToFormatMap(temp[1], "&", "=");
      } else {
        return stringToFormatMap(temp[0], "&", "=");
      }
    }
    return new Map<string, Object>();
  }

  /**
   * 补位逻辑
   * @param array
   * @returns
   */
  export function padUint8Array(array: Uint8Array, paddingLength: number = 16): Uint8Array {
    const length: number = array.length;
    const paddedLength: number = Math.ceil(length / paddingLength) * paddingLength;
    const paddedArray: Uint8Array = new Uint8Array(paddedLength);
    paddedArray.set(array);
    return paddedArray;
  }

  /**
   * 获取Bool类型的值
   * @param value
   * @returns
   */
  export function getBoolean(value: Object | undefined): boolean {
    if (value !== null && value !== undefined) {
      value = value.toString().toLowerCase();
      return (value != "0" && value != "" && value != "false");
    } else {
      return false;
    }
  }

  /**
   * 驼峰转下划线
   */
  export function humpToUnderLine(str: string): string {
    return str.replace(/([A-Z])/g, "_$1").toLowerCase();
  }

  /**
   * 下划线转驼峰
   */
  export function underLineToHump(str: string): string {
    let temp: RegExpMatchArray | null = str.match(/_(.)/g);
    if (temp && temp.length > 0) {
      for (let i = 0; i < temp.length; i++) {
        str = str.replace(temp[i], temp[i].replace("_", "").toUpperCase());
      }
    }
    return str;
  }

  /**
   * 驼峰对象转下划线对象
   */
  export function humpObjToUnderLineObj(humpObj: Object): Object {
    if (humpObj === null || humpObj === undefined) {
      return humpObj;
    }
    let underLineObj: Record<string, Object> = {};
    if ((humpObj instanceof Map) || (humpObj instanceof HashMap) || (humpObj instanceof TreeMap)) {
      humpObj.forEach((value: Object, key: string) => {
        if (Array.isArray(value)) {
          value = value.map((item: Object) => humpObjToUnderLineObj(item));
        } else if (!(value instanceof Uint8Array) && typeof value === 'object') {
          value = humpObjToUnderLineObj(value);
        }
        underLineObj[key] = value;
      });
    } else {
      Object.entries(humpObj).forEach((e: Array<Object>, i) => {
        let key: string = humpToUnderLine(String(e[0]));
        let value: Object = e[1] as Object;
        if (Array.isArray(value)) {
          value = value.map((item: Object) => humpObjToUnderLineObj(item));
        } else if (!(value instanceof Uint8Array) && typeof value === 'object') {
          value = humpObjToUnderLineObj(value);
        }
        underLineObj[key] = value;
      });
    }
    return underLineObj;
  }

  /**
   * 下划线对象转驼峰对象
   */
  export function underLineObjToHumpObj(underLineObj: Object): Object {
    if (underLineObj === null || underLineObj === undefined) {
      return underLineObj;
    }
    let humpObj: Record<string, Object> = {};
    if ((underLineObj instanceof Map) || (underLineObj instanceof HashMap) || (underLineObj instanceof TreeMap)) {
      underLineObj.forEach((value: Object, key: string) => {
        if (Array.isArray(value)) {
          value = value.map((item: Object) => underLineObjToHumpObj(item));
        } else if (!(value instanceof Uint8Array) && typeof value === 'object') {
          value = underLineObjToHumpObj(value);
        }
        humpObj[key] = value;
      });
    } else {
      Object.entries(underLineObj).forEach((e: Array<Object>, i) => {
        let key: string = underLineToHump(String(e[0]));
        let value: Object = e[1] as Object;
        if (Array.isArray(value)) {
          value = value.map((item: Object) => underLineObjToHumpObj(item));
        } else if (!(value instanceof Uint8Array) && typeof value === 'object') {
          value = underLineObjToHumpObj(value);
        }
        humpObj[key] = value;
      });
    }
    return humpObj;
  }

}