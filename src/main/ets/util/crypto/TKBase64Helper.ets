/**
 * Base64编码
 */
import { util } from '@kit.ArkTS';
import { TKLog } from '../../util/logger/TKLog';
import { TKDataHelper } from '../data/TKDataHelper';

export namespace TKBase64Helper {

  /**
   *  base64加密
   *
   * @param content 需要加密的数据
   *
   * @return 加密后的字符串
   */
  export function stringWithBase64Encode(content: string | Uint8Array): string {
    let result: Uint8Array = dataWithBase64Encode(content);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   *  base64解密
   *
   * @param content 需要解密的数据
   *
   * @return 解密后后的字符串
   */
  export function stringWithBase64Decode(content: string | Uint8Array): string {
    let result: Uint8Array = dataWithBase64Decode(content);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   *  base64加密
   *
   * @param content 需要加密的数据
   *
   * @return 加密后的数据
   */
  export function dataWithBase64Encode(content: string | Uint8Array): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content) {
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        if (data && data.length > 0) {
          result = new util.Base64Helper().encodeSync(data);
        }
      }
    } catch (error) {
      TKLog.error(`[TKBase64Helper]Base64编码异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  base64解密
   *
   * @param content 需要解密的数据
   *
   * @return 解密后的数据
   */
  export function dataWithBase64Decode(content: string | Uint8Array): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content) {
        if (typeof content === "string") {
          const reg: RegExp = new RegExp('data:image/\\w+;base64,');
          content = content.startsWith("data:image") ? content.replace(reg, '') : content;
        }
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        if (data && data.length > 0) {
          result = new util.Base64Helper().decodeSync(data);
        }
      }
    } catch (error) {
      TKLog.error(`[TKBase64Helper]Base64解码异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

}