/**
 * uuid帮组类
 */
export namespace TKUUIDHelper {

  /**
   * <AUTHOR> 2014-12-04 11:12:13
   *
   *  获取uuid
   *
   * @return uuid
   */
  export function uuid(): string {
    let template = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";
    return template.replace(/[xy]/g, c => {
      let r = Math.random() * 16 | 0;
      let v = (c == 'x') ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

}
