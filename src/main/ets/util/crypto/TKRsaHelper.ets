/**
 * RSA加解密
 */
import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import certFramework from '@ohos.security.cert';
import { TKDataHelper } from '../data/TKDataHelper';
import { TKFileHelper } from '../file/TKFileHelper';
import { TKLog } from '../logger/TKLog';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKAesHelper } from './TKAesHelper';
import { TKBase64Helper } from './TKBase64Helper';
import { TKHexHelper } from './TKHexHelper';
import { TKPasswordGenerator } from './TKPasswordGenerator';

export namespace TKRsaHelper {

  /** */
  /**
   * RSA最大加密明文大小
   */
  const MAX_ENCRYPT_BLOCK: number = 117;

  /** */
  /**
   * RSA最大解密密文大小
   */
  const MAX_DECRYPT_BLOCK: number = 128;

  const BEGIN_PUBLIC_KEY: string = "-----BEGIN PUBLIC KEY-----";

  const END_PUBLIC_KEY: string = "-----END PUBLIC KEY-----";

  const BEGIN_PRIVATE_KEY: string = "-----BEGIN PRIVATE KEY-----";

  const END_PRIVATE_KEY: string = "-----END PRIVATE KEY-----";

  const BEGIN_RSA_PRIVATE_KEY: string = "-----BEGIN RSA PRIVATE KEY-----";

  const END_RSA_PRIVATE_KEY: string = "-----END RSA PRIVATE KEY-----";

  /**
   *
   * 描述：生成公钥
   *
   * @param modulus
   *            加密模数
   * @param publicExponent
   *            公钥指数
   * @return
   */
  async function generateRSAPublicKey(modulus: string,
    publicExponent: string): Promise<cryptoFramework.PubKey | undefined> {
    try {
      let n = BigInt(`0x${modulus}`);
      let e = BigInt(`0x${publicExponent}`);
      let d = BigInt(0);
      let rsaCommSpec: cryptoFramework.RSACommonParamsSpec = {
        n: n,
        algName: "RSA",
        specType: cryptoFramework.AsyKeySpecType.COMMON_PARAMS_SPEC
      };
      let rsaKeyPairSpec: cryptoFramework.RSAKeyPairSpec = {
        params: rsaCommSpec,
        pk: e,
        sk: d,
        algName: "RSA",
        specType: cryptoFramework.AsyKeySpecType.KEY_PAIR_SPEC
      };
      let rsaGeneratorSpec = cryptoFramework.createAsyKeyGeneratorBySpec(rsaKeyPairSpec);
      return rsaGeneratorSpec.generatePubKey();
    } catch (error) {
      TKLog.error(`[TKRsaHelper]异步生成公钥异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成私钥
   *
   * @param modulus
   *            加密模数
   * @param privateExponent
   *            私钥指数
   * @return
   */
  async function generateRSAPrivateKey(modulus: string,
    privateExponent: string): Promise<cryptoFramework.PriKey | undefined> {
    try {
      let n = BigInt(`0x${modulus}`);
      let e = BigInt('0x010001');
      let d = BigInt(`0x${privateExponent}`);
      let rsaCommSpec: cryptoFramework.RSACommonParamsSpec = {
        n: n,
        algName: "RSA",
        specType: cryptoFramework.AsyKeySpecType.COMMON_PARAMS_SPEC
      };
      let rsaKeyPairSpec: cryptoFramework.RSAKeyPairSpec = {
        params: rsaCommSpec,
        pk: e,
        sk: d,
        algName: "RSA",
        specType: cryptoFramework.AsyKeySpecType.KEY_PAIR_SPEC
      };
      let rsaGeneratorSpec = cryptoFramework.createAsyKeyGeneratorBySpec(rsaKeyPairSpec);
      return rsaGeneratorSpec.generatePriKey();
    } catch (error) {
      TKLog.error(`[TKRsaHelper]异步生成私钥异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成公钥
   *
   * @param pubKey
   *            加密公钥
   * @return
   */
  async function generateRSAPublicKey2(pubKey: string): Promise<cryptoFramework.PubKey | undefined> {
    try {
      let spos: number = pubKey.indexOf(BEGIN_PUBLIC_KEY);
      let epos: number = pubKey.indexOf(END_PUBLIC_KEY);
      if (spos >= 0 && epos > 0) {
        let s: number = spos + BEGIN_PUBLIC_KEY.length;
        let e: number = epos;
        pubKey = pubKey.substring(s, e);
      }
      pubKey = TKStringHelper.replace(pubKey, "\r|\n|\t| ", "");
      let pubKeyBlob: cryptoFramework.DataBlob = { data: TKBase64Helper.dataWithBase64Decode(pubKey) };
      let rsaGenerator = cryptoFramework.createAsyKeyGenerator('RSA1024');
      let keyPair = await rsaGenerator.convertKey(pubKeyBlob, null);
      return keyPair.pubKey;
    } catch (error) {
      TKLog.error(`[TKRsaHelper]异步生成公钥2异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成私钥
   *
   * @param privKey
   *            解密私钥
   * @return
   */
  async function generateRSAPrivateKey2(privKey: string): Promise<cryptoFramework.PriKey | undefined> {
    try {
      let spos: number = privKey.indexOf(BEGIN_RSA_PRIVATE_KEY);
      let epos: number;
      let sposLength: number;
      if (spos >= 0) {
        epos = privKey.indexOf(END_RSA_PRIVATE_KEY);
        sposLength = BEGIN_RSA_PRIVATE_KEY.length;
      } else {
        spos = privKey.indexOf(BEGIN_PRIVATE_KEY);
        epos = privKey.indexOf(END_PRIVATE_KEY);
        sposLength = BEGIN_PRIVATE_KEY.length;
      }
      if (spos >= 0 && epos > 0) {
        let s: number = spos + sposLength;
        let e: number = epos;
        privKey = privKey.substring(s, e);
      }
      privKey = TKStringHelper.replace(privKey, "\r|\n|\t| ", "");
      let priKeyBlob: cryptoFramework.DataBlob = { data: TKBase64Helper.dataWithBase64Decode(privKey) };
      let rsaGenerator = cryptoFramework.createAsyKeyGenerator('RSA1024');
      let keyPair = await rsaGenerator.convertKey(null, priKeyBlob);
      return keyPair.priKey;
    } catch (error) {
      TKLog.error(`[TKRsaHelper]异步生成私钥2异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成公钥
   *
   * @param publicCertFilePath
   *            加密公钥证书路径
   * @return
   */
  async function generateRSAPublicKey3(publicCertFilePath: string,
    isEncryptCer: boolean = false): Promise<cryptoFramework.PubKey | undefined> {
    try {
      let publicCertData: Uint8Array = TKFileHelper.readFile(publicCertFilePath);
      if (isEncryptCer) {
        publicCertData = TKBase64Helper.dataWithBase64Decode(publicCertData);
        publicCertData = TKAesHelper.dataWithAesDecryptSync(publicCertData, TKPasswordGenerator.shareInstance()
          .generatorPassword());
      }
      let encodingBlob: certFramework.EncodingBlob = {
        // 将证书数据从string类型转换成Unit8Array
        data: publicCertData,
        // 证书格式，仅支持PEM和DER。在此示例中，证书为PEM格式
        encodingFormat: certFramework.EncodingFormat.FORMAT_DER
      };
      // 创建X509Cert实例
      let x509Cert: certFramework.X509Cert = await certFramework.createX509Cert(encodingBlob);
      let rsaGenerator: cryptoFramework.AsyKeyGenerator = cryptoFramework.createAsyKeyGenerator('RSA1024');
      let keyPair: cryptoFramework.KeyPair = await rsaGenerator.convertKey(x509Cert.getPublicKey().getEncoded(), null);
      return keyPair.pubKey;
    } catch (error) {
      TKLog.error(`[TKRsaHelper]异步生成公钥3异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成私钥
   *
   * @param privateCertFilePath
   *            加密私钥证书路径
   * @param clientPassword
   *           加密私钥证书密码
   * @return
   */
  async function generateRSAPrivateKey3(privateCertFilePath: string,
    clientPassword: string): Promise<cryptoFramework.PriKey | undefined> {
    try {
      let privateCertData: Uint8Array = TKFileHelper.readFile(privateCertFilePath);
      return generateRSAPrivateKey2(TKDataHelper.uint8ArrayToString(privateCertData));
    } catch (error) {
      TKLog.error(`[TKRsaHelper]异步生成私钥3异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   * RSA公钥加密
   * @param content 原文
   * @param pubKey 公钥
   * @returns
   */
  async function dataWithRsaPublicKeyEncryptInner(content: Uint8Array,
    pubKey: cryptoFramework.PubKey | undefined): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content && content.length > 0) {
        let cipher = cryptoFramework.createCipher("RSA|PKCS1");
        await cipher.init(cryptoFramework.CryptoMode.ENCRYPT_MODE, pubKey as cryptoFramework.PubKey, null);
        let cipherBytes: Array<number> = new Array<number>();
        let plainBytes: Uint8Array = content;
        const blockSize = MAX_ENCRYPT_BLOCK;
        for (let i = 0; i < plainBytes.length; i += blockSize) {
          let updateMessage = plainBytes.subarray(i, i + blockSize);
          let updateMessageBlob: cryptoFramework.DataBlob = { data: updateMessage };
          let updateOutput = await cipher.doFinal(updateMessageBlob);
          cipherBytes.push(...updateOutput.data);
        }
        result = new Uint8Array(cipherBytes);
      }
    } catch (error) {
      TKLog.error(`[TKRsaHelper]异步RSA公钥加密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *
   * 描述：RSA私钥解密
   *
   * @param content
   *            密文
   * @param priKey
   *            私钥
   * @return
   */
  async function dataWithRsaPrivateKeyDecryptInner(content: Uint8Array, priKey: cryptoFramework.PriKey | undefined,
    isNoPadding: boolean = false): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content && content.length > 0) {
        let cipher = cryptoFramework.createCipher("RSA|PKCS1");
        await cipher.init(cryptoFramework.CryptoMode.DECRYPT_MODE, priKey, null);
        let plainBytes: Array<number> = new Array<number>();
        let cipherBytes: Uint8Array = content;
        const blockSize = MAX_DECRYPT_BLOCK;
        for (let i = 0; i < cipherBytes.length; i += blockSize) {
          let updateMessage = cipherBytes.subarray(i, i + blockSize);
          let updateMessageBlob: cryptoFramework.DataBlob = { data: updateMessage };
          let updateOutput = await cipher.doFinal(updateMessageBlob);
          let outputData: Uint8Array = updateOutput.data;
          if (isNoPadding) {
            let idxFirstZero: number = -1;
            let idxNextZero: number = -1;
            for (let index: number = 0; index < outputData.length; index++) {
              if (outputData[index] == 0 || outputData[index] == 0x1) {
                if (idxFirstZero < 0) {
                  idxFirstZero = index;
                } else {
                  idxNextZero = index;
                  break;
                }
              }
            }
            if (idxNextZero < 0) {
              idxNextZero = idxFirstZero;
            }
            outputData = outputData.subarray(idxNextZero + 1, outputData.length);
          }
          plainBytes.push(...outputData);
        }
        result = new Uint8Array(plainBytes);
      }
    } catch (error) {
      TKLog.error(`[TKRsaHelper]异步RSA私钥解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *
   * 描述：RSA公钥加密，对应要用RSA私钥解密,加密后转成16进制字符串
   *
   * @param content
   *            数据
   * @param modulus
   *            模数
   * @param pubExponent
   *            指数
   * @return
   */
  export async function stringWithRsaPublicKeyEncrypt(content: string | Uint8Array, modulus: string,
    pubExponent: string): Promise<string> {
    let result: Uint8Array = await dataWithRsaPublicKeyEncrypt(content, modulus, pubExponent);
    return TKHexHelper.stringWithHexEncode(result);
  }

  /**
   *
   * 描述：RSA公钥加密，对应要用RSA私钥解密
   *
   * @param content
   *            数据
   * @param modulus
   *            模数
   * @param pubExponent
   *            指数
   * @return
   */
  export async function dataWithRsaPublicKeyEncrypt(content: string | Uint8Array, modulus: string,
    pubExponent: string): Promise<Uint8Array> {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
    let pubKey: cryptoFramework.PubKey | undefined = await generateRSAPublicKey(modulus, pubExponent);
    return dataWithRsaPublicKeyEncryptInner(data, pubKey);
  }

  /**
   *
   * 描述：RSA私钥解密，content是16进制加密的字符串
   *
   * @param content
   *            密文
   * @param modulus
   *            模数
   * @param privExponent
   *            指数
   * @return
   */
  export async function stringWithRsaPrivateKeyDecrypt(content: string | Uint8Array, modulus: string,
    privExponent: string): Promise<string> {
    let result: Uint8Array = await dataWithRsaPrivateKeyDecrypt(content, modulus, privExponent);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   *
   * 描述：RSA私钥解密，content是16进制加密的字符串
   *
   * @param content
   *            密文
   * @param modulus
   *            模数
   * @param privExponent
   *            指数
   * @return
   */
  export async function dataWithRsaPrivateKeyDecrypt(content: string | Uint8Array, modulus: string,
    privExponent: string, isNoPadding: boolean = false): Promise<Uint8Array> {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKHexHelper.dataWithHexDecode(content as string);
    let priKey: cryptoFramework.PriKey | undefined = await generateRSAPrivateKey(modulus, privExponent);
    return dataWithRsaPrivateKeyDecryptInner(data, priKey, isNoPadding);
  }

  /**
   *
   * 描述：RSA公钥加密，对应要用RSA私钥解密,加密后转成Base64字符串
   *
   * @param pubKey
   *            公钥Key,Base64格式
   * @param content
   *            字符串
   * @return
   */
  export async function stringWithRsaPublicKeyEncrypt2(content: string | Uint8Array, pubKey: string): Promise<string> {
    let result: Uint8Array = await dataWithRsaPublicKeyEncrypt2(content, pubKey);
    return TKBase64Helper.stringWithBase64Encode(result);
  }


  /**
   *
   * 描述：RSA公钥加密，对应要用RSA私钥解密
   *
   * @param content
   *            数据
   * @param pubKey
   *            公钥Key,Base64格式
   * @return
   */
  export async function dataWithRsaPublicKeyEncrypt2(content: string | Uint8Array,
    pubKey: string): Promise<Uint8Array> {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
    let publicKey: cryptoFramework.PubKey | undefined = await generateRSAPublicKey2(pubKey);
    return dataWithRsaPublicKeyEncryptInner(data, publicKey);
  }

  /**
   *
   * 描述：RSA私钥解密，content是加密后的Base64字符串
   *
   * @param content
   *            字符串
   * @param privKey
   *            私钥Key,Base64格式
   * @return
   */
  export async function stringWithRsaPrivateKeyDecrypt2(content: string | Uint8Array,
    privKey: string): Promise<string> {
    let result: Uint8Array = await dataWithRsaPrivateKeyDecrypt2(content, privKey);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   *
   * 描述：RSA私钥解密，content是加密后的Base64字符串
   *
   * @param content
   *            字符串
   * @param privKey
   *            私钥Key,Base64格式
   * @return
   */
  export async function dataWithRsaPrivateKeyDecrypt2(content: string | Uint8Array, privKey: string,
    isNoPadding: boolean = false): Promise<Uint8Array> {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
    let priKey: cryptoFramework.PriKey | undefined = await generateRSAPrivateKey2(privKey);
    return dataWithRsaPrivateKeyDecryptInner(data, priKey, isNoPadding);
  }

  /**
   *
   * 描述：RSA公钥证书加密
   *
   * @param content
   *            数据
   * @param publicCerFilePath
   *            证书路径
   * @return
   */
  export async function stringWithRsaPublicCertEncrypt(content: string | Uint8Array, publicCerFilePath: string,
    isEncryptCer: boolean = false): Promise<string> {
    let result: Uint8Array = await dataWithRsaPublicCertEncrypt(content, publicCerFilePath, isEncryptCer);
    return TKBase64Helper.stringWithBase64Encode(result);
  }

  /**
   *
   * 描述：RSA公钥证书加密
   *
   * @param content
   *            数据
   * @param publicCerFilePath
   *            证书路径
   * @return
   */
  export async function dataWithRsaPublicCertEncrypt(content: string | Uint8Array, publicCerFilePath: string,
    isEncryptCer: boolean = false): Promise<Uint8Array> {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
    let publicKey: cryptoFramework.PubKey | undefined = await generateRSAPublicKey3(publicCerFilePath, isEncryptCer);
    return dataWithRsaPublicKeyEncryptInner(data, publicKey);
  }

  /**
   *
   * 描述： RSA私钥证书解密，证书文件为p12或者pem格式 作者：刘宝 时间：2020年6月20日 上午9:36:06
   *
   * @param data
   *            数据
   * @param clientPrivateCerFile
   *            证书
   * @param clientPassword
   *            密码
   * @return
   */
  export async function stringWithRsaPrivateCertDecrypt(content: string | Uint8Array, clientPrivateCerFile: string,
    clientPassword: string): Promise<string> {
    let result: Uint8Array = await dataWithRsaPrivateCertDecrypt(content, clientPrivateCerFile, clientPassword);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   *
   * 描述： RSA私钥证书解密，证书文件为p12或者pem格式 作者：刘宝 时间：2020年6月20日 上午9:36:06
   *
   * @param data
   *            数据
   * @param clientPrivateCerFile
   *            证书
   * @param clientPassword
   *            密码
   * @return
   */
  export async function dataWithRsaPrivateCertDecrypt(content: string | Uint8Array, clientPrivateCerFile: string,
    clientPassword: string, isNoPadding: boolean = false): Promise<Uint8Array> {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
    let priKey: cryptoFramework.PriKey | undefined = await generateRSAPrivateKey3(clientPrivateCerFile, clientPassword);
    return dataWithRsaPrivateKeyDecryptInner(data, priKey, isNoPadding);
  }

  /**
   *
   * 描述：生成公钥
   *
   * @param modulus
   *            加密模数
   * @param publicExponent
   *            公钥指数
   * @return
   */
  function generateRSAPublicKeySync(modulus: string,
    publicExponent: string): cryptoFramework.PubKey | undefined {
    try {
      let n = BigInt(`0x${modulus}`);
      let e = BigInt(`0x${publicExponent}`);
      let d = BigInt(0);
      let rsaCommSpec: cryptoFramework.RSACommonParamsSpec = {
        n: n,
        algName: "RSA",
        specType: cryptoFramework.AsyKeySpecType.COMMON_PARAMS_SPEC
      };
      let rsaKeyPairSpec: cryptoFramework.RSAKeyPairSpec = {
        params: rsaCommSpec,
        pk: e,
        sk: d,
        algName: "RSA",
        specType: cryptoFramework.AsyKeySpecType.KEY_PAIR_SPEC
      };
      let rsaGeneratorSpec = cryptoFramework.createAsyKeyGeneratorBySpec(rsaKeyPairSpec);
      return rsaGeneratorSpec.generatePubKeySync();
    } catch (error) {
      TKLog.error(`[TKRsaHelper]同步生成公钥异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成私钥
   *
   * @param modulus
   *            加密模数
   * @param privateExponent
   *            私钥指数
   * @return
   */
  function generateRSAPrivateKeySync(modulus: string,
    privateExponent: string): cryptoFramework.PriKey | undefined {
    try {
      let n = BigInt(`0x${modulus}`);
      let e = BigInt('0x010001');
      let d = BigInt(`0x${privateExponent}`);
      let rsaCommSpec: cryptoFramework.RSACommonParamsSpec = {
        n: n,
        algName: "RSA",
        specType: cryptoFramework.AsyKeySpecType.COMMON_PARAMS_SPEC
      };
      let rsaKeyPairSpec: cryptoFramework.RSAKeyPairSpec = {
        params: rsaCommSpec,
        pk: e,
        sk: d,
        algName: "RSA",
        specType: cryptoFramework.AsyKeySpecType.KEY_PAIR_SPEC
      };
      let rsaGeneratorSpec = cryptoFramework.createAsyKeyGeneratorBySpec(rsaKeyPairSpec);
      return rsaGeneratorSpec.generatePriKeySync();
    } catch (error) {
      TKLog.error(`[TKRsaHelper]同步生成私钥异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成公钥
   *
   * @param pubKey
   *            加密公钥
   * @return
   */
  function generateRSAPublicKey2Sync(pubKey: string): cryptoFramework.PubKey | undefined {
    try {
      let spos: number = pubKey.indexOf(BEGIN_PUBLIC_KEY);
      let epos: number = pubKey.indexOf(END_PUBLIC_KEY);
      if (spos >= 0 && epos > 0) {
        let s: number = spos + BEGIN_PUBLIC_KEY.length;
        let e: number = epos;
        pubKey = pubKey.substring(s, e);
      }
      pubKey = TKStringHelper.replace(pubKey, "\r|\n|\t| ", "");
      let pubKeyBlob: cryptoFramework.DataBlob = { data: TKBase64Helper.dataWithBase64Decode(pubKey) };
      let rsaGenerator = cryptoFramework.createAsyKeyGenerator('RSA1024');
      let keyPair = rsaGenerator.convertKeySync(pubKeyBlob, null);
      return keyPair.pubKey;
    } catch (error) {
      TKLog.error(`[TKRsaHelper]同步生成公钥2异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成私钥
   *
   * @param privKey
   *            解密私钥
   * @return
   */
  function generateRSAPrivateKey2Sync(privKey: string): cryptoFramework.PriKey | undefined {
    try {
      let spos: number = privKey.indexOf(BEGIN_RSA_PRIVATE_KEY);
      let epos: number;
      let sposLength: number;
      if (spos >= 0) {
        epos = privKey.indexOf(END_RSA_PRIVATE_KEY);
        sposLength = BEGIN_RSA_PRIVATE_KEY.length;
      } else {
        spos = privKey.indexOf(BEGIN_PRIVATE_KEY);
        epos = privKey.indexOf(END_PRIVATE_KEY);
        sposLength = BEGIN_PRIVATE_KEY.length;
      }
      if (spos >= 0 && epos > 0) {
        let s: number = spos + sposLength;
        let e: number = epos;
        privKey = privKey.substring(s, e);
      }
      privKey = TKStringHelper.replace(privKey, "\r|\n|\t| ", "");
      let priKeyBlob: cryptoFramework.DataBlob = { data: TKBase64Helper.dataWithBase64Decode(privKey) };
      let rsaGenerator = cryptoFramework.createAsyKeyGenerator('RSA1024');
      let keyPair = rsaGenerator.convertKeySync(null, priKeyBlob);
      return keyPair.priKey;
    } catch (error) {
      TKLog.error(`[TKRsaHelper]同步生成私钥2异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成私钥
   *
   * @param privateCertFilePath
   *            加密私钥证书路径
   * @param clientPassword
   *           加密私钥证书密码
   * @return
   */
  function generateRSAPrivateKey3Sync(privateCertFilePath: string,
    clientPassword: string): cryptoFramework.PriKey | undefined {
    try {
      let privateCertData: Uint8Array = TKFileHelper.readFile(privateCertFilePath);
      return generateRSAPrivateKey2Sync(TKDataHelper.uint8ArrayToString(privateCertData));
    } catch (error) {
      TKLog.error(`[TKRsaHelper]同步生成私钥3异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   * RSA公钥加密
   * @param content 原文
   * @param pubKey 公钥
   * @returns
   */
  function dataWithRsaPublicKeyEncryptInnerSync(content: Uint8Array,
    pubKey: cryptoFramework.PubKey | undefined): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content && content.length > 0) {
        let cipher = cryptoFramework.createCipher("RSA|PKCS1");
        cipher.initSync(cryptoFramework.CryptoMode.ENCRYPT_MODE, pubKey as cryptoFramework.PubKey, null);
        let cipherBytes: Array<number> = new Array<number>();
        let plainBytes: Uint8Array = content;
        const blockSize = MAX_ENCRYPT_BLOCK;
        for (let i = 0; i < plainBytes.length; i += blockSize) {
          let updateMessage = plainBytes.subarray(i, i + blockSize);
          let updateMessageBlob: cryptoFramework.DataBlob = { data: updateMessage };
          let updateOutput = cipher.doFinalSync(updateMessageBlob);
          cipherBytes.push(...updateOutput.data);
        }
        result = new Uint8Array(cipherBytes);
      }
    } catch (error) {
      TKLog.error(`[TKRsaHelper]同步RSA公钥加密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *
   * 描述：RSA私钥解密
   *
   * @param content
   *            密文
   * @param priKey
   *            私钥
   * @return
   */
  function dataWithRsaPrivateKeyDecryptInnerSync(content: Uint8Array, priKey: cryptoFramework.PriKey | undefined,
    isNoPadding: boolean = false): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content && content.length > 0) {
        let cipher = cryptoFramework.createCipher("RSA|PKCS1");
        cipher.initSync(cryptoFramework.CryptoMode.DECRYPT_MODE, priKey, null);
        let plainBytes: Array<number> = new Array<number>();
        let cipherBytes: Uint8Array = content;
        const blockSize = MAX_DECRYPT_BLOCK;
        for (let i = 0; i < cipherBytes.length; i += blockSize) {
          let updateMessage = cipherBytes.subarray(i, i + blockSize);
          let updateMessageBlob: cryptoFramework.DataBlob = { data: updateMessage };
          let updateOutput = cipher.doFinalSync(updateMessageBlob);
          let outputData: Uint8Array = updateOutput.data;
          if (isNoPadding) {
            let idxFirstZero: number = -1;
            let idxNextZero: number = -1;
            for (let index: number = 0; index < outputData.length; index++) {
              if (outputData[index] == 0 || outputData[index] == 0x1) {
                if (idxFirstZero < 0) {
                  idxFirstZero = index;
                } else {
                  idxNextZero = index;
                  break;
                }
              }
            }
            if (idxNextZero < 0) {
              idxNextZero = idxFirstZero;
            }
            outputData = outputData.subarray(idxNextZero + 1, outputData.length);
          }
          plainBytes.push(...outputData);
        }
        result = new Uint8Array(plainBytes);
      }
    } catch (error) {
      TKLog.error(`[TKRsaHelper]同步RSA私钥解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *
   * 描述：RSA公钥加密，对应要用RSA私钥解密,加密后转成16进制字符串
   *
   * @param content
   *            数据
   * @param modulus
   *            模数
   * @param pubExponent
   *            指数
   * @return
   */
  export function stringWithRsaPublicKeyEncryptSync(content: string | Uint8Array, modulus: string,
    pubExponent: string): string {
    let result: Uint8Array = dataWithRsaPublicKeyEncryptSync(content, modulus, pubExponent);
    return TKHexHelper.stringWithHexEncode(result);
  }

  /**
   *
   * 描述：RSA公钥加密，对应要用RSA私钥解密
   *
   * @param content
   *            数据
   * @param modulus
   *            模数
   * @param pubExponent
   *            指数
   * @return
   */
  export function dataWithRsaPublicKeyEncryptSync(content: string | Uint8Array, modulus: string,
    pubExponent: string): Uint8Array {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
    let pubKey: cryptoFramework.PubKey | undefined = generateRSAPublicKeySync(modulus, pubExponent);
    return dataWithRsaPublicKeyEncryptInnerSync(data, pubKey);
  }

  /**
   *
   * 描述：RSA私钥解密，content是16进制加密的字符串
   *
   * @param content
   *            密文
   * @param modulus
   *            模数
   * @param privExponent
   *            指数
   * @return
   */
  export function stringWithRsaPrivateKeyDecryptSync(content: string | Uint8Array, modulus: string,
    privExponent: string): string {
    let result: Uint8Array = dataWithRsaPrivateKeyDecryptSync(content, modulus, privExponent);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   *
   * 描述：RSA私钥解密，content是16进制加密的字符串
   *
   * @param content
   *            密文
   * @param modulus
   *            模数
   * @param privExponent
   *            指数
   * @return
   */
  export function dataWithRsaPrivateKeyDecryptSync(content: string | Uint8Array, modulus: string,
    privExponent: string, isNoPadding: boolean = false): Uint8Array {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKHexHelper.dataWithHexDecode(content as string);
    let priKey: cryptoFramework.PriKey | undefined = generateRSAPrivateKeySync(modulus, privExponent);
    return dataWithRsaPrivateKeyDecryptInnerSync(data, priKey, isNoPadding);
  }

  /**
   *
   * 描述：RSA公钥加密，对应要用RSA私钥解密,加密后转成Base64字符串
   *
   * @param pubKey
   *            公钥Key,Base64格式
   * @param content
   *            字符串
   * @return
   */
  export function stringWithRsaPublicKeyEncrypt2Sync(content: string | Uint8Array, pubKey: string): string {
    let result: Uint8Array = dataWithRsaPublicKeyEncrypt2Sync(content, pubKey);
    return TKBase64Helper.stringWithBase64Encode(result);
  }


  /**
   *
   * 描述：RSA公钥加密，对应要用RSA私钥解密
   *
   * @param content
   *            数据
   * @param pubKey
   *            公钥Key,Base64格式
   * @return
   */
  export function dataWithRsaPublicKeyEncrypt2Sync(content: string | Uint8Array,
    pubKey: string): Uint8Array {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
    let publicKey: cryptoFramework.PubKey | undefined = generateRSAPublicKey2Sync(pubKey);
    return dataWithRsaPublicKeyEncryptInnerSync(data, publicKey);
  }

  /**
   *
   * 描述：RSA私钥解密，content是加密后的Base64字符串
   *
   * @param content
   *            字符串
   * @param privKey
   *            私钥Key,Base64格式
   * @return
   */
  export function stringWithRsaPrivateKeyDecrypt2Sync(content: string | Uint8Array,
    privKey: string): string {
    let result: Uint8Array = dataWithRsaPrivateKeyDecrypt2Sync(content, privKey);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   *
   * 描述：RSA私钥解密，content是加密后的Base64字符串
   *
   * @param content
   *            字符串
   * @param privKey
   *            私钥Key,Base64格式
   * @return
   */
  export function dataWithRsaPrivateKeyDecrypt2Sync(content: string | Uint8Array, privKey: string,
    isNoPadding: boolean = false): Uint8Array {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
    let priKey: cryptoFramework.PriKey | undefined = generateRSAPrivateKey2Sync(privKey);
    return dataWithRsaPrivateKeyDecryptInnerSync(data, priKey, isNoPadding);
  }

  /**
   *
   * 描述： RSA私钥证书解密，证书文件为p12或者pem格式 作者：刘宝 时间：2020年6月20日 上午9:36:06
   *
   * @param data
   *            数据
   * @param clientPrivateCerFile
   *            证书
   * @param clientPassword
   *            密码
   * @return
   */
  export function stringWithRsaPrivateCertDecryptSync(content: string | Uint8Array, clientPrivateCerFile: string,
    clientPassword: string): string {
    let result: Uint8Array = dataWithRsaPrivateCertDecryptSync(content, clientPrivateCerFile, clientPassword);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   *
   * 描述： RSA私钥证书解密，证书文件为p12或者pem格式 作者：刘宝 时间：2020年6月20日 上午9:36:06
   *
   * @param data
   *            数据
   * @param clientPrivateCerFile
   *            证书
   * @param clientPassword
   *            密码
   * @return
   */
  export function dataWithRsaPrivateCertDecryptSync(content: string | Uint8Array, clientPrivateCerFile: string,
    clientPassword: string, isNoPadding: boolean = false): Uint8Array {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
    let priKey: cryptoFramework.PriKey | undefined = generateRSAPrivateKey3Sync(clientPrivateCerFile, clientPassword);
    return dataWithRsaPrivateKeyDecryptInnerSync(data, priKey, isNoPadding);
  }
}