import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { TKDataHelper } from '../data/TKDataHelper';
import { TKLog } from '../logger/TKLog';
import { TKHexHelper } from './TKHexHelper';

/**
 * SM3帮组类
 */
export namespace TKSm3Helper {

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  SM3摘要算法
   *
   * @param content 要编码的内容
   *
   * @return 编码后的字符串
   */
  export async function stringWithSm3(content: string | Uint8Array): Promise<string> {
    let result: Uint8Array = await dataWithSm3(content);
    return TKHexHelper.stringWithHexEncode(result);
  }

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  SM3摘要算法
   *
   * @param content 要编码的内容
   *
   * @return 编码后的字符串
   */
  export async function dataWithSm3(content: string | Uint8Array): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content) {
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        if (data && data.length > 0) {
          let blob: cryptoFramework.DataBlob = { data: data };
          let md: cryptoFramework.Md = cryptoFramework.createMd("SM3");
          await md.update(blob);
          let mdResult: cryptoFramework.DataBlob = await md.digest();
          result = mdResult.data;
        }
      }
    } catch (error) {
      TKLog.error(`[TKSm3Helper]异步SM3摘要算法异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  SM3摘要算法
   *
   * @param content 要编码的内容
   *
   * @return 编码后的字符串
   */
  export function stringWithSm3Sync(content: string | Uint8Array): string {
    let result: Uint8Array = dataWithSm3Sync(content);
    return TKHexHelper.stringWithHexEncode(result);
  }

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  SM3摘要算法
   *
   * @param content 要编码的内容
   *
   * @return 编码后的字符串
   */
  export function dataWithSm3Sync(content: string | Uint8Array): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content) {
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        if (data && data.length > 0) {
          let blob: cryptoFramework.DataBlob = { data: data };
          let md: cryptoFramework.Md = cryptoFramework.createMd("SM3");
          md.updateSync(blob);
          let mdResult: cryptoFramework.DataBlob = md.digestSync();
          result = mdResult.data;
        }
      }
    } catch (error) {
      TKLog.error(`[TKSm3Helper]同步SM3摘要算法异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }
}