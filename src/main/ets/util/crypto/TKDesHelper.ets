/**
 * DES帮组类
 */
import { TKDataHelper } from '../data/TKDataHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKBase64Helper } from './TKBase64Helper';
import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { TKLog } from '../logger/TKLog';

export enum TKDesMode {
  ECB,
  CBC,
  ECB_NOPADDING,
  CBC_NOPADDING
}

export namespace TKDesHelper {

  /**
   * 生成秘钥
   * @param symKeyData
   * @param algName
   * @returns
   */
  async function genSymKeyByData(symKeyData: Uint8Array, algName: string = "3DES192"): Promise<cryptoFramework.SymKey> {
    let symKeyBlob: cryptoFramework.DataBlob = { data: symKeyData };
    let aesGenerator = cryptoFramework.createSymKeyGenerator(algName);
    let symKey = await aesGenerator.convertKey(symKeyBlob);
    return symKey;
  }

  /**
   * 生成矢量
   * @returns
   */
  function genIvParamsSpec(dataIv: Uint8Array): cryptoFramework.IvParamsSpec {
    let ivBlob: cryptoFramework.DataBlob = { data: dataIv };
    let ivParamsSpec: cryptoFramework.IvParamsSpec = {
      algName: "IvParamsSpec",
      iv: ivBlob
    };
    return ivParamsSpec;
  }

  /**
   * 通用加解密核心逻辑
   * @param data
   * @param key
   * @param vector
   * @param cryptoMode
   * @param desMode
   * @returns
   */
  async function dataWith3DesEncrptAndDecryptData(data: Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", cryptoMode: cryptoFramework.CryptoMode = cryptoFramework.CryptoMode.ENCRYPT_MODE,
    desMode: TKDesMode = TKDesMode.ECB): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    try {
      let keyData: Uint8Array =
        key instanceof Uint8Array ? key as Uint8Array : TKDataHelper.stringToUint8Array(key as string);
      let vectorData: Uint8Array =
        vector instanceof Uint8Array ? vector as Uint8Array : TKDataHelper.stringToUint8Array(vector);
      if (data && data.length > 0 && keyData && keyData.length > 0) {
        if (keyData.length > 24) {
          keyData = keyData.subarray(0, 24);
        }
        if (!vectorData || vectorData.length == 0) {
          vectorData = keyData;
        }
        if (vectorData.length > 8) {
          vectorData = vectorData.subarray(0, 8);
        }
        if (desMode == TKDesMode.ECB_NOPADDING || desMode == TKDesMode.CBC_NOPADDING) {
          data = TKDataHelper.padUint8Array(data, 8);
        }
        let algName: string = "3DES192";
        let symKey: cryptoFramework.SymKey = await genSymKeyByData(keyData, algName);
        let iv: cryptoFramework.IvParamsSpec | null = genIvParamsSpec(vectorData);
        let transformation: string = "";
        switch (desMode) {
          case TKDesMode.ECB: {
            transformation = `${algName}|ECB|PKCS7`;
            iv = null;
            break;
          }
          case TKDesMode.ECB_NOPADDING: {
            transformation = `${algName}|ECB|NoPadding`;
            iv = null;
            break;
          }
          case TKDesMode.CBC: {
            transformation = `${algName}|CBC|PKCS7`;
            break;
          }
          case TKDesMode.CBC_NOPADDING: {
            transformation = `${algName}|CBC|NoPadding`;
            break;
          }
        }
        let cipher = cryptoFramework.createCipher(transformation);
        await cipher.init(cryptoMode, symKey, iv);
        let plainText: cryptoFramework.DataBlob = { data: data };
        let cipherData = await cipher.doFinal(plainText);
        result = cipherData.data;
      }
    } catch (error) {
      TKLog.error(`[TKDesHelper]异步加解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  3Des加密，先做DES加密，再做Base64编码
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 加密后的字符串
   */
  export async function stringWith3DesEncrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", desMode: TKDesMode = TKDesMode.ECB): Promise<string> {
    let result: Uint8Array = await dataWith3DesEncrypt(content, key, vector, desMode);
    return TKBase64Helper.stringWithBase64Encode(result);
  }

  /**
   *  3Des加密
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 加密后的数据
   */
  export async function dataWith3DesEncrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", desMode: TKDesMode = TKDesMode.ECB): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
      result =
        await dataWith3DesEncrptAndDecryptData(data, key, vector, cryptoFramework.CryptoMode.ENCRYPT_MODE, desMode);
    }
    return result;
  }

  /**
   *  3Des解密，先做base64解码，再做aes解密
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 解密后的字符串
   */
  export async function stringWith3DesDecrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", desMode: TKDesMode = TKDesMode.ECB): Promise<string> {
    let result: Uint8Array = await dataWith3DesDecrypt(content, key, vector, desMode);
    let decryptStr: string = TKDataHelper.uint8ArrayToString(result);
    decryptStr = TKStringHelper.trim(decryptStr);
    return decryptStr;
  }

  /**
   *  3Des解密，先做base64解码，再做aes解密
   *
   * @param content 字符串
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 解密后的数据
   */
  export async function dataWith3DesDecrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", desMode: TKDesMode = TKDesMode.ECB): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
      result =
        await dataWith3DesEncrptAndDecryptData(data, key, vector, cryptoFramework.CryptoMode.DECRYPT_MODE, desMode);
    }
    return result;
  }

  /**
   * 生成秘钥
   * @param symKeyData
   * @param algName
   * @returns
   */
  function genSymKeyByDataSync(symKeyData: Uint8Array, algName: string = "3DES192"): cryptoFramework.SymKey {
    let symKeyBlob: cryptoFramework.DataBlob = { data: symKeyData };
    let aesGenerator = cryptoFramework.createSymKeyGenerator(algName);
    let symKey = aesGenerator.convertKeySync(symKeyBlob);
    return symKey;
  }

  /**
   * 通用加解密核心逻辑
   * @param data
   * @param key
   * @param vector
   * @param cryptoMode
   * @param desMode
   * @returns
   */
  function dataWith3DesEncrptAndDecryptDataSync(data: Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", cryptoMode: cryptoFramework.CryptoMode = cryptoFramework.CryptoMode.ENCRYPT_MODE,
    desMode: TKDesMode = TKDesMode.ECB): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      let keyData: Uint8Array =
        key instanceof Uint8Array ? key as Uint8Array : TKDataHelper.stringToUint8Array(key as string);
      let vectorData: Uint8Array =
        vector instanceof Uint8Array ? vector as Uint8Array : TKDataHelper.stringToUint8Array(vector);
      if (data && data.length > 0 && keyData && keyData.length > 0) {
        if (keyData.length > 24) {
          keyData = keyData.subarray(0, 24);
        }
        if (!vectorData || vectorData.length == 0) {
          vectorData = keyData;
        }
        if (vectorData.length > 8) {
          vectorData = vectorData.subarray(0, 8);
        }
        if (desMode == TKDesMode.ECB_NOPADDING || desMode == TKDesMode.CBC_NOPADDING) {
          data = TKDataHelper.padUint8Array(data, 8);
        }
        let algName: string = "3DES192";
        let symKey: cryptoFramework.SymKey = genSymKeyByDataSync(keyData, algName);
        let iv: cryptoFramework.IvParamsSpec | null = genIvParamsSpec(vectorData);
        let transformation: string = "";
        switch (desMode) {
          case TKDesMode.ECB: {
            transformation = `${algName}|ECB|PKCS7`;
            iv = null;
            break;
          }
          case TKDesMode.ECB_NOPADDING: {
            transformation = `${algName}|ECB|NoPadding`;
            iv = null;
            break;
          }
          case TKDesMode.CBC: {
            transformation = `${algName}|CBC|PKCS7`;
            break;
          }
          case TKDesMode.CBC_NOPADDING: {
            transformation = `${algName}|CBC|NoPadding`;
            break;
          }
        }
        let cipher = cryptoFramework.createCipher(transformation);
        cipher.initSync(cryptoMode, symKey, iv);
        let plainText: cryptoFramework.DataBlob = { data: data };
        let cipherData = cipher.doFinalSync(plainText);
        result = cipherData.data;
      }
    } catch (error) {
      TKLog.error(`[TKDesHelper]同步加解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  3Des加密，先做DES加密，再做Base64编码
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 加密后的字符串
   */
  export function stringWith3DesEncryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", desMode: TKDesMode = TKDesMode.ECB): string {
    let result: Uint8Array = dataWith3DesEncryptSync(content, key, vector, desMode);
    return TKBase64Helper.stringWithBase64Encode(result);
  }

  /**
   *  3Des加密
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 加密后的数据
   */
  export function dataWith3DesEncryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", desMode: TKDesMode = TKDesMode.ECB): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
      result =
        dataWith3DesEncrptAndDecryptDataSync(data, key, vector, cryptoFramework.CryptoMode.ENCRYPT_MODE, desMode);
    }
    return result;
  }

  /**
   *  3Des解密，先做base64解码，再做aes解密
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 解密后的字符串
   */
  export function stringWith3DesDecryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", desMode: TKDesMode = TKDesMode.ECB): string {
    let result: Uint8Array = dataWith3DesDecryptSync(content, key, vector, desMode);
    let decryptStr: string = TKDataHelper.uint8ArrayToString(result);
    decryptStr = TKStringHelper.trim(decryptStr);
    return decryptStr;
  }

  /**
   *  3Des解密，先做base64解码，再做aes解密
   *
   * @param content 字符串
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 解密后的数据
   */
  export function dataWith3DesDecryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", desMode: TKDesMode = TKDesMode.ECB): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
      result =
        dataWith3DesEncrptAndDecryptDataSync(data, key, vector, cryptoFramework.CryptoMode.DECRYPT_MODE, desMode);
    }
    return result;
  }

}