import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { TKDataHelper } from '../data/TKDataHelper';
import { TKLog } from '../logger/TKLog';
import { TKHexHelper } from './TKHexHelper';

/**
 * MD5帮组类
 */
export namespace TKMd5Helper {

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  MD5摘要算法
   *
   * @param content 要编码的内容
   *
   * @return 编码后的字符串
   */
  export async function stringWithMd5(content: string | Uint8Array): Promise<string> {
    let result: Uint8Array = await dataWithMd5(content);
    return TKHexHelper.stringWithHexEncode(result);
  }

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  MD5摘要算法
   *
   * @param content 要编码的内容
   *
   * @return 编码后的字符串
   */
  export async function dataWithMd5(content: string | Uint8Array): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content) {
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        if (data && data.length > 0) {
          let blob: cryptoFramework.DataBlob = { data: data };
          let md: cryptoFramework.Md = cryptoFramework.createMd("MD5");
          await md.update(blob);
          let mdResult: cryptoFramework.DataBlob = await md.digest();
          result = mdResult.data;
        }
      }
    } catch (error) {
      TKLog.error(`[TKMd5Helper]异步MD5摘要算法异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  MD5摘要算法
   *
   * @param content 要编码的内容
   *
   * @return 编码后的字符串
   */
  export function stringWithMd5Sync(content: string | Uint8Array): string {
    let result: Uint8Array = dataWithMd5Sync(content);
    return TKHexHelper.stringWithHexEncode(result);
    ;
  }

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  MD5摘要算法
   *
   * @param content 要编码的内容
   *
   * @return 编码后的字符串
   */
  export function dataWithMd5Sync(content: string | Uint8Array): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content) {
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        if (data && data.length > 0) {
          let blob: cryptoFramework.DataBlob = { data: data };
          let md: cryptoFramework.Md = cryptoFramework.createMd("MD5");
          md.updateSync(blob);
          let mdResult: cryptoFramework.DataBlob = md.digestSync();
          result = mdResult.data;
        }
      }
    } catch (error) {
      TKLog.error(`[TKMd5Helper]同步MD5摘要算法异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }
}