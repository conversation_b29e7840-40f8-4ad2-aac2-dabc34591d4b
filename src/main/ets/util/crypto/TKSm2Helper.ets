/**
 * SM2加解密
 */
import { TKDataHelper } from '../data/TKDataHelper';
import { TKHexHelper } from './TKHexHelper';
import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { TKLog } from '../logger/TKLog';
import { TKStringHelper } from '../string/TKStringHelper';

class TK_SM2_SEQUENCE {
  public C1x: string = "";
  public C1y: string = "";
  public C2: string = "";
  public C3: string = "";
}

class TKASN1Util {
  static BOOLEAN: string = "01";
  static INTEGER: string = "02";
  static BIT_STRING: string = "03";
  static OCTET_STRING: string = "04";
  static NULL: string = "05";
  static REAL: string = "09";
  static ENUMERATED: string = "0a";
  static SEQUENCE: string = "30";
  static SET: string = "31";
}

export enum TKSm2Mode {
  C1C2C3,
  C1C3C2
}

export namespace TKSm2Helper {

  /**
   *
   * 描述：生成公钥
   *
   * @param publicKey
   *            加密公钥
   * @return
   */
  async function generateSm2PublicKey(publicKey: string): Promise<cryptoFramework.PubKey | undefined> {
    try {
      let pkx: string = publicKey.substring(0, publicKey.length / 2);
      let pky: string = publicKey.substring(publicKey.length / 2, publicKey.length);
      let sk: number = 0;
      let sm2CommonParamsSpec: cryptoFramework.ECCCommonParamsSpec =
        cryptoFramework.ECCKeyUtil.genECCCommonParamsSpec('NID_sm2');
      let sm2KeyPairSpec: cryptoFramework.ECCKeyPairSpec = {
        algName: "SM2",
        specType: cryptoFramework.AsyKeySpecType.KEY_PAIR_SPEC,
        params: sm2CommonParamsSpec,
        sk: BigInt(sk),
        pk: {
          x: BigInt(`0x${pkx}`),
          y: BigInt(`0x${pky}`)
        },
      };
      let generatorBySpec: cryptoFramework.AsyKeyGeneratorBySpec =
        cryptoFramework.createAsyKeyGeneratorBySpec(sm2KeyPairSpec);
      return generatorBySpec.generatePubKey();
      ;
    } catch (error) {
      TKLog.error(`[TKSm2Helper]异步生成公钥异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成私钥
   *
   * @param privateKey
   *            解密私钥
   * @return
   */
  async function generateSm2PrivateKey(publicKey: string,
    privateKey: string): Promise<cryptoFramework.PriKey | undefined> {
    try {
      let pkx: string = publicKey.substring(0, publicKey.length / 2);
      let pky: string = publicKey.substring(publicKey.length / 2, publicKey.length);
      let sk: string = privateKey;
      let sm2CommonParamsSpec: cryptoFramework.ECCCommonParamsSpec =
        cryptoFramework.ECCKeyUtil.genECCCommonParamsSpec('NID_sm2');
      let sm2KeyPairSpec: cryptoFramework.ECCKeyPairSpec = {
        algName: "SM2",
        specType: cryptoFramework.AsyKeySpecType.KEY_PAIR_SPEC,
        params: sm2CommonParamsSpec,
        sk: BigInt(`0x${sk}`),
        pk: {
          x: BigInt(`0x${pkx}`),
          y: BigInt(`0x${pky}`)
        },
      };
      let generatorBySpec: cryptoFramework.AsyKeyGeneratorBySpec =
        cryptoFramework.createAsyKeyGeneratorBySpec(sm2KeyPairSpec);
      return generatorBySpec.generatePriKey();
    } catch (error) {
      TKLog.error(`[TKSm2Helper]异步生成私钥异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   * SM2公钥加密
   * @param content 原文
   * @param pubKey 公钥
   * @returns
   */
  async function dataWithSm2PublicKeyEncryptInner(content: Uint8Array,
    pubKey: cryptoFramework.PubKey | undefined): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content && content.length > 0) {
        let cipher = cryptoFramework.createCipher('SM2|SM3');
        await cipher.init(cryptoFramework.CryptoMode.ENCRYPT_MODE, pubKey, null);
        let dataBlob: cryptoFramework.DataBlob = { data: content };
        let encryptDataBlob: cryptoFramework.DataBlob = await cipher.doFinal(dataBlob);
        result = encryptDataBlob.data;
      }
    } catch (error) {
      TKLog.error(`[TKSm2Helper]异步SM2公钥加密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * SM2私钥解密
   * @param content 密文
   * @param priKey 私钥
   * @returns
   */
  async function dataWithSm2PrivateKeyDecryptInner(content: Uint8Array,
    priKey: cryptoFramework.PriKey | undefined): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content && content.length > 0) {
        let cipher = cryptoFramework.createCipher('SM2|SM3');
        await cipher.init(cryptoFramework.CryptoMode.DECRYPT_MODE, priKey, null);
        let cipherDataBlob: cryptoFramework.DataBlob = { data: content };
        let plainDataBlob: cryptoFramework.DataBlob = await cipher.doFinal(cipherDataBlob);
        result = plainDataBlob.data;
      }
    } catch (error) {
      TKLog.error(`[TKSm2Helper]异步SM2私钥解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * <AUTHOR> 2015-07-14 13:07:06
   *
   *  SM2公钥加密，对应要用SM2私钥解密,加密后转成16进制字符串
   *
   * @param content 需要加密的原始内容
   * @param publicKey    公钥
   *
   * @return 加密后内容
   */
  export async function stringWithSm2PublicKeyEncrypt(content: string | Uint8Array, publicKey: string,
    mode: TKSm2Mode = TKSm2Mode.C1C2C3): Promise<string> {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
    let pubKey: cryptoFramework.PubKey | undefined = await generateSm2PublicKey(publicKey);
    let result: Uint8Array = await dataWithSm2PublicKeyEncryptInner(data, pubKey);
    let hexResult: string = TKHexHelper.stringWithHexEncode(result);
    hexResult = d2i_SM2_Ciphertext(hexResult, mode);
    return `04${hexResult}`;
  }

  /**
   * <AUTHOR> 2015-07-14 13:07:06
   *
   *  SM2公钥加密，对应要用SM2私钥解密,加密后转成16进制字符串
   *
   * @param content 需要加密的原始内容
   * @param publicKey    公钥
   *
   * @return 加密后内容
   */
  export async function dataWithSm2PublicKeyEncrypt(content: string | Uint8Array, publicKey: string,
    mode: TKSm2Mode = TKSm2Mode.C1C2C3): Promise<Uint8Array> {
    let result: string = await stringWithSm2PublicKeyEncrypt(content, publicKey, mode);
    return TKHexHelper.dataWithHexDecode(result);
  }

  /**
   * <AUTHOR> 2015-07-14 13:07:02
   *
   *  SM2私钥解密，先解码16进制，再解密
   *
   * @param content      需要解密的密文内容
   * @param publicKey    公钥
   * @param privateKey   私钥
   *
   * @return 解密后内容
   */
  export async function stringWithSm2PrivateKeyDecrypt(content: string | Uint8Array, publicKey: string,
    privateKey: string, mode: TKSm2Mode = TKSm2Mode.C1C2C3): Promise<string> {
    let result: Uint8Array = await dataWithSm2PrivateKeyDecrypt(content, publicKey, privateKey, mode);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   * <AUTHOR> 2015-07-14 13:07:02
   *
   *  SM2私钥解密，先解码16进制，再解密
   *
   * @param content      需要解密的密文内容
   * @param publicKey    公钥
   * @param privateKey   私钥
   *
   * @return 解密后内容
   */
  export async function dataWithSm2PrivateKeyDecrypt(content: string | Uint8Array, publicKey: string,
    privateKey: string, mode: TKSm2Mode = TKSm2Mode.C1C2C3): Promise<Uint8Array> {
    content =
      content instanceof Uint8Array ? TKHexHelper.stringWithHexEncode(content as Uint8Array) : content as string;
    if (TKStringHelper.startsWith(content, "04")) {
      content = content.substring(2, content.length);
    }
    content = i2d_SM2_Ciphertext(content, mode);
    let data: Uint8Array = TKHexHelper.dataWithHexDecode(content);
    let priKey: cryptoFramework.PriKey | undefined = await generateSm2PrivateKey(publicKey, privateKey);
    return dataWithSm2PrivateKeyDecryptInner(data, priKey);
  }

  /**
   *
   * 描述：生成公钥
   *
   * @param publicKey
   *            加密公钥
   * @return
   */
  function generateSm2PublicKeySync(publicKey: string): cryptoFramework.PubKey | undefined {
    try {
      let pkx: string = publicKey.substring(0, publicKey.length / 2);
      let pky: string = publicKey.substring(publicKey.length / 2, publicKey.length);
      let sk: number = 0;
      let sm2CommonParamsSpec: cryptoFramework.ECCCommonParamsSpec =
        cryptoFramework.ECCKeyUtil.genECCCommonParamsSpec('NID_sm2');
      let sm2KeyPairSpec: cryptoFramework.ECCKeyPairSpec = {
        algName: "SM2",
        specType: cryptoFramework.AsyKeySpecType.KEY_PAIR_SPEC,
        params: sm2CommonParamsSpec,
        sk: BigInt(sk),
        pk: {
          x: BigInt(`0x${pkx}`),
          y: BigInt(`0x${pky}`)
        },
      };
      let generatorBySpec: cryptoFramework.AsyKeyGeneratorBySpec =
        cryptoFramework.createAsyKeyGeneratorBySpec(sm2KeyPairSpec);
      return generatorBySpec.generatePubKeySync();
      ;
    } catch (error) {
      TKLog.error(`[TKSm2Helper]同步生成公钥异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   *
   * 描述：生成私钥
   *
   * @param privateKey
   *            解密私钥
   * @return
   */
  function generateSm2PrivateKeySync(publicKey: string,
    privateKey: string): cryptoFramework.PriKey | undefined {
    try {
      let pkx: string = publicKey.substring(0, publicKey.length / 2);
      let pky: string = publicKey.substring(publicKey.length / 2, publicKey.length);
      let sk: string = privateKey;
      let sm2CommonParamsSpec: cryptoFramework.ECCCommonParamsSpec =
        cryptoFramework.ECCKeyUtil.genECCCommonParamsSpec('NID_sm2');
      let sm2KeyPairSpec: cryptoFramework.ECCKeyPairSpec = {
        algName: "SM2",
        specType: cryptoFramework.AsyKeySpecType.KEY_PAIR_SPEC,
        params: sm2CommonParamsSpec,
        sk: BigInt(`0x${sk}`),
        pk: {
          x: BigInt(`0x${pkx}`),
          y: BigInt(`0x${pky}`)
        },
      };
      let generatorBySpec: cryptoFramework.AsyKeyGeneratorBySpec =
        cryptoFramework.createAsyKeyGeneratorBySpec(sm2KeyPairSpec);
      return generatorBySpec.generatePriKeySync();
    } catch (error) {
      TKLog.error(`[TKSm2Helper]同步生成私钥异常，code: ${error.code}， message: ${error.message}`);
      return undefined;
    }
  }

  /**
   * SM2公钥加密
   * @param content 原文
   * @param pubKey 公钥
   * @returns
   */
  function dataWithSm2PublicKeyEncryptInnerSync(content: Uint8Array,
    pubKey: cryptoFramework.PubKey | undefined): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content && content.length > 0) {
        let cipher = cryptoFramework.createCipher('SM2|SM3');
        cipher.initSync(cryptoFramework.CryptoMode.ENCRYPT_MODE, pubKey, null);
        let dataBlob: cryptoFramework.DataBlob = { data: content };
        let encryptDataBlob: cryptoFramework.DataBlob = cipher.doFinalSync(dataBlob);
        result = encryptDataBlob.data;
      }
    } catch (error) {
      TKLog.error(`[TKSm2Helper]同步SM2公钥加密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * SM2私钥解密
   * @param content 密文
   * @param priKey 私钥
   * @returns
   */
  function dataWithSm2PrivateKeyDecryptInnerSync(content: Uint8Array,
    priKey: cryptoFramework.PriKey | undefined): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content && content.length > 0) {
        let cipher = cryptoFramework.createCipher('SM2|SM3');
        cipher.initSync(cryptoFramework.CryptoMode.DECRYPT_MODE, priKey, null);
        let cipherDataBlob: cryptoFramework.DataBlob = { data: content };
        let plainDataBlob: cryptoFramework.DataBlob = cipher.doFinalSync(cipherDataBlob);
        result = plainDataBlob.data;
      }
    } catch (error) {
      TKLog.error(`[TKSm2Helper]同步SM2私钥解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * <AUTHOR> 2015-07-14 13:07:06
   *
   *  SM2公钥加密，对应要用SM2私钥解密,加密后转成16进制字符串
   *
   * @param content 需要加密的原始内容
   * @param publicKey    公钥
   *
   * @return 加密后内容
   */
  export function stringWithSm2PublicKeyEncryptSync(content: string | Uint8Array, publicKey: string,
    mode: TKSm2Mode = TKSm2Mode.C1C2C3): string {
    let data: Uint8Array =
      content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
    let pubKey: cryptoFramework.PubKey | undefined = generateSm2PublicKeySync(publicKey);
    let result: Uint8Array = dataWithSm2PublicKeyEncryptInnerSync(data, pubKey);
    let hexResult: string = TKHexHelper.stringWithHexEncode(result);
    hexResult = d2i_SM2_Ciphertext(hexResult, mode);
    return `04${hexResult}`;
  }

  /**
   * <AUTHOR> 2015-07-14 13:07:06
   *
   *  SM2公钥加密，对应要用SM2私钥解密,加密后转成16进制字符串
   *
   * @param content 需要加密的原始内容
   * @param publicKey    公钥
   *
   * @return 加密后内容
   */
  export function dataWithSm2PublicKeyEncryptSync(content: string | Uint8Array, publicKey: string,
    mode: TKSm2Mode = TKSm2Mode.C1C2C3): Uint8Array {
    let result: string = stringWithSm2PublicKeyEncryptSync(content, publicKey, mode);
    return TKHexHelper.dataWithHexDecode(result);
  }

  /**
   * <AUTHOR> 2015-07-14 13:07:02
   *
   *  SM2私钥解密，先解码16进制，再解密
   *
   * @param content      需要解密的密文内容
   * @param publicKey    公钥
   * @param privateKey   私钥
   *
   * @return 解密后内容
   */
  export function stringWithSm2PrivateKeyDecryptSync(content: string | Uint8Array, publicKey: string,
    privateKey: string, mode: TKSm2Mode = TKSm2Mode.C1C2C3): string {
    let result: Uint8Array = dataWithSm2PrivateKeyDecryptSync(content, publicKey, privateKey, mode);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   * <AUTHOR> 2015-07-14 13:07:02
   *
   *  SM2私钥解密，先解码16进制，再解密
   *
   * @param content      需要解密的密文内容
   * @param publicKey    公钥
   * @param privateKey   私钥
   *
   * @return 解密后内容
   */
  export function dataWithSm2PrivateKeyDecryptSync(content: string | Uint8Array, publicKey: string,
    privateKey: string, mode: TKSm2Mode = TKSm2Mode.C1C2C3): Uint8Array {
    content =
      content instanceof Uint8Array ? TKHexHelper.stringWithHexEncode(content as Uint8Array) : content as string;
    if (TKStringHelper.startsWith(content, "04")) {
      content = content.substring(2, content.length);
    }
    content = i2d_SM2_Ciphertext(content, mode);
    let data: Uint8Array = TKHexHelper.dataWithHexDecode(content);
    let priKey: cryptoFramework.PriKey | undefined = generateSm2PrivateKeySync(publicKey, privateKey);
    return dataWithSm2PrivateKeyDecryptInnerSync(data, priKey);
  }

  /**
   * 用于将SM2裸密文数据序列化
   * @param primal_data SM2裸密钥数据，长度为96+明文长度（字节），输入格式为C1C2C3的Hex字符串
   * @returns 返回序列化后的标准密文数据，输出格式为Hex字符串
   */
  function i2d_SM2_Ciphertext(primal_data: string, mode: TKSm2Mode = TKSm2Mode.C1C2C3): string {
    let sm2_sequence = new TK_SM2_SEQUENCE();
    sm2_sequence.C1x = primal_data.slice(0, 64);
    primal_data = primal_data.slice(64, primal_data.length);
    sm2_sequence.C1y = primal_data.slice(0, 64);
    primal_data = primal_data.slice(64, primal_data.length);
    if (mode == TKSm2Mode.C1C2C3) {
      sm2_sequence.C2 = primal_data.slice(0, primal_data.length - 64);
      primal_data = primal_data.slice(primal_data.length - 64, primal_data.length);
      sm2_sequence.C3 = primal_data;
    } else {
      sm2_sequence.C3 = primal_data.slice(0, 64);
      primal_data = primal_data.slice(64, primal_data.length);
      sm2_sequence.C2 = primal_data;
    }

    let C1x_title: string = (Number.parseInt("0x" + sm2_sequence.C1x.slice(0, 2)) > 127) ? "022100" : "0220";
    let C1y_title: string = (Number.parseInt("0x" + sm2_sequence.C1y.slice(0, 2)) > 127) ? "022100" : "0220";
    let C3_title: string = "0420";
    let C2_title: string = "04" + genLenHex(sm2_sequence.C2);
    let sequence_message: string =
      C1x_title + sm2_sequence.C1x + C1y_title + sm2_sequence.C1y + C3_title + sm2_sequence.C3 + C2_title +
      sm2_sequence.C2;
    let sequence_lenHex: string = genLenHex(sequence_message);

    let standard_data: string = "30" + sequence_lenHex + sequence_message;
    return standard_data;
  }

  /**
   * 用于将标准SM2密文数据解码
   * @param standard_data 标准SM2密文数据，符合ASN.1编码标准，输入格式为Hex字符串
   * @returns 返回ASN.1解码后的SM2密文数据
   */
  function d2i_SM2_Ciphertext(standard_data: string, mode: TKSm2Mode = TKSm2Mode.C1C2C3): string {
    let message: string = standard_data;
    // 起始标识为03
    if (!message.startsWith(TKASN1Util.SEQUENCE)) {
      throw new Error("SM2 ciphertext error!");
    }
    message = message.slice(TKASN1Util.SEQUENCE.length, message.length);

    // SM2 sequence内容的长度
    let sequence_lenHex: string = getLenHex(message);
    message = message.slice(sequence_lenHex.length, message.length);
    let sequence_len: number = lenHex2number(sequence_lenHex);
    if (sequence_len != message.length / 2) {
      throw new Error("SM2 ciphertext error!");
    }

    let sm2_sequence = new TK_SM2_SEQUENCE();
    message = readC1(sm2_sequence, message);
    message = readC3(sm2_sequence, message);
    message = readC2(sm2_sequence, message);

    let primal_data: string = sm2_sequence.C1x + sm2_sequence.C1y + sm2_sequence.C2 + sm2_sequence.C3;
    if (mode === TKSm2Mode.C1C3C2) {
      primal_data = sm2_sequence.C1x + sm2_sequence.C1y + sm2_sequence.C3 + sm2_sequence.C2;
    }
    return primal_data;
  }

  // 生成传入内容的长度域
  function genLenHex(content: string): string {
    let size: number = content.length / 2;
    let lenHex: string;
    if (size.toString(16).length % 2 == 1) {
      lenHex = '0' + size.toString(16);
    } else {
      lenHex = size.toString(16);
    }
    if (size < 0x80) {
      return lenHex;
    }
    let lenHex_size: number = lenHex.length / 2;
    return (lenHex_size | 0x80).toString(16) + lenHex;
  }

  // 提取长度域的Hex字符串
  function getLenHex(data: string): string {
    let byte: number = Number.parseInt("0x" + data.slice(0, 2));
    let len_size: number = byte > 127 ? byte - 0x80 + 1 : 1;
    return data.slice(0, len_size * 2);
  }

  // 将长度域的Hex字符串转为整型
  function lenHex2number(lenHex: string): number {
    if (lenHex.length == 2) {
      return Number.parseInt("0x" + lenHex);
    }
    return Number.parseInt("0x" + lenHex.slice(2, lenHex.length));
  }

  function readC1(sm2_sequence: TK_SM2_SEQUENCE, data: string): string {
    let xy: string[] = [];
    for (let i = 0; i < 2; i++) {
      if (data.startsWith("0220")) {
        xy[i] = data.slice(4, 68);
        data = data.slice(68, data.length);
      } else if (data.startsWith("022100")) {
        xy[i] = data.slice(6, 70);
        data = data.slice(70, data.length);
      } else {
        throw new Error("SM2 ciphertext error!");
      }
    }
    sm2_sequence.C1x = xy[0];
    sm2_sequence.C1y = xy[1];
    return data;
  }

  function readC2(sm2_sequence: TK_SM2_SEQUENCE, data: string): string {
    if (data.startsWith(TKASN1Util.OCTET_STRING)) {
      data = data.slice(TKASN1Util.OCTET_STRING.length, data.length);
      let C2_lenHex = getLenHex(data);
      data = data.slice(C2_lenHex.length, data.length);
      if (lenHex2number(C2_lenHex) != data.length / 2) {
        throw new Error("SM2 ciphertext error!");
      }
      sm2_sequence.C2 = data;
    } else {
      throw new Error("SM2 ciphertext error!")
    }
    return data;
  }

  function readC3(sm2_sequence: TK_SM2_SEQUENCE, data: string): string {
    if (data.startsWith("0420")) {
      sm2_sequence.C3 = data.slice(4, 68);
      data = data.slice(68, data.length);
    } else {
      throw new Error("SM2 ciphertext error!");
    }
    return data;
  }

}