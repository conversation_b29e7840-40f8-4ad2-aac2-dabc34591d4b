/**
 * SM4帮组类
 */
import { TKDataHelper } from '../data/TKDataHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKBase64Helper } from './TKBase64Helper';
import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { TKLog } from '../logger/TKLog';

export enum TKSm4Mode {
  ECB,
  CBC,
  ECB_NOPADDING,
  CBC_NOPADDING
}

export namespace TKSm4Helper {

  /**
   * 生成秘钥
   * @param symKeyData
   * @param algName
   * @returns
   */
  async function genSymKeyByData(symKeyData: Uint8Array, algName: string = "SM4_128"): Promise<cryptoFramework.SymKey> {
    let symKeyBlob: cryptoFramework.DataBlob = { data: symKeyData };
    let aesGenerator = cryptoFramework.createSymKeyGenerator(algName);
    let symKey = await aesGenerator.convertKey(symKeyBlob);
    return symKey;
  }

  /**
   * 生成矢量
   * @returns
   */
  function genIvParamsSpec(dataIv: Uint8Array): cryptoFramework.IvParamsSpec {
    let ivBlob: cryptoFramework.DataBlob = { data: dataIv };
    let ivParamsSpec: cryptoFramework.IvParamsSpec = {
      algName: "IvParamsSpec",
      iv: ivBlob
    };
    return ivParamsSpec;
  }

  /**
   * 通用加解密核心逻辑
   * @param data
   * @param key
   * @param vector
   * @param cryptoMode
   * @param desMode
   * @returns
   */
  async function dataWithSm4EncrptAndDecryptData(data: Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", cryptoMode: cryptoFramework.CryptoMode = cryptoFramework.CryptoMode.ENCRYPT_MODE,
    sm4Mode: TKSm4Mode = TKSm4Mode.ECB): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    try {
      let keyData: Uint8Array =
        key instanceof Uint8Array ? key as Uint8Array : TKDataHelper.stringToUint8Array(key as string);
      let vectorData: Uint8Array =
        vector instanceof Uint8Array ? vector as Uint8Array : TKDataHelper.stringToUint8Array(vector);
      if (data && data.length > 0 && keyData && keyData.length > 0) {
        if (keyData.length > 16) {
          keyData = keyData.subarray(0, 16);
        }
        if (!vectorData || vectorData.length == 0) {
          vectorData = keyData;
        }
        if (vectorData.length > 16) {
          vectorData = vectorData.subarray(0, 16);
        }
        if (sm4Mode == TKSm4Mode.ECB_NOPADDING || sm4Mode == TKSm4Mode.CBC_NOPADDING) {
          data = TKDataHelper.padUint8Array(data);
        }
        let algName: string = "SM4_128";
        let symKey: cryptoFramework.SymKey = await genSymKeyByData(keyData, algName);
        let iv: cryptoFramework.IvParamsSpec | null = genIvParamsSpec(vectorData);
        let transformation: string = "";
        switch (sm4Mode) {
          case TKSm4Mode.ECB: {
            transformation = `${algName}|ECB|PKCS7`;
            iv = null;
            break;
          }
          case TKSm4Mode.ECB_NOPADDING: {
            transformation = `${algName}|ECB|NoPadding`;
            iv = null;
            break;
          }
          case TKSm4Mode.CBC: {
            transformation = `${algName}|CBC|PKCS7`;
            break;
          }
          case TKSm4Mode.CBC_NOPADDING: {
            transformation = `${algName}|CBC|NoPadding`;
            break;
          }
        }
        let cipher = cryptoFramework.createCipher(transformation);
        await cipher.init(cryptoMode, symKey, iv);
        let plainText: cryptoFramework.DataBlob = { data: data };
        let cipherData = await cipher.doFinal(plainText);
        result = cipherData.data;
      }
    } catch (error) {
      TKLog.error(`[TKSm4Helper]异步SM4加解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  Sm4加密，先做Sm4加密，再做Base64编码
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 加密后的字符串
   */
  export async function stringWithSm4Encrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", sm4Mode: TKSm4Mode = TKSm4Mode.ECB): Promise<string> {
    let result: Uint8Array = await dataWithSm4Encrypt(content, key, vector, sm4Mode);
    let encryptStr: string = TKBase64Helper.stringWithBase64Encode(result);
    return TKStringHelper.trim(encryptStr);
  }

  /**
   *  Sm4加密
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 加密后的数据
   */
  export async function dataWithSm4Encrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", sm4Mode: TKSm4Mode = TKSm4Mode.ECB): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
      result =
        await dataWithSm4EncrptAndDecryptData(data, key, vector, cryptoFramework.CryptoMode.ENCRYPT_MODE, sm4Mode);
    }
    return result;
  }

  /**
   *  Sm4解密，先做base64解码，再做aes解密
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 解密后的字符串
   */
  export async function stringWithSm4Decrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", sm4Mode: TKSm4Mode = TKSm4Mode.ECB): Promise<string> {
    let result: Uint8Array = await dataWithSm4Decrypt(content, key, vector, sm4Mode);
    let decryptStr: string = TKDataHelper.uint8ArrayToString(result);
    decryptStr = TKStringHelper.trim(decryptStr);
    return decryptStr;
  }

  /**
   *  Sm4解密，先做base64解码，再做aes解密
   *
   * @param content 字符串
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 解密后的数据
   */
  export async function dataWithSm4Decrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", sm4Mode: TKSm4Mode = TKSm4Mode.ECB): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
      result =
        await dataWithSm4EncrptAndDecryptData(data, key, vector, cryptoFramework.CryptoMode.DECRYPT_MODE, sm4Mode);
    }
    return result;
  }

  /**
   * 生成秘钥
   * @param symKeyData
   * @param algName
   * @returns
   */
  function genSymKeyByDataSync(symKeyData: Uint8Array, algName: string = "SM4_128"): cryptoFramework.SymKey {
    let symKeyBlob: cryptoFramework.DataBlob = { data: symKeyData };
    let aesGenerator = cryptoFramework.createSymKeyGenerator(algName);
    let symKey = aesGenerator.convertKeySync(symKeyBlob);
    return symKey;
  }

  /**
   * 通用加解密核心逻辑
   * @param data
   * @param key
   * @param vector
   * @param cryptoMode
   * @param desMode
   * @returns
   */
  function dataWithSm4EncrptAndDecryptDataSync(data: Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", cryptoMode: cryptoFramework.CryptoMode = cryptoFramework.CryptoMode.ENCRYPT_MODE,
    sm4Mode: TKSm4Mode = TKSm4Mode.ECB): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      let keyData: Uint8Array =
        key instanceof Uint8Array ? key as Uint8Array : TKDataHelper.stringToUint8Array(key as string);
      let vectorData: Uint8Array =
        vector instanceof Uint8Array ? vector as Uint8Array : TKDataHelper.stringToUint8Array(vector);
      if (data && data.length > 0 && keyData && keyData.length > 0) {
        if (keyData.length > 16) {
          keyData = keyData.subarray(0, 16);
        }
        if (!vectorData || vectorData.length == 0) {
          vectorData = keyData;
        }
        if (vectorData.length > 16) {
          vectorData = vectorData.subarray(0, 16);
        }
        if (sm4Mode == TKSm4Mode.ECB_NOPADDING || sm4Mode == TKSm4Mode.CBC_NOPADDING) {
          data = TKDataHelper.padUint8Array(data);
        }
        let algName: string = "SM4_128";
        let symKey: cryptoFramework.SymKey = genSymKeyByDataSync(keyData, algName);
        let iv: cryptoFramework.IvParamsSpec | null = genIvParamsSpec(vectorData);
        let transformation: string = "";
        switch (sm4Mode) {
          case TKSm4Mode.ECB: {
            transformation = `${algName}|ECB|PKCS7`;
            iv = null;
            break;
          }
          case TKSm4Mode.ECB_NOPADDING: {
            transformation = `${algName}|ECB|NoPadding`;
            iv = null;
            break;
          }
          case TKSm4Mode.CBC: {
            transformation = `${algName}|CBC|PKCS7`;
            break;
          }
          case TKSm4Mode.CBC_NOPADDING: {
            transformation = `${algName}|CBC|NoPadding`;
            break;
          }
        }
        let cipher = cryptoFramework.createCipher(transformation);
        cipher.initSync(cryptoMode, symKey, iv);
        let plainText: cryptoFramework.DataBlob = { data: data };
        let cipherData = cipher.doFinalSync(plainText);
        result = cipherData.data;
      }
    } catch (error) {
      TKLog.error(`[TKSm4Helper]同步SM4加解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  Sm4加密，先做Sm4加密，再做Base64编码
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 加密后的字符串
   */
  export function stringWithSm4EncryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", sm4Mode: TKSm4Mode = TKSm4Mode.ECB): string {
    let result: Uint8Array = dataWithSm4EncryptSync(content, key, vector, sm4Mode);
    let encryptStr: string = TKBase64Helper.stringWithBase64Encode(result);
    return TKStringHelper.trim(encryptStr);
  }

  /**
   *  Sm4加密
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 加密后的数据
   */
  export function dataWithSm4EncryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", sm4Mode: TKSm4Mode = TKSm4Mode.ECB): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
      result = dataWithSm4EncrptAndDecryptDataSync(data, key, vector, cryptoFramework.CryptoMode.ENCRYPT_MODE, sm4Mode);
    }
    return result;
  }

  /**
   *  Sm4解密，先做base64解码，再做aes解密
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 解密后的字符串
   */
  export function stringWithSm4DecryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", sm4Mode: TKSm4Mode = TKSm4Mode.ECB): string {
    let result: Uint8Array = dataWithSm4DecryptSync(content, key, vector, sm4Mode);
    let decryptStr: string = TKDataHelper.uint8ArrayToString(result);
    decryptStr = TKStringHelper.trim(decryptStr);
    return decryptStr;
  }

  /**
   *  Sm4解密，先做base64解码，再做aes解密
   *
   * @param content 字符串
   * @param key    秘钥
   * @param vector 矢量
   * @param desMode   模式
   *
   * @return 解密后的数据
   */
  export function dataWithSm4DecryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", sm4Mode: TKSm4Mode = TKSm4Mode.ECB): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
      result = dataWithSm4EncrptAndDecryptDataSync(data, key, vector, cryptoFramework.CryptoMode.DECRYPT_MODE, sm4Mode);
    }
    return result;
  }
}