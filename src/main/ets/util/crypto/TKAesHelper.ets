/**
 * AES帮组类
 */
import { TKDataHelper } from '../data/TKDataHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKBase64Helper } from './TKBase64Helper';
import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { TKLog } from '../logger/TKLog';

export enum TKAesMode {
  ECB,
  CBC,
  ECB_NOPADDING,
  CBC_NOPADDING
}

export namespace TKAesHelper {

  /**
   * 获取加密方式
   * @param key
   * @returns
   */
  function getAlgName(key: string | Uint8Array): string {
    if (key.length <= 16) {
      return 'AES128'
    }
    return 'AES256'
  }

  /**
   * 生成秘钥
   * @param symKeyData
   * @param algName
   * @returns
   */
  async function genSymKeyByData(symKeyData: Uint8Array, algName: string = "AES128"): Promise<cryptoFramework.SymKey> {
    let symKeyBlob: cryptoFramework.DataBlob = { data: symKeyData };
    let aesGenerator = cryptoFramework.createSymKeyGenerator(algName);
    let symKey = await aesGenerator.convertKey(symKeyBlob);
    return symKey;
  }

  /**
   * 生成矢量
   * @returns
   */
  function genIvParamsSpec(dataIv: Uint8Array): cryptoFramework.IvParamsSpec {
    let ivBlob: cryptoFramework.DataBlob = { data: dataIv };
    let ivParamsSpec: cryptoFramework.IvParamsSpec = {
      algName: "IvParamsSpec",
      iv: ivBlob
    };
    return ivParamsSpec;
  }

  /**
   * 通用加解密核心逻辑
   * @param data
   * @param key
   * @param vector
   * @param cryptoMode
   * @param aesMode
   * @returns
   */
  async function dataWithAesEncrptAndDecryptData(data: Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", cryptoMode: cryptoFramework.CryptoMode = cryptoFramework.CryptoMode.ENCRYPT_MODE,
    aesMode: TKAesMode = TKAesMode.ECB): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    try {
      let keyData: Uint8Array =
        key instanceof Uint8Array ? key as Uint8Array : TKDataHelper.stringToUint8Array(key as string);
      let vectorData: Uint8Array =
        vector instanceof Uint8Array ? vector as Uint8Array : TKDataHelper.stringToUint8Array(vector);
      if (data && data.length > 0 && keyData && keyData.length > 0) {
        if (!vectorData || vectorData.length == 0) {
          vectorData = keyData;
        }
        if (vectorData.length > 16) {
          vectorData = vectorData.subarray(0, 16);
        }
        if (aesMode == TKAesMode.ECB_NOPADDING || aesMode == TKAesMode.CBC_NOPADDING) {
          data = TKDataHelper.padUint8Array(data);
        }
        let algName: string = getAlgName(key);
        let symKey: cryptoFramework.SymKey = await genSymKeyByData(keyData, algName);
        let iv: cryptoFramework.IvParamsSpec | undefined = genIvParamsSpec(vectorData);
        let transformation: string = "";
        switch (aesMode) {
          case TKAesMode.ECB: {
            transformation = `${algName}|ECB|PKCS7`;
            break;
          }
          case TKAesMode.ECB_NOPADDING: {
            transformation = `${algName}|ECB|NoPadding`;
            break;
          }
          case TKAesMode.CBC: {
            transformation = `${algName}|CBC|PKCS7`;
            break;
          }
          case TKAesMode.CBC_NOPADDING: {
            transformation = `${algName}|CBC|NoPadding`;
            break;
          }
        }
        let cipher = cryptoFramework.createCipher(transformation);
        await cipher.init(cryptoMode, symKey, iv);
        let plainText: cryptoFramework.DataBlob = { data: data };
        let cipherData = await cipher.doFinal(plainText);
        result = cipherData.data;
      }
    } catch (error) {
      TKLog.error(`[TKAesHelper]异步加解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  Aes加密，先做AES加密，再做Base64编码
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param aesMode   模式
   *
   * @return 加密后的字符串
   */
  export async function stringWithAesEncrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", aesMode: TKAesMode = TKAesMode.ECB): Promise<string> {
    let result: Uint8Array = await dataWithAesEncrypt(content, key, vector, aesMode);
    let encryptStr: string = TKBase64Helper.stringWithBase64Encode(result);
    return TKStringHelper.trim(encryptStr);
  }

  /**
   *  Aes加密
   *
   * @param content   数据
   * @param key    秘钥
   * @param vector 矢量
   * @param aesMode   模式
   *
   * @return 加密后的数据
   */
  export async function dataWithAesEncrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", aesMode: TKAesMode = TKAesMode.ECB): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
      result =
        await dataWithAesEncrptAndDecryptData(data, key, vector, cryptoFramework.CryptoMode.ENCRYPT_MODE, aesMode);
    }
    return result;
  }

  /**
   *  Aes解密，先做base64解码，再做aes解密
   *
   * @param content 字符串
   * @param key    秘钥
   * @param vector 矢量
   * @param aesMode   模式
   *
   * @return 解密后的字符串
   */
  export async function stringWithAesDecrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", aesMode: TKAesMode = TKAesMode.ECB): Promise<string> {
    let result: Uint8Array = await dataWithAesDecrypt(content, key, vector, aesMode);
    let decryptStr: string = TKDataHelper.uint8ArrayToString(result);
    decryptStr = TKStringHelper.trim(decryptStr);
    return decryptStr;
  }

  /**
   *  Aes解密，先做base64解码，再做aes解密
   *
   * @param content 内容
   * @param key    秘钥
   * @param vector 矢量
   * @param aesMode   模式
   *
   * @return 解密后的数据
   */
  export async function dataWithAesDecrypt(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", aesMode: TKAesMode = TKAesMode.ECB): Promise<Uint8Array> {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
      result =
        await dataWithAesEncrptAndDecryptData(data, key, vector, cryptoFramework.CryptoMode.DECRYPT_MODE, aesMode);
    }
    return result;
  }

  /**
   * 生成秘钥
   * @param symKeyData
   * @param algName
   * @returns
   */
  function genSymKeyByDataSync(symKeyData: Uint8Array, algName: string = "AES128"): cryptoFramework.SymKey {
    let symKeyBlob: cryptoFramework.DataBlob = { data: symKeyData };
    let aesGenerator = cryptoFramework.createSymKeyGenerator(algName);
    let symKey = aesGenerator.convertKeySync(symKeyBlob);
    return symKey;
  }

  /**
   * 通用加解密核心逻辑
   * @param data
   * @param key
   * @param vector
   * @param cryptoMode
   * @param aesMode
   * @returns
   */
  function dataWithAesEncrptAndDecryptDataSync(data: Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", cryptoMode: cryptoFramework.CryptoMode = cryptoFramework.CryptoMode.ENCRYPT_MODE,
    aesMode: TKAesMode = TKAesMode.ECB): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      let keyData: Uint8Array =
        key instanceof Uint8Array ? key as Uint8Array : TKDataHelper.stringToUint8Array(key as string);
      let vectorData: Uint8Array =
        vector instanceof Uint8Array ? vector as Uint8Array : TKDataHelper.stringToUint8Array(vector);
      if (data && data.length > 0 && keyData && keyData.length > 0) {
        if (!vectorData || vectorData.length == 0) {
          vectorData = keyData;
        }
        if (vectorData.length > 16) {
          vectorData = vectorData.subarray(0, 16);
        }
        if (aesMode == TKAesMode.ECB_NOPADDING || aesMode == TKAesMode.CBC_NOPADDING) {
          data = TKDataHelper.padUint8Array(data);
        }
        let algName: string = getAlgName(key);
        let symKey: cryptoFramework.SymKey = genSymKeyByDataSync(keyData, algName);
        let iv: cryptoFramework.IvParamsSpec | undefined = genIvParamsSpec(vectorData);
        let transformation: string = "";
        switch (aesMode) {
          case TKAesMode.ECB: {
            transformation = `${algName}|ECB|PKCS7`;
            break;
          }
          case TKAesMode.ECB_NOPADDING: {
            transformation = `${algName}|ECB|NoPadding`;
            break;
          }
          case TKAesMode.CBC: {
            transformation = `${algName}|CBC|PKCS7`;
            break;
          }
          case TKAesMode.CBC_NOPADDING: {
            transformation = `${algName}|CBC|NoPadding`;
            break;
          }
        }
        let cipher = cryptoFramework.createCipher(transformation);
        cipher.initSync(cryptoMode, symKey, iv);
        let plainText: cryptoFramework.DataBlob = { data: data };
        let cipherData = cipher.doFinalSync(plainText);
        result = cipherData.data;
      }
    } catch (error) {
      TKLog.error(`[TKAesHelper]同步加解密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  Aes加密，先做AES加密，再做Base64编码
   *
   * @param content 数据
   * @param key    秘钥
   * @param vector 矢量
   * @param aesMode   模式
   *
   * @return 加密后的字符串
   */
  export function stringWithAesEncryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", aesMode: TKAesMode = TKAesMode.ECB): string {
    let result: Uint8Array = dataWithAesEncryptSync(content, key, vector, aesMode);
    let encryptStr: string = TKBase64Helper.stringWithBase64Encode(result);
    return TKStringHelper.trim(encryptStr);
  }

  /**
   *  Aes加密
   *
   * @param content   数据
   * @param key    秘钥
   * @param vector 矢量
   * @param aesMode   模式
   *
   * @return 加密后的数据
   */
  export function dataWithAesEncryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", aesMode: TKAesMode = TKAesMode.ECB): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
      result = dataWithAesEncrptAndDecryptDataSync(data, key, vector, cryptoFramework.CryptoMode.ENCRYPT_MODE, aesMode);
    }
    return result;
  }

  /**
   *  Aes解密，先做base64解码，再做aes解密
   *
   * @param content 字符串
   * @param key    秘钥
   * @param vector 矢量
   * @param aesMode   模式
   *
   * @return 解密后的字符串
   */
  export function stringWithAesDecryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", aesMode: TKAesMode = TKAesMode.ECB): string {
    let result: Uint8Array = dataWithAesDecryptSync(content, key, vector, aesMode);
    let decryptStr: string = TKDataHelper.uint8ArrayToString(result);
    decryptStr = TKStringHelper.trim(decryptStr);
    return decryptStr;
  }

  /**
   *  Aes解密，先做base64解码，再做aes解密
   *
   * @param content 内容
   * @param key    秘钥
   * @param vector 矢量
   * @param aesMode   模式
   *
   * @return 解密后的数据
   */
  export function dataWithAesDecryptSync(content: string | Uint8Array, key: string | Uint8Array,
    vector: string | Uint8Array = "", aesMode: TKAesMode = TKAesMode.ECB): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    if (content) {
      let data: Uint8Array =
        content instanceof Uint8Array ? content as Uint8Array : TKBase64Helper.dataWithBase64Decode(content as string);
      result = dataWithAesEncrptAndDecryptDataSync(data, key, vector, cryptoFramework.CryptoMode.DECRYPT_MODE, aesMode);
    }
    return result;
  }
}