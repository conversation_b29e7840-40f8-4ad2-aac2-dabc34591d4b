/**
 * SHA帮组类
 */
import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { TKDataHelper } from '../data/TKDataHelper';
import { TKLog } from '../logger/TKLog';
import { TKHexHelper } from './TKHexHelper';

export namespace TKShaHelper {

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  MD通用摘要算法
   *
   * @param content 要编码的内容
   * @param mdAlgName MD加密算法
   *
   * @return 编码后的字符串
   */
  async function md(content: string | Uint8Array, mdAlgName: string = "SHA1"): Promise<string> {
    let result: string = "";
    try {
      if (content) {
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        if (data && data.length > 0) {
          let blob: cryptoFramework.DataBlob = { data: data };
          let md: cryptoFramework.Md = cryptoFramework.createMd(mdAlgName);
          await md.update(blob);
          let mdResult: cryptoFramework.DataBlob = await md.digest();
          result = TKHexHelper.stringWithHexEncode(mdResult.data);
        }
      }
    } catch (error) {
      TKLog.error(`[TKShaHelper][MD ${mdAlgName}]异步加密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * 生成秘钥
   * @param symKeyData
   * @param algName
   * @returns
   */
  async function genSymKeyByData(symKeyData: Uint8Array, algName: string = "AES128"): Promise<cryptoFramework.SymKey> {
    let symKeyBlob: cryptoFramework.DataBlob = { data: symKeyData };
    let aesGenerator = cryptoFramework.createSymKeyGenerator(algName);
    let symKey = await aesGenerator.convertKey(symKeyBlob);
    return symKey;
  }

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  MAC通用摘要算法
   *
   * @param content 要编码的内容
   * @param secret MAC加密秘钥
   * @param mdAlgName MAC加密算法
   *
   * @return 编码后的字符串
   */
  async function mac(content: string | Uint8Array, secret: string | Uint8Array,
    mdAlgName: string = "SHA1"): Promise<string> {
    let result: string = "";
    try {
      if (content) {
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        let secretData: Uint8Array =
          secret instanceof Uint8Array ? secret as Uint8Array : TKDataHelper.stringToUint8Array(secret as string);
        if (data && data.length > 0) {
          if (secretData.length > 16) {
            secretData = secretData.subarray(0, 16);
          }
          let blob: cryptoFramework.DataBlob = { data: data };
          let symKey: cryptoFramework.SymKey = await genSymKeyByData(secretData);
          let mac: cryptoFramework.Mac = cryptoFramework.createMac(mdAlgName);
          await mac.init(symKey);
          await mac.update(blob);
          let macResult: cryptoFramework.DataBlob = await mac.doFinal();
          result = TKHexHelper.stringWithHexEncode(macResult.data);
        }
      }
    } catch (error) {
      TKLog.error(`[TKShaHelper][MAC ${mdAlgName}]异步加密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  SHA224摘要算法
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export async function sha224(content: string | Uint8Array): Promise<string> {
    return md(content, "SHA224");
  }

  /**
   *  SHA256摘要算法
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export async function sha256(content: string | Uint8Array): Promise<string> {
    return md(content, "SHA256");
  }

  /**
   *  SHA384摘要算法
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export async function sha384(content: string | Uint8Array): Promise<string> {
    return md(content, "SHA384");
  }

  /**
   *  SHA512摘要算法
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export async function sha512(content: string | Uint8Array): Promise<string> {
    return md(content, "SHA512");
  }

  /**
   *  SHA1摘要算法,增加秘钥签名
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export async function hmacSHA1WithSecret(content: string | Uint8Array, secret: string | Uint8Array): Promise<string> {
    return mac(content, secret, "SHA1");
  }

  /**
   *  SHA256摘要算法,增加秘钥签名
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export async function hmacSHA256WithSecret(content: string | Uint8Array,
    secret: string | Uint8Array): Promise<string> {
    return mac(content, secret, "SHA256");
  }

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  MD通用摘要算法
   *
   * @param content 要编码的内容
   * @param mdAlgName MD加密算法
   *
   * @return 编码后的字符串
   */
  function mdSync(content: string | Uint8Array, mdAlgName: string = "SHA1"): string {
    let result: string = "";
    try {
      if (content) {
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        if (data && data.length > 0) {
          let blob: cryptoFramework.DataBlob = { data: data };
          let md: cryptoFramework.Md = cryptoFramework.createMd(mdAlgName);
          md.updateSync(blob);
          let mdResult: cryptoFramework.DataBlob = md.digestSync();
          result = TKHexHelper.stringWithHexEncode(mdResult.data);
        }
      }
    } catch (error) {
      TKLog.error(`[TKShaHelper][MD ${mdAlgName}]同步加密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * 生成秘钥
   * @param symKeyData
   * @param algName
   * @returns
   */
  function genSymKeyByDataSync(symKeyData: Uint8Array, algName: string = "AES128"): cryptoFramework.SymKey {
    let symKeyBlob: cryptoFramework.DataBlob = { data: symKeyData };
    let aesGenerator = cryptoFramework.createSymKeyGenerator(algName);
    let symKey = aesGenerator.convertKeySync(symKeyBlob);
    return symKey;
  }

  /**
   * <AUTHOR> 2016-11-16 10:11:31
   *
   *  MAC通用摘要算法
   *
   * @param content 要编码的内容
   * @param secret MAC加密秘钥
   * @param mdAlgName MAC加密算法
   *
   * @return 编码后的字符串
   */
  function macSync(content: string | Uint8Array, secret: string | Uint8Array,
    mdAlgName: string = "SHA1"): string {
    let result: string = "";
    try {
      if (content) {
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        let secretData: Uint8Array =
          secret instanceof Uint8Array ? secret as Uint8Array : TKDataHelper.stringToUint8Array(secret as string);
        if (data && data.length > 0) {
          if (secretData.length > 16) {
            secretData = secretData.subarray(0, 16);
          }
          let blob: cryptoFramework.DataBlob = { data: data };
          let symKey: cryptoFramework.SymKey = genSymKeyByDataSync(secretData);
          let mac: cryptoFramework.Mac = cryptoFramework.createMac(mdAlgName);
          mac.initSync(symKey);
          mac.updateSync(blob);
          let macResult: cryptoFramework.DataBlob = mac.doFinalSync();
          result = TKHexHelper.stringWithHexEncode(macResult.data);
        }
      }
    } catch (error) {
      TKLog.error(`[TKShaHelper][MAC ${mdAlgName}]同步加密异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  SHA224摘要算法
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export function sha224Sync(content: string | Uint8Array): string {
    return mdSync(content, "SHA224");
  }

  /**
   *  SHA256摘要算法
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export function sha256Sync(content: string | Uint8Array): string {
    return mdSync(content, "SHA256");
  }

  /**
   *  SHA384摘要算法
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export function sha384Sync(content: string | Uint8Array): string {
    return mdSync(content, "SHA384");
  }

  /**
   *  SHA512摘要算法
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export function sha512Sync(content: string | Uint8Array): string {
    return mdSync(content, "SHA512");
  }

  /**
   *  SHA1摘要算法,增加秘钥签名
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export function hmacSHA1WithSecretSync(content: string | Uint8Array, secret: string | Uint8Array): string {
    return macSync(content, secret, "SHA1");
  }

  /**
   *  SHA256摘要算法,增加秘钥签名
   *
   * @param content 字符串或者二进制
   *
   * @return 编码后的字符串
   */
  export function hmacSHA256WithSecretSync(content: string | Uint8Array,
    secret: string | Uint8Array): string {
    return macSync(content, secret, "SHA256");
  }

}