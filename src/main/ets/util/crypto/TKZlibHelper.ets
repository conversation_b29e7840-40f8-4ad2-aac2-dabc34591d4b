/**
 * zlib帮组类
 */
import { deflate, inflate } from 'pako';
import { TKLog } from '../logger/TKLog';

export namespace TKZlibHelper {

  /**
   *  压缩数据
   * @param data 需要压缩的数据
   *
   * @return 压缩后的数据
   */
  export function compress(data: Uint8Array): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (data && data.length > 0) {
        result = deflate(data);
      }
    } catch (error) {
      TKLog.error(`[TKZlibHelper]压缩数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  解压数据
   * @param data      需要解压的数据
   *
   * @return 解压后的数据
   */
  export function unCompress(data: Uint8Array): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (data && data.length > 0) {
        result = inflate(data);
      }
    } catch (error) {
      TKLog.error(`[TKZlibHelper]解压数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

}