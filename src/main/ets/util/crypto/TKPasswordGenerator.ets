/**
 * 密码生成器
 */
import { TKStringHelper } from '../string/TKStringHelper';

/**
 * 密码生成器
 */
export class TKPasswordGenerator {
  private static instance: TKPasswordGenerator | undefined = undefined;
  private password: string = "";

  private constructor() {
  }

  public static shareInstance() {
    if (!TKPasswordGenerator.instance) {
      TKPasswordGenerator.instance = new TKPasswordGenerator();
    }
    return TKPasswordGenerator.instance;
  }

  /**
   * 设定特定密码
   * @param password
   */
  public savePassword(password: string) {
    this.password = password;
  }

  /**
   * 生成秘钥
   * @returns
   */
  public generatorPassword(): string {
    if (TKStringHelper.isBlank(this.password)) {
      let temp: Array<string> = ["i", "k", "e", "v", "h", "t", "n", "i"];
      let array: Array<string> = new Array<string>();
      for (let i: number = 0; i < temp.length; i++) {
        if (i % 2 == 0) {
          array.push(temp[i + 1]);
        } else {
          array.push(temp[i - 1]);
        }
      }
      for (let i: number = 0; i < (array.length / 2); i++) {
        let x: string = array[i];
        let y: string = array[i + (array.length / 2)];
        array[i] = y;
        array[i + (array.length / 2)] = x;
      }
      let ps: string = "";
      for (let i: number = 0; i < (array.length / 2); i++) {
        for (let j: number = 0; j < array.length; j++) {
          ps += array[j];
        }
      }
      this.password = ps;
    }
    return this.password;
  }
}