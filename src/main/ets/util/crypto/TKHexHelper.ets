/**
 * 16进制帮组类
 */
import { TKDataHelper } from '../data/TKDataHelper';
import { TKStringHelper } from '../string/TKStringHelper';
import { buffer } from '@kit.ArkTS';
import { TKLog } from '../../util/logger/TKLog';

export namespace TKHexHelper {

  /**
   *  16进制编码为字符串
   *
   * @param content 原数据
   *
   * @return 16进制字符串
   */
  export function stringWithHexEncode(content: string | Uint8Array | ArrayBuffer): string {
    let result = '';
    try {
      if (content) {
        let bytes: Uint8Array | undefined = undefined;
        if (content instanceof ArrayBuffer) {
          bytes = new Uint8Array(content as ArrayBuffer)
        } else if (content instanceof Uint8Array) {
          bytes = content as Uint8Array;
        } else {
          bytes = TKDataHelper.stringToUint8Array(content as string);
        }
        if (bytes && bytes.length > 0) {
          result = buffer.from(bytes).toString("hex");
        }
      }
    } catch (error) {
      TKLog.error(`[TKHexHelper]进行16进制编码为字符串异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  16进制编码为二进制
   *
   * @param content 原数据
   *
   * @return 16进制数据
   */
  export function dataWithHexEncode(content: string | Uint8Array | ArrayBuffer): Uint8Array {
    let result: string = stringWithHexEncode(content);
    return TKDataHelper.stringToUint8Array(result);
  }

  /**
   *  16进制解码为字符串
   *
   * @param content 16进制数据
   *
   * @return 原始字符串
   */
  export function stringWithHexDecode(content: string | Uint8Array): string {
    let result: Uint8Array = dataWithHexDecode(content);
    return TKDataHelper.uint8ArrayToString(result);
  }

  /**
   *  16进制解码为二进制
   *
   * @param content 16进制数据
   *
   * @return 普通数据
   */
  export function dataWithHexDecode(content: string | Uint8Array): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (content) {
        let hexString: string =
          content instanceof Uint8Array ? TKDataHelper.uint8ArrayToString(content as Uint8Array) : content as string;
        if (hexString && hexString.length > 0) {
          result = new Uint8Array(buffer.from(hexString, "hex").buffer);
        }
      }
    } catch (error) {
      TKLog.error(`[TKHexHelper]16进制数据解码二进制数据异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  10进制数字转成16进制的字符串
   *
   * @return 16进制字符串
   */
  export function hexStringFromInteger(content: number | string): string {
    try {
      let value: Number = Number(content);
      let hex: string = value.toString(16);
      return "0x" + hex;
    } catch (error) {
      TKLog.error(`[TKHexHelper]10进制数字转成16进制的字符串异常，code: ${error.code}， message: ${error.message}`);
      return "";
    }
  }

  /**
   *  16进制字符串转成10进制数字
   *
   * @return 10进制数字
   */
  export function integerFromHexString(hexString: string): number {
    if (TKStringHelper.startsWith(hexString, "0x") || TKStringHelper.startsWith(hexString, "0X")) {
      hexString = hexString.substring(2, hexString.length);
    }
    return parseInt(hexString, 16);
  }

  /**
   *  16进制字符串转成10进制数字字符串
   *
   * @return 10进制数字字符串
   */
  export function integerStringFromHexString(hexString: string): string {
    return String(integerFromHexString(hexString));
  }
}