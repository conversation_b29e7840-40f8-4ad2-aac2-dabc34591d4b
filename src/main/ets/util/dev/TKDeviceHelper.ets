import deviceinfo from '@ohos.deviceInfo';
import display from '@ohos.display';
import { batteryInfo, Callback } from '@kit.BasicServicesKit';
import { TKLog } from '../logger/TKLog';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKAssetStore } from '../cache/TKAssetStore';
import { TKCacheManager } from '../cache/TKCacheManager';
import { TKUUIDHelper } from '../crypto/TKUUIDHelper';
import { TKContextHelper } from '../system/TKContextHelper';
import { i18n, resourceManager } from '@kit.LocalizationKit';
import { vibrator } from '@kit.SensorServiceKit';
import { TKSystemHelper } from '../system/TKSystemHelper';
import hidebug from '@ohos.hidebug';
import { TKNumberHelper, TKNumberRadix } from '../number/TKNumberHelper';

/**
 * 设备屏幕大小
 */
export enum TKScreenSizeType {
  XS, //超小设备  手表
  SM, //小设备    手机
  MD, //中等设备  PAD
  LG //大设备    PC
}

/**
 *  设备信息获取代理接口
 */
export interface TKDeviceInfoDelegate {
  /**
   *  获取设备的唯一性id，这里取MAC地址,大于7.0的取identifierForVendor
   */
  getDeviceMac?: () => string;

  /**
   *  获取设备的uuid
   * @returns
   */
  getDeviceUUID?: () => string;

  /**
   *  获取运营商信息(IMSI)
   * @return 获取运营商信息(IMSI)
   */
  getPhoneIMSI?: () => string;

  /**
   *  获取手机设备的本地IP地址
   *
   * @return 手机设备的IP地址
   */
  getLocalIP?: () => string;
}

/**
 * 内存对象
 */
export interface TKDeviceMemory {
  /**
   * 总共内存
   */
  totalMem: string;

  /**
   * 空闲内存
   */
  freeMem: string;

  /**
   * 可用内存
   */
  availableMem: string;
}

/**
 * 设备操作操作帮组类
 */
export namespace TKDeviceHelper {

  //设备UUID
  let deviceId: string = "";
  //设备缓存key
  const deviceIdKey: string = "device_id_cache_harmony_key";
  //设备代理
  let deviceInfoDelegate: TKDeviceInfoDelegate | undefined = undefined;

  /**
   设置代理类
   */
  export function setTKDeviceInfoDelegate(delegate: TKDeviceInfoDelegate) {
    deviceInfoDelegate = delegate;
  }

  /**
   * 是否模拟器
   * @returns
   */
  export function isEmulator(): boolean {
    return deviceinfo.marketName == "emulator";
  }

  /**
   * 获取设备名称
   */
  export function getDeviceName(): string {
    return deviceinfo.marketName;
  }

  /**
   * 获取设备型号名称
   */
  export function getDevicePlatformInfo(): string {
    return deviceinfo.marketName;
  }

  /**
   * 获取设备型号
   * @returns
   */
  export function getDevicePlatform(): string {
    return deviceinfo.productModel;
  }

  /**
   * 获取设备类型
   * @returns
   */
  export function getDeviceType(): resourceManager.DeviceType {
    return getResConfiguration().deviceType;
  }

  /**
   * 获取设备类型名称
   * @returns
   */
  export function getDeviceTypeName(): string {
    return deviceinfo.deviceType;
  }

  /**
   * 获取系统版本
   * @returns
   */
  export function getDeviceSysVersion(): string {
    let temp: Array<string> = deviceinfo.osFullName.split("-");
    return temp[temp.length-1];
  }

  /**
   *  获取当前设备的语言
   * @return 系统语言
   */
  export function getDeviceSysLanguage(): string {
    return i18n.System.getSystemLanguage();
  }

  /**
   * <AUTHOR> 2016-08-11 23:08:25
   *
   *  获取当前设备的时区
   *
   * @return 系统时区
   */
  export function getDeviceTimeZone(): string {
    return i18n.getTimeZone().getDisplayName();
  }

  /**
   *  获取设备内存的情况
   * @return 设备内存的信息
   */
  export function getDeviceMemory(): TKDeviceMemory {
    let systemMemInfo = hidebug.getSystemMemInfo();
    return {
      totalMem: TKNumberHelper.formatNumberRadix(Number(systemMemInfo.totalMem) * 1024, 0, TKNumberRadix.BIN_MEM),
      availableMem: TKNumberHelper.formatNumberRadix(Number(systemMemInfo.availableMem) * 1024, 0,
        TKNumberRadix.BIN_MEM),
      freeMem: TKNumberHelper.formatNumberRadix(Number(systemMemInfo.freeMem) * 1024, 0, TKNumberRadix.BIN_MEM)
    } as TKDeviceMemory;
  }

  /**
   *  获取设备CPU的情况
   * @return 设备CPU的信息
   */
  export function getDeviceCpu(): string {
    return TKNumberHelper.formatNumber(hidebug.getSystemCpuUsage(), 2) + "%";
  }

  /**
   *  获取应用CPU的情况
   * @return 获取应用CPU的情况
   */
  export function getAppCpu(): string {
    return TKNumberHelper.formatNumber(hidebug.getCpuUsage(), 2) + "%";
  }

  /**
   *  获取设备电池量
   * @return 设备电池信息
   */
  export function getDeviceBattery(): string {
    return TKNumberHelper.formatNumber(batteryInfo.batterySOC, 2) + "%";
  }

  /**
   * 获取设备UUID
   */
  export function getDeviceUUID(): string {
    if (deviceInfoDelegate && deviceInfoDelegate.getDeviceUUID) {
      return deviceInfoDelegate.getDeviceUUID();
    }
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return "";
    }
    if (TKStringHelper.isBlank(deviceId)) {
      deviceId = TKCacheManager.shareInstance().getFileCacheData(deviceIdKey) as string;
    }
    if (TKStringHelper.isBlank(deviceId)) {
      deviceId = TKUUIDHelper.uuid();
      TKCacheManager.shareInstance().saveFileCacheData(deviceIdKey, deviceId);
      if (canIUse("SystemCapability.Security.Asset")) {
        //将deviceId保存到关键资产（在应用卸载时保留）
        TKAssetStore.setData(deviceIdKey, deviceId);
      }
    }
    return deviceId;
  }

  /**
   * 异步获取设备UUID
   */
  export async function getDeviceUUIDASync(): Promise<string> {
    if (deviceInfoDelegate && deviceInfoDelegate.getDeviceUUID) {
      return deviceInfoDelegate.getDeviceUUID();
    }
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return "";
    }
    let isLocalExist: boolean = true;
    if (TKStringHelper.isBlank(deviceId)) {
      deviceId = TKCacheManager.shareInstance().getFileCacheData(deviceIdKey) as string;
    }
    if (TKStringHelper.isBlank(deviceId)) {
      isLocalExist = false;
      if (canIUse("SystemCapability.Security.Asset")) {
        deviceId = await TKAssetStore.getData(deviceIdKey) as string;
      }
    }
    if (TKStringHelper.isBlank(deviceId)) {
      isLocalExist = false;
      deviceId = TKUUIDHelper.uuid();
    }
    if (!isLocalExist) {
      TKCacheManager.shareInstance().saveFileCacheData(deviceIdKey, deviceId);
      if (canIUse("SystemCapability.Security.Asset")) {
        //将deviceId保存到关键资产（在应用卸载时保留）
        await TKAssetStore.setData(deviceIdKey, deviceId);
      }
    }
    return deviceId;
  }

  /**
   * 获取设备MAC
   */
  export function getDeviceMac(): string {
    if (deviceInfoDelegate && deviceInfoDelegate.getDeviceMac) {
      return deviceInfoDelegate.getDeviceMac();
    }
    if (TKSystemHelper.getConfig("system.isUserAgreeRight", "1") == "0") {
      return "";
    }
    return "";
  }

  /**
   * 获取屏幕宽度
   */
  export function getScreenWidth(): number {
    return display.getDefaultDisplaySync().width;
  }

  /**
   * 获取屏幕宽高度
   */
  export function getScreenHeight(): number {
    return display.getDefaultDisplaySync().height;
  }

  /**
   * 获取屏幕DPI
   */
  export function getScreenDPI(): number {
    return display.getDefaultDisplaySync().densityDPI;
  }

  /**
   *  获取屏幕分辨率
   * @returns
   */
  export function getScreenDisplay(): string {
    return display.getDefaultDisplaySync().width + "x" + display.getDefaultDisplaySync().height;
  }

  /**
   * 获取设备当前显示的方向
   * Orientation:
   *   PORTRAIT  0  表示设备当前以竖屏方式显示。
   *   LANDSCAPE  1  表示设备当前以横屏方式显示。
   *   PORTRAIT_INVERTED  2  表示设备当前以反向竖屏方式显示。
   *   LANDSCAPE_INVERTED  3  表示设备当前以反向横屏方式显示。
   * @returns
   */
  export function getOrientation(): display.Orientation {
    return display.getDefaultDisplaySync().orientation;
  }

  /**
   *  获取设备当前旋转角度
   * @returns
   */
  export function getOrientationAngle(): number {
    let orientation: display.Orientation = getOrientation();
    let angle: number = 0;
    switch (orientation) {
      case display.Orientation.PORTRAIT: {
        angle = 0;
        break;
      }
      case display.Orientation.LANDSCAPE: {
        angle = 90;
        break;
      }
      case display.Orientation.PORTRAIT_INVERTED: {
        angle = 180;
        break;
      }
      case display.Orientation.LANDSCAPE_INVERTED: {
        angle = 270;
        break;
      }
    }
    return angle;
  }

  /**
   * 获取设备的Configuration
   * @returns
   */
  export function getResConfiguration(): resourceManager.Configuration {
    return TKContextHelper.getCurrentContext().resourceManager.getConfigurationSync();
  }

  /**
   * 获取当前设备屏幕方向。
   * resourceManager.Direction:
   *   DIRECTION_VERTICAL 0 竖屏。
   *   DIRECTION_HORIZONTAL 1 横屏。
   * @returns
   */
  export function getDirection(): number {
    return getResConfiguration().direction;
  }

  /**
   * 获取设备的状态
   * DisplayState:
   *   STATE_UNKNOWN  0  表示显示设备状态未知。
   *   STATE_OFF  1  表示显示设备状态为关闭。
   *   STATE_ON  2  表示显示设备状态为开启。
   *   STATE_DOZE  3  表示显示设备为低电耗模式。
   *   STATE_DOZE_SUSPEND  4  表示显示设备为睡眠模式，CPU为挂起状态。
   *   STATE_VR  5  表示显示设备为VR模式。
   *   STATE_ON_SUSPEND  6 表示显示设备为开启状态，CPU为挂起状态。
   * @returns
   */
  export function getDisplayState(): display.DisplayState {
    return display.getDefaultDisplaySync().state;
  }

  /**
   * 获取挖孔屏、刘海屏、瀑布屏等不可用屏幕区域信息。使用Promise异步回调。建议应用布局规避该区域。
   * @returns
   */
  export async function getCutoutRect(): Promise<display.Rect> {
    let cutoutInfo = await display.getDefaultDisplaySync().getCutoutInfo();
    return cutoutInfo.boundingRects[0];
  }

  /**
   * 获取挖孔屏、刘海屏等不可用屏幕区域的高度。单位px。
   * @returns
   */
  export async function getCutoutHeight(): Promise<number> {
    let rect = await getCutoutRect();
    if (rect) {
      return rect.height;
    }
    return 0;
  }

  /**
   * 检查设备是否可折叠。
   * @returns
   */
  export function isFoldable(): boolean {
    return display.isFoldable();
  }


  /**
   * 获取可折叠设备的当前折叠状态。
   * FoldStatus:
   *   FOLD_STATUS_UNKNOWN  0  表示设备当前折叠状态未知。
   *   FOLD_STATUS_EXPANDED 1  表示设备当前折叠状态为完全展开。
   *   FOLD_STATUS_FOLDED  2  表示设备当前折叠状态为折叠。
   *   FOLD_STATUS_HALF_FOLDED  3  表示设备当前折叠状态为半折叠。半折叠指完全展开和折叠之间的状态。
   * @returns
   * @returns
   */
  export function getFoldStatus(): display.FoldStatus {
    return display.getFoldStatus();
  }

  /**
   * 获取可折叠设备的显示模式。
   * FoldDisplayMode:
   *   FOLD_DISPLAY_MODE_UNKNOWN  0  表示设备当前折叠显示模式未知。
   *   FOLD_DISPLAY_MODE_FULL  1  表示设备当前全屏显示。
   *   FOLD_DISPLAY_MODE_MAIN  2  表示设备当前主屏幕显示。
   *   FOLD_DISPLAY_MODE_SUB  3  表示设备当前子屏幕显示。
   *   FOLD_DISPLAY_MODE_COORDINATION  4  表示设备当前双屏协同显示。
   * @returns
   */
  export function getFoldDisplayMode(): display.FoldDisplayMode {
    return display.getFoldDisplayMode();
  }

  /**
   * 开启折叠设备折叠状态变化的监听。
   * @param callback
   */
  export function onFoldStatusChange(callback: Callback<display.FoldStatus>) {
    try {
      display.off('foldStatusChange', callback)
      display.on('foldStatusChange', callback);
    } catch (error) {
      TKLog.error(`[TKDeviceHelper]开启折叠设备折叠状态监听异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 关闭折叠设备折叠状态变化的监听。
   * @param callback
   */
  export function offFoldStatusChange(callback?: Callback<display.FoldStatus>) {
    try {
      if (callback) {
        display.off('foldStatusChange', callback);
      } else {
        display.off('foldStatusChange')
      }
    } catch (error) {
      TKLog.error(`[TKDeviceHelper]关闭折叠设备折叠状态监听异常，code: ${error.code}， message: ${error.message}`);
    }
  }

  /**
   * 获取主题颜色模式
   * ColorMode:
   *   LIGHT  0  表示白色主题
   *   DARK  1   表示黑色主题
   * @returns
   */
  export function getDarkMode(): resourceManager.ColorMode {
    return getResConfiguration().colorMode
  }

  /**
   * 用户获取设备的DeviceCapability
   * @returns
   */
  export function getDeviceCapability(): resourceManager.DeviceCapability {
    return TKContextHelper.getCurrentContext().resourceManager.getDeviceCapabilitySync();
  }

  /**
   * 获取当前设备屏幕密度。
   * resourceManager.ScreenDensity:
   *   SCREEN_SDPI 120 小规模的屏幕密度。
   *   SCREEN_MDPI 160 中规模的屏幕密度。
   *   SCREEN_LDPI 240 大规模的屏幕密度。
   *   SCREEN_XLDPI 320 特大规模的屏幕密度。
   *   SCREEN_XXLDPI 480 超大规模的屏幕密度。
   *   SCREEN_XXXLDPI 640 超特大规模的屏幕密度。
   * @returns
   */
  export function getScreenDensity(): resourceManager.ScreenDensity {
    return getResConfiguration().screenDensity;
  }

  /**
   * 获取当前设备对应图片目录
   * @returns
   */
  export function getScreenDensityImageDir(): string {
    let screenDensity: resourceManager.ScreenDensity = getScreenDensity();
    let imageDir: string = "";
    switch (screenDensity) {
      case resourceManager.ScreenDensity.SCREEN_SDPI: {
        imageDir = "sdpi";
        break;
      }
      case resourceManager.ScreenDensity.SCREEN_MDPI: {
        imageDir = "mdpi";
        break;
      }
      case resourceManager.ScreenDensity.SCREEN_LDPI: {
        imageDir = "ldpi";
        break;
      }
      case resourceManager.ScreenDensity.SCREEN_XLDPI: {
        imageDir = "xldpi";
        break;
      }
      case resourceManager.ScreenDensity.SCREEN_XXLDPI: {
        imageDir = "xxldpi";
        break;
      }
      case resourceManager.ScreenDensity.SCREEN_XXXLDPI: {
        imageDir = "xxxldpi";
        break;
      }
    }
    return imageDir;
  }

  /**
   * 获取当前设备大小
   * @returns
   */
  export function getScreenSizeType(): TKScreenSizeType {
    let screenWidth: number = getScreenWidth()
    if (screenWidth >= 0 && screenWidth < 320) {
      return TKScreenSizeType.XS;
    } else if (screenWidth >= 320 && screenWidth < 600) {
      return TKScreenSizeType.SM;
    } else if (screenWidth >= 600 && screenWidth < 840) {
      return TKScreenSizeType.MD;
    } else if (screenWidth >= 840) {
      return TKScreenSizeType.LG;
    }
    return TKScreenSizeType.SM;
  }

  /**
   * 开启振动
   * @param duration
   * @param usage
   */
  export function startVibration(duration: number = 1000, usage: vibrator.Usage = 'media'): Promise<void> {
    return vibrator.startVibration({ type: 'time', duration: duration }, { id: 0, usage: usage });
  }

  /**
   * 停止振动（按照VIBRATOR_STOP_MODE_TIME模式）
   */
  export function stopVibration(): Promise<void> {
    return vibrator.stopVibration(vibrator.VibratorStopMode.VIBRATOR_STOP_MODE_TIME);
  }

}