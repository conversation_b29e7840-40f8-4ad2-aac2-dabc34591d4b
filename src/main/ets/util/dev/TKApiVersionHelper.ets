import deviceInfo from '@ohos.deviceInfo';

/**
 * API判断帮组类
 */
export namespace TKApiVersionHelper {

  /**
   * 是否API12
   * @returns
   */
  export function isApi12OrLater(): boolean {
    return getApiVersion() >= 12;
  }

  /**
   * 是否API13
   * @returns
   */
  export function isApi13OrLater(): boolean {
    return getApiVersion() >= 13;
  }

  /**
   * 获取具体的API版本
   */
  export function getApiVersion(): number {
    return deviceInfo.sdkApiVersion;
  }

  /**
   * 检查特定API版本范围
   */
  export function isApiVersionInRange(minVersion: number, maxVersion: number): boolean {
    const currentVersion: number = getApiVersion();
    return currentVersion >= minVersion && currentVersion <= maxVersion;
  }

}