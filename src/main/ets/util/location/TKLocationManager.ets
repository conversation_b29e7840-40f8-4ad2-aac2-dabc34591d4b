/**
 * 地理位置管理器
 */
import { geoLocationManager } from '@kit.LocationKit';
import { TKPermission, TKPermissionHelper, TKPermissionsRequestResult } from '../system/TKPermissionHelper';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKLog } from '../logger/TKLog';
import { TKDialogHelper } from '../ui/TKDialogHelper';

export class TKLocationManager {
  private static instance: TKLocationManager | undefined = undefined;

  public static shareInstance(): TKLocationManager {
    if (!TKLocationManager.instance) {
      TKLocationManager.instance = new TKLocationManager();
    }
    return TKLocationManager.instance;
  }

  private locationRequest: geoLocationManager.LocationRequest = {
    'priority': geoLocationManager.LocationRequestPriority.FIRST_FIX, //表示快速获取位置优先，如果应用希望快速拿到一个位置，可以将优先级设置为该字段。
    'scenario': geoLocationManager.LocationRequestScenario.UNSET, //表示未设置优先级，表示LocationRequestPriority无效。
    'timeInterval': 10, //表示上报位置信息的时间间隔，单位是秒。默认值为1，取值范围为大于等于0。10秒钟获取一下位置
    'distanceInterval': 0, //表示上报位置信息的距离间隔。单位是米，默认值为0，取值范围为大于等于0。
    'maxAccuracy': 0 //表示精度信息，单位是米。
  }; //开启位置变化订阅，默认Request参数

  private currentRequest: geoLocationManager.CurrentLocationRequest = {
    // 'priority': geoLocationManager.LocationRequestPriority.FIRST_FIX, //表示快速获取位置优先，如果应用希望快速拿到一个位置，可以将优先级设置为该字段。
    'scenario': geoLocationManager.LocationRequestScenario.DAILY_LIFE_SERVICE, //表示日常服务使用场景。适用于不需要定位用户精确位置的使用场景。此场景默认以最小1秒间隔上报定位结果。
    'maxAccuracy': 0, //表示精度信息，单位是米。
    'timeoutMs': 1000 //表示超时时间，单位是毫秒，最小为1000毫秒。取值范围为大于等于1000。
  } //当前位置，默认Request参数

  //开启位置变化订阅，监听回调
  private locationCallBack: Callback<geoLocationManager.Location> | undefined = undefined;

  /**
   * 判断位置服务是否已经使能(定位是否开启)。
   * @returns
   */
  public isLocationEnabled(): boolean {
    return geoLocationManager.isLocationEnabled()
  }

  /**
   * 申请定位权限
   * @returns
   */
  public async requestLocationPermissions(): Promise<boolean> {
    let requestResult: TKPermissionsRequestResult =
      await TKPermissionHelper.requestPermissionsFromUser([TKPermission.APPROXIMATELY_LOCATION, TKPermission.LOCATION]);
    return requestResult.isGrant;
  }

  /**
   * 获取当前位置，通过callback方式异步返回结果。
   */
  public getCurrentLocationEasy(callBack: Callback<geoLocationManager.Location>) {
    geoLocationManager.getCurrentLocation(this.currentRequest,
      (error: BusinessError, location: geoLocationManager.Location): void => {
        if (error) {
          TKLog.error(`[TKLocationManager]获取当前位置异常，code: ${error.code}， message: ${error.message}`);
          TKDialogHelper.showToast(this.getErrorMsg(error.code, '获取当前位置失败：' + error.message));
        }
        if (location && callBack) {
          callBack(location)
        }
      });
  }

  /**
   * 获取当前位置，使用Promise方式异步返回结果。
   * @param request
   */
  public async getCurrentLocation(request?: geoLocationManager.CurrentLocationRequest): Promise<geoLocationManager.Location> {
    return await geoLocationManager.getCurrentLocation(request);
  }


  /**
   * 开启位置变化订阅，并发起定位请求。
   * @param callBack
   */
  public onLocationChangeEasy(callBack: Callback<geoLocationManager.Location>) {
    try {
      this.locationCallBack = callBack;
      geoLocationManager.on('locationChange', this.locationRequest, callBack);
    } catch (error) {
      TKLog.error(`[TKLocationManager]开启位置变化订阅异常，code: ${error.code}， message: ${error.message}`);
      TKDialogHelper.showToast(this.getErrorMsg(error.code, '开启位置变化订阅失败：' + error.message));
    }
  }


  /**
   * 开启位置变化订阅，并发起定位请求。
   * @param request
   * @param callBack
   */
  public onLocationChange(request: geoLocationManager.LocationRequest,
    callBack: Callback<geoLocationManager.Location>) {
    try {
      if (!request) {
        request = this.locationRequest;
      }
      this.locationCallBack = callBack;
      geoLocationManager.on('locationChange', request, callBack);
    } catch (error) {
      TKLog.error(`[TKLocationManager]开启位置变化订阅异常，code: ${error.code}， message: ${error.message}`);
      TKDialogHelper.showToast(this.getErrorMsg(error.code, '开启位置变化订阅失败：' + error.message));
    }
  }

  /**
   * 关闭位置变化订阅，并删除对应的定位请求。
   * @param all 取消当前类型的所有订阅。
   */
  public offLocationChange(all: boolean = false) {
    try {
      if (all) {
        geoLocationManager.off('locationChange');
      } else {
        if (this.locationCallBack) {
          geoLocationManager.off('locationChange', this.locationCallBack);
        } else {
          geoLocationManager.off('locationChange'); //callback:需要取消订阅的回调函数。若无此参数，则取消当前类型的所有订阅。
        }
      }
    } catch (error) {
      TKLog.error(`[TKLocationManager]关闭位置变化订阅异常，code: ${error.code}， message: ${error.message}`);
      TKDialogHelper.showToast(this.getErrorMsg(error.code, '关闭位置变化订阅失败：' + error.message));
    }
  }

  /**
   * 地理逆编码,将地理描述转换为具体坐标-无需申请定位权限
   * @param locationName  地理位置中文描述
   * @returns 编码后location对象
   */
  public async getAddressFromLocationName(locationName: string): Promise<geoLocationManager.GeoAddress> {
    try {
      let isAvailable = geoLocationManager.isGeocoderAvailable(); //（逆）地理编码服务是否可用
      if (isAvailable) {
        let geocodeRequest: geoLocationManager.GeoCodeRequest = {
          description: locationName, //表示位置信息描述，如“上海市浦东新区xx路xx号”。
          maxItems: 1, //表示返回位置信息的最大个数。取值范围为大于等于0，推荐该值小于10。
          locale: 'zh' //表示位置描述信息的语言，“zh”代表中文，“en”代表英文。
        };
        let result = await geoLocationManager.getAddressesFromLocationName(geocodeRequest);
        if (result && result.length > 0) {
          return result[0];
        } else {
          TKDialogHelper.showToast('地理编码失败!');
          return {};
        }
      } else {
        TKDialogHelper.showToast("（逆）地理编码服务不可用！")
        return {};
      }
    } catch (error) {
      TKLog.error(`[TKLocationManager]地理编码异常，code: ${error.code}， message: ${error.message}`);
      TKDialogHelper.showToast(this.getErrorMsg(error.code, '地理编码失败：' + error.message));
      return {};
    }
  }


  /**
   * 地理逆编码,将地理描述转换为具体坐标-无需申请定位权限
   * @param locationName  地理位置中文描述
   * @param maxItems 表示返回位置信息的最大个数。
   * @returns
   * @returns 编码后集合
   */
  public async getGeoAddressFromLocationName(locationName: string,
    maxItems: number = 1): Promise<Array<geoLocationManager.GeoAddress>> {
    try {
      let isAvailable = geoLocationManager.isGeocoderAvailable(); //（逆）地理编码服务是否可用
      if (isAvailable) {
        let geocodeRequest: geoLocationManager.GeoCodeRequest = {
          description: locationName, //表示位置信息描述，如“上海市浦东新区xx路xx号”。
          maxItems: maxItems, //表示返回位置信息的最大个数。取值范围为大于等于0，推荐该值小于10。
          locale: 'zh' //表示位置描述信息的语言，“zh”代表中文，“en”代表英文。
        };
        let result = await geoLocationManager.getAddressesFromLocationName(geocodeRequest);
        if (result && result.length > 0) {
          return result;
        } else {
          TKDialogHelper.showToast('地理编码失败!');
          return [];
        }
      } else {
        TKDialogHelper.showToast("（逆）地理编码服务不可用！")
        return [];
      }
    } catch (error) {
      TKLog.error(`[TKLocationManager]地理编码异常，code: ${error.code}， message: ${error.message}`);
      TKDialogHelper.showToast(this.getErrorMsg(error.code, '地理编码失败：' + error.message));
      return [];
    }
  }

  /**
   * 地理逆编码,将坐标转换为地理描述-无需申请定位权限
   * @param latitude  维度
   * @param longitude 经度
   * @returns 逆编码后对象
   */
  public async getAddressFromLocation(latitude: number, longitude: number): Promise<geoLocationManager.GeoAddress> {
    try {
      let isAvailable = geoLocationManager.isGeocoderAvailable(); //校验是否逆编码可用
      if (isAvailable) {
        let reverseGeocodeRequest: geoLocationManager.ReverseGeoCodeRequest = {
          latitude: latitude,
          longitude: longitude,
          maxItems: 1,
          locale: 'zh'
        };
        let result = await geoLocationManager.getAddressesFromLocation(reverseGeocodeRequest);
        if (result && result.length > 0) {
          return result[0];
        } else {
          TKDialogHelper.showToast('地理编码失败!');
          return {} as geoLocationManager.GeoAddress;
        }
      } else {
        TKDialogHelper.showToast("（逆）地理编码服务不可用！")
        return {} as geoLocationManager.GeoAddress;
      }
    } catch (error) {
      TKLog.error(`[TKLocationManager]地理编码异常，code: ${error.code}， message: ${error.message}`);
      TKDialogHelper.showToast(this.getErrorMsg(error.code, '地理编码失败：' + error.message));
      return {}
    }
  }

  /**
   * 地理逆编码,将坐标转换为地理描述集合-无需申请定位权限
   * @param latitude  维度
   * @param longitude 经度
   * @returns 逆编码后集合
   */
  public async getGeoAddressFromLocation(latitude: number, longitude: number,
    maxItems: number = 1): Promise<Array<geoLocationManager.GeoAddress>> {
    try {
      let isAvailable = geoLocationManager.isGeocoderAvailable(); //校验是否逆编码可用
      if (isAvailable) {
        let reverseGeocodeRequest: geoLocationManager.ReverseGeoCodeRequest = {
          latitude: latitude,
          longitude: longitude,
          maxItems: maxItems,
          locale: 'zh'
        };
        let result = await geoLocationManager.getAddressesFromLocation(reverseGeocodeRequest);
        if (result && result.length > 0) {
          return result;
        } else {
          TKDialogHelper.showToast('地理编码失败!');
          return [];
        }
      } else {
        TKDialogHelper.showToast("（逆）地理编码服务不可用！")
        return [];
      }
    } catch (error) {
      TKLog.error(`[TKLocationManager]地理编码异常，code: ${error.code}， message: ${error.message}`);
      TKDialogHelper.showToast(this.getErrorMsg(error.code, '地理编码失败：' + error.message));
      return [];
    }
  }

  /**
   * 获取当前的国家码-无需申请定位权限
   * @returns 返回当前位置中文描述
   */
  public async getCountryCode(): Promise<string> {
    let result = await geoLocationManager.getCountryCode(); //获取当前的国家码
    if (result.country) {
      return result.country;
    }
    return "";
  }

  /**
   * 获取错误msg
   * @param code
   * @param defaultMsg
   */
  private getErrorMsg(code: number, defaultMsg: string) {
    if (201 == code) {
      return '权限校验失败！'
    } else if (202 == code) {
      return '系统API权限校验失败！'
    } else if (401 == code) {
      return '参数检查失败！'
    } else if (801 == code) {
      return '该设备不支持此API！'
    } else if (3301000 == code) {
      return '位置服务不可用！'
    } else if (3301100 == code) {
      return '请开启位置功能开关！'
    } else if (3301200 == code) {
      return '定位失败，未获取到定位结果！'
    } else if (3301300 == code) {
      return '逆地理编码查询失败！'
    } else if (3301400 == code) {
      return '地理编码查询失败！'
    } else if (3301500 == code) {
      return '区域信息（包含国家码）查询失败！'
    } else if (3301600 == code) {
      return '地理围栏操作失败！'
    } else {
      return defaultMsg
    }
  }
}