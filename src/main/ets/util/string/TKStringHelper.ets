/**
 * 字符串的帮组类
 */
export namespace TKStringHelper {

  /**
   *  产生num位随机数
   *
   * @param num 随机数的位数
   *
   * @return 生成的随机数
   */
  export function randomStr(num: number): string {
    let result: string = "";
    for (let i = 0; i < num; i++) {
      result += parseInt(String(Math.random() * 10));
    }
    return result;
  }

  /**
   * 去空格
   * @param str
   * @returns
   */
  export function trim(str: string | undefined | null): string {
    let result = str ? str.toString().trim() : "";
    if (result.toLowerCase() === "undefined" || result.toLowerCase() === "null") {
      result = "";
    }
    return result;
  }

  /**
   * 判断字符串是否为空
   * @param str 字符串
   * @returns 判断结果
   */
  export function isBlank(str: string | undefined | null): boolean {
    return trim(str).length == 0;
  }

  /**
   * 判断字符串是否不为空
   * @param str 字符串
   * @returns 判断结果
   */
  export function isNotBlank(str: string | undefined | null): boolean {
    return !isBlank(str);
  }

  /**
   * 判断字符串是否为空
   * @param str 字符串
   * @returns 判断结果
   */
  export function isEmpty(str: string | undefined | null): boolean {
    let result: string = str ?? "";
    if (result.toLowerCase() === "undefined" || result.toLowerCase() === "null") {
      result = "";
    }
    return result.length == 0;
  }

  /**
   * 判断字符串是否不为空
   * @param str 字符串
   * @returns 判断结果
   */
  export function isNotEmpty(str: string | undefined | null): boolean {
    return !isEmpty(str);
  }

  /**
   * 字符串是否是以startValue开头
   * @param srcStr
   * @param startValue
   */
  export function startsWith(srcStr: string | undefined | null, startValue: string | undefined | null): boolean {
    let result = false;
    if (srcStr && startValue) {
      result = srcStr.startsWith(startValue);
    }
    return result;
  }

  /**
   * 字符串是否是以endValue结束
   * @param srcStr
   * @param endValue
   */
  export function endsWith(srcStr: string | undefined | null, endValue: string | undefined | null): boolean {
    let result = false;
    if (srcStr && endValue) {
      result = srcStr.endsWith(endValue)
    }
    return result;
  }

  /**
   * 字符串的替换
   * @param srcStr
   * @param searchValue
   * @param replaceValue
   */
  export function replace(srcStr: string, searchValue: string,
    replaceValue: string | ((searchSubValue: string, pos: number) => string),
    isUseRegex: boolean = true): string {
    if (srcStr && searchValue) {
      const regex = isUseRegex ? new RegExp(searchValue, 'g') : searchValue;
      if (typeof replaceValue === "string") {
        return srcStr.replaceAll(regex, replaceValue);
      } else {
        return srcStr.replaceAll(regex, (subStr: string, pos: number) => {
          return replaceValue(subStr, pos);
        })
      }
    }
    return srcStr;
  }

  /**
   * 原字符串中srcStr是否包含containsValue
   * @param srcStr
   * @param containsValue
   */
  export function contains(srcStr: string | undefined | null, containsValue: string | undefined | null): boolean {
    let result = false;
    if (srcStr && containsValue) {
      result = srcStr.includes(containsValue)
    }
    return result;
  }

  /**
   * 字符串分割
   * @param srcStr
   * @param splitFlag
   * @returns
   */
  export function split(srcStr: string | undefined | null, splitFlag: string | undefined | null,
    isUseRegex: boolean = false): Array<string> {
    let result: Array<string> = new Array<string>();
    if (srcStr && splitFlag) {
      const regex = isUseRegex ? new RegExp(splitFlag, 'g') : splitFlag;
      result = srcStr.split(regex);
    }
    return result;
  }

  /**
   *  数组转字符串
   * @param temp
   * @param separator
   * @returns
   */
  export function join(temp: Array<string> | undefined | null, separator: string = ""): string {
    let result: string = "";
    if (temp) {
      result = temp.join(separator);
    }
    return result;
  }

  /**
   * 去除HTML标签及样式,获取其中的纯文本内容
   * @param html
   * @returns
   */
  export function removeHtmlTags(html: string): string {
    let result: string = "";
    if (html) {
      //匹配HTML标签的正则表达式
      let regex = /<[^>]+>/g;
      //使用replace方法将HTML标签替换为空字符串
      result = html.replace(regex, "");
    }
    //返回去除HTML标签后的纯文本内容
    return result;
  }

  /**
   * 是否本地文件路径
   * @param url
   * @returns
   */
  export function isFileUrl(url: string): boolean {
    return startsWith(url, "file://");
  }

  /**
   * 是否服务器路径
   * @param url
   * @returns
   */
  export function isHttpUrl(url: string): boolean {
    return (startsWith(url, "http://") || startsWith(url, "https://"));
  }
}