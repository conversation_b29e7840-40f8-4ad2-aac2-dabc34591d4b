/**
 * 文件管理工具类
 */
import { TKContextHelper } from '../system/TKContextHelper';
import { common } from '@kit.AbilityKit';
import { TKLog } from '../logger/TKLog';
import fs, { ListFileOptions } from '@ohos.file.fs';
import { TKStringHelper } from '../string/TKStringHelper';
import { TKDataHelper } from '../data/TKDataHelper';
import { buffer } from '@kit.ArkTS';
import { TKSystemHelper } from '../system/TKSystemHelper';
import { TKXMLHelper } from './xml/TKXMLHelper';
import { TKMapHelper } from '../map/TKMapHelper';
import { TKJson } from '../data/TKObjectHelper';

export namespace TKFileHelper {

  /**
   * 获取上下文
   * @param component
   * @returns
   */
  export function getUIAbilityContext(component?: Object): common.UIAbilityContext {
    return TKContextHelper.getCurrentContext(component) as common.UIAbilityContext;
  }

  /**
   * 构建目录路径
   * @param dirPath
   * @returns
   */
  function buildDirPath(dirPath: string): string {
    if (dirPath.endsWith("/")) {
      dirPath = dirPath.substring(0, dirPath.length - 1);
    }
    return dirPath;
  }

  /**
   * 缓存根目录
   */
  export function cacheDir(): string {
    return buildDirPath(getUIAbilityContext().cacheDir);
  }

  /**
   * 临时根目录
   * @returns
   */
  export function tempDir(): string {
    return buildDirPath(getUIAbilityContext().tempDir);
  }

  /**
   * 文件根目录
   * @returns
   */
  export function fileDir(): string {
    return buildDirPath(getUIAbilityContext().filesDir);
  }

  /**
   * 数据库根目录
   * @returns
   */
  export function databaseDir(): string {
    return buildDirPath(getUIAbilityContext().databaseDir);
  }

  /**
   * 资源目录
   * @returns
   */
  export function resourceDir(): string {
    return buildDirPath(getUIAbilityContext().resourceDir);
  }

  /**
   * 格式化文件路径
   * @param filePath
   * @returns
   */
  export function formatFilePath(filePath: string) {
    return TKStringHelper.replace(filePath, `file://${TKSystemHelper.getAppIdentifier()}`, "");
  }

  /**
   *  创建文件
   *
   * @param filePath 文件路径
   *
   * @return 成功标志
   */
  export function createFile(filePath: string): boolean {
    let result: boolean = true;
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        if (!fs.accessSync(filePath)) {
          let index: number = filePath.lastIndexOf("/");
          let fileDir = filePath.substring(0, index);
          if (!fs.accessSync(fileDir)) {
            fs.mkdirSync(fileDir, true);
          }
          let file = fs.openSync(filePath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
          fs.closeSync(file);
        }
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]创建文件异常，code: ${error.code}， message: ${error.message}`);
      result = false;
    }
    return result;
  }

  /**
   *  删除文件或者目录
   *
   * @param filePath 文件路径
   *
   * @return 成功标志
   */
  export function deleteFile(filePath: string): boolean {
    let result: boolean = true;
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        if (fs.accessSync(filePath)) {
          fs.rmdirSync(filePath);
        }
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]删除文件或者目录异常，code: ${error.code}， message: ${error.message}`);
      result = false;
    }
    return result;
  }

  /**
   *  创建文件目录
   *
   * @param filePath       文件路径
   *
   * @return 成功标志
   */
  export function createFileDir(filePath: string): boolean {
    let result: boolean = false;
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        if (!fs.accessSync(filePath)) {
          fs.mkdirSync(filePath, true);
        }
        result = true;
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]创建文件目录异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  移动文件
   *
   * @param srcPath  原始文件
   * @param destPath 目标文件
   *
   * @return 成功标志
   */
  export function moveFile(srcPath: string, destPath: string): boolean {
    let result: boolean = false;
    try {
      if (TKStringHelper.isNotBlank(srcPath) && TKStringHelper.isNotBlank(destPath)) {
        srcPath = formatFilePath(srcPath);
        destPath = formatFilePath(destPath);
        if (fs.accessSync(srcPath)) {
          let index: number = destPath.lastIndexOf("/");
          let destDir = destPath.substring(0, index);
          if (!fs.accessSync(destDir)) {
            fs.mkdirSync(destDir, true);
          }
          if (fs.statSync(srcPath).isDirectory()) {
            fs.moveDirSync(srcPath, destDir, 0);
          } else {
            fs.moveFileSync(srcPath, destPath, 0);
          }
          result = true;
        }
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]移动文件异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  复制文件
   *
   * @param srcPath  原始文件
   * @param destPath 目标文件
   *
   * @return 成功标志
   */
  export function copyFile(srcPath: string, destPath: string): boolean {
    let result: boolean = false;
    try {
      if (TKStringHelper.isNotBlank(srcPath) && TKStringHelper.isNotBlank(destPath)) {
        srcPath = formatFilePath(srcPath);
        destPath = formatFilePath(destPath);
        let index: number = destPath.lastIndexOf("/");
        let destDir = destPath.substring(0, index);
        if (!fs.accessSync(destDir)) {
          fs.mkdirSync(destDir, true);
        }
        if (TKStringHelper.startsWith(srcPath, "/") || TKStringHelper.startsWith(srcPath, "file://")) {
          if (fs.accessSync(srcPath) || TKStringHelper.startsWith(srcPath, "file://")) {
            if (fs.statSync(srcPath).isDirectory()) {
              fs.copyDirSync(srcPath, destPath, 0);
            } else {
              fs.copyFileSync(srcPath, destPath, 0);
            }
            result = true;
          }
        } else {
          let isFile: boolean = true;
          try {
            let srcFile = TKContextHelper.getCurHspModuleContextSync().resourceManager.getRawFdSync(srcPath);
            isFile = fs.statSync(srcFile.fd).isFile();
          } catch (e) {
            isFile = false;
          }
          if (isFile) {
            let srcFileData: Uint8Array =
              TKContextHelper.getCurHspModuleContextSync().resourceManager.getRawFileContentSync(srcPath);
            writeFile(srcFileData, destPath);
          } else {
            let srcSubFileList =
              TKContextHelper.getCurHspModuleContextSync().resourceManager.getRawFileListSync(`rawfile/${srcPath}`);
            if (srcSubFileList && srcSubFileList.length > 0) {
              for (let srcSubFile of srcSubFileList) {
                result = copyFile(`${srcPath}/${srcSubFile}`, `${destPath}/${srcSubFile}`);
                if (!result) {
                  return result;
                }
              }
            }
          }
          result = true;
        }
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]复制文件异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  写数据到文件中
   *
   * @param content  内容
   * @param filePath 路径
   * @param isExtend 是否用追加模式
   *
   * @return 成功标志
   */
  export function writeFile(content: string | Uint8Array, filePath: string, isExtend: boolean = false): boolean {
    let result: boolean = false;
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        let data: Uint8Array =
          content instanceof Uint8Array ? content as Uint8Array : TKDataHelper.stringToUint8Array(content as string);
        let index: number = filePath.lastIndexOf("/");
        let fileDir = filePath.substring(0, index);
        if (!fs.accessSync(fileDir)) {
          fs.mkdirSync(fileDir, true);
        }
        let file = fs.openSync(filePath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
        if (!isExtend) {
          fs.truncateSync(file.fd); //清空文件
          fs.lseek(file.fd, 0, fs.WhenceType.SEEK_SET); //移动文件偏移量为0
        } else {
          fs.lseek(file.fd, 0, fs.WhenceType.SEEK_END);
        }
        if (data && data.length > 0) {
          fs.writeSync(file.fd, buffer.from(data).buffer);
        }
        fs.closeSync(file);
        result = true;
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]写入文件异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * 获取系统配置文件路径
   * @param filePath
   * @returns
   */
  function getSystemConfigPath(filePath: string): string {
    if (TKStringHelper.startsWith(filePath, "@bundle:")) {
      return filePath;
    }
    if (TKStringHelper.startsWith(filePath, "/")) {
      return filePath.substring(1, filePath.length);
    }
    if (TKStringHelper.startsWith(filePath, "thinkive/config/")) {
      return filePath;
    }
    let lastFilePath = `thinkive/config/${TKSystemHelper.getEnvironment()}/${filePath}`;
    if (readFile(lastFilePath).length <= 0) {
      lastFilePath = `thinkive/config/default/${filePath}`;
    }
    return lastFilePath;
  }

  /**
   *  读取系统配置文件的内容,二层数据结构，例如：
   *  <system>
   *      <catalog name="system" description="系统配置">
   *          <item name="isDevelop" value="1" description="是否开发环境(0:否,1:是)，默认是0"/>
   *      </catalog>
   *  </system>
   *
   * @param filePath 文件路径
   *
   * @return Map内容，例如 {"system.isDevelop":"1"}
   */
  export function readSystemConfig(filePath: string, context?: Context): Map<string, string> {
    filePath = getSystemConfigPath(filePath);
    let sysConfig: Map<string, string> = new Map<string, string>();
    let systemConfig: Map<string, Object> = TKXMLHelper.readConfigXml(filePath, context);
    if (systemConfig && systemConfig.size > 0) {
      let catalogConfigs: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(systemConfig, "children");
      if (catalogConfigs && catalogConfigs.length > 0) {
        catalogConfigs.forEach((catalogConfig, catalogConfigIndex) => {
          let catalogName: string = TKMapHelper.getString(catalogConfig, "name")
          let itemConfigs: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(catalogConfig, "children");
          if (itemConfigs && itemConfigs.length > 0) {
            itemConfigs.forEach((itemConfig, itemConfigIndex) => {
              let itemName: string = TKMapHelper.getString(itemConfig, "name");
              let itemValue: string = TKMapHelper.getString(itemConfig, "value");
              let itemKey: string = `${catalogName}.${itemName}`;
              sysConfig.set(itemKey, itemValue);
            });
          }
        });
      }
    } else {
      TKLog.error(`[TKFileHelper]读取系统配置文件${filePath}不存在，请检查你的配置文件！`);
    }
    return sysConfig;
  }

  /**
   *  读取系统通用文件的内容
   *
   * @param filePath 文件路径
   *
   * @return 内容
   */
  export function readSystemFileData(filePath: string): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = getSystemConfigPath(filePath);
        if (TKStringHelper.startsWith(filePath, "@bundle:")) {
          let resPaths: Array<string> = TKStringHelper.split(TKStringHelper.replace(filePath, "@bundle:", ""), ":");
          let resBundleModule: string = resPaths[0];
          let resPath: string = resPaths[1];
          result =
            TKContextHelper.getCurHspModuleContextSync(resBundleModule).resourceManager.getRawFileContentSync(resPath);
        } else {
          result = TKContextHelper.getCurHspModuleContextSync().resourceManager.getRawFileContentSync(filePath);
        }
      }
    } catch (error) {
      TKLog.warn(`[TKFileHelper]读取系统文件[${filePath}]异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  读取系统通用文件的内容
   *
   * @param filePath 文件路径
   *
   * @return 内容
   */
  export function readSystemFileContent(filePath: string): string {
    let data: Uint8Array = readSystemFileData(filePath);
    return TKDataHelper.uint8ArrayToString(data);
  }

  /**
   *  读取系统通用文件的内容
   *
   * @param filePath 文件路径
   *
   * @return 内容
   */
  export function readSystemFileObject(filePath: string): TKJson {
    let content: string = readSystemFileContent(filePath);
    return JSON.parse(content);
  }

  /**
   *  读取系统通用XML文件的内容,一层目录结构，例如：
   *   <xxxxx>
   *      <item name="50000" value="TKPlugin50000" description="获取应用配置"></item>
   *   </xxxxx>
   *
   * @param filePath 文件路径
   *
   * @return Map内容，例如 {"50000":"TKPlugin50000"}
   */
  export function readSystemFileXml(filePath: string, context?: Context): Map<string, string> {
    filePath = getSystemConfigPath(filePath);
    let sysXmlConfig: Map<string, string> = new Map<string, string>();
    let systemConfig: Map<string, Object> = TKXMLHelper.readConfigXml(filePath, context);
    if (systemConfig && systemConfig.size > 0) {
      let itemConfigs: Array<Map<string, Object>> | undefined = TKMapHelper.getObject(systemConfig, "children");
      if (itemConfigs && itemConfigs.length > 0) {
        itemConfigs.forEach((itemConfig, itemConfigIndex) => {
          let itemName: string = TKMapHelper.getString(itemConfig, "name");
          let itemValue: string = TKMapHelper.getString(itemConfig, "value");
          sysXmlConfig.set(itemName, itemValue);
        });
      }
    } else {
      TKLog.error(`[TKFileHelper]读取系统配置文件${filePath}不存在，请检查你的配置文件！`);
    }
    return sysXmlConfig;
  }

  /**
   *  读取文件的内容
   *
   * @param filePath 文件路径
   *
   * @return 内容
   */
  export function readFile(filePath: string, moduleContext?: Context): Uint8Array {
    let result: Uint8Array = new Uint8Array();
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        if (TKStringHelper.startsWith(filePath, "/") || TKStringHelper.startsWith(filePath, "file://")) {
          if (fs.accessSync(filePath) || TKStringHelper.startsWith(filePath, "file://")) {
            let file = fs.openSync(filePath, fs.OpenMode.READ_ONLY);
            const fileStat: fs.Stat = fs.statSync(file.fd);
            const fileSize: number = fileStat.size;
            let fileData: Uint8Array = new Uint8Array(fileSize);
            let buf: ArrayBuffer = new ArrayBuffer(1024 * 1024);
            let offsetRead = 0;
            let bytesRead = 0;
            while ((bytesRead = fs.readSync(file.fd, buf)) > 0) {
              let readData: Uint8Array = new Uint8Array(buf.slice(0, bytesRead));
              fileData.set(readData, offsetRead);
              offsetRead += bytesRead;
            }
            fs.closeSync(file);
            result = fileData;
          }
        } else {
          if (TKStringHelper.startsWith(filePath, "@bundle:")) {
            let resPaths: Array<string> = TKStringHelper.split(TKStringHelper.replace(filePath, "@bundle:", ""), ":");
            let resBundleModule: string = resPaths[0];
            let resPath: string = resPaths[1];
            moduleContext = moduleContext ?? TKContextHelper.getCurHspModuleContextSync(resBundleModule);
            result = moduleContext.resourceManager.getRawFileContentSync(resPath);
          } else {
            result = TKContextHelper.getCurHspModuleContextSync().resourceManager.getRawFileContentSync(filePath);
          }
        }
      }
    } catch (error) {
      TKLog.warn(`[TKFileHelper]读取文件[${filePath}]异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  判断文件是否存在
   *
   * @param filePath 文件路径
   *
   * @return 存在标识
   */
  export function isFileExists(filePath: string): boolean {
    let result: boolean = false;
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        result = fs.accessSync(filePath);
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]判断文件是否存在异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  获取目录下面的子目录
   *
   * @param filePath 目录路径
   *
   * @return 子文件数组
   */
  export function listFiles(filePath: string, options?: ListFileOptions): Array<string> {
    let result: Array<string> = new Array<string>();
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        if (fs.accessSync(filePath)) {
          result = fs.listFileSync(filePath, options);
        }
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]获取目录下面的子目录异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   *  判断是否是目录
   *
   * @param filePath 文件路径
   *
   * @return 是否是目录
   */
  export function isDirectory(filePath: string): boolean {
    let result: boolean = false;
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        if (fs.accessSync(filePath)) {
          result = fs.statSync(filePath).isDirectory();
        }
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]判断是否是目录异常，code: ${error.code}， message: ${error.message}`);
    }
    return result;
  }

  /**
   * 获取文件信息
   * @param filePath
   * @returns
   */
  export function fileInfo(filePath: string): fs.Stat | undefined {
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        if (fs.accessSync(filePath) || TKStringHelper.startsWith(filePath, "file://")) {
          let file = fs.openSync(filePath, fs.OpenMode.READ_ONLY);
          return fs.statSync(file.fd);
        }
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]获取文件信息异常，code: ${error.code}， message: ${error.message}`);
    }
    return undefined;
  }

  /**
   * 获取文件大小
   * @param filePath
   * @returns
   */
  export function fileSize(filePath: string): number {
    try {
      if (TKStringHelper.isNotBlank(filePath)) {
        filePath = formatFilePath(filePath);
        if (fs.accessSync(filePath) || TKStringHelper.startsWith(filePath, "file://")) {
          let file = fs.openSync(filePath, fs.OpenMode.READ_ONLY);
          return fs.statSync(file.fd).size;
        }
      }
    } catch (error) {
      TKLog.error(`[TKFileHelper]获取文件大小异常，code: ${error.code}， message: ${error.message}`);
    }
    return -1;
  }

  /**
   * 打开文件
   * @param filePath
   * @returns
   */
  export function openFile(filePath: string): fs.File {
    filePath = formatFilePath(filePath);
    return fs.openSync(filePath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
  }

  /**
   * 关闭文件
   * @param filePath
   * @returns
   */
  export function closeFile(file: number | fs.File) {
    return fs.closeSync(file);
  }

  /**
   * 获取文件类型
   * @param filePath
   * @returns
   */
  export function fileType(filePath: string): string {
    filePath = formatFilePath(filePath);
    return filePath.split(".").pop() as string;
  }

}