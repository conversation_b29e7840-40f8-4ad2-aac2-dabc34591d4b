import { buffer, Stack, xml } from '@kit.ArkTS';
import { TKLog } from '../../logger/TKLog';
import { TKStringHelper } from '../../string/TKStringHelper';
import { TKContextHelper } from '../../system/TKContextHelper';
import { TKFileHelper } from '../TKFileHelper';

/**
 * XML代码解析工具
 */
export namespace TKXMLHelper {

  /**
   * 读取系统配置文件
   * @param path  文件路径
   * @param context  上下文
   */
  export function readConfigXml(path: string, context?: Context): Map<string, Object> {
    try {
      if (!context) {
        context = TKContextHelper.getCurHspModuleContextSync();
      }
      if (!context) {
        return new  Map<string, Object>();
      }
      let xmlData: Uint8Array = TKFileHelper.readFile(path);
      if (!xmlData || xmlData.length == 0) {
        return new  Map<string, Object>();
      }
      let xmlPullParser = new xml.XmlPullParser(buffer.from(xmlData).buffer, 'UTF-8');
      let rootXmlItem: Map<string, Object> | undefined = undefined;
      let curXmlItem: Map<string, Object> | undefined = undefined;
      let parentXmlItem: Map<string, Object> | undefined = undefined;
      let parseDepth: number = 0;
      let parseStack = new Stack<Map<string, Object>>();
      let options: xml.ParseOptions = {
        supportDoctype: true,
        ignoreNameSpace: true,
        tagValueCallbackFunction: (name, value) => {
          return true;
        },
        attributeValueCallbackFunction: (name, value) => {
          value = value ? TKStringHelper.replace(value.toString(), "amp;", "&") : value;
          curXmlItem?.set(name, value);
          return true;
        },
        tokenValueCallbackFunction: (eventType, value) => {
          let name = value.getName();
          let depth = value.getDepth();
          switch (eventType) {
            case 2: {
              // START_TAG
              if (!rootXmlItem) {
                curXmlItem = new Map<string, Object>();
                curXmlItem.set("$tag", name);
                rootXmlItem = curXmlItem;
                parseDepth = depth;
              } else {
                if (depth > parseDepth) {
                  parseStack.push(curXmlItem);
                } else if (depth < parseDepth) {
                  let popNum: number = parseDepth - depth;
                  for (let i = 0; i < popNum; i++) {
                    parseStack.pop();
                  }
                }
                parentXmlItem = parseStack.peek();
                let xmlChildItems = parentXmlItem.get("children") as Array<Map<string, Object>>;
                if (!xmlChildItems) {
                  xmlChildItems = new Array<Map<string, Object>>();
                  parentXmlItem.set("children", xmlChildItems);
                }
                curXmlItem = new Map<string, Object>();
                curXmlItem.set("$tag", name);
                xmlChildItems.push(curXmlItem);
                parseDepth = depth;
              }
              break;
            }
            case 3: {
              // END_TAG
              break;
            }
          }
          return true;
        }
      }
      xmlPullParser.parse(options);
      return rootXmlItem ? rootXmlItem : new Map<string, Object>();
    } catch (error) {
      TKLog.error(`[TKXMLHelper]读取系统配置文件异常，code: ${error.code}， message: ${error.message}`);
      return new  Map<string, Object>();
    }
  }
}