/**
 * 日期帮组类
 */
import { TKLog } from '../logger/TKLog';

export namespace TKDateHelper {
  /**
   * 一天的毫秒数
   */
  const MSEL_OF_DAY: number = (60 * 1000 * 60 * 24);

  function padZero(num: number, length: number = 2): string {
    let result: string = num.toString();
    let temp: number = length - num.toString().length;
    if (temp > 0) {
      for (let i = 0; i < temp; i++) {
        result = "0" + result;
      }
    }
    return result;
  }

  /**
   * 数秒格式化
   * @param seconds 秒
   * @returns "00:01" >> "24:60:60"
   */
  export function formatSeconds(seconds: number): string {
    try {
      let date: Date = new Date(1901, 1, 1, 0, 0, seconds);
      let format: string = new Intl.DateTimeFormat("en-GB", { timeStyle: 'medium', hourCycle: "h24" }).format(date);
      return seconds < 3600 ? format.substring(3, format.length) : format;
    } catch (error) {
      TKLog.error(`[TKDateHelper]数秒格式化异常，code: ${error.code}， message: ${error.message}`);
      return "";
    }
  }

  /**
   * 输出 YYY-MM-DD HH:mm:ss 格式时间
   * @param milliseconds   要转换的毫秒数
   * @returns
   */
  export function formatDateTime(milliseconds?: number): string {
    const date = milliseconds ? new Date(milliseconds) : new Date();
    return formatDate(date, "YYYY-MM-DD HH:mm:ss");
  }

  /**
   *  把Date转换日期格式字串
   *
   * @param date   要转换的日期
   * @param format 格式串 例如：YYYY-MM-DD HH:mm:ss:SSS
   *
   * @return 格式化以后的日期字符串
   */
  export function formatDate(date: Date, format: string = "YYYY-MM-DD"): string {
    return format.replace(/YYYY|MM|DD|HH|hh|mm|ss|SSS/g, (match) => {
      switch (match) {
        case 'YYYY':
          return date.getFullYear().toString();
        case 'MM':
          return padZero(date.getMonth() + 1);
        case 'DD':
          return padZero(date.getDate());
        case 'HH':
          return padZero(date.getHours());
        case 'hh':
          return padZero(date.getHours() % 12 === 0 ? 12 : date.getHours() % 12);
        case 'mm':
          return padZero(date.getMinutes(), 2);
        case 'ss':
          return padZero(date.getSeconds(), 2);
        case 'SSS':
          return padZero(date.getMilliseconds(), 3);
        default:
          return match.substring(0, 2);
      }
    });
  }

  /**
   *  转换字符串为Date,若转换成功，则返回相应的Date对象 若转换失败，则返回nil。
   *
   * @param str    日期字符串
   * @return 转换后的日期对象
   */
  export function parseDateStr(dateStr: string): Date {
    try {
      const regex =
        /^(?:(\d{4})-(\d{2})-(\d{2})|(?:(\d{4}))\/(\d{2})\/(\d{2})|(\d{4})(\d{2})(\d{2})|(\d{4})年(\d{2})月(\d{2})日)\s?(?:(\d{2}):?(\d{2}):?(\d{2})(?:\.?(\d{3}))?)?$/;
      let match: Array<string> | null = dateStr.match(regex);
      if (match) {
        match = match.filter(value => value !== undefined);
        let year: string = match.length > 1 ? match[1] : "";
        let month: string = match.length > 2 ? match[2] : "";
        let day: string = match.length > 3 ? match[3] : "";
        let hour: string = match.length > 4 ? match[4] : "00";
        let minute: string = match.length > 5 ? match[5] : "00";
        let second: string = match.length > 6 ? match[6] : "00";
        let msecond: string = match.length > 7 ? match[7] : "000";
        // 注意月份需要减去 1，因为 JavaScript 中的月份是从 0 开始的
        return new Date(Number(year), Number(month) - 1, Number(day), Number(hour), Number(minute), Number(second),
          Number(msecond));
      }
    } catch (error) {
      TKLog.error(`[TKDateHelper]转换字符串为Date异常，code: ${error.code}， message: ${error.message}`);
    }
    return new Date(dateStr);
  }

  /**
   *  获得特定日期的星期字串
   *
   * @param date 日期
   *
   * @return 星期字符串
   */
  export function getWeekStr(date: Date): string {
    let weekStr: string = "";
    let week: number = date.getDay();
    switch (week) {
      case 0:
        weekStr = "星期日";
        break;
      case 1:
        weekStr = "星期一";
        break;
      case 2:
        weekStr = "星期二";
        break;
      case 3:
        weekStr = "星期三";
        break;
      case 4:
        weekStr = "星期四";
        break;
      case 5:
        weekStr = "星期五";
        break;
      case 6:
        weekStr = "星期六";
    }
    return weekStr;
  }

  /**
   *  获得特定日期的星期数，7是星期天，1是星期一，如此类推
   *
   * @param date 日期
   *
   * @return 星期数
   */
  export function getWeek(date: Date): number {
    let week: number = date.getDay();
    if (week === 0) {
      week = 7;
    }
    return week;
  }

  /**
   *  获取指定日期的年份
   *
   * @param date 日期
   *
   * @return 年份
   */
  export function getYear(date: Date): number {
    return date.getFullYear();
  }

  /**
   *  获取指定日期的月份
   *
   * @param date 日期
   *
   * @return 月份
   */
  export function getMonth(date: Date): number {
    return date.getMonth() + 1;
  }

  /**
   *  获取指定日期的天数
   *
   * @param date 日期
   *
   * @return 天数
   */
  export function getDay(date: Date): number {
    return date.getDate();
  }

  /**
   *  提取当前时间的星期数，7是星期天，1是星期一，如此类推
   *
   * @return 星期数
   */
  export function getCurrentWeek(): number {
    return getWeek(new Date());
  }

  /**
   *  提取当前的星期数的中文字串，星期一...星期日
   *
   * @return 星期数
   */
  export function getCurrentWeekStr(): string {
    return getWeekStr(new Date());
  }

  /**
   *  获取当前年份
   *
   * @return 年份
   */
  export function getCurrentYear(): number {
    return getYear(new Date());
  }

  /**
   *  获取当前月份
   *
   * @return 月份
   */
  export function getCurrentMonth(): number {
    return getMonth(new Date());
  }

  /**
   *  获取当前的天数
   *
   * @return 天数
   */
  export function getCurrentDay(): number {
    return getDay(new Date());
  }

  /**
   *  获取两个日期差的秒数
   *
   * @param date0 日期1
   * @param date1 日期2
   *
   * @return 日期差的秒数 date0 - date1
   */
  export function getTwoDateDiffSecs(date0: Date, date1: Date): number {
    return Math.floor((date0.getTime() - date1.getTime()) / 1000);
  }

  /**
   *  获取两个日期差的天数
   *
   * @param date0 日期1 yyyy-MM-dd
   * @param date1 日期2 yyyy-MM-dd
   *
   * @return 日期差的天数 date0 - date1
   */
  export function getTwoDateDiffDays(date0: Date, date1: Date): number {
    date0 = parseDateStr(formatDate(date0));
    date1 = parseDateStr(formatDate(date1));
    if (date0 && date1) {
      return Math.ceil((date0.getTime() - date1.getTime()) / MSEL_OF_DAY);
    }
    return 0;
  }

  /**
   *  日期减去天数，得到新的日期
   *
   * @param date 日期
   * @param days 天数
   *
   * @return 新的日期
   */
  export function getDateDiffDate(date: Date | string, days: number): Date {
    date = date instanceof Date ? date as Date : parseDateStr(date as string) as Date;
    if (date) {
      let time: number = (date as Date).getTime();
      time = time - (MSEL_OF_DAY * days);
      return new Date(time);
    }
    return date;
  }

  /**
   *  日期减去天数，得到新的日期
   *
   * @param date 日期
   * @param days 天数
   *
   * @return 新的日期
   */
  export function getDateStrDiffDate(date: Date | string, days: number): string {
    date = getDateDiffDate(date, days);
    return formatDate(date as Date);
  }
}