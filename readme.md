# 版本更新说明
```typescript
V1.3.0（框架启动和通知优化版本）
1：框架启动类优化: TKAppEngine=>public start(tkAppEngineStartOption: TKAppEngineStartOption)
  支持设置全局HSP模块名称,兼容框架被打包成HSP的场景，升级后不兼容旧版本，请注意核对
2：优化框架内部定义的通知，包含NOTE_OPEN_MODULE（打开模块通知），NOTE_SHOW_HIDE_TAB（Tab隐藏显示通知），NOTE_PLUGIN_EVENT（其他默认插件通知）
  通知的obj改成当前Web的控制器jSProxyController，userInfo改成携带的业务参数，升级后不兼容旧版本，请注意核对
3：插件调用当前控制器的onPluginEvent入参里新增当前控制器的对象jSProxyController，向下兼容
4：TKWebPage属性中onBackClick，onCloseClick，onRightBtnClick，onOpenModule 均新增当前控制器的对象jSProxyController，向下兼容
5：新增50200,50202插件
6：优化原生和Webview的双向cookie同步逻辑
7：消息引擎兼容Ios打开模块的逻辑
8：网络通信层缓存对象逻辑优化  
9：支持动态导航栏路由统一注册管理  
10: 修改原生版本覆盖安装升级时候，本地www缓存目录没有清理的bug
11：TKObjectHelper.toObject方法优化，增加扩展参数，isOnlyOverwrite 代表是否仅仅进行覆盖逻辑，如果是true那就意味着target的key只做覆盖，不做合并 
12: 优化框架的工具类，进行常规异常的统一捕获处理，防止崩溃  
13：优化50115对title传空格时候场景的兼容处理
14: 网络通信文件上传接口，针对文件传路径时候，进行逻辑优化，自动走底层获取文件名称和类型
15：提交网络诊断功能，页面名称为TKComNetTestPage
16: TKCommonService 新增网络请求Promise格式调用:  public serviceInvokePromise(reqParamVO: TKReqParamVO): Promise<TKResultVO>
17: 新增system配置<item name="isRouterBackExitAppTip" value="1" description="是否路由返回到栈顶的时候进行App退出提示(0:否,1:是)，默认是1"/>
18: 新增获取当前框架版本号的API: TKAppEngine.shareInstance().getVersion()
19: 新增动态导入模块的帮组类TKImportHelper  
20: TKReqParamVO增加属性isKeepOriginalParam，解决访问三方接口需要保持原始的参数格式不做容错的处理，公司标准的默认会把value为json格式的对象转成json字符串
    另外也不会拼接@@_OP等参数,相当于isAutoAddSysComParam=false，同时isURLEncode=false
21：通络通信层修复TKReqParamVO直接设置contentType为TKContentType.JSON格式的请求数据bug 
22：优化TKWebPage对URL变化时候实时进行沉浸式全屏判断，给H5拼接topmargin，bottommargin参数,同时支持webViewPool.isShowLoading配置功能

V1.2.0（折叠屏适配版本）
1：手势密码，安全键盘，打开PDF插件进行折叠屏的相关适配兼容
2：文件帮助类优化，copyFile支持从rawfile目录进行copy，例如：TKFileHelper.copyFile("thinkive/aaa.txt",TKFileHelper.fileDir()+"/thinkive/aaa.txt");
3：支持网络变化监听提示
  <catalog name="networkChange" description="网络切换相关配置">
  <item name="isShowNetChange" value="1" description="是否显示网络连接提醒(0:否,1:是)，默认是1" />
  </catalog>
  4：支持监听状态栏和窗口变化通知
TKWindowListener.NOTE_WINDOW_CHANGE TKWindowListener.NOTE_STATUS_CHANGE
5: 设备信息获取支持设置代理供三方传入
6：TKWeb支持错误返回按钮展示

V1.1.0（路由适配版本）
1：修改路由的跳转方式，兼容router跳转和NavPathStack跳转，影响到TKRouterHelper相关API升级适配，不兼容V1.0.0
2：修改调用插件的参数，改成TKPluginInvokeOption对象入参，方便后续扩展参数，API不兼容V1.0.0
3：优化网络请求对，支持对请求参数对象类型的自动进行序列化为字符串
4：提交支持H5三方支付，待后续验证
5：框架加载弹层支持旋转度的设置
6：修复前后台标识获取的bug
7: 新增50131插件功能
8: 新增50275视频播放插件
9：TKWeb新增onLoadIntercept代理拦截
10：新增50282视频上传插件
11：TKWeb新增加载进度条功能
12：支持适配信安世纪国密SDK
13：增加权限申请拦截器
14: 新增50127,50128,50129,50108插件
15：新增TKAppStartManager启动页功能
16：新增TKDialogHelper showSuccessToast、showInfoToast、showErrorToast的带图标的吐司功能
17：新增TKAnimatedImage GIF图片组件
18：支持自定义安全键盘功能
    @State textInputKeyBoardOption: TKTextInputKeyBoardOption = {
      keyBoardType: "800",
      delegate: {
        textFieldConfirm: () => {},
        textFieldChange: (value:string) => {},
        doOtherChar: (charStr: string) => {},
      }
    } as TKTextInputKeyBoardOption;

   TextInput({
      controller: this.textInputKeyBoardOption.controller,
      text: this.textInputKeyBoardOption.inputValue
   })
  .attributeModifier(this.textInputKeyBoardOption.modifier)
  .customKeyboard(this.textInputKeyBoardOption.isUseSystemKeyBoard ? undefined :
  buildTKTextInputKeyBoard(this.textInputKeyBoardOption), { supportAvoidance: true })
19：日志支持写本地临时文件
  <catalog name="log" description="日志的配置">
      <item name="logLevel" value="debug" description="off|error|warn|info|debug|all"/>
      <item name="logType" value="console|file" description="console:控制台，file:文件，server:服务器，多种日志模式用|分割，如console|file|server" />
  </catalog>
20：优化唯一设备号获取的逻辑，防止变化
21：TKFileHelper 新增读取系统配置的API:readSystemConfig
22: 新增 TKImageCacheManager 图片缓存类
    @State loadImage: TKImageCacheOption = {
      loadSrc: 'http://**************:8888/work-manage-view/login-figure-scatter.png'
    }
    TKImageCacheManager.shareInstance().loadImage(this.loadImage);
    Image(this.loadImage.image).width(100).height(100)
  
V1.0.0（初始化版本）
初始化代码
```

[toc]

# 1. 框架的接入

- 本地har包依赖
```typescript
oh-package.json5 文件中添加，如下：
"dependencies": {
  "@thinkive/tk-harmony-base": "file:../libs/thinkive-harmony-base-xxxx.har"
}
```

- 私服方式依赖
1. 安装私服仓库

```typescript 
- ohpm config set registry http://*************:9090/repos/ohpm
```

2. 添加依赖

```typescript
oh-package.json5 文件中添加，如下：
"dependencies": {
  "@thinkive/thinkive-harmony-base": "^x.x.x"
}
```

# 2. 框架初始化
- **异步初始化**
> 主要是初始化解析本地配置文件

```typescript
  TKAppEngine.shareInstance().start(this.context, () => {
    //框架初始化完成
  });
```

# 3. 配置文件说明
> resources/rawfile/thinkive 目录中,区分环境配置 dev prod test uat

## 3.1 获取配置信息

```typescript
TKSystemHelper.getConfig("networkRequest.isRequestURLRestFull")
```

# 4. 发送网络请求

- 请求示例

```typescript
    private commonService: TKCommonService = new TKCommonService();

    let reqParamVO: TKReqParamVO = this.commonService.createReqParamVO();
    reqParamVO.protocol = TKDaoType.Socket;
    reqParamVO.url = "HQ_URL_HTTP";
    reqParamVO.quoteFunctionMode = TKQuoteFunctionMode.BEAN;
    reqParamVO.isFilterRepeatRequest = false;
    reqParamVO.reqParam = {
        "funcno": "26000",
        "stock_list": "SZ:000001",
        "field": "22:24:2:10:11:9:12:14:6:23:21:3:1:32"
    };
    this.commonService.serviceInvoke(reqParamVO, (resultVO: TKResultVO) => {
        TKLog.info(TKObjectHelper.toJsonStr(resultVO));
    });

    TKReqParamVO 属性如下:
      /**
       *  流水号
       */
      public flowNo: string = "";
      /**
       *  请求模块标示
       */
      public reqModule: string = "";
      /**
       *  是否过滤重复请求，进行请求拦截
       */
      public isFilterRepeatRequest: boolean = true;
      /**
       *  重复请求，进行请求拦截时间，单位毫秒
       */
      public filterRepeatRequestTimeOut: number = 0;
      /**
       *  是否自动添加系统公共参数
       */
      public isAutoAddSysComParam: boolean = true;
      /**
       *  是否全局请求，全局请求不可以取消掉,默认是NO
       */
      public isGlobRequest: boolean = false;
      /**
       *  Http的请求的头
       */
      public headerFieldDic: Record<string, Object | undefined> = {};
      /**
       *  请求对象
       */
      public reqParam: Record<string, Object | undefined> = {};
      /**
       *  URL地址
       */
      public url: string = "";
      /**
       *  是否post请求
       */
      public isPost: boolean = true;
      /**
       *
       *  Http请求方法
       */
      private _httpMethod: string = "";
      /**
       * <AUTHOR> 2017-01-19 16:01:54
       *
       *  Http请求ContentType
       */
      public contentType: TKContentType = TKContentType.NONE;
      /**
       *  请求超时时间，单位秒
       */
      public timeOut: number = 60;
      /**
       *  请求的协议
       */
      public protocol: TKDaoType = TKDaoType.Http;
      /**
       *  调用开始时间
       */
      public beginTime: number = 0;
      /**
       *  是否显示缓冲效果（转菊花）
       */
      public isShowWait: boolean = false;
      /**
       *  缓冲效果的文字
       */
      public waitTip: string = "加载中";
      /**
       *  是否返回list数据
       */
      public isReturnList: boolean = true;
      /**
       *  请求组号,如果是javascript请求，传对应浏览器对象的名称
       */
      public group: string = "";
      /**
       *  扩展字段对象，目前javascript协议的时候传的是WebView对象，其他后面在定义
       */
      public userInfo: Object | undefined = undefined;
      /**
       *  是否上传文件
       */
      private _isUpload: boolean = false;
      /**
       *  上传代理
       */
      public uploadBlock: TKUploadBlock | undefined = undefined;
      /**
       *  是否缓存
       */
      public isCache: boolean = false;
      /**
       *  缓存类型
       */
      public cacheType: TKCacheType = TKCacheType.Mem;
      /**
       *
       *  缓存时间，单位是秒
       */
      public cacheTime: number = 0;
      /**
       *  自定义dao的实现类名称，一般不需要设置
       */
      public daoName: Object = "";
      /**
       *  是否微服务RestFull接口
       */
      public isRestFull: boolean = false;
      /**
       *  是否对参数进行url编码
       */
      public isURLEncode: boolean = false;
      /**
       *  是否对参数进行签名
       */
      public isURLSign: boolean = false;
      /**
       *  签名key
       */
      public signKey: string = "";
      /**
       *  签名ID
       */
      public signAppId: string = "";
      /**
       *
       *  是否对参数进行加密
       */
      public isURLEncry: boolean = false;
      /**
       *  是否对响应包进行加密
       */
      public isURLResponseEncry: boolean = false;
      /**
       *  对参数进行加密模式
       */
      public encryMode: TKEncryMode = TKEncryMode.Aes;
      /**
       *  加密key
       */
      public encryKey: string = "";
      /**
       渠道ID
       */
      public channelId: string = "";
      /**
       *  请求公司编号
       */
      public companyId: string = "";
      /**
       *  系统编号
       */
      public systemId: string = "";
      /**
       *  socket的服务器ID,即BusConfig.xml里面配置的server的id，如果为空就自动用url代替
       */
      private _busServerId: string = ""
      /**
       *  socket的服务器接口功能号，如果为空，自动用reqParam里面的funcno或者funcNo代替
       */
      private _busFuncNo: string = "";
      /**
       *  是否登录请求
       */
      public isLoginReq: boolean = false;
      /**
       *  返回字符编码集
       */
      public charEncoding: TKCharEncoding = TKCharEncoding.DEFAULT;
      /**
       *  数据类型协议
       */
      public dataType: TKDataType = TKDataType.Normal;
      /**
       *  Dao请求模式,目前针对2进制socket协议有效，支持长短连接
       */
      public daoMode: TKDaoMode = TKDaoMode.Long;
      /**
       *  Https是否验证SSl证书的合法性
       */
      public isValidatesSSLCertificate: boolean = true;
      /**
       * 长连接服务器唯一标示
       */
      public busClientUUID: string = "";
      /**
       *  是否有回调函数
       */
      public isHasCallBackFunc: boolean = false;
      /**
       * 行情接口模式
       */
      public quoteFunctionMode: TKQuoteFunctionMode = TKQuoteFunctionMode.Auto;
```

## 4.1 文件下载

```typescript
let fileDownloadBean: TKDownLoadRequestVO = new TKDownLoadRequestVO()
fileDownloadBean.url = 'http://ggssl.gaotime.com/%7B22188ED5-FB89-45B8-9A6B-E4F3D1D26D90%7D.pdf'
TKDownLoadManager.shareInstance().download(fileDownloadBean, (downLoadResult) => {
  switch (downLoadResult.status) {
    case TKDownLoadStatus.STATUS_RUNNING:
      TKLog.debug('=======DownloadStatus:下载中,状态:'+result.status+',finishedSize:'+result.finishedSize + ',totalSize:' +result.totalSize + ":errorInfo:"+result.errorInfo)
      break
    case TKDownLoadStatus.STATUS_FINISHED:
      TKLog.debug('=======DownloadStatus:下载完成,状态:'+result.status+',finishedSize:'+result.finishedSize + ',totalSize:' +result.totalSize + ":errorInfo:"+result.errorInfo)
      break
    default :
      TKLog.debug('=======DownloadStatus:状态:'+result.status+',finishedSize:'+result.finishedSize + ',totalSize:' +result.totalSize + ":errorInfo:"+result.errorInfo)
      break
  }
})
```

## 4.2 文件断点上传

```typescript
TKUploadManager.shareInstance()对象:
/**
 * 功能描述：根据文件绝对路径上传文件
 *
 * @param uploadUrl:           上传文件服务器地址
 * @param filePath：           文件本地存储路径
 * @param callBackFunc：       上传结果回调函数，入参为result，格式为：{code:错误号,msg:错误信息,data:”文件路径”}
 * @param uploadProgressFunc： 上传进度回调函数，入参为loadInfo,格式为：{bytesTotal:总共大小,bytesLoaded:已上传大小,progress:进度比例,例如0.1}
 */
public uploadFile(uploadUrl: string, filePath: string, callBackFunc: (result: Record<string, Object>) => void,
uploadProgressFunc?: (progress: TKLoadInfoVO) => void);

/**
 * 功能描述：上传二进制文件流
 *
 * uploadUrl:   上传文件服务器地址
 * fileData：    二进制文件数据
 *fileName:    文件名称 test.jpeg
 * callBackFunc：上传结果回调函数，入参为result，格式为：{code:错误号,msg:错误信息,data:”文件路径”}
 * uploadProgressFunc： 上传进度回调函数，入参为loadInfo,格式为：{bytesTotal:总共大小,bytesLoaded:已上传大小,progress:进度比例,例如0.1}
 */
public uploadFileData(uploadUrl: string, fileData: Uint8Array, fileName: string,
callBackFunc: (result: Record<string, Object>) => void, uploadProgressFunc?: (progress: TKLoadInfoVO) => void);
```

## 4.2 获取和监听网络变化状态

```typescript
TKNotificationCenter.defaultCenter.addObserver(this, this.doNetWorkChange, TKNetworkListener.NOTE_NETWORK_CHANGE);
/**
 *  网络变化
 *
 * @param note
 */
private doNetWorkChange(note: TKNotification) {
  this.isNetWorkOK = TKDataHelper.getBoolean(note.obj);
  if (this.isNetWorkOK) {
  }
}
```

## 4.3 获取和监听折叠屏化状态

```typescript
TKNotificationCenter.defaultCenter.addObserver(this, this.doFoldStatusChange, TKFoldDisplayListener.NOTE_FOLD_CHANGE);
/**
 *  折叠屏状态变化
 *
 * @param note
 */
private doFoldStatusChange(note: TKNotification) {
  let foldSatus = note.obj as display.FoldStatus
}

//获取折叠屏状态
TKFoldDisplayListener.shareInstance().getFoldDisplayMode();
```
# 5. 组件使用说明
## 5.1 TKWeb组件
> 基本用法和系统的类似，需要先创建一个 WebviewController 对象
- 加载H5页面示例代码，如下：

```typescript
@Entry
@Component
struct CustomTransition {

  build() {
    Column() {
      TKWeb({
        src: this.webViewUrl,//本地包传www/m/trade/index.html
        attribute:{
          isShowLoading:false,
          isShowError:false
        }
      })
    }
    .width('100%')
  }
}
```

## 5.2 TKTitleBar组件
> 统一思迪标题栏，支持状态栏区域背景色
- **基本属性说明**

```typescript
@Observed
export class TKTitleBarAttribute extends TKAttribute {
  /**
   * 是否是全屏
   */
  isLayoutFullScreen?: boolean = true;
  /**
   * 标题文本
   */
  title?: string = '';
  /**
   * 状态栏显示/隐藏，设置了全屏才会生效，非全屏无法隐藏系统状态栏
   */
  statusBarEnable?: boolean = true;
  /**
   * 返回键按钮显示/隐藏
   */
  backPressEnable?: boolean = true;
  /**
   * 标题栏显示/隐藏
   */
  titleBarEnable?: boolean = true;
  /**
   * 回调左上角返回按钮事件
   */
  onBackClick?: (event: ClickEvent, jSProxyController?: TKJSProxyController) => boolean | void = () => {
    return false
  }
  /**
   * 回调标题栏+状态栏的高度
   */
  onTitleBarHeight?: (height: number) => void = () => {
  }
}

@Observed
export class TKTitleBarStyleAttribute extends TKStyleAttribute {
  /**
   * 状态栏风格，状态栏上的小图标颜色风格(0:黑色，1:白色)
   */
  statusStyle?: string = '0';
  /**
   * 标题的颜色
   */
  titleColor?: number | string = '#ff000000';
  /**
   * 标题栏背景颜色
   */
  titleBgColor?: string | undefined = undefined;
  /**
   * 返回按钮图片
   */
  backImage?: Resource = $r('app.media.tk_framework_back_btn');
}
```

## 5.3 TKBottomBar组件
> 统一思迪底部栏
- **基本属性说明**

```typescript
@Observed
export class TKBottomBarAttribute extends TKAttribute {
  /**
   * 是否是全屏
   */
  isLayoutFullScreen?: boolean = false;
  /**
   * 显示/隐藏，设置了全屏才会生效，非全屏时会显示非安全区
   */
  bottomBarEnable?: boolean = true;
  /**
   * 回调底部栏的高度
   */
  onBottomBarHeight?: (height: number) => void = () => {
  }
}

/**
 * 子组件的样式属性类
 */
@Observed
export class TKBottomBarStyleAttribute extends TKStyleAttribute {
  bottomAreaBgColor?: string | undefined = undefined
}
```

## 5.5 TKWebPage组件
> 统一思迪H5页加载组件，支持原生标题栏、支持状态栏区域背景色、支持底部导航区域背景色
- **基本属性说明**

```typescript
@Observed
export class TKWebPageAttribute extends TKWebAttribute {
  /**
   * 标题文本
   */
  title?: string = ''
  /**
   * 状态栏显示/隐藏，入口页设置了全屏才会生效，非全屏无法隐藏系统状态栏
   */
  statusBarEnable?: boolean = true
  /**
   * 底部栏显示/隐藏，设置了全屏才会生效，非全屏时会显示非安全区
   */
  bottomBarEnable?: boolean = true
  /**
   * 标题栏显示/隐藏
   */
  titleBarEnable?: boolean = true
  /**
   * 返回按钮的显示/隐藏
   */
  backPressEnable?: boolean = true
  /**
   * 左上角返回按钮回调监听事件
   */
  onBackPressClick?: () => boolean = () => {
    return false;
  }
}

/**
 * 子组件的样式属性类
 */
@Observed
export class TKWebPageStyleAttribute extends TKWebStyleAttribute {
  /**
   * 状态栏风格，状态栏上的小图标颜色风格(0:黑色，1:白色)
   */
  statusStyle?: string = '0'
  /**
   * 标题栏颜色
   */
  titleColor?: string = '#ff000000'
  /**
   * 标题栏背景颜色
   */
  titleBgColor?: string | undefined = '#ffffffff'
  /**
   * 标题栏返回按钮
   */
  backImage?: Resource = $r('app.media.tk_framework_back_btn')
  /**
   * 底部栏非安全区背景颜色
   */
  bottomAreaBgColor?: string | undefined = undefined
}

```
- **H5内部页面的返回处理**
> 当按下系统返回按键或者点击左上角原生返回按钮时，页面返回按照H5网页的页面逐级返回，而不是直接返回到上一个page页。

> 为什么需要使用如下代码实现 ？是因为只有 @Entry 修饰的 page 才能监听到系统的 onBackPress 事件
```typescript
1. 在 @Entry page页添加如下修饰变量
    // 实现H5内部页面的逐级返回
    @Provide onBackPressChanged: boolean = false;
    @Provide onBackPressFilter: boolean = false;
        
2. 复写系统返回键的监听函数 onBackPress，并实现如下代码
    // 实现H5内部页面的逐级返回
    onBackPress(): boolean | void {
      this.onBackPressChanged = !this.onBackPressChanged;
      return this.onBackPressFilter;
    }
```
- **实现自定义标题栏和返回按钮**

```typescript
在 @Entry page页添加如下修饰函数
// 自定义标题的 Builder 函数
@Builder
customTitleView(titleBarController: TKTitleBarController) {
  Button('自定义标题栏')
    .width('100%')
    .height('100%')
    .backgroundColor(Color.Blue)
    .onClick((event) => {
      // 实现返回按钮功能
      titleBarController.routerBack(event)
    })
}
        
build() {
    Column() {
      TKWebPage({
        customTitleView: (titleBarController: TKTitleBarController) => { // 设置自定义标题栏
          this.customTitleView(titleBarController)
        }
      }) // 设置自定义返回按钮
    }
}
```
- **标题栏左上角返回点击事件监听**

```typescript
TKWebPage({
  attribute: {
    onBackPressClick:() => {
      return false // false 不拦截，true 拦截
    }
  }
})
```
- **插件事件的监听(主要在插件中给当前WebPage发送插件事件数据)**
> 应用场景说明：H5 --> 调用插件 --> 插件实现事件发送 --> 当前WebPage中添加监听 --> webPage中收到监听后改变原生UI样式等

```typescript
TKWebPage({
  webAttrEventController: TKWebAttrEventController.builder().onPluginEvent((eventId: string, eventParam?: Record<string, Object>) => {
    
  })
})
```

# 6. 插件使用
## 6.1 插件定义

```typescript
import { TKMapHelper } from '../../../util/map/TKMapHelper';
import { TKStringHelper } from '../../../util/string/TKStringHelper';
import { TKSystemHelper } from '../../../util/system/TKSystemHelper';
import { TKResultVO } from '../../mvc/model/TKResultVO';
import { TKBasePlugin } from '../TKBasePlugin';

/**
 * 获取应用配置---同步场景
 */
export class TKPlugin50000 extends TKBasePlugin {
  public constructor() {
    super();
    this.isSyncPlugin = true;
  }

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    //一级标签
    let storeKey: string = TKMapHelper.getString(param, "storeKey");
    //二级标签
    let innerKey: string = TKMapHelper.getString(param, "innerKey");
    let resultVO: TKResultVO = new TKResultVO();
    if (TKStringHelper.isNotBlank(storeKey)) {
      resultVO.errorNo = -5000001;
      resultVO.errorInfo = "一级标签不能为空!";
    } else if (TKStringHelper.isNotBlank(innerKey)) {
      resultVO.errorNo = -5000002;
      resultVO.errorInfo = "二级标签不能为空!";
    } else {
      //取出系统配置
      let result: string = TKSystemHelper.getConfig(`${storeKey}.${innerKey}`);
      let dataRow: Record<string, Object> = {
        "result": result
      };
      resultVO.results = dataRow;
    }
    return resultVO;
  }
}

/**
 * 获取应用配置---异步场景
 */
export class TKPlugin50000 extends TKBasePlugin {

  public serverInvoke(param: Record<string, Object>): TKResultVO {
    this.asyncExute(param);
    let resultVO: TKResultVO = new TKResultVO();
    return resultVO;
  }
  
  private async asyncExute(){
    //写业务代码逻辑省略.....
    //最终结果回调H5
    let result:Record<string,Object> = {
    };
    this.harmonyCallPluginCallBack(result);
  }
}

//插件回调
let result:Record<string,Object> = {
  
};
this.harmonyCallPluginCallBack(result);

```
## 6.2 插件注册
```typescript
TKPluginIndex.ets 文件
  
export { TKPlugin50000 } from './common/TKPlugin50000'
export { TKPlugin50001 } from './common/TKPlugin50001'
export { TKPlugin50002 } from './common/TKPlugin50002'
export { TKPlugin50010 } from './common/TKPlugin50010'
export { TKPlugin50011 } from './common/TKPlugin50011'
export { TKPlugin50020 } from './common/TKPlugin50020'
export { TKPlugin50021 } from './common/TKPlugin50021'
export { TKPlugin50022 } from './common/TKPlugin50022'
export { TKPlugin50023 } from './common/TKPlugin50023'
export { TKPlugin50024 } from './common/TKPlugin50024'
export { TKPlugin50025 } from './common/TKPlugin50025'
export { TKPlugin50030 } from './common/TKPlugin50030'
export { TKPlugin50031 } from './common/TKPlugin50031'
export { TKPlugin50040 } from './common/TKPlugin50040'
export { TKPlugin50041 } from './common/TKPlugin50041'
export { TKPlugin50042 } from './common/TKPlugin50042'
export { TKPlugin50043 } from './common/TKPlugin50043'
export { TKPlugin50100 } from './common/TKPlugin50100'
export { TKPlugin50101 } from './common/TKPlugin50101'
export { TKPlugin50104 } from './common/TKPlugin50104'
export { TKPlugin50105 } from './common/TKPlugin50105'
export { TKPlugin50106 } from './common/TKPlugin50106'
export { TKPlugin50110 } from './common/TKPlugin50110'
export { TKPlugin50112 } from './common/TKPlugin50112'
export { TKPlugin50114 } from './common/TKPlugin50114'
export { TKPlugin50115 } from './common/TKPlugin50115'
export { TKPlugin50116 } from './common/TKPlugin50116'
export { TKPlugin50118 } from './common/TKPlugin50118'
export { TKPlugin50119 } from './common/TKPlugin50119'
export { TKPlugin50120 } from './common/TKPlugin50120'
export { TKPlugin50123 } from './common/TKPlugin50123'
export { TKPlugin50124 } from './common/TKPlugin50124'
export { TKPlugin50125 } from './common/TKPlugin50125'
export { TKPlugin50130 } from './common/TKPlugin50130'
export { TKPlugin50201 } from './common/TKPlugin50201'
export { TKPlugin50203 } from './common/TKPlugin50203'
export { TKPlugin50220 } from './common/TKPlugin50220'
export { TKPlugin50221 } from './common/TKPlugin50221'
export { TKPlugin50222 } from './common/TKPlugin50222'
export { TKPlugin50224 } from './common/TKPlugin50224'
export { TKPlugin50240 } from './common/TKPlugin50240'
export { TKPlugin50271 } from './common/TKPlugin50271'
export { TKPlugin50273 } from './common/TKPlugin50273'
export { TKPlugin50277 } from './common/TKPlugin50277'
export { TKPlugin50500 } from './common/TKPlugin50500'
export { TKPlugin50501 } from './common/TKPlugin50501'
export { TKPlugin50502 } from './common/TKPlugin50502'
export { TKPlugin50503 } from './common/TKPlugin50503'
export { TKPlugin50504 } from './common/TKPlugin50504'



import * as TKPlugin from './TKPluginIndex';
TKPluginInvokeCenter.shareInstance().registerPlugin(TKPlugin);
```
## 6.2 插件配置
```typescript
SystemPlugin.xml
<plugins>
  <item name="50000" value="TKPlugin50000" description="获取应用配置"></item>
  <item name="50001" value="TKPlugin50001" description="获取应用设备的详细信息"></item>
  <item name="50002" value="TKPlugin50002" description="修改应用配置"></item>
  <item name="50010" value="TKPlugin50010" description="获取应用版本号"></item>
  <item name="50011" value="TKPlugin50011" description="获取应用名称"></item>
  <item name="50020" value="TKPlugin50020" description="获取设备型号"></item>
  <item name="50021" value="TKPlugin50021" description="获取设备SDK版本号"></item>
  <item name="50022" value="TKPlugin50022" description="获取设备唯一码"></item>
  <item name="50023" value="TKPlugin50023" description="获取设备IP地址"></item>
  <item name="50024" value="TKPlugin50024" description="获取设备MAC地址"></item>
  <item name="50025" value="TKPlugin50025" description="获取设备地理位置"></item>
  <item name="50030" value="TKPlugin50030" description="获取设备网络环境"></item>
  <item name="50031" value="TKPlugin50031" description="获取设备网络运营商"></item>
  <item name="50040" value="TKPlugin50040" description="保存数据到内存缓存"></item>
  <item name="50041" value="TKPlugin50041" description="从内存缓存获取数据"></item>
  <item name="50042" value="TKPlugin50042" description="保存数据到用户Cache缓存目录"></item>
  <item name="50043" value="TKPlugin50043" description="从用户Cache缓存目录获取数据"></item>
  <item name="50100" value="TKPlugin50100" description="通知原生H5加载完毕"></item>
  <item name="50101" value="TKPlugin50101" description="通知原生打开模块"></item>
  <item name="50102" value="TKPlugin50102" description="通知原生打开/关闭左右菜单"></item>
  <item name="50103" value="TKPlugin50103" description="设置左右抽屉不生效的区域范围(此范围内滑动不触发)"></item>
  <item name="50104" value="TKPlugin50104" description="设置APP主题"></item>
  <item name="50105" value="TKPlugin50105" description="退出APP应用"></item>
  <item name="50106" value="TKPlugin50106" description="显示Toast提示"></item>
  <item name="50108" value="TKPlugin50108" description="隐藏/显示系统底部TabBar"></item>
  <item name="50109" value="TKPlugin50109" description="通知原生打开系统浏览器的网页"></item>
  <item name="50110" value="TKPlugin50110" description="实现打开原生的信息提示框接口"></item>
  <item name="50112" value="TKPlugin50112" description="获取备用站点中最优站点"></item>
  <item name="50114" value="TKPlugin50114" description="通知原生关闭模块"></item>
  <item name="50115" value="TKPlugin50115" description="通知原生打开一个WebView加载url"></item>
  <item name="50116" value="TKPlugin50116" description="通知原生设备震动"></item>
  <item name="50118" value="TKPlugin50118" description="代理发送http/https相关的网络请求"></item>
  <item name="50119" value="TKPlugin50119" description="设置webView状态栏的颜色"></item>
  <item name="50120" value="TKPlugin50120" description="H5插件的检测"></item>
  <item name="50122" value="TKPlugin50122" description="滑动返回，通知原生H5页面发生跳转，方便原生记录H5历史堆栈"></item>
  <item name="50123" value="TKPlugin50123" description="H5通知原生WebView加载失败"></item>
  <item name="50124" value="TKPlugin50124" description="获取APP进入后台的状态"></item>
  <item name="50125" value="TKPlugin50125" description="获取App主题"></item>
  <item name="50200" value="TKPlugin50200" description="获取软件是否安装"></item>
  <item name="50201" value="TKPlugin50201" description="进行软件的版本升级或者安装"></item>
  <item name="50202" value="TKPlugin50202" description="打开软件"></item>
  <item name="50203" value="TKPlugin50203" description="检测软件版本并实现自动更新下载"></item>
  <item name="50210" value="TKPlugin50210" description="打开原生键盘"></item>
  <item name="50211" value="TKPlugin50211" description="关闭原生键盘"></item>
  <item name="50213" value="TKPlugin50213" description="修改原生键盘"></item>
  <item name="50220" value="TKPlugin50220" description="拨打电话"></item>
  <item name="50221" value="TKPlugin50221" description="发送短信"></item>
  <item name="50222" value="TKPlugin50222" description="弹出手机通讯录"></item>
  <item name="50224" value="TKPlugin50224" description="获取手机号码"></item>
  <item name="50225" value="TKPlugin50225" description="获取手机通讯录数据"></item>
  <item name="50228" value="TKPlugin50228" description="保存用户数据到通信录"></item>
  <item name="50230" value="TKPlugin50230" description="弹出原生分享菜单"></item>
  <item name="50231" value="TKPlugin50231" description="发布分享内容"></item>
  <item name="50240" value="TKPlugin50240" description="查看pdf文件"></item>
  <item name="50250" value="TKPlugin50250" description="弹出日期控件"></item>
  <item name="50252" value="TKPlugin50252" description="弹出滚动选择控件"></item>
  <item name="50260" value="TKPlugin50260" description="设置手势密码"></item>
  <item name="50261" value="TKPlugin50261" description="验证手势密码"></item>
  <item name="50263" value="TKPlugin50263" description="获取手势密码的设置状态"></item>
  <item name="50264" value="TKPlugin50264" description="设置手势密码的设置状态"></item>
  <item name="50266" value="TKPlugin50266" description="关闭掉弹出的手势密码界面"></item>
  <item name="50270" value="TKPlugin50270" description="弹出生成图片二维码组件"></item>
  <item name="50271" value="TKPlugin50271" description="弹出扫描图片二维码组件"></item>
  <item name="50273" value="TKPlugin50273" description="选择照片或者拍照，经过裁剪实现图片上传"></item>
  <item name="50275" value="TKPlugin50275" description="进行视频文件的播放"></item>
  <item name="50276" value="TKPlugin50276" description="自动识别二维码图片(一般用于长按图片触发)"></item>
  <item name="50277" value="TKPlugin50277" description="批量上传图片"></item>
  <item name="50300" value="TKPlugin50300" description="老版本统计页面访问次数，停留时长等（此接口只用在页面进入和退出的时候调用，APP会自动进行页面访问统计）"></item>
  <item name="50301" value="TKPlugin50301" description="老版本统计自定义事件次数"></item>
  <item name="50302" value="TKPlugin50302" description="老版本统计页面错误事件"></item>
  <item name="50303" value="TKPlugin50303" description="老版本统计页面访问次数，停留时长等（H5自己统计页面的进入和退出的时间，通过此接口保存到服务器）"></item>
  <item name="50400" value="TKPlugin50400" description="大数据版本获取H5可视化埋点的页面配置信息"></item>
  <item name="50401" value="TKPlugin50401" description="大数据版本提交H5可视化埋点的页面配置信息"></item>
  <item name="50404" value="TKPlugin50404" description="大数据版本统计页面可视化埋点事件"></item>
  <item name="50405" value="TKPlugin50405" description="大数据版本统计页面固定埋点事件"></item>
  <item name="50406" value="TKPlugin50406" description="大数据版本统计页面错误事件"></item>
  <item name="50407" value="TKPlugin50407" description="大数据版本统计H5设置SID"></item>
  <item name="50408" value="TKPlugin50408" description="大数据版本统计H5页面访问开始事件"></item>
  <item name="50409" value="TKPlugin50409" description="大数据版本统计H5切换设置账户"></item>
  <item name="50410" value="TKPlugin50410" description="大数据版本统计页面固定埋点结束事件"></item>
  <item name="50411" value="TKPlugin50411" description="大数据版本统计H5页面访问结束事件"></item>
  <item name="50500" value="TKPlugin50500" description="统一模块交互消息定义"></item>
  <item name="50501" value="TKPlugin50501" description="H5日志统计"></item>
  <item name="50502" value="TKPlugin50502" description="H5发送原生广播"></item>
  <item name="50503" value="TKPlugin50503" description="H5注册原生广播"></item>
  <item name="50504" value="TKPlugin50504" description="H5卸载原生广播"></item>
</plugins>

Configuration.xml
<catalog name="system" description="系统配置">
   <item name="pluginPath" value="SystemPlugin.xml" description="系统插件配置文件地址,多个用|分割"/>
</catalog>
```
# 7 组件换肤

## 7.1 换肤使用
```typescript
import { TKAppEngine, TKPageContainer, TKRouterHelper, TKStyleAttribute,TKThemeManager } from '@thinkive/thinkive-harmony-base';
import { url } from '@kit.ArkTS';

@Observed
export class TestStyleAttribute extends TKStyleAttribute {
  fontColor?: ResourceColor = TKThemeManager.shareInstance().getCssRulesetByClassName("Test").fontColor;
  image?:Resource =  TKThemeManager.shareInstance().getCssRulesetByClassName("Test").image;
}

@Entry
@Component
struct Index {
  @State message: string = 'Hello World';
  @State styleAttribute: TestStyleAttribute = new TestStyleAttribute();

  aboutToAppear(): void {
    setTimeout(() => {
    if(TKThemeManager.shareInstance().theme  == "theme1"){
    TKThemeManager.shareInstance().theme = "theme2";
  }else{
    TKThemeManager.shareInstance().theme = "theme1";
  }
}, 5000);
}

build() {
  Row() {
    TKPageContainer({ page: new WeakRef(this), styleAttribute: this.styleAttribute }) {
      Column(){
        Text(this.message)
          .fontColor(this.styleAttribute.fontColor)
          .fontSize(50)
          .fontWeight(FontWeight.Bold)
          .onClick(() => {
            TKRouterHelper.pushUrl({url:"pages/TKHqThirdH5View"});
          })
        Image(this.styleAttribute.image).width(50).height(50)
      }.justifyContent(FlexAlign.Center).align(Alignment.Center).width("100%").height("100%")
    }
  }.width("100%").height('100%').alignItems(VerticalAlign.Center)
}
}
```

## 7.2 换肤配置
```typescript
.Test {
    width: 100;
    height: 100;
    padding: 0;
    margin: 0;
    borderWidth: 1;
    borderColor: #ffffffff;
    borderRadius: 10;
    backgroundColor: #ffffffff;
    selectedBackgroundColor: #ffffffff;
    highlightedBackgroundColor: #ffffffff;
    disabledBackgroundColor: #ffffffff;
    opacity: 1;
    fontSize: 16;
    fontColor: #ffffff00;
    fontWeight: 1
    fontFamily: 宋体;
    space: 10;
    color: #ffffffff;
    selectedColor:#ffffffff;
    highlightedColor:#ffffffff;
    disabledColor:#ffffffff;
    image: "$r(@bundle:hqModuel:icon)";
    selectedImage:"$rawfile(@bundle:hqModuel:icon.png)";
    highlightedImage:"";
    disabledImage:"";
    align: certer;
    horizontalAlign: certer;
    verticalAlign:certer;
}

Configuration.xml
<catalog name="theme" description="主题配置">
  <item name="currentTheme" value="theme1" description="默认主题名称"/>
  <item name="isSaveLastTheme" value="1" description="覆盖安装时，是否缓存上次的主题(0:不缓存,1:缓存)，默认是0"/>
  <item name="updateUrl" value=""
description="版本自动检测的服务器地址,http/https格式:http://地址:端口/servlet/json?key=value&amp;key=value, socket格式：socket://busconfig.xml中的serverID?companyId=THINKIVE&amp;systemId=MALL&amp;key=value, server.xml格式：server://server.xml中的serverID?key=value&amp;key=value"/>
  <item name="theme1" value="theme1.css" description="主题1样式文件地址"/>
  <item name="theme2" value="theme2.css" description="主题2样式文件地址"/>
  </catalog>
```

# 8. 数据存储
## 8.1 key-value内存存储
- 示例:

```typescript
//保存内存缓存
TKCacheManager.shareInstance().saveMemeCacheData(key, value, time, isEncrypt);
//获取内存缓存对象
let value: Object = TKCacheManager.shareInstance().getMemCacheData(key) ?? "";

```

## 8.2 key-value磁盘存储
- 示例:

```typescript
//保存用户本地缓存
TKCacheManager.shareInstance().saveFileCacheData(key, value, time, isEncrypt);
//获取内存缓存对象
let value: Object = TKCacheManager.shareInstance().getFileCacheData(key) ?? "";
```

## 8.3 key-value数据库存储
- 示例:

```typescript
//保存用户本地缓存
TKCacheManager.shareInstance().saveDBCacheData(key, value, time, isEncrypt);
//获取内存缓存对象
TKCacheManager.shareInstance().getDBCacheData(key) ?? "";
```

# 9. 插件的调用
```typescript
 TKAppEngine.shareInstance().callPlugin("50115", {
      "url": "www/m/index.html"
    }, "TKOpen",(result)=>{
  });
```

# 10. 工具类
```typescript
export { TKLog } from './src/main/ets/util/logger/TKLog';
export { TKXMLHelper } from './src/main/ets/util/file/xml/TKXMLHelper';
export { TKFileHelper } from './src/main/ets/util/file/TKFileHelper';
export { TKDataHelper } from './src/main/ets/util/data/TKDataHelper';
export { TKObjectHelper } from './src/main/ets/util/data/TKObjectHelper';
export { TKStringHelper } from './src/main/ets/util/string/TKStringHelper';
export { TKArrayHelper } from './src/main/ets/util/array/TKArrayHelper';
export { TKNumberHelper } from './src/main/ets/util/number/TKNumberHelper';
export { TKDateHelper } from './src/main/ets/util/date/TKDateHelper';
export { TKMapHelper } from './src/main/ets/util/map/TKMapHelper';
export {TKCacheVO} from './src/main/ets/util/cache/domain/TKCacheVO'
export {TKCacheManager} from './src/main/ets/util/cache/TKCacheManager'
export {TKAssetStore} from './src/main/ets/util/cache/TKAssetStore'
export {TKPreferences} from './src/main/ets/util/cache/TKPreferences'
export {TKSingleKVStore} from './src/main/ets/util/cache/TKSingleKVStore'
export {TKContextHelper} from './src/main/ets/util/system/TKContextHelper'
export {TKSystemHelper} from './src/main/ets/util/system/TKSystemHelper'
export {TKPasteboardHelper} from './src/main/ets/util/system/TKPasteboardHelper'
export {TKPermissionHelper} from './src/main/ets/util/system/TKPermissionHelper'
export {TKLocationManager} from './src/main/ets/util/location/TKLocationManager'

export {TKUUIDHelper} from './src/main/ets/util/crypto/TKUUIDHelper'
export {TKHexHelper} from './src/main/ets/util/crypto/TKHexHelper'
export {TKBase64Helper} from './src/main/ets/util/crypto/TKBase64Helper'
export {TKMd5Helper} from './src/main/ets/util/crypto/TKMd5Helper'
export {TKShaHelper} from './src/main/ets/util/crypto/TKShaHelper'
export {TKSm3Helper} from './src/main/ets/util/crypto/TKSm3Helper'
export {TKAesHelper,TKAesMode} from './src/main/ets/util/crypto/TKAesHelper'
export {TKDesHelper,TKDesMode} from './src/main/ets/util/crypto/TKDesHelper'
export {TKSm4Helper,TKSm4Mode} from './src/main/ets/util/crypto/TKSm4Helper'
export {TKRsaHelper} from './src/main/ets/util/crypto/TKRsaHelper'
export {TKSm2Helper} from './src/main/ets/util/crypto/TKSm2Helper'
export {TKZlibHelper} from './src/main/ets/util/crypto/TKZlibHelper'
export {TKPasswordGenerator} from './src/main/ets/util/crypto/TKPasswordGenerator'
export {TKDeviceHelper} from './src/main/ets/util/dev/TKDeviceHelper'
export {TKWindowHelper,TKBarName,TKWinOptions} from './src/main/ets/util/ui/TKWindowHelper'
export {TKSnapshotHelper} from './src/main/ets/util/ui/TKSnapshotHelper'
export {TKKeyboardHelper} from './src/main/ets/util/ui/TKKeyboardHelper'
export {TKPickerHelper,TKCameraOptions,TKPhotoSelectOptions,TKDocumentSelectOptions} from './src/main/ets/util/ui/TKPickerHelper'
export {TKClickHelper} from './src/main/ets/util/ui/TKClickHelper'
export {TKImageHelper} from './src/main/ets/util/ui/TKImageHelper'
export {TKDialogHelper,TKToastOptions} from './src/main/ets/util/ui/TKDialogHelper'
export {TKWebHelper} from './src/main/ets/util/ui/TKWebHelper'
export {TKNetHelper} from './src/main/ets/util/net/TKNetHelper'
export {TKURLRequestHelper} from './src/main/ets/util/net/TKURLRequestHelper'
export {TKTimer} from './src/main/ets/util/timer/TKTimer'
export {TKScanHelper} from './src/main/ets/util/scan/TKScanHelper'
```

# 11. 通知中心
```typescript
 TKNotificationCenter.defaultCenter.addObserver(this, this.doNetWorkChange, TKNetworkListener.NOTE_NETWORK_CHANGE);
 TKNotificationCenter.defaultCenter.postNotificationName(TKBusClientManager.NOTE_BUSCLIENT_PUSH_DATA, result);
TKNotificationCenter.defaultCenter.removeObserver(this);
```

# 12. 定时器
```typescript
 /**
 *  心跳定时任务
 */
private heartTimer: TKTimer | undefined = undefined;
this.heartTimer = new TKTimer(this.heartTime * 1000, this, this.sendHeart);
async sendHeart() {
}
this.heartTimer.stop();
this.heartTimer.pause();
this.heartTimer.resume();

```

# 13. 提示框
```typescript
TKDialogHelper.showAlertDialog({title:"温馨提示",message:"ddddd",cancelText:"取消",confirmText:"确定"});

TKDialogHelper.showActionSheet({
  bottomMenu:[{tag:"dddd",name:"dddddd"},{tag:"111",name:"dddddd"},{tag:"333",name:"dddddd"}]
})
  
let value: number = 10;
let progressDialog = TKDialogHelper.createProgressDialog({ value: value, });
progressDialog.open();
progressDialog.reuse({value:100});
progressDialog.close();

TKDialogHelper.showToast("dddddd");

let loading = TKDialogHelper.createLoadingDialog();
loading.open();
loading.close();
```
