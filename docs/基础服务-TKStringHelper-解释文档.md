# TKStringHelper

> **文件路径**: `src/main/ets/util/string/TKStringHelper.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 初级  
> **前置知识**: 字符串操作、正则表达式、编码转换

## 📋 文件概述

### 功能定位
TKStringHelper是HMThinkBaseHar框架的**字符串工具类**，提供了完整的字符串处理功能。它采用命名空间设计，集成了常用的字符串操作方法，支持空值检查、格式化、替换、转换等功能，为应用提供统一的字符串处理服务。

### 核心职责
- **空值检查**：提供字符串空值、空白的检查和验证
- **字符串格式化**：支持字符串的格式化和模板替换
- **字符串转换**：提供大小写转换、编码转换等功能
- **字符串操作**：支持替换、截取、分割等基础操作
- **工具方法**：提供常用的字符串处理工具方法

### 设计模式
- **命名空间模式**：使用namespace封装字符串处理功能
- **工具类模式**：提供静态方法的工具类设计
- **策略模式**：支持不同的字符串处理策略
- **适配器模式**：封装JavaScript字符串API，提供统一接口

## 🔧 核心功能

### 空值检查方法

#### 方法1：`isBlank(str: string)`
- **功能**: 检查字符串是否为空或只包含空白字符
- **参数**: str - 要检查的字符串
- **返回值**: boolean - true表示为空或空白
- **使用场景**: 验证用户输入、参数校验

#### 方法2：`isNotBlank(str: string)`
- **功能**: 检查字符串是否不为空且包含非空白字符
- **参数**: str - 要检查的字符串
- **返回值**: boolean - true表示不为空且有内容
- **使用场景**: 确保字符串有有效内容

#### 方法3：`isEmpty(str: string)`
- **功能**: 检查字符串是否为空（null、undefined或空字符串）
- **参数**: str - 要检查的字符串
- **返回值**: boolean - true表示为空
- **使用场景**: 基础的空值检查

#### 方法4：`isNotEmpty(str: string)`
- **功能**: 检查字符串是否不为空
- **参数**: str - 要检查的字符串
- **返回值**: boolean - true表示不为空
- **使用场景**: 确保字符串不为空

### 字符串操作方法

#### 方法1：`replace(str: string, searchValue: string, replaceValue: string)`
- **功能**: 替换字符串中的指定内容
- **参数**: 
  - str - 源字符串
  - searchValue - 要查找的字符串
  - replaceValue - 替换的字符串
- **返回值**: string - 替换后的字符串
- **使用场景**: 字符串内容替换

#### 方法2：`replaceAll(str: string, searchValue: string, replaceValue: string)`
- **功能**: 替换字符串中所有匹配的内容
- **参数**: 同replace方法
- **返回值**: string - 替换后的字符串
- **使用场景**: 全局字符串替换

#### 方法3：`substring(str: string, start: number, end?: number)`
- **功能**: 截取字符串的子串
- **参数**: 
  - str - 源字符串
  - start - 开始位置
  - end - 结束位置（可选）
- **返回值**: string - 截取的子串
- **使用场景**: 字符串截取

#### 方法4：`split(str: string, separator: string)`
- **功能**: 分割字符串为数组
- **参数**: 
  - str - 源字符串
  - separator - 分隔符
- **返回值**: Array<string> - 分割后的字符串数组
- **使用场景**: 字符串分割处理

### 字符串转换方法

#### 方法1：`toLowerCase(str: string)`
- **功能**: 转换为小写
- **参数**: str - 源字符串
- **返回值**: string - 小写字符串
- **使用场景**: 大小写标准化

#### 方法2：`toUpperCase(str: string)`
- **功能**: 转换为大写
- **参数**: str - 源字符串
- **返回值**: string - 大写字符串
- **使用场景**: 大小写标准化

#### 方法3：`trim(str: string)`
- **功能**: 去除首尾空白字符
- **参数**: str - 源字符串
- **返回值**: string - 去除空白后的字符串
- **使用场景**: 清理用户输入

### 格式化方法

#### 方法1：`format(template: string, ...args: any[])`
- **功能**: 格式化字符串模板
- **参数**: 
  - template - 模板字符串
  - args - 参数列表
- **返回值**: string - 格式化后的字符串
- **使用场景**: 字符串模板替换

## 💡 技术要点

### 核心算法/逻辑

#### 空值检查实现
```typescript
export function isBlank(str: string): boolean {
  return str == null || str == undefined || str.trim().length === 0;
}

export function isNotBlank(str: string): boolean {
  return !isBlank(str);
}

export function isEmpty(str: string): boolean {
  return str == null || str == undefined || str.length === 0;
}

export function isNotEmpty(str: string): boolean {
  return !isEmpty(str);
}
```

#### 字符串替换实现
```typescript
export function replace(str: string, searchValue: string, replaceValue: string): string {
  if (isBlank(str) || isBlank(searchValue)) {
    return str || '';
  }
  return str.replace(searchValue, replaceValue);
}

export function replaceAll(str: string, searchValue: string, replaceValue: string): string {
  if (isBlank(str) || isBlank(searchValue)) {
    return str || '';
  }
  return str.replace(new RegExp(escapeRegExp(searchValue), 'g'), replaceValue);
}

// 转义正则表达式特殊字符
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
```

#### 字符串格式化实现
```typescript
export function format(template: string, ...args: any[]): string {
  if (isBlank(template)) {
    return '';
  }
  
  return template.replace(/\{(\d+)\}/g, (match, index) => {
    const argIndex = parseInt(index);
    return argIndex < args.length ? String(args[argIndex]) : match;
  });
}
```

#### 安全字符串操作
```typescript
export function substring(str: string, start: number, end?: number): string {
  if (isBlank(str)) {
    return '';
  }
  
  if (end !== undefined) {
    return str.substring(start, end);
  } else {
    return str.substring(start);
  }
}

export function split(str: string, separator: string): Array<string> {
  if (isBlank(str)) {
    return [];
  }
  
  if (isBlank(separator)) {
    return [str];
  }
  
  return str.split(separator);
}
```

### 实现机制分析

#### 空值安全设计
- **多层检查**：同时检查null、undefined和空字符串
- **空白字符处理**：使用trim()方法处理空白字符
- **一致性保证**：所有方法都有一致的空值处理逻辑
- **防御性编程**：在所有操作前进行空值检查

#### 字符串操作安全
- **参数验证**：所有方法都验证输入参数
- **边界处理**：正确处理字符串边界情况
- **异常预防**：避免字符串操作引发的异常
- **默认值处理**：提供合理的默认返回值

#### 性能优化
- **早期返回**：空值检查后早期返回，避免不必要的处理
- **原生API使用**：充分利用JavaScript原生字符串API
- **正则表达式优化**：合理使用正则表达式，避免性能问题
- **内存管理**：避免创建不必要的临时字符串

#### 扩展性设计
- **命名空间封装**：便于添加新的字符串处理方法
- **方法组织**：按功能分类组织方法，便于维护
- **接口一致性**：保持方法接口的一致性和可预测性

### 性能考虑
- **空值快速检查**：优先进行空值检查，避免后续处理
- **原生方法优先**：优先使用JavaScript原生字符串方法
- **正则表达式缓存**：对常用正则表达式进行缓存
- **字符串不可变性**：利用字符串不可变特性优化内存使用

### 错误处理
- **参数验证**：验证所有输入参数的有效性
- **异常预防**：预防可能的字符串操作异常
- **优雅降级**：异常情况下返回合理的默认值
- **错误隔离**：单个方法的错误不影响其他方法

### 最佳实践
- **统一使用**：在整个应用中统一使用字符串工具类
- **空值检查**：在字符串操作前进行空值检查
- **方法选择**：根据需求选择合适的字符串处理方法
- **性能考虑**：在性能敏感的场景中注意方法选择

## 🔗 依赖关系

### 依赖的模块
- 无外部依赖，基于JavaScript原生字符串API实现

### 被依赖情况
- `框架所有模块` - 几乎所有框架模块都使用字符串工具进行字符串处理
- `TKFileHelper` - 文件工具使用字符串工具处理路径和内容
- `TKLog` - 日志工具使用字符串工具格式化日志信息
- `TKHttpDao` - HTTP数据访问使用字符串工具处理URL和参数

### 关联文件
- `TKDataHelper.ets` - 数据工具类，提供数据转换功能
- `TKObjectHelper.ets` - 对象工具类，提供对象处理功能
- `TKValidateHelper.ets` - 验证工具类，使用字符串工具进行验证

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKStringHelper是框架字符串处理的基础工具：
1. **基础工具**：为字符串处理提供基础功能
2. **使用频率极高**：在几乎所有模块中都会使用
3. **稳定可靠**：提供安全的字符串操作方法
4. **简单易用**：API简洁明了，易于理解和使用

理解TKStringHelper有助于掌握框架的字符串处理规范，是日常开发的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解字符串基础**：掌握字符串的基本概念和操作
2. **分析空值处理**：理解不同空值检查方法的区别
3. **学习字符串操作**：掌握常用的字符串处理方法
4. **实践字符串处理**：在实际开发中使用字符串工具

### 前置学习
- JavaScript字符串基础
- 正则表达式基础
- 字符编码概念
- 空值和空白字符的区别

### 后续学习
- `TKDataHelper.ets` - 学习数据转换工具
- `TKValidateHelper.ets` - 了解字符串验证功能
- `TKObjectHelper.ets` - 掌握对象处理工具

### 实践建议
1. **基础方法练习**：练习各种字符串处理方法
2. **空值检查实验**：理解不同空值检查方法的差异
3. **格式化功能测试**：测试字符串格式化功能
4. **性能对比**：对比不同字符串处理方法的性能

### 常见问题
1. **问题**: isBlank和isEmpty有什么区别？
   **解答**: isEmpty只检查null、undefined和空字符串，而isBlank还会检查只包含空白字符的字符串。

2. **问题**: 如何安全地进行字符串操作？
   **解答**: 在进行字符串操作前，先使用isNotBlank或isNotEmpty检查字符串是否有效，避免空指针异常。

## 📝 代码示例

### 基础使用
```typescript
import { TKStringHelper } from '@thinkive/tk-harmony-base';

// 空值检查示例
function stringValidation() {
  const testStrings = [
    null,
    undefined,
    "",
    "   ",
    "hello",
    "  world  "
  ];

  testStrings.forEach((str, index) => {
    console.log(`字符串 ${index}:`, str);
    console.log('  isEmpty:', TKStringHelper.isEmpty(str));
    console.log('  isNotEmpty:', TKStringHelper.isNotEmpty(str));
    console.log('  isBlank:', TKStringHelper.isBlank(str));
    console.log('  isNotBlank:', TKStringHelper.isNotBlank(str));
    console.log('---');
  });
}

// 字符串操作示例
function stringOperations() {
  const text = "Hello World, Hello Universe";

  // 替换操作
  const replaced = TKStringHelper.replace(text, "Hello", "Hi");
  console.log('单次替换:', replaced); // "Hi World, Hello Universe"

  const replacedAll = TKStringHelper.replaceAll(text, "Hello", "Hi");
  console.log('全部替换:', replacedAll); // "Hi World, Hi Universe"

  // 截取操作
  const substring1 = TKStringHelper.substring(text, 0, 5);
  console.log('截取0-5:', substring1); // "Hello"

  const substring2 = TKStringHelper.substring(text, 6);
  console.log('从6开始:', substring2); // "World, Hello Universe"

  // 分割操作
  const parts = TKStringHelper.split(text, ", ");
  console.log('分割结果:', parts); // ["Hello World", "Hello Universe"]

  // 大小写转换
  console.log('转小写:', TKStringHelper.toLowerCase(text));
  console.log('转大写:', TKStringHelper.toUpperCase(text));

  // 去除空白
  const textWithSpaces = "  Hello World  ";
  console.log('去除空白:', `"${TKStringHelper.trim(textWithSpaces)}"`);
}

// 字符串格式化示例
function stringFormatting() {
  // 基础格式化
  const template1 = "Hello {0}, welcome to {1}!";
  const formatted1 = TKStringHelper.format(template1, "张三", "HarmonyOS");
  console.log('格式化结果1:', formatted1); // "Hello 张三, welcome to HarmonyOS!"

  // 多参数格式化
  const template2 = "用户 {0} 在 {1} 执行了 {2} 操作";
  const formatted2 = TKStringHelper.format(template2, "admin", "2024-01-01", "登录");
  console.log('格式化结果2:', formatted2);

  // 数字格式化
  const template3 = "订单号: {0}, 金额: {1} 元, 数量: {2} 件";
  const formatted3 = TKStringHelper.format(template3, "ORD001", 99.99, 3);
  console.log('格式化结果3:', formatted3);
}

// 执行示例
stringValidation();
stringOperations();
stringFormatting();
```

### 高级用法
```typescript
// 字符串验证器
class StringValidator {
  // 验证邮箱格式
  static isValidEmail(email: string): boolean {
    if (TKStringHelper.isBlank(email)) {
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(TKStringHelper.trim(email));
  }

  // 验证手机号格式
  static isValidPhone(phone: string): boolean {
    if (TKStringHelper.isBlank(phone)) {
      return false;
    }

    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(TKStringHelper.trim(phone));
  }

  // 验证身份证号格式
  static isValidIdCard(idCard: string): boolean {
    if (TKStringHelper.isBlank(idCard)) {
      return false;
    }

    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    return idCardRegex.test(TKStringHelper.trim(idCard));
  }

  // 验证密码强度
  static validatePasswordStrength(password: string): {
    isValid: boolean,
    strength: 'weak' | 'medium' | 'strong',
    issues: string[]
  } {
    const issues: string[] = [];

    if (TKStringHelper.isBlank(password)) {
      return {
        isValid: false,
        strength: 'weak',
        issues: ['密码不能为空']
      };
    }

    if (password.length < 8) {
      issues.push('密码长度至少8位');
    }

    if (!/[a-z]/.test(password)) {
      issues.push('密码需包含小写字母');
    }

    if (!/[A-Z]/.test(password)) {
      issues.push('密码需包含大写字母');
    }

    if (!/\d/.test(password)) {
      issues.push('密码需包含数字');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      issues.push('密码需包含特殊字符');
    }

    const isValid = issues.length === 0;
    let strength: 'weak' | 'medium' | 'strong' = 'weak';

    if (isValid) {
      if (password.length >= 12) {
        strength = 'strong';
      } else {
        strength = 'medium';
      }
    }

    return { isValid, strength, issues };
  }
}

// 字符串格式化器
class StringFormatter {
  // 格式化文件大小
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    const size = (bytes / Math.pow(k, i)).toFixed(2);
    return TKStringHelper.format('{0} {1}', size, sizes[i]);
  }

  // 格式化时间间隔
  static formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return TKStringHelper.format('{0}天{1}小时', days, hours % 24);
    } else if (hours > 0) {
      return TKStringHelper.format('{0}小时{1}分钟', hours, minutes % 60);
    } else if (minutes > 0) {
      return TKStringHelper.format('{0}分钟{1}秒', minutes, seconds % 60);
    } else {
      return TKStringHelper.format('{0}秒', seconds);
    }
  }

  // 格式化金额
  static formatCurrency(amount: number, currency: string = '¥'): string {
    const formatted = amount.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return TKStringHelper.format('{0}{1}', currency, formatted);
  }

  // 格式化百分比
  static formatPercentage(value: number, decimals: number = 2): string {
    const percentage = (value * 100).toFixed(decimals);
    return TKStringHelper.format('{0}%', percentage);
  }

  // 脱敏处理
  static maskSensitiveInfo(info: string, type: 'phone' | 'email' | 'idCard'): string {
    if (TKStringHelper.isBlank(info)) {
      return '';
    }

    switch (type) {
      case 'phone':
        if (info.length === 11) {
          return TKStringHelper.format('{0}****{1}',
            TKStringHelper.substring(info, 0, 3),
            TKStringHelper.substring(info, 7)
          );
        }
        break;
      case 'email':
        const atIndex = info.indexOf('@');
        if (atIndex > 0) {
          const username = TKStringHelper.substring(info, 0, atIndex);
          const domain = TKStringHelper.substring(info, atIndex);
          const maskedUsername = username.length > 2 ?
            TKStringHelper.format('{0}***{1}',
              TKStringHelper.substring(username, 0, 1),
              TKStringHelper.substring(username, username.length - 1)
            ) : '***';
          return maskedUsername + domain;
        }
        break;
      case 'idCard':
        if (info.length === 18) {
          return TKStringHelper.format('{0}**********{1}',
            TKStringHelper.substring(info, 0, 4),
            TKStringHelper.substring(info, 14)
          );
        }
        break;
    }

    return info;
  }
}

// 字符串工具扩展
class StringUtils {
  // 生成随机字符串
  static generateRandomString(length: number, charset?: string): string {
    const defaultCharset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const chars = charset || defaultCharset;
    let result = '';

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return result;
  }

  // 计算字符串相似度
  static calculateSimilarity(str1: string, str2: string): number {
    if (TKStringHelper.isBlank(str1) && TKStringHelper.isBlank(str2)) {
      return 1;
    }

    if (TKStringHelper.isBlank(str1) || TKStringHelper.isBlank(str2)) {
      return 0;
    }

    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) {
      return 1;
    }

    const editDistance = this.calculateEditDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  // 计算编辑距离
  private static calculateEditDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        if (str1[i - 1] === str2[j - 1]) {
          matrix[j][i] = matrix[j - 1][i - 1];
        } else {
          matrix[j][i] = Math.min(
            matrix[j - 1][i - 1] + 1, // substitution
            matrix[j][i - 1] + 1,     // insertion
            matrix[j - 1][i] + 1      // deletion
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }

  // 字符串压缩
  static compress(str: string): string {
    if (TKStringHelper.isBlank(str)) {
      return '';
    }

    let compressed = '';
    let count = 1;

    for (let i = 0; i < str.length; i++) {
      if (i + 1 < str.length && str[i] === str[i + 1]) {
        count++;
      } else {
        compressed += str[i] + (count > 1 ? count : '');
        count = 1;
      }
    }

    return compressed.length < str.length ? compressed : str;
  }

  // 字符串解压缩
  static decompress(str: string): string {
    if (TKStringHelper.isBlank(str)) {
      return '';
    }

    let decompressed = '';
    let i = 0;

    while (i < str.length) {
      const char = str[i];
      let count = '';

      // 读取数字
      while (i + 1 < str.length && /\d/.test(str[i + 1])) {
        count += str[i + 1];
        i++;
      }

      const repeatCount = count ? parseInt(count) : 1;
      decompressed += char.repeat(repeatCount);
      i++;
    }

    return decompressed;
  }
}

// 使用示例
function demonstrateAdvancedStringUtils() {
  // 验证器示例
  console.log('=== 字符串验证 ===');
  console.log('邮箱验证:', StringValidator.isValidEmail('<EMAIL>'));
  console.log('手机验证:', StringValidator.isValidPhone('13812345678'));

  const passwordResult = StringValidator.validatePasswordStrength('MyPass123!');
  console.log('密码强度:', passwordResult);

  // 格式化器示例
  console.log('\n=== 字符串格式化 ===');
  console.log('文件大小:', StringFormatter.formatFileSize(1024 * 1024 * 2.5));
  console.log('时间间隔:', StringFormatter.formatDuration(3661000));
  console.log('金额格式:', StringFormatter.formatCurrency(12345.67));
  console.log('百分比:', StringFormatter.formatPercentage(0.8567));

  console.log('手机脱敏:', StringFormatter.maskSensitiveInfo('13812345678', 'phone'));
  console.log('邮箱脱敏:', StringFormatter.maskSensitiveInfo('<EMAIL>', 'email'));

  // 工具扩展示例
  console.log('\n=== 字符串工具 ===');
  console.log('随机字符串:', StringUtils.generateRandomString(10));
  console.log('相似度计算:', StringUtils.calculateSimilarity('hello', 'hallo'));

  const original = 'aaabbccccdddd';
  const compressed = StringUtils.compress(original);
  const decompressed = StringUtils.decompress(compressed);
  console.log('原始字符串:', original);
  console.log('压缩后:', compressed);
  console.log('解压后:', decompressed);
}

// 执行演示
demonstrateAdvancedStringUtils();
```

## 📚 相关资源

### 官方文档
- JavaScript字符串API参考
- 正则表达式语法指南

### 参考资料
- 《JavaScript权威指南》- 字符串处理
- 《正则表达式必知必会》- 正则表达式应用

### 相关文件
- `TKDataHelper.ets` - 数据转换工具
- `TKValidateHelper.ets` - 验证工具类
- `TKObjectHelper.ets` - 对象处理工具
