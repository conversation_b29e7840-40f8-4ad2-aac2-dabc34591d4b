# TKWeb

> **文件路径**: `src/main/ets/components/webview/TKWeb.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中高级  
> **前置知识**: WebView开发、H5与原生交互、JS桥接、混合开发

## 📋 文件概述

### 功能定位
TKWeb是HMThinkBaseHar框架的**WebView组件**，提供了完整的H5混合开发解决方案。它封装了鸿蒙WebView的复杂配置，集成了JS桥接、插件调用、页面管理等企业级功能，为H5与原生的无缝交互提供强大支持。

### 核心职责
- **WebView容器管理**：提供完整的WebView容器和配置管理
- **H5与原生桥接**：实现H5页面与原生功能的双向通信
- **插件系统集成**：支持H5调用原生插件功能
- **页面生命周期管理**：管理H5页面的加载、显示、隐藏等生命周期
- **错误处理和加载状态**：提供完善的错误处理和加载状态管理

### 设计模式
- **组件模式**：使用@Component装饰器定义可复用的WebView组件
- **控制器模式**：通过多个控制器管理不同方面的功能
- **代理模式**：通过JS代理控制器实现H5与原生的通信
- **观察者模式**：监听页面加载状态和生命周期事件

## 🔧 核心功能

### 主要属性

#### 属性1：`src: string`
- **功能**: H5页面加载的URL地址
- **类型**: @Prop @Watch
- **使用场景**: 指定要加载的H5页面地址

#### 属性2：`data: string`
- **功能**: H5页面加载的HTML内容
- **类型**: @Prop @Watch
- **使用场景**: 直接加载HTML字符串内容

#### 属性3：`attribute: TKWebAttribute`
- **功能**: WebView组件的控制属性
- **类型**: @Prop @Watch
- **包含配置**: 模块名称、加载超时、用户代理等

#### 属性4：`webAttrEventController: TKWebAttrEventController`
- **功能**: Web属性和事件监听控制器
- **类型**: @State
- **使用场景**: 监听WebView的各种事件

#### 属性5：`jSProxyController: TKJSProxyController`
- **功能**: 原生与JS的交互代理控制器
- **类型**: @State
- **使用场景**: 实现H5与原生的数据交互

### 主要方法

#### 方法1：`onSrcUpdated()`
- **功能**: URL更新时的处理逻辑
- **触发时机**: src属性变化时
- **处理内容**: 重新加载WebView内容

#### 方法2：`onDataUpdated()`
- **功能**: HTML内容更新时的处理逻辑
- **触发时机**: data属性变化时
- **处理内容**: 加载新的HTML内容

#### 方法3：`onAttributeUpdated()`
- **功能**: 属性更新时的处理逻辑
- **触发时机**: attribute属性变化时
- **处理内容**: 更新WebView配置

## 💡 技术要点

### 核心算法/逻辑

#### WebView配置和初始化
```typescript
Web({
  src: this.jSProxyController?.url,
  controller: this.jSProxyController?.webviewController
})
.nestedScroll({
  scrollForward: NestedScrollMode.PARENT_FIRST,
  scrollBackward: NestedScrollMode.SELF_FIRST
})
.initialScale(this.styleAttribute.initialScale)
.textZoomRatio(this.styleAttribute.textZoomRatio)
.layoutMode(this.styleAttribute.layoutMode)
.backgroundColor(this.styleAttribute.backgroundColor)
.databaseAccess(true)
.javaScriptAccess(true)
.domStorageAccess(true)
.horizontalScrollBarAccess(false)
.verticalScrollBarAccess(false)
.fileAccess(this.jSProxyController?.url.toString().startsWith("file://") ? true : false)
.opacity(this.styleAttribute.opacity)
.cacheMode(CacheMode.Default)
```

#### JS桥接机制
```typescript
// 注册JS桥接对象
this.webviewController!.registerJavaScriptProxy(
  new TKJSBridege(this), 
  "external", 
  ["callMessage"]
);

// H5调用原生插件
public callMessage(param: string): string {
  if (TKObjectHelper.isJsonStr(param)) {
    let argsJSON: Record<string, Object> = JSON.parse(param);
    let funcNo: string = TKMapHelper.getString(argsJSON, "funcNo");
    let moduleName: string = TKMapHelper.getString(argsJSON, "moduleName");
    
    let resultVO: TKResultVO = TKPluginInvokeCenter.shareInstance().callPlugin({
      funcNo: funcNo,
      param: argsJSON,
      moduleName: moduleName,
      isH5: true
    });
    
    return resultVO.toJsonStr();
  }
  return "{}";
}
```

#### 页面加载状态管理
```typescript
.onPageBegin((event) => {
  // 页面开始加载
  this.isShowFullLoadingView = this.attribute.isShowLoading;
  if (this.webAttrEventController?.webAttrEvent.onPageBegin) {
    this.webAttrEventController.webAttrEvent.onPageBegin(event);
  }
})
.onPageEnd((event) => {
  // 页面加载完成
  this.isShowFullLoadingView = false;
  this.isShowErrorView = false;
  
  if (this.webAttrEventController?.webAttrEvent.onPageEnd) {
    this.webAttrEventController.webAttrEvent.onPageEnd(event);
  }
  
  // 发送初始化消息给H5
  if (this.attribute.isSupportReInitH5) {
    this.jSProxyController?.sendInitMsgToH5();
  }
})
```

#### 错误处理机制
```typescript
.onErrorReceive((event) => {
  // 页面加载错误
  this.isShowFullLoadingView = false;
  if (this.attribute.isShowError) {
    this.isShowErrorView = true;
  }
  
  if (this.webAttrEventController?.webAttrEvent.onErrorReceive) {
    this.webAttrEventController.webAttrEvent.onErrorReceive();
  }
})
```

#### 对话框处理
```typescript
.onAlert((event) => {
  if (event) {
    this.showAlertDialog(event);
  }
  return true;
})
.onConfirm((event) => {
  if (event) {
    this.showConfirmDialog(event);
  }
  return true;
})

private showAlertDialog(event: OnAlertEvent) {
  let option: TKNormalDialogOption = {
    title: "提示",
    message: event.message,
    positiveText: "确定",
    onPositiveClick: () => {
      event.result.handleConfirm();
    }
  };
  TKDialogHelper.showNormalDialog(option);
}
```

### 实现机制分析

#### 多控制器协作
- **TKJSProxyController**：负责JS桥接和H5通信
- **TKWebAttrEventController**：负责WebView事件监听
- **TKPageContainer**：负责页面容器和生命周期管理

#### H5与原生通信流程
1. **H5调用原生**：通过external.callMessage()调用原生插件
2. **参数传递**：JSON格式传递参数和功能号
3. **插件执行**：通过插件中心调用相应的原生功能
4. **结果返回**：将执行结果以JSON格式返回给H5

#### 页面生命周期管理
- **加载阶段**：显示加载动画，初始化JS桥接
- **完成阶段**：隐藏加载动画，发送初始化消息
- **错误阶段**：显示错误页面，提供重试机制
- **销毁阶段**：清理资源，注销监听器

#### 本地文件支持
- **本地域名映射**：支持本地HTML文件的加载
- **资源拦截**：拦截网络请求，返回本地资源
- **MIME类型处理**：根据文件类型返回相应的内容

### 性能考虑
- **懒加载**：WebView控制器按需创建和初始化
- **资源缓存**：支持WebView缓存模式，提升加载性能
- **内存管理**：及时清理WebView资源，避免内存泄漏
- **异步处理**：所有JS调用都是异步的，不阻塞UI线程

### 错误处理
- **网络错误**：显示错误页面，提供重试功能
- **JS异常**：捕获JS执行异常，记录错误日志
- **插件调用异常**：处理插件调用失败的情况
- **资源加载失败**：提供本地资源回退机制

### 最佳实践
- **统一配置**：通过TKWebAttribute统一管理WebView配置
- **事件监听**：合理使用事件监听器，及时清理资源
- **错误处理**：完善的错误处理和用户提示机制
- **性能优化**：合理使用缓存和懒加载策略

## 🔗 依赖关系

### 依赖的模块
- `TKJSProxyController` - JS代理控制器，H5与原生通信桥梁
- `TKWebAttrEventController` - Web事件控制器，事件监听管理
- `TKPageContainer` - 页面容器，提供页面基础功能
- `TKPluginInvokeCenter` - 插件调用中心，处理H5调用原生插件
- `TKDialogHelper` - 对话框工具，显示各种对话框

### 被依赖情况
- `TKWebPage` - Web页面组件，使用TKWeb作为核心WebView
- `各H5页面` - 通过TKWeb组件加载和显示H5内容
- `混合开发应用` - 使用TKWeb实现H5与原生的混合开发

### 关联文件
- `TKJSProxyController.ets` - JS代理控制器实现
- `TKWebAttrEventController.ets` - Web事件控制器实现
- `TKWebPage.ets` - Web页面组件，TKWeb的使用者
- `TKPluginInvokeCenter.ets` - 插件调用中心

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKWeb是框架H5混合开发的核心组件：
1. **混合开发基础**：提供H5与原生混合开发的基础能力
2. **企业级功能**：集成了插件调用、错误处理等企业级功能
3. **使用场景广泛**：在需要H5功能的应用中广泛使用
4. **技术复杂度高**：涉及WebView、JS桥接等复杂技术

理解TKWeb有助于掌握框架的H5混合开发能力，是实现复杂交互功能的重要组件。

## 🎯 学习建议

### 学习路径
1. **理解WebView基础**：掌握鸿蒙WebView的基本使用和配置
2. **学习JS桥接机制**：理解H5与原生的双向通信原理
3. **分析插件集成方式**：掌握H5调用原生插件的实现机制
4. **实践混合开发**：使用TKWeb组件开发H5混合应用

### 前置学习
- 鸿蒙WebView组件使用
- H5与原生交互原理
- JavaScript和TypeScript基础
- 插件系统的使用

### 后续学习
- `TKJSProxyController.ets` - 深入学习JS桥接实现
- `TKWebPage.ets` - 了解Web页面的完整实现
- `TKPluginInvokeCenter.ets` - 掌握插件调用机制

### 实践建议
1. **基础WebView使用**：创建简单的WebView页面
2. **JS桥接实验**：实现H5调用原生功能
3. **插件调用测试**：测试H5调用各种原生插件
4. **错误处理验证**：测试各种错误情况的处理

### 常见问题
1. **问题**: H5如何调用原生插件？
   **解答**: 通过external.callMessage()方法，传递包含funcNo的JSON参数，框架会自动调用相应的原生插件并返回结果。

2. **问题**: 如何处理WebView的加载错误？
   **解答**: 通过设置attribute.isShowError为true，框架会自动显示错误页面，并提供重试功能。

## 📝 代码示例

### 基础使用
```typescript
import { TKWeb, TKWebAttribute, TKWebStyleAttribute, TKWebAttrEventController } from '@thinkive/tk-harmony-base';

// 基础WebView组件
@Component
struct BasicWebView {
  @State private webAttribute: TKWebAttribute = new TKWebAttribute();
  @State private webStyleAttribute: TKWebStyleAttribute = new TKWebStyleAttribute();
  @State private webEventController: TKWebAttrEventController = TKWebAttrEventController.builder();

  aboutToAppear() {
    // 配置WebView属性
    this.webAttribute.moduleName = 'MyModule';
    this.webAttribute.loadTimeout = 30000;
    this.webAttribute.isShowLoading = true;
    this.webAttribute.isShowError = true;

    // 配置事件监听
    this.webEventController
      .onPageBegin(() => {
        console.log('页面开始加载');
      })
      .onPageEnd(() => {
        console.log('页面加载完成');
      })
      .onErrorReceive(() => {
        console.log('页面加载错误');
      });
  }

  build() {
    Column() {
      TKWeb({
        src: 'https://www.example.com',
        attribute: this.webAttribute,
        styleAttribute: this.webStyleAttribute,
        webAttrEventController: this.webEventController
      })
    }
    .width('100%')
    .height('100%')
  }
}

// 加载本地HTML内容
@Component
struct LocalHtmlWebView {
  @State private htmlContent: string = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>本地HTML</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
    </head>
    <body>
        <h1>Hello HarmonyOS</h1>
        <button onclick="callNativePlugin()">调用原生插件</button>
        <script>
            function callNativePlugin() {
                if (window.external && window.external.callMessage) {
                    const result = window.external.callMessage(JSON.stringify({
                        funcNo: "50023",
                        moduleName: "TestModule"
                    }));
                    console.log('插件调用结果:', result);
                    alert('插件调用结果: ' + result);
                }
            }
        </script>
    </body>
    </html>
  `;

  build() {
    TKWeb({
      data: this.htmlContent,
      attribute: new TKWebAttribute()
    })
  }
}
```

### 高级用法
```typescript
// 高级WebView管理器
class WebViewManager {
  private webEventController: TKWebAttrEventController;
  private webAttribute: TKWebAttribute;
  private loadingCallback?: (isLoading: boolean) => void;
  private errorCallback?: (error: string) => void;

  constructor() {
    this.webAttribute = new TKWebAttribute();
    this.setupWebAttribute();
    this.setupEventController();
  }

  private setupWebAttribute() {
    this.webAttribute.moduleName = 'AdvancedModule';
    this.webAttribute.loadTimeout = 60000;
    this.webAttribute.isShowLoading = false; // 自定义加载处理
    this.webAttribute.isShowError = false;   // 自定义错误处理
    this.webAttribute.isSupportReInitH5 = true;
    this.webAttribute.customUserAgent = 'MyApp/1.0.0 HarmonyOS';
  }

  private setupEventController() {
    this.webEventController = TKWebAttrEventController.builder()
      .onPageBegin(() => {
        console.log('页面开始加载');
        this.loadingCallback?.(true);
      })
      .onPageEnd(() => {
        console.log('页面加载完成');
        this.loadingCallback?.(false);
        this.sendCustomInitMessage();
      })
      .onErrorReceive(() => {
        console.log('页面加载错误');
        this.loadingCallback?.(false);
        this.errorCallback?.('页面加载失败，请检查网络连接');
      })
      .onProgressChange((event) => {
        if (event) {
          console.log(`加载进度: ${event.newProgress}%`);
        }
      })
      .onTitleReceive((event) => {
        if (event) {
          console.log(`页面标题: ${event.title}`);
        }
      })
      .onPluginEvent((eventId, eventParam, jsProxyController) => {
        this.handlePluginEvent(eventId, eventParam, jsProxyController);
      });
  }

  private sendCustomInitMessage() {
    // 发送自定义初始化消息给H5
    const initData = {
      appVersion: '1.0.0',
      platform: 'HarmonyOS',
      timestamp: Date.now()
    };

    // 这里可以通过jsProxyController发送消息
    console.log('发送初始化数据:', initData);
  }

  private handlePluginEvent(eventId: string, eventParam?: Record<string, Object>, jsProxyController?: any) {
    console.log('插件事件:', eventId, eventParam);

    switch (eventId) {
      case 'pageReady':
        this.onPageReady(eventParam);
        break;
      case 'userAction':
        this.onUserAction(eventParam);
        break;
      default:
        console.log('未知插件事件:', eventId);
    }
  }

  private onPageReady(param?: Record<string, Object>) {
    console.log('H5页面准备就绪:', param);
  }

  private onUserAction(param?: Record<string, Object>) {
    console.log('用户操作事件:', param);
  }

  // 设置回调函数
  setLoadingCallback(callback: (isLoading: boolean) => void) {
    this.loadingCallback = callback;
  }

  setErrorCallback(callback: (error: string) => void) {
    this.errorCallback = callback;
  }

  // 获取配置
  getWebAttribute(): TKWebAttribute {
    return this.webAttribute;
  }

  getEventController(): TKWebAttrEventController {
    return this.webEventController;
  }
}

// 使用高级WebView管理器的组件
@Component
struct AdvancedWebView {
  private webManager = new WebViewManager();
  @State private isLoading: boolean = false;
  @State private errorMessage: string = '';
  @State private currentUrl: string = 'https://www.example.com';

  aboutToAppear() {
    // 设置回调
    this.webManager.setLoadingCallback((isLoading) => {
      this.isLoading = isLoading;
    });

    this.webManager.setErrorCallback((error) => {
      this.errorMessage = error;
    });
  }

  build() {
    Stack() {
      // WebView组件
      TKWeb({
        src: this.currentUrl,
        attribute: this.webManager.getWebAttribute(),
        webAttrEventController: this.webManager.getEventController()
      })

      // 自定义加载指示器
      if (this.isLoading) {
        Column() {
          LoadingProgress()
            .width(50)
            .height(50)
            .color(Color.Blue)

          Text('加载中...')
            .fontSize(14)
            .fontColor(Color.Gray)
            .margin({ top: 10 })
        }
        .width('100%')
        .height('100%')
        .backgroundColor(Color.White)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
      }

      // 自定义错误页面
      if (this.errorMessage) {
        Column() {
          Image($r('app.media.error_icon'))
            .width(80)
            .height(80)
            .margin({ bottom: 20 })

          Text(this.errorMessage)
            .fontSize(16)
            .fontColor(Color.Red)
            .textAlign(TextAlign.Center)
            .margin({ bottom: 20 })

          Button('重试')
            .onClick(() => {
              this.errorMessage = '';
              this.currentUrl = this.currentUrl + '?t=' + Date.now(); // 强制刷新
            })
        }
        .width('100%')
        .height('100%')
        .backgroundColor(Color.White)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
  }
}
```

### 扩展示例
```typescript
// WebView池管理器
class WebViewPoolManager {
  private static instance: WebViewPoolManager;
  private webViewPool: Map<string, any> = new Map();
  private maxPoolSize: number = 5;

  static getInstance(): WebViewPoolManager {
    if (!WebViewPoolManager.instance) {
      WebViewPoolManager.instance = new WebViewPoolManager();
    }
    return WebViewPoolManager.instance;
  }

  // 获取或创建WebView
  getWebView(url: string): any {
    let webView = this.webViewPool.get(url);

    if (!webView) {
      webView = this.createWebView(url);

      // 控制池大小
      if (this.webViewPool.size >= this.maxPoolSize) {
        const firstKey = this.webViewPool.keys().next().value;
        this.webViewPool.delete(firstKey);
      }

      this.webViewPool.set(url, webView);
    }

    return webView;
  }

  private createWebView(url: string): any {
    const webAttribute = new TKWebAttribute();
    webAttribute.moduleName = `Pool_${Date.now()}`;
    webAttribute.loadTimeout = 30000;

    return {
      url: url,
      attribute: webAttribute,
      createTime: Date.now(),
      lastUsed: Date.now()
    };
  }

  // 清理过期WebView
  cleanupExpiredWebViews(maxAge: number = 300000) { // 5分钟
    const now = Date.now();

    for (const [url, webView] of this.webViewPool) {
      if (now - webView.lastUsed > maxAge) {
        this.webViewPool.delete(url);
        console.log(`清理过期WebView: ${url}`);
      }
    }
  }

  // 获取池状态
  getPoolStatus(): {
    size: number,
    maxSize: number,
    urls: string[]
  } {
    return {
      size: this.webViewPool.size,
      maxSize: this.maxPoolSize,
      urls: Array.from(this.webViewPool.keys())
    };
  }
}

// H5与原生通信助手
class H5NativeBridge {
  private jsProxyController?: any;

  setJSProxyController(controller: any) {
    this.jsProxyController = controller;
  }

  // 调用H5函数
  async callH5Function(functionName: string, params: any[]): Promise<string | undefined> {
    if (!this.jsProxyController) {
      console.error('JSProxyController未设置');
      return undefined;
    }

    const javascript = `${functionName}(${params.map(p => JSON.stringify(p)).join(',')})`;
    return await this.jsProxyController.runJavaScript(javascript);
  }

  // 发送消息给H5
  sendMessageToH5(message: Record<string, any>) {
    if (this.jsProxyController) {
      this.jsProxyController.sendPlugMsgToH5(message);
    }
  }

  // 发送流水号消息给H5
  sendFlowNoMessageToH5(flowNo: string, data: Record<string, any>) {
    if (this.jsProxyController) {
      this.jsProxyController.sendFlowNoMsgToH5(flowNo, data);
    }
  }

  // 批量调用H5函数
  async batchCallH5Functions(calls: Array<{
    functionName: string,
    params: any[]
  }>): Promise<Array<string | undefined>> {
    const results: Array<string | undefined> = [];

    for (const call of calls) {
      const result = await this.callH5Function(call.functionName, call.params);
      results.push(result);
    }

    return results;
  }
}

// 使用扩展功能的组件
@Component
struct ExtendedWebView {
  private webViewPool = WebViewPoolManager.getInstance();
  private h5Bridge = new H5NativeBridge();
  @State private currentWebView: any = null;

  aboutToAppear() {
    // 获取WebView配置
    this.currentWebView = this.webViewPool.getWebView('https://www.example.com');

    // 定期清理过期WebView
    setInterval(() => {
      this.webViewPool.cleanupExpiredWebViews();
    }, 60000); // 每分钟清理一次
  }

  build() {
    Column() {
      // 显示池状态
      Text(`WebView池状态: ${JSON.stringify(this.webViewPool.getPoolStatus())}`)
        .fontSize(12)
        .margin(10)

      // WebView组件
      if (this.currentWebView) {
        TKWeb({
          src: this.currentWebView.url,
          attribute: this.currentWebView.attribute,
          webAttrEventController: TKWebAttrEventController.builder()
            .onControllerAttached(() => {
              console.log('WebView控制器已附加');
              // 设置桥接控制器
              // this.h5Bridge.setJSProxyController(jsProxyController);
            })
            .onPageEnd(() => {
              // 页面加载完成后调用H5函数
              this.testH5Communication();
            })
        })
      }

      // 控制按钮
      Row() {
        Button('调用H5函数')
          .onClick(async () => {
            const result = await this.h5Bridge.callH5Function('testFunction', ['hello', 123]);
            console.log('H5函数调用结果:', result);
          })

        Button('发送消息')
          .onClick(() => {
            this.h5Bridge.sendMessageToH5({
              type: 'notification',
              message: 'Hello from Native',
              timestamp: Date.now()
            });
          })
      }
      .justifyContent(FlexAlign.SpaceEvenly)
      .width('100%')
      .margin(10)
    }
    .width('100%')
    .height('100%')
  }

  private async testH5Communication() {
    // 批量调用H5函数
    const results = await this.h5Bridge.batchCallH5Functions([
      { functionName: 'getPageTitle', params: [] },
      { functionName: 'getUserInfo', params: [] },
      { functionName: 'getPageData', params: ['section1'] }
    ]);

    console.log('批量调用结果:', results);
  }
}
```

## 📚 相关资源

### 官方文档
- HarmonyOS WebView开发指南
- H5与原生交互最佳实践

### 参考资料
- 《混合开发实战》- WebView与原生交互
- 《JavaScript桥接技术》- JS Bridge实现原理

### 相关文件
- `TKJSProxyController.ets` - JS代理控制器
- `TKWebAttrEventController.ets` - Web事件控制器
- `TKWebPage.ets` - Web页面组件
