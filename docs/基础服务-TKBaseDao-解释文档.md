# TKBaseDao

> **文件路径**: `src/main/ets/base/mvc/service/dao/TKBaseDao.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐⭐ (4星)  
> **学习难度**: 中级  
> **前置知识**: DAO模式、抽象类设计、MVC架构、数据访问层概念

## 📋 文件概述

### 功能定位
TKBaseDao是HMThinkBaseHar框架**数据访问对象的抽象基类**，实现了DAO模式的基础功能。它定义了数据访问层的通用逻辑，包括请求预处理、公共请求头构建、调试日志记录等，为具体的数据访问实现类提供统一的基础能力。

### 核心职责
- **请求预处理**：统一处理请求对象的初始化和预处理逻辑
- **公共请求头构建**：自动添加通用的HTTP请求头信息
- **调试日志记录**：提供详细的请求调试信息输出
- **接口规范实现**：实现TKServiceDaoDelegate接口的基础方法
- **抽象层定义**：为子类提供可扩展的抽象基础

### 设计模式
- **DAO模式**：数据访问对象模式，分离数据访问逻辑
- **模板方法模式**：定义算法骨架，子类实现具体细节
- **抽象类模式**：提供部分实现，强制子类实现关键方法

## 🔧 核心功能

### 主要API/方法

#### 方法1：`invoke(reqParamVO: TKReqParamVO)`
- **功能**: 处理请求的入口方法，执行请求预处理
- **参数**: TKReqParamVO - 请求参数对象
- **返回值**: void
- **使用场景**: 所有数据访问请求的统一入口点

#### 方法2：`clearRequest(flowNo: string)`
- **功能**: 清理指定流水号的请求（抽象方法，子类实现）
- **参数**: string - 请求流水号
- **返回值**: void
- **使用场景**: 需要取消或清理特定请求时

#### 方法3：`clearGroup(groupNo: string)`
- **功能**: 清理指定组号的所有请求（抽象方法，子类实现）
- **参数**: string - 请求组号
- **返回值**: void
- **使用场景**: 需要批量清理某个组的请求时

### 关键私有方法

#### 方法1：`buildCommonHttpHead(reqParamVO: TKReqParamVO)`
- **功能**: 构建公共HTTP请求头
- **参数**: TKReqParamVO - 请求参数对象
- **处理内容**: 自动添加User-Agent等通用请求头

#### 方法2：`debugRequest(reqParamVO: TKReqParamVO)`
- **功能**: 输出请求的调试信息
- **参数**: TKReqParamVO - 请求参数对象
- **输出内容**: URL、流水号、请求参数等详细信息

## 💡 技术要点

### 核心算法/逻辑

#### 请求预处理流程
```typescript
public invoke(reqParamVO: TKReqParamVO) {
  // 1. 输出调试信息
  this.debugRequest(reqParamVO);
  
  // 2. 记录请求开始时间
  reqParamVO.beginTime = new Date().getTime();
  
  // 3. 构建公共请求头
  this.buildCommonHttpHead(reqParamVO);
}
```

#### 公共请求头构建
```typescript
private buildCommonHttpHead(reqParamVO: TKReqParamVO) {
  let headerFieldMap: Record<string, Object | undefined> = {};
  
  // 1. 保留已有的请求头
  if (reqParamVO.headerFieldDic) {
    headerFieldMap = reqParamVO.headerFieldDic;
  }
  
  // 2. 设置User-Agent
  let userAgent: string = TKMapHelper.getString(
    headerFieldMap, 
    "User-Agent", 
    TKSystemHelper.getConfig("webViewPool.userAgent")
  );
  
  if (TKStringHelper.isNotBlank(userAgent)) {
    headerFieldMap["User-Agent"] = userAgent;
  }
  
  // 3. 更新请求头
  reqParamVO.headerFieldDic = headerFieldMap;
}
```

#### 调试信息输出
```typescript
private debugRequest(reqParamVO: TKReqParamVO) {
  let reqStr: string = "";
  let reqParam: Record<string, Object | undefined> = reqParamVO.reqParam;
  
  if (reqParam) {
    Object.entries(reqParam).forEach((e, i) => {
      let key: string = e[0] as string;
      let value: Object = e[1] as Object;
      
      // 过滤特殊参数和空值
      if (!TKStringHelper.endsWith(key, "@@F") && TKObjectHelper.nonNull(value)) {
        reqStr += `${key}:${value}\n`;
      }
    });
  }
  
  // 输出格式化的调试信息
  TKLog.debug(`
-------请求URL---------
${reqParamVO.url}
-------请求流水号-------
flowNo:${reqParamVO.flowNo}
-------请求参数begin-------
${reqStr}-------请求参数end--------`);
}
```

### 实现机制分析

#### DAO模式实现
- **抽象基类**：TKBaseDao定义了数据访问的基础结构
- **接口实现**：实现TKServiceDaoDelegate接口，确保规范一致性
- **模板方法**：invoke方法定义了请求处理的标准流程
- **扩展点**：clearRequest和clearGroup为抽象方法，由子类实现

#### 请求生命周期管理
1. **请求开始**：记录beginTime时间戳
2. **预处理**：构建公共请求头和调试信息
3. **执行处理**：由子类实现具体的数据访问逻辑
4. **清理管理**：提供请求清理的抽象接口

### 性能考虑
- **轻量级预处理**：只进行必要的公共处理，避免性能开销
- **条件调试**：调试信息只在需要时输出，不影响生产性能
- **请求头复用**：保留已有请求头，避免重复设置
- **时间记录**：精确记录请求开始时间，便于性能分析

### 错误处理
- **空值检查**：对请求参数进行空值验证
- **异常隔离**：基类异常不影响子类的具体实现
- **日志记录**：通过TKLog记录调试和错误信息
- **优雅降级**：请求头构建失败时不影响主要流程

### 最佳实践
- **单一职责**：基类只处理通用逻辑，具体实现由子类负责
- **开闭原则**：对扩展开放，对修改封闭
- **依赖注入**：通过参数传递依赖，降低耦合
- **接口隔离**：实现标准接口，确保一致性

## 🔗 依赖关系

### 依赖的模块
- `TKServiceDaoDelegate` - 服务DAO接口，定义数据访问规范
- `TKReqParamVO` - 请求参数对象，数据访问的载体
- `TKLog` - 日志工具，记录调试和错误信息
- `TKStringHelper` - 字符串工具，参数验证和处理
- `TKSystemHelper` - 系统工具，获取配置信息

### 被依赖情况
- `TKBaseHttpDao` - HTTP数据访问基类，继承TKBaseDao
- `TKHttpDao` - HTTP数据访问实现类，间接继承TKBaseDao
- `TKBusDao` - Socket数据访问类，可能继承TKBaseDao
- `TKDaoFactory` - DAO工厂，创建TKBaseDao的子类实例

### 关联文件
- `TKBaseHttpDao.ets` - HTTP数据访问基类，直接继承者
- `TKHttpDao.ets` - HTTP数据访问实现，具体实现类
- `TKDaoFactory.ets` - DAO工厂，管理DAO实例创建
- `TKServiceDaoDelegate.ets` - 服务DAO接口定义

## 📊 重要程度评级

### 评级标准
⭐⭐⭐⭐ **重要推荐**: 常用功能，建议掌握

### 本文件评级理由
TKBaseDao是数据访问层的基础架构组件：
1. **架构基础**：定义了数据访问层的基础结构和规范
2. **模式实现**：体现了DAO模式和模板方法模式的经典应用
3. **扩展性**：为不同类型的数据访问提供统一的基础能力
4. **规范性**：确保所有数据访问实现的一致性和标准化

理解TKBaseDao是掌握框架数据访问机制的基础，它是MVC架构中数据层的核心组件。

## 🎯 学习建议

### 学习路径
1. **理解DAO模式**：掌握数据访问对象模式的设计思想
2. **分析抽象类设计**：理解抽象类与接口的区别和应用
3. **学习模板方法模式**：了解如何定义算法骨架
4. **实践继承扩展**：创建自定义的数据访问实现类

### 前置学习
- DAO模式的基本概念和应用
- 抽象类和接口的设计原理
- MVC架构中数据访问层的作用
- TypeScript继承和多态机制

### 后续学习
- `TKBaseHttpDao.ets` - 了解HTTP数据访问的具体实现
- `TKHttpDao.ets` - 学习完整的HTTP数据访问功能
- `TKDaoFactory.ets` - 掌握DAO工厂的创建和管理
- `TKServiceDaoDelegate.ets` - 理解数据访问接口规范

### 实践建议
1. **继承实现练习**：创建继承TKBaseDao的自定义数据访问类
2. **请求处理实验**：观察invoke方法的预处理流程
3. **调试信息分析**：启用调试模式，分析请求信息输出
4. **扩展功能开发**：在基类基础上添加自定义的通用功能

### 常见问题
1. **问题**: 为什么使用抽象类而不是接口？
   **解答**: 抽象类可以提供部分实现（如公共请求头构建），而接口只能定义规范。这样既保证了规范性，又避免了重复代码。

2. **问题**: clearRequest和clearGroup方法为什么是空实现？
   **解答**: 这两个方法在基类中是抽象概念，具体的清理逻辑依赖于子类的实现方式（如HTTP请求的取消、Socket连接的关闭等）。

## 📝 代码示例

### 基础使用
```typescript
import { TKBaseDao, TKReqParamVO, TKServiceDaoDelegate } from '@thinkive/tk-harmony-base';

// 继承TKBaseDao创建自定义数据访问类
class CustomFileDao extends TKBaseDao {
  private fileRequests: Map<string, any> = new Map();

  // 重写invoke方法，添加自定义逻辑
  public invoke(reqParamVO: TKReqParamVO) {
    // 调用父类的预处理逻辑
    super.invoke(reqParamVO);

    // 添加自定义的文件访问逻辑
    console.log(`开始处理文件访问请求: ${reqParamVO.flowNo}`);

    // 存储请求信息
    this.fileRequests.set(reqParamVO.flowNo, {
      reqParamVO: reqParamVO,
      startTime: Date.now(),
      status: 'processing'
    });

    // 执行具体的文件访问逻辑
    this.processFileRequest(reqParamVO);
  }

  // 实现抽象方法：清理单个请求
  public clearRequest(flowNo: string) {
    const request = this.fileRequests.get(flowNo);
    if (request) {
      console.log(`清理文件请求: ${flowNo}`);
      // 取消文件操作
      this.cancelFileOperation(flowNo);
      // 从映射中移除
      this.fileRequests.delete(flowNo);
    }
  }

  // 实现抽象方法：清理组请求
  public clearGroup(groupNo: string) {
    console.log(`清理文件请求组: ${groupNo}`);

    // 遍历所有请求，清理指定组的请求
    for (const [flowNo, request] of this.fileRequests) {
      if (request.reqParamVO.group === groupNo) {
        this.clearRequest(flowNo);
      }
    }
  }

  private processFileRequest(reqParamVO: TKReqParamVO) {
    // 模拟文件访问处理
    setTimeout(() => {
      console.log(`文件访问完成: ${reqParamVO.flowNo}`);
      this.fileRequests.delete(reqParamVO.flowNo);
    }, 1000);
  }

  private cancelFileOperation(flowNo: string) {
    // 取消文件操作的具体逻辑
    console.log(`取消文件操作: ${flowNo}`);
  }
}

// 使用自定义DAO
const fileDao = new CustomFileDao();

// 创建请求参数
const reqParam: TKReqParamVO = new TKReqParamVO();
reqParam.flowNo = "FILE_001";
reqParam.url = "/api/files/upload";
reqParam.reqParam = {
  fileName: "document.pdf",
  fileSize: 1024000
};
reqParam.group = "fileGroup";

// 执行请求
fileDao.invoke(reqParam);
```

### 高级用法
```typescript
// 带缓存功能的数据访问基类
abstract class CachedBaseDao extends TKBaseDao {
  private requestCache: Map<string, any> = new Map();
  private requestTimestamps: Map<string, number> = new Map();

  public invoke(reqParamVO: TKReqParamVO) {
    // 调用父类预处理
    super.invoke(reqParamVO);

    // 检查缓存
    if (reqParamVO.isCache && this.isCacheValid(reqParamVO)) {
      const cachedResult = this.getCachedResult(reqParamVO);
      if (cachedResult) {
        console.log(`使用缓存结果: ${reqParamVO.flowNo}`);
        this.handleCachedResult(reqParamVO, cachedResult);
        return;
      }
    }

    // 执行实际请求
    this.executeRequest(reqParamVO);
  }

  // 抽象方法：子类实现具体的请求执行
  protected abstract executeRequest(reqParamVO: TKReqParamVO): void;

  // 缓存结果
  protected cacheResult(reqParamVO: TKReqParamVO, result: any) {
    if (reqParamVO.isCache) {
      const cacheKey = this.generateCacheKey(reqParamVO);
      this.requestCache.set(cacheKey, result);
      this.requestTimestamps.set(cacheKey, Date.now());

      console.log(`缓存结果: ${cacheKey}`);
    }
  }

  private isCacheValid(reqParamVO: TKReqParamVO): boolean {
    const cacheKey = this.generateCacheKey(reqParamVO);
    const timestamp = this.requestTimestamps.get(cacheKey);

    if (!timestamp) return false;

    const cacheTime = reqParamVO.cacheTime * 1000; // 转换为毫秒
    return (Date.now() - timestamp) < cacheTime;
  }

  private getCachedResult(reqParamVO: TKReqParamVO): any {
    const cacheKey = this.generateCacheKey(reqParamVO);
    return this.requestCache.get(cacheKey);
  }

  private generateCacheKey(reqParamVO: TKReqParamVO): string {
    return `${reqParamVO.url}_${JSON.stringify(reqParamVO.reqParam)}`;
  }

  private handleCachedResult(reqParamVO: TKReqParamVO, result: any) {
    // 处理缓存结果的逻辑
    console.log("处理缓存结果:", result);
  }

  public clearRequest(flowNo: string) {
    // 基础清理逻辑
    console.log(`清理请求: ${flowNo}`);
  }

  public clearGroup(groupNo: string) {
    // 基础组清理逻辑
    console.log(`清理请求组: ${groupNo}`);
  }

  // 清理过期缓存
  public clearExpiredCache() {
    const now = Date.now();

    for (const [key, timestamp] of this.requestTimestamps) {
      if (now - timestamp > 3600000) { // 1小时过期
        this.requestCache.delete(key);
        this.requestTimestamps.delete(key);
        console.log(`清理过期缓存: ${key}`);
      }
    }
  }
}

// 具体实现类
class ApiDao extends CachedBaseDao {
  protected executeRequest(reqParamVO: TKReqParamVO): void {
    console.log(`执行API请求: ${reqParamVO.url}`);

    // 模拟API请求
    setTimeout(() => {
      const result = {
        code: 0,
        message: "success",
        data: { id: 1, name: "test" }
      };

      // 缓存结果
      this.cacheResult(reqParamVO, result);

      console.log(`API请求完成: ${reqParamVO.flowNo}`);
    }, 500);
  }
}
```

### 扩展示例
```typescript
// DAO基类的监控和统计扩展
class MonitoredBaseDao extends TKBaseDao {
  private static requestStats = {
    totalRequests: 0,
    successRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0
  };

  private requestStartTimes: Map<string, number> = new Map();

  public invoke(reqParamVO: TKReqParamVO) {
    // 调用父类预处理
    super.invoke(reqParamVO);

    // 记录请求开始时间
    this.requestStartTimes.set(reqParamVO.flowNo, Date.now());

    // 更新统计信息
    MonitoredBaseDao.requestStats.totalRequests++;

    console.log(`[监控] 请求开始: ${reqParamVO.flowNo}`);
    console.log(`[统计] 总请求数: ${MonitoredBaseDao.requestStats.totalRequests}`);
  }

  // 请求成功时调用
  protected onRequestSuccess(flowNo: string, result: any) {
    this.updateRequestStats(flowNo, true);
    console.log(`[监控] 请求成功: ${flowNo}`);
  }

  // 请求失败时调用
  protected onRequestFailure(flowNo: string, error: any) {
    this.updateRequestStats(flowNo, false);
    console.log(`[监控] 请求失败: ${flowNo}`, error);
  }

  private updateRequestStats(flowNo: string, success: boolean) {
    const startTime = this.requestStartTimes.get(flowNo);
    if (startTime) {
      const responseTime = Date.now() - startTime;

      // 更新平均响应时间
      const stats = MonitoredBaseDao.requestStats;
      stats.averageResponseTime =
        (stats.averageResponseTime * (stats.totalRequests - 1) + responseTime) / stats.totalRequests;

      if (success) {
        stats.successRequests++;
      } else {
        stats.failedRequests++;
      }

      this.requestStartTimes.delete(flowNo);

      console.log(`[统计] 响应时间: ${responseTime}ms, 平均响应时间: ${stats.averageResponseTime.toFixed(2)}ms`);
      console.log(`[统计] 成功率: ${(stats.successRequests / stats.totalRequests * 100).toFixed(2)}%`);
    }
  }

  // 获取统计信息
  public static getRequestStats() {
    return { ...MonitoredBaseDao.requestStats };
  }

  // 重置统计信息
  public static resetRequestStats() {
    MonitoredBaseDao.requestStats = {
      totalRequests: 0,
      successRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0
    };
  }

  public clearRequest(flowNo: string) {
    // 清理监控数据
    this.requestStartTimes.delete(flowNo);
    console.log(`[监控] 清理请求: ${flowNo}`);
  }

  public clearGroup(groupNo: string) {
    console.log(`[监控] 清理请求组: ${groupNo}`);
  }
}

// 使用监控DAO
class MonitoredApiDao extends MonitoredBaseDao {
  public invoke(reqParamVO: TKReqParamVO) {
    super.invoke(reqParamVO);

    // 模拟API调用
    setTimeout(() => {
      if (Math.random() > 0.2) { // 80%成功率
        this.onRequestSuccess(reqParamVO.flowNo, { data: "success" });
      } else {
        this.onRequestFailure(reqParamVO.flowNo, new Error("网络错误"));
      }
    }, Math.random() * 1000 + 100); // 100-1100ms随机响应时间
  }
}

// 使用示例
const monitoredDao = new MonitoredApiDao();

// 发送多个测试请求
for (let i = 0; i < 10; i++) {
  const reqParam = new TKReqParamVO();
  reqParam.flowNo = `TEST_${i}`;
  reqParam.url = `/api/test/${i}`;

  monitoredDao.invoke(reqParam);
}

// 5秒后查看统计信息
setTimeout(() => {
  console.log("最终统计信息:", MonitoredBaseDao.getRequestStats());
}, 5000);
```

## 📚 相关资源

### 官方文档
- TypeScript官方文档 - 抽象类和继承
- 设计模式文档 - DAO模式和模板方法模式

### 参考资料
- 《设计模式：可复用面向对象软件的基础》- DAO模式
- 《企业应用架构模式》- 数据访问层设计

### 相关文件
- `基础服务-TKBaseHttpDao-解释文档.md` - HTTP数据访问基类
- `基础服务-TKDaoFactory-解释文档.md` - DAO工厂管理
- `TKServiceDaoDelegate.ets` - 数据访问接口规范
