# TKRouterHelper

> **文件路径**: `src/main/ets/base/router/TKRouterHelper.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐⭐ (4星)  
> **学习难度**: 中级  
> **前置知识**: 鸿蒙路由系统、Navigation导航、拦截器模式、Promise异步编程

## 📋 文件概述

### 功能定位
TKRouterHelper是HMThinkBaseHar框架的**路由辅助工具**，提供了完整的页面跳转、返回和拦截功能。它封装了鸿蒙系统的路由API，同时支持传统路由和Navigation导航，为应用提供统一、强大的路由管理能力。

### 核心职责
- **路由跳转管理**：提供pushUrl、replaceUrl、pushNamedRoute等跳转方法
- **路由拦截控制**：支持前置和后置拦截器，实现权限控制和日志记录
- **Navigation支持**：同时支持传统路由和Navigation导航系统
- **页面返回处理**：提供智能的页面返回和结果回调机制
- **状态管理集成**：与TKRouterPageManager协作维护页面状态

### 设计模式
- **命名空间模式**：使用namespace组织相关功能
- **拦截器模式**：支持路由前后的拦截处理
- **策略模式**：支持多种路由模式和导航方式
- **观察者模式**：通过回调函数实现页面间通信

## 🔧 核心功能

### 主要API/方法

#### 方法1：`pushUrl(routerPageInfo: TKRouterPageInfo)`
- **功能**: 通过URL路径跳转到新页面
- **参数**: TKRouterPageInfo - 路由信息对象
- **返回值**: Promise<void>
- **使用场景**: 需要跳转到指定URL路径的页面时

#### 方法2：`replaceUrl(routerPageInfo: TKRouterPageInfo)`
- **功能**: 替换当前页面为新的URL页面
- **参数**: TKRouterPageInfo - 路由信息对象
- **返回值**: Promise<void>
- **使用场景**: 需要替换当前页面而不增加历史记录时

#### 方法3：`pushNamedRoute(routerPageInfo: TKRouterPageInfo)`
- **功能**: 通过命名路由跳转到新页面
- **参数**: TKRouterPageInfo - 路由信息对象
- **返回值**: Promise<void>
- **使用场景**: 使用命名路由进行页面跳转时

#### 方法4：`back(option: TKRouterBackOption)`
- **功能**: 返回上一页面，支持结果回调
- **参数**: TKRouterBackOption - 返回选项，包含返回结果等
- **返回值**: Promise<void>
- **使用场景**: 需要返回上一页面并传递结果数据时

#### 方法5：`registerRouterFilter(filter: TKRouterFilter)`
- **功能**: 注册路由拦截器
- **参数**: TKRouterFilter - 拦截器对象
- **返回值**: void
- **使用场景**: 需要对路由进行权限控制或日志记录时

### 关键属性/配置
- `routerFilters: Array<TKRouterFilter>` - 路由拦截器数组
- `navPathStackMap: Map<string, NavPathStack>` - Navigation导航栈映射
- `isCanExitApp: boolean` - 是否可以退出应用的标识

## 💡 技术要点

### 核心算法/逻辑

#### 统一路由处理机制
```typescript
function urlOrNameRoute(routerPageInfo: TKRouterPageInfo, isReplace: boolean, isUrlMode: boolean): Promise<void> {
  return new Promise(async (resolve, reject) => {
    try {
      // 1. 前置拦截器处理
      if (!await executeBeforeFilters(routerPageInfo)) {
        reject(new Error("路由被前置拦截器拦截"));
        return;
      }
      
      // 2. 处理单例模式的页面状态转移
      if (routerPageInfo.mode == router.RouterMode.Single) {
        singleOffsetSrcPageStyle(routerPageInfo, routerPageInfo.url ?? routerPageInfo.name ?? "");
      }
      
      // 3. 初始化路由参数
      buildRouterPageInfo(routerPageInfo);
      
      // 4. 注册结果观察者
      if (routerPageInfo.onResult) {
        TKRouterResultObserverManager.shareInstance()
          .register(routerPageInfo.flowNo!, { onResult: routerPageInfo.onResult });
      }
      
      // 5. 执行实际路由跳转
      await performRouterNavigation(routerPageInfo, isReplace, isUrlMode);
      
      // 6. 后置拦截器处理
      await executeAfterFilters(routerPageInfo);
      
      resolve();
    } catch (error) {
      reject(error);
    }
  });
}
```

#### 拦截器执行机制
```typescript
async function executeBeforeFilters(routerPageInfo: TKRouterPageInfo): Promise<boolean> {
  for (const filter of routerFilters) {
    if (filter.before) {
      const result = await filter.before(routerPageInfo);
      if (!result) {
        TKLog.warn(`路由被拦截器拦截: ${routerPageInfo.url || routerPageInfo.name}`);
        return false;
      }
    }
  }
  return true;
}
```

#### Navigation导航支持
```typescript
// 获取当前Navigation栈
function getCurNavPathStack(navComponentOrPathStack?: CustomComponent | NavPathStack): NavPathStack | undefined {
  if (navComponentOrPathStack instanceof NavPathStack) {
    return navComponentOrPathStack;
  }
  // 从映射中获取当前活跃的导航栈
  return navPathStackMap.get(curNavPathStackName);
}

// 执行导航操作
async function performNavigation(routerPageInfo: TKRouterPageInfo, isReplace: boolean) {
  const navPathStack = getCurNavPathStack(routerPageInfo.navComponentOrPathStack);
  
  if (navPathStack) {
    // 使用Navigation导航
    if (isReplace) {
      await navPathStack.replacePathByName(routerPageInfo.name, routerPageInfo.params);
    } else {
      await navPathStack.pushPathByName(routerPageInfo.name, routerPageInfo.params);
    }
  } else {
    // 使用传统路由
    if (isReplace) {
      await router.replaceNamedRoute({ name: routerPageInfo.name, params: routerPageInfo.params }, routerPageInfo.mode);
    } else {
      await router.pushNamedRoute({ name: routerPageInfo.name, params: routerPageInfo.params }, routerPageInfo.mode);
    }
  }
}
```

#### 智能返回处理
```typescript
export async function back(option: TKRouterBackOption = {}) {
  try {
    // 1. 检查是否在栈顶（防止退出应用）
    if (isRouteStackTop(option.navComponentOrPathStack)) {
      if (!isCanExitApp) {
        isCanExitApp = true;
        TKDialogHelper.showSysToast("再按一次将退出程序到后台");
        setTimeout(() => { isCanExitApp = false; }, 2000);
        return;
      }
    }
    
    // 2. 获取当前页面信息
    let urlOrName = TKRouterPageManager.shareInstance().getRoutePageUrlOrName(getRouteState(option.navComponentOrPathStack));
    
    // 3. 防止重复返回
    if (prevBackRoutePage != urlOrName) {
      prevBackRoutePage = urlOrName;
      setTimeout(() => { prevBackRoutePage = ""; }, 1000);
      
      // 4. 处理返回结果回调
      let hisRouterInfo = TKRouterPageManager.shareInstance().get(urlOrName);
      if (hisRouterInfo && hisRouterInfo.flowNo) {
        let resultObserver = TKRouterResultObserverManager.shareInstance().get(hisRouterInfo.flowNo);
        if (option.result && resultObserver) {
          resultObserver.onResult(option.result);
        }
      }
      
      // 5. 执行实际返回操作
      if (option.isRouterBack) {
        await performBackNavigation(option);
      }
    }
  } catch (error) {
    TKLog.error(`页面返回失败: ${error.message}`);
  }
}
```

### 性能考虑
- **拦截器优化**：拦截器按顺序执行，任一拦截器返回false即停止后续处理
- **状态缓存**：使用Map缓存Navigation栈，避免重复查找
- **防重复处理**：通过时间戳和标识符防止重复的路由操作
- **异步处理**：所有路由操作都是异步的，不阻塞UI线程

### 错误处理
- **拦截器异常**：拦截器执行异常时记录日志并继续执行
- **路由失败处理**：路由跳转失败时提供详细的错误信息
- **参数验证**：对路由参数进行完整性检查
- **状态恢复**：路由失败时自动恢复相关状态

### 最佳实践
- **统一接口**：提供统一的路由接口，屏蔽底层实现差异
- **拦截器链**：支持多个拦截器的链式处理
- **状态同步**：与页面管理器协作，保持状态一致性
- **错误隔离**：单个路由操作的异常不影响其他功能

## 🔗 依赖关系

### 依赖的模块
- `TKRouterPageInfo` - 路由信息定义，路由操作的数据载体
- `TKRouterPageManager` - 页面管理器，维护页面栈状态
- `TKRouterResultObserverManager` - 结果观察者管理器
- `router` - 鸿蒙系统路由API
- `TKLog` - 日志工具，记录路由操作日志

### 被依赖情况
- `各页面组件` - 通过路由辅助类进行页面跳转
- `业务模块` - 使用路由功能进行页面导航
- `拦截器实现` - 注册到路由辅助类进行拦截处理

### 关联文件
- `TKRouterPageManager.ets` - 页面管理器，路由状态的维护者
- `TKRouterPageInfo.ets` - 路由信息定义，数据结构规范
- `TKRouterResultObserverManager.ets` - 结果观察者管理器

## 📊 重要程度评级

### 评级标准
⭐⭐⭐⭐ **重要推荐**: 常用功能，建议掌握

### 本文件评级理由
TKRouterHelper是框架路由功能的核心实现：
1. **功能完整**：提供了完整的路由跳转、返回和拦截功能
2. **系统集成**：深度集成鸿蒙路由系统和Navigation导航
3. **扩展性强**：支持拦截器扩展和多种路由模式
4. **易用性好**：提供统一的API接口，简化路由操作

理解TKRouterHelper是掌握框架路由机制的关键，它是页面导航功能的核心组件。

## 🎯 学习建议

### 学习路径
1. **理解路由基础**：掌握鸿蒙路由系统和Navigation导航的基本概念
2. **学习拦截器模式**：了解如何实现路由拦截和控制
3. **分析跳转流程**：理解从路由请求到页面显示的完整流程
4. **实践路由功能**：创建自定义的路由拦截器和页面跳转

### 前置学习
- 鸿蒙路由系统基础知识
- Navigation导航组件使用
- Promise异步编程
- 拦截器模式的实现原理

### 后续学习
- `TKRouterPageManager.ets` - 了解页面状态管理
- `TKRouterPageInfo.ets` - 学习路由信息的数据结构
- 各页面组件实现 - 查看路由的实际应用

### 实践建议
1. **基础跳转练习**：实现简单的页面跳转功能
2. **拦截器开发**：创建权限检查和日志记录拦截器
3. **返回结果处理**：实现页面间的数据传递
4. **Navigation集成**：在Navigation导航中使用路由功能

### 常见问题
1. **问题**: 传统路由和Navigation导航有什么区别？
   **解答**: 传统路由基于页面栈管理，Navigation导航基于组件栈管理。框架自动检测并使用合适的导航方式，开发者使用统一的API。

2. **问题**: 拦截器的执行顺序是怎样的？
   **解答**: 前置拦截器按注册顺序执行，任一拦截器返回false即停止路由；后置拦截器在路由成功后执行，用于日志记录等后处理。

## 📝 代码示例

### 基础使用
```typescript
import { TKRouterHelper, TKRouterPageInfo } from '@thinkive/tk-harmony-base';

// 基本的页面跳转
async function navigateToUserDetail(userId: string) {
  const routerInfo: TKRouterPageInfo = {
    url: 'pages/UserDetailPage',
    params: {
      userId: userId,
      from: 'userList'
    },
    // 设置结果回调
    onResult: (result) => {
      console.log('用户详情页返回结果:', result);
      if (result.action === 'update') {
        refreshUserList();
      }
    }
  };

  try {
    await TKRouterHelper.pushUrl(routerInfo);
    console.log('页面跳转成功');
  } catch (error) {
    console.error('页面跳转失败:', error);
  }
}

// 命名路由跳转
async function navigateToSettings() {
  const routerInfo: TKRouterPageInfo = {
    name: 'SettingsPage',
    params: {
      theme: 'dark',
      language: 'zh-CN'
    }
  };

  await TKRouterHelper.pushNamedRoute(routerInfo);
}

// 替换当前页面
async function replaceWithLoginPage() {
  const routerInfo: TKRouterPageInfo = {
    url: 'pages/LoginPage',
    params: {
      redirectUrl: 'pages/HomePage'
    }
  };

  await TKRouterHelper.replaceUrl(routerInfo);
}

// 页面返回
async function goBackWithResult() {
  await TKRouterHelper.back({
    result: {
      action: 'save',
      data: { name: '张三', age: 25 }
    }
  });
}

function refreshUserList() {
  console.log('刷新用户列表');
}
```

### 高级用法
```typescript
// 路由拦截器实现
class AuthRouterFilter implements TKRouterFilter {
  // 前置拦截器 - 权限检查
  async before(routerPageInfo: TKRouterPageInfo): Promise<boolean> {
    const protectedPages = ['pages/AdminPage', 'pages/VipPage'];
    const targetPage = routerPageInfo.url || routerPageInfo.name;

    if (protectedPages.includes(targetPage)) {
      const hasPermission = await this.checkUserPermission(targetPage);
      if (!hasPermission) {
        console.log(`权限不足，无法访问页面: ${targetPage}`);
        // 跳转到登录页
        await TKRouterHelper.pushUrl({
          url: 'pages/LoginPage',
          params: { redirectUrl: targetPage }
        });
        return false; // 拦截原路由
      }
    }

    return true; // 允许路由继续
  }

  // 后置拦截器 - 日志记录
  async after(routerPageInfo: TKRouterPageInfo): Promise<boolean> {
    const logData = {
      page: routerPageInfo.url || routerPageInfo.name,
      params: routerPageInfo.params,
      timestamp: new Date().toISOString(),
      userId: await this.getCurrentUserId()
    };

    console.log('页面访问日志:', logData);
    // 发送到日志服务
    await this.sendLogToServer(logData);

    return true;
  }

  // 页面显示拦截器
  async pageShow(pageName?: string, pageInfo?: Record<string, Object>): Promise<boolean> {
    console.log(`页面显示: ${pageName}`, pageInfo);
    // 可以在这里进行页面显示时的处理
    return true;
  }

  // 页面隐藏拦截器
  async pageHide(pageName?: string, pageInfo?: Record<string, Object>): Promise<boolean> {
    console.log(`页面隐藏: ${pageName}`, pageInfo);
    // 可以在这里进行页面隐藏时的清理工作
    return true;
  }

  private async checkUserPermission(page: string): Promise<boolean> {
    // 模拟权限检查
    const userRole = await this.getUserRole();
    return userRole === 'admin' || userRole === 'vip';
  }

  private async getCurrentUserId(): Promise<string> {
    // 获取当前用户ID
    return 'user123';
  }

  private async getUserRole(): Promise<string> {
    // 获取用户角色
    return 'normal';
  }

  private async sendLogToServer(logData: any): Promise<void> {
    // 发送日志到服务器
    console.log('发送日志到服务器:', logData);
  }
}

// 注册拦截器
const authFilter = new AuthRouterFilter();
TKRouterHelper.registerRouterFilter(authFilter);
```

### 扩展示例
```typescript
// 路由管理工具类
class RouterManager {
  private static routeHistory: Array<{page: string, timestamp: number}> = [];

  // 带历史记录的路由跳转
  static async navigateWithHistory(routerInfo: TKRouterPageInfo) {
    // 记录路由历史
    this.routeHistory.push({
      page: routerInfo.url || routerInfo.name || 'unknown',
      timestamp: Date.now()
    });

    // 限制历史记录长度
    if (this.routeHistory.length > 50) {
      this.routeHistory.shift();
    }

    // 执行路由跳转
    await TKRouterHelper.pushUrl(routerInfo);
  }

  // 获取路由历史
  static getRouteHistory(): Array<{page: string, timestamp: number}> {
    return [...this.routeHistory];
  }

  // 清空路由历史
  static clearRouteHistory() {
    this.routeHistory = [];
  }

  // 智能返回（支持多级返回）
  static async smartBack(levels: number = 1, result?: Record<string, Object>) {
    for (let i = 0; i < levels; i++) {
      if (i === levels - 1) {
        // 最后一次返回时传递结果
        await TKRouterHelper.back({ result });
      } else {
        await TKRouterHelper.back();
      }
    }
  }

  // 条件路由跳转
  static async conditionalNavigate(
    condition: () => Promise<boolean>,
    trueRoute: TKRouterPageInfo,
    falseRoute?: TKRouterPageInfo
  ) {
    const shouldNavigate = await condition();

    if (shouldNavigate) {
      await TKRouterHelper.pushUrl(trueRoute);
    } else if (falseRoute) {
      await TKRouterHelper.pushUrl(falseRoute);
    }
  }
}

// 页面跳转动画管理
class RouterAnimationManager {
  // 带动画的页面跳转
  static async animatedNavigate(routerInfo: TKRouterPageInfo, animation: 'slide' | 'fade' | 'zoom' = 'slide') {
    // 添加动画参数
    const animatedRouterInfo = {
      ...routerInfo,
      params: {
        ...routerInfo.params,
        '@animation': animation,
        '@animationDuration': 300
      }
    };

    await TKRouterHelper.pushUrl(animatedRouterInfo);
  }

  // 自定义转场动画
  static async customTransition(routerInfo: TKRouterPageInfo, transitionConfig: any) {
    const configuredRouterInfo = {
      ...routerInfo,
      params: {
        ...routerInfo.params,
        '@transitionConfig': transitionConfig
      }
    };

    await TKRouterHelper.pushUrl(configuredRouterInfo);
  }
}

// 使用示例
class MyPageComponent {
  // 使用路由管理工具
  async navigateToProfile() {
    await RouterManager.navigateWithHistory({
      url: 'pages/ProfilePage',
      params: { userId: 'current' }
    });
  }

  // 使用动画跳转
  async navigateWithAnimation() {
    await RouterAnimationManager.animatedNavigate({
      url: 'pages/DetailPage',
      params: { id: '123' }
    }, 'fade');
  }

  // 条件跳转
  async conditionalLogin() {
    await RouterManager.conditionalNavigate(
      async () => {
        // 检查是否已登录
        return await this.isUserLoggedIn();
      },
      { url: 'pages/HomePage' }, // 已登录跳转到首页
      { url: 'pages/LoginPage' }  // 未登录跳转到登录页
    );
  }

  // 多级返回
  async backToHome() {
    await RouterManager.smartBack(3, {
      action: 'backToHome',
      timestamp: Date.now()
    });
  }

  private async isUserLoggedIn(): Promise<boolean> {
    // 检查用户登录状态
    return false; // 示例返回
  }
}
```

## 📚 相关资源

### 官方文档
- HarmonyOS路由开发指南
- HarmonyOS Navigation导航组件

### 参考资料
- 《设计模式：可复用面向对象软件的基础》- 拦截器模式
- 《JavaScript异步编程》- Promise和async/await

### 相关文件
- `基础服务-TKRouterPageManager-解释文档.md` - 页面状态管理
- `TKRouterPageInfo.ets` - 路由信息数据结构
- `TKRouterResultObserverManager.ets` - 结果回调管理
