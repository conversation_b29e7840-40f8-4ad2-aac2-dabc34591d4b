# TKThemeStyleAttribute

> **文件路径**: `src/main/ets/base/theme/TKThemeStyleAttribute.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 观察者模式、响应式编程、主题系统、通知机制

## 📋 文件概述

### 功能定位
TKThemeStyleAttribute是HMThinkBaseHar框架的**主题样式属性类**，提供了响应式的主题样式管理功能。它采用观察者模式设计，集成了鸿蒙的@ObservedV2和@Trace装饰器，支持主题变更的自动响应、样式的实时更新等功能，为UI组件提供响应式的主题样式服务。

### 核心职责
- **响应式样式管理**：自动响应主题变更，实时更新样式数据
- **样式数据缓存**：缓存当前主题的样式映射，提供快速访问
- **通知监听机制**：监听主题变更通知，触发样式更新
- **样式查询服务**：提供样式查询接口，支持组件获取主题样式
- **UI自动刷新**：通过响应式机制触发UI的自动刷新

### 设计模式
- **观察者模式**：监听主题变更通知，实现响应式更新
- **代理模式**：作为主题管理器的代理，提供样式访问接口
- **单例模式**：通常作为组件的单例属性使用
- **装饰器模式**：使用鸿蒙装饰器实现响应式功能

## 🔧 核心功能

### 响应式属性

#### 属性1：`@Trace private style`
- **类型**: Map<string, TKCSSRuleset>
- **功能**: 存储当前主题的样式映射
- **响应式**: 使用@Trace装饰器，变更时自动触发UI更新
- **初始化**: 从主题管理器获取当前主题样式

### 核心方法

#### 方法1：`constructor()`
- **功能**: 构造函数，初始化主题样式属性
- **执行逻辑**: 注册通知监听，初始化样式数据
- **使用场景**: 创建主题样式属性实例时

#### 方法2：`getCssRulesetByClassName(className: string)`
- **功能**: 根据类名获取CSS规则集
- **参数**: className - CSS类名
- **返回值**: TKCSSRuleset - CSS规则集对象
- **使用场景**: 组件获取特定样式时

### 通知处理方法

#### 方法1：`listNotification()`
- **功能**: 定义需要监听的通知列表
- **返回值**: Array<string> - 通知名称数组
- **包含通知**: TKThemeManager.NOTE_THEME_CHANGED
- **使用场景**: 注册通知监听时

#### 方法2：`onRegisterNotification()`
- **功能**: 注册通知监听
- **执行逻辑**: 向通知中心注册主题变更监听
- **使用场景**: 初始化时自动调用

#### 方法3：`handleNotification(note: TKNotification)`
- **功能**: 处理接收到的通知
- **参数**: note - 通知对象
- **执行逻辑**: 主题变更时更新样式数据
- **使用场景**: 主题切换时自动调用

## 💡 技术要点

### 核心算法/逻辑

#### 响应式样式更新机制
```typescript
@ObservedV2
export class TKThemeStyleAttribute {
  @Trace private style: Map<string, TKCSSRuleset> = TKThemeManager.shareInstance().getCssRulesetMap();
  
  public constructor() {
    this.onRegisterNotification();
  }
  
  // 处理主题变更通知
  private handleNotification(note: TKNotification) {
    if (note.name == TKThemeManager.NOTE_THEME_CHANGED) {
      // 更新样式数据，触发UI自动刷新
      this.style = TKThemeManager.shareInstance().getCssRulesetMap();
    }
  }
}
```

#### 通知监听注册机制
```typescript
private onRegisterNotification() {
  let notes: Array<string> = this.listNotification();
  TKNotificationCenter.defaultCenter.addObservers(this, this.handleNotification, notes);
}

private listNotification(): Array<string> {
  let notes: Array<string> = new Array<string>();
  notes.push(TKThemeManager.NOTE_THEME_CHANGED);
  return notes;
}
```

#### 样式查询机制
```typescript
public getCssRulesetByClassName(className: string): TKCSSRuleset {
  return TKMapHelper.getObject(this.style, className, new TKCSSRuleset()) as TKCSSRuleset;
}
```

### 实现机制分析

#### 响应式编程模型
- **@ObservedV2装饰器**：标记类为可观察对象，支持响应式更新
- **@Trace装饰器**：标记属性为可追踪，变更时自动通知UI
- **自动刷新机制**：样式数据变更时自动触发组件重新渲染
- **数据绑定**：实现样式数据与UI的双向绑定

#### 通知监听机制
- **集中注册**：在构造函数中统一注册所有需要的通知
- **类型安全**：通过通知名称常量确保类型安全
- **自动处理**：通知到达时自动调用处理方法
- **解耦设计**：通过通知中心实现与主题管理器的解耦

#### 样式缓存策略
- **内存缓存**：将样式映射缓存在内存中，提供快速访问
- **懒更新**：只在主题变更时更新缓存，避免不必要的操作
- **完整替换**：主题变更时完整替换样式映射，确保一致性
- **默认值处理**：查询不存在的样式时返回默认值

#### 生命周期管理
- **自动初始化**：构造时自动注册通知和初始化样式
- **自动清理**：依赖鸿蒙框架的生命周期管理
- **内存管理**：通过弱引用避免内存泄漏
- **异常处理**：通知处理失败时不影响正常功能

### 性能考虑
- **响应式优化**：只在样式真正变更时触发UI更新
- **缓存机制**：样式数据缓存在内存中，避免重复查询
- **批量更新**：主题变更时批量更新所有样式
- **延迟加载**：样式数据按需获取和更新

### 错误处理
- **通知异常**：通知处理失败时不影响其他功能
- **样式缺失**：查询不存在的样式时返回默认值
- **初始化异常**：初始化失败时使用空样式映射
- **内存保护**：避免样式数据过大导致的内存问题

### 最佳实践
- **组件集成**：在需要主题响应的组件中使用此类
- **样式命名**：使用规范的CSS类名进行样式查询
- **性能优化**：避免频繁的样式查询，合理使用缓存
- **生命周期管理**：正确管理通知监听的生命周期

## 🔗 依赖关系

### 依赖的模块
- `TKThemeManager` - 主题管理器，提供样式数据源
- `TKNotificationCenter` - 通知中心，用于监听主题变更
- `TKNotification` - 通知对象，承载通知信息
- `TKMapHelper` - Map工具，用于样式数据查询
- `TKCSSRuleset` - CSS规则集，样式数据结构

### 被依赖情况
- `各UI组件` - 使用主题样式属性获取响应式样式
- `TKTitleBar` - 标题栏组件的样式属性基类
- `TKPatternLock` - 手势密码组件的样式属性基类
- `自定义组件` - 需要主题响应的自定义组件

### 关联文件
- `TKThemeManager.ets` - 主题管理器，样式数据提供者
- `TKCSSRuleset.ets` - CSS规则集定义
- `TKStyleAttribute.ets` - 样式属性基类
- 各组件的样式属性类 - 继承或使用此类

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKThemeStyleAttribute是主题系统的响应式组件：
1. **响应式支持**：为UI组件提供响应式的主题样式支持
2. **自动更新**：主题变更时自动更新样式，无需手动处理
3. **性能优化**：通过缓存和响应式机制优化性能
4. **易于集成**：简化组件的主题集成复杂度

理解TKThemeStyleAttribute有助于掌握响应式主题系统的使用，是实现主题响应UI的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解响应式编程**：掌握鸿蒙响应式编程的基本概念
2. **分析观察者模式**：理解通知监听和响应机制
3. **学习主题集成**：掌握如何在组件中集成主题支持
4. **实践响应式UI**：创建响应主题变更的UI组件

### 前置学习
- 鸿蒙响应式编程基础
- @ObservedV2和@Trace装饰器
- 观察者模式和通知机制
- 主题系统基本概念

### 后续学习
- `TKThemeManager.ets` - 深入理解主题管理器
- 各UI组件的主题集成实现
- 自定义组件的主题支持开发

### 实践建议
1. **基础集成**：在简单组件中集成主题样式属性
2. **响应式测试**：测试主题切换时的UI响应效果
3. **性能验证**：验证响应式更新的性能表现
4. **自定义扩展**：扩展样式属性以支持更多样式需求

### 常见问题
1. **问题**: 如何在组件中使用主题样式属性？
   **解答**: 创建TKThemeStyleAttribute实例，使用@State装饰器标记，通过getCssRulesetByClassName方法获取样式。

2. **问题**: 主题切换后UI没有自动更新怎么办？
   **解答**: 确保使用了@ObservedV2和@Trace装饰器，检查通知监听是否正确注册，验证样式属性是否正确绑定到UI。

## 📝 代码示例

### 基础使用
```typescript
import { TKThemeStyleAttribute, TKThemeManager } from '@thinkive/tk-harmony-base';

// 基础主题样式属性使用
@Component
struct BasicThemeComponent {
  @State private themeStyle: TKThemeStyleAttribute = new TKThemeStyleAttribute();

  build() {
    Column() {
      // 使用主题样式的文本
      Text('主题化文本')
        .fontSize(this.themeStyle.getCssRulesetByClassName('TKText').fontSize)
        .fontColor(this.themeStyle.getCssRulesetByClassName('TKText').fontColor)
        .margin(10)

      // 使用主题样式的按钮
      Button('主题化按钮')
        .backgroundColor(this.themeStyle.getCssRulesetByClassName('TKButton').backgroundColor)
        .fontColor(this.themeStyle.getCssRulesetByClassName('TKButton').fontColor)
        .borderRadius(this.themeStyle.getCssRulesetByClassName('TKButton').borderRadius)
        .margin(10)
        .onClick(() => {
          // 切换主题测试响应式更新
          const currentTheme = TKThemeManager.shareInstance().theme;
          const newTheme = currentTheme === 'light' ? 'dark' : 'light';
          TKThemeManager.shareInstance().theme = newTheme;
        })

      // 使用主题样式的面板
      Column() {
        Text('主题化面板')
          .fontSize(this.themeStyle.getCssRulesetByClassName('TKPanelTitle').fontSize)
          .fontColor(this.themeStyle.getCssRulesetByClassName('TKPanelTitle').fontColor)
      }
      .backgroundColor(this.themeStyle.getCssRulesetByClassName('TKPanel').backgroundColor)
      .borderRadius(this.themeStyle.getCssRulesetByClassName('TKPanel').borderRadius)
      .padding(20)
      .margin(10)
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.themeStyle.getCssRulesetByClassName('TKBackground').backgroundColor)
  }
}

// 手动创建和使用主题样式属性
function manualThemeStyleUsage() {
  // 创建主题样式属性实例
  const themeStyle = new TKThemeStyleAttribute();

  // 获取特定组件的样式
  const buttonStyle = themeStyle.getCssRulesetByClassName('TKButton');
  console.log('按钮样式:', {
    backgroundColor: buttonStyle.backgroundColor,
    fontColor: buttonStyle.fontColor,
    fontSize: buttonStyle.fontSize,
    borderRadius: buttonStyle.borderRadius
  });

  // 获取文本样式
  const textStyle = themeStyle.getCssRulesetByClassName('TKText');
  console.log('文本样式:', {
    fontColor: textStyle.fontColor,
    fontSize: textStyle.fontSize,
    fontWeight: textStyle.fontWeight
  });
}

// 执行示例
manualThemeStyleUsage();
```

### 高级用法
```typescript
// 自定义主题样式属性类
@ObservedV2
class CustomThemeStyleAttribute extends TKThemeStyleAttribute {
  // 扩展样式缓存
  @Trace private customStyles: Map<string, any> = new Map();

  constructor() {
    super();
    this.initCustomStyles();
  }

  // 初始化自定义样式
  private initCustomStyles() {
    this.updateCustomStyles();
  }

  // 重写通知处理，添加自定义逻辑
  protected handleNotification(note: TKNotification) {
    super.handleNotification(note);

    if (note.name === TKThemeManager.NOTE_THEME_CHANGED) {
      // 更新自定义样式
      this.updateCustomStyles();

      // 执行自定义主题变更逻辑
      this.onThemeChanged(note.object as string);
    }
  }

  // 更新自定义样式
  private updateCustomStyles() {
    // 计算派生样式
    const buttonStyle = this.getCssRulesetByClassName('TKButton');
    const textStyle = this.getCssRulesetByClassName('TKText');

    // 创建组合样式
    this.customStyles.set('primaryButton', {
      backgroundColor: buttonStyle.backgroundColor,
      fontColor: buttonStyle.fontColor,
      fontSize: textStyle.fontSize,
      padding: 12,
      borderRadius: 8
    });

    this.customStyles.set('secondaryButton', {
      backgroundColor: 'transparent',
      fontColor: buttonStyle.backgroundColor,
      fontSize: textStyle.fontSize,
      borderColor: buttonStyle.backgroundColor,
      borderWidth: 1,
      padding: 12,
      borderRadius: 8
    });
  }

  // 主题变更回调
  private onThemeChanged(themeName: string) {
    console.log(`主题已切换到: ${themeName}`);
    // 可以在这里添加自定义的主题变更处理逻辑
  }

  // 获取自定义样式
  getCustomStyle(styleName: string): any {
    return this.customStyles.get(styleName) || {};
  }

  // 获取组合样式
  getCombinedStyle(classNames: string[]): any {
    const combinedStyle: any = {};

    classNames.forEach(className => {
      const style = this.getCssRulesetByClassName(className);
      Object.assign(combinedStyle, style);
    });

    return combinedStyle;
  }
}

// 主题样式管理器
class ThemeStyleManager {
  private static instance: ThemeStyleManager;
  private themeStyleAttribute: TKThemeStyleAttribute;
  private styleCache: Map<string, any> = new Map();

  private constructor() {
    this.themeStyleAttribute = new TKThemeStyleAttribute();
    this.initializeStyleCache();
  }

  static getInstance(): ThemeStyleManager {
    if (!ThemeStyleManager.instance) {
      ThemeStyleManager.instance = new ThemeStyleManager();
    }
    return ThemeStyleManager.instance;
  }

  // 初始化样式缓存
  private initializeStyleCache() {
    // 监听主题变更，清理缓存
    TKNotificationCenter.defaultCenter.addObserver(
      this,
      () => {
        this.styleCache.clear();
      },
      TKThemeManager.NOTE_THEME_CHANGED
    );
  }

  // 获取样式（带缓存）
  getStyle(className: string): TKCSSRuleset {
    if (!this.styleCache.has(className)) {
      const style = this.themeStyleAttribute.getCssRulesetByClassName(className);
      this.styleCache.set(className, style);
    }
    return this.styleCache.get(className);
  }

  // 获取样式属性值
  getStyleProperty(className: string, property: string): any {
    const style = this.getStyle(className);
    return style[property];
  }

  // 批量获取样式
  getBatchStyles(classNames: string[]): Map<string, TKCSSRuleset> {
    const styles = new Map<string, TKCSSRuleset>();

    classNames.forEach(className => {
      styles.set(className, this.getStyle(className));
    });

    return styles;
  }

  // 创建样式对象
  createStyleObject(className: string): any {
    const style = this.getStyle(className);

    return {
      width: style.width,
      height: style.height,
      backgroundColor: style.backgroundColor,
      fontColor: style.fontColor,
      fontSize: style.fontSize,
      borderRadius: style.borderRadius,
      borderWidth: style.borderWidth,
      borderColor: style.borderColor,
      padding: style.padding,
      margin: style.margin
    };
  }
}

// 响应式主题组件
@Component
struct ResponsiveThemeComponent {
  @State private customThemeStyle: CustomThemeStyleAttribute = new CustomThemeStyleAttribute();
  @State private currentTheme: string = TKThemeManager.shareInstance().theme;

  aboutToAppear() {
    // 监听主题变更
    TKNotificationCenter.defaultCenter.addObserver(
      this,
      (notification) => {
        this.currentTheme = notification.object as string;
      },
      TKThemeManager.NOTE_THEME_CHANGED
    );
  }

  build() {
    Column() {
      // 主题信息显示
      Text(`当前主题: ${this.currentTheme}`)
        .fontSize(this.customThemeStyle.getCssRulesetByClassName('TKTitle').fontSize)
        .fontColor(this.customThemeStyle.getCssRulesetByClassName('TKTitle').fontColor)
        .margin(10)

      // 使用自定义样式的按钮
      this.buildCustomButton('主要按钮', 'primaryButton')
      this.buildCustomButton('次要按钮', 'secondaryButton')

      // 组合样式示例
      Column() {
        Text('组合样式面板')
          .fontSize(this.customThemeStyle.getCombinedStyle(['TKText', 'TKTitle']).fontSize)
          .fontColor(this.customThemeStyle.getCombinedStyle(['TKText', 'TKTitle']).fontColor)
      }
      .backgroundColor(this.customThemeStyle.getCssRulesetByClassName('TKPanel').backgroundColor)
      .borderRadius(this.customThemeStyle.getCssRulesetByClassName('TKPanel').borderRadius)
      .padding(20)
      .margin(10)

      // 主题切换按钮
      Row() {
        ['light', 'dark', 'blue'].forEach(theme => {
          Button(theme)
            .backgroundColor(this.customThemeStyle.getCssRulesetByClassName('TKButton').backgroundColor)
            .fontColor(this.customThemeStyle.getCssRulesetByClassName('TKButton').fontColor)
            .margin(5)
            .onClick(() => {
              TKThemeManager.shareInstance().theme = theme;
            })
        })
      }
      .justifyContent(FlexAlign.SpaceEvenly)
      .width('100%')
      .margin(20)
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.customThemeStyle.getCssRulesetByClassName('TKBackground').backgroundColor)
  }

  @Builder
  buildCustomButton(text: string, styleType: string) {
    const customStyle = this.customThemeStyle.getCustomStyle(styleType);

    Button(text)
      .backgroundColor(customStyle.backgroundColor)
      .fontColor(customStyle.fontColor)
      .fontSize(customStyle.fontSize)
      .borderRadius(customStyle.borderRadius)
      .borderWidth(customStyle.borderWidth)
      .borderColor(customStyle.borderColor)
      .padding(customStyle.padding)
      .margin(10)
      .onClick(() => {
        console.log(`点击了${text}`);
      })
  }
}

// 主题样式工具函数
class ThemeStyleUtils {
  // 创建响应式样式绑定
  static createStyleBinding(themeStyle: TKThemeStyleAttribute, className: string) {
    return {
      get backgroundColor() {
        return themeStyle.getCssRulesetByClassName(className).backgroundColor;
      },
      get fontColor() {
        return themeStyle.getCssRulesetByClassName(className).fontColor;
      },
      get fontSize() {
        return themeStyle.getCssRulesetByClassName(className).fontSize;
      },
      get borderRadius() {
        return themeStyle.getCssRulesetByClassName(className).borderRadius;
      }
    };
  }

  // 创建样式计算函数
  static createStyleComputer(themeStyle: TKThemeStyleAttribute) {
    return {
      // 计算对比色
      getContrastColor: (className: string) => {
        const style = themeStyle.getCssRulesetByClassName(className);
        // 简单的对比色计算逻辑
        return style.backgroundColor === '#FFFFFF' ? '#000000' : '#FFFFFF';
      },

      // 计算阴影样式
      getShadowStyle: (className: string) => {
        const style = themeStyle.getCssRulesetByClassName(className);
        return {
          shadowColor: style.fontColor,
          shadowOffset: { x: 2, y: 2 },
          shadowRadius: 4,
          shadowOpacity: 0.1
        };
      },

      // 计算渐变样式
      getGradientStyle: (className: string) => {
        const style = themeStyle.getCssRulesetByClassName(className);
        return {
          colors: [style.backgroundColor, style.selectedBackgroundColor],
          direction: 'vertical'
        };
      }
    };
  }
}

// 使用示例
function demonstrateAdvancedThemeStyle() {
  // 使用自定义主题样式属性
  const customThemeStyle = new CustomThemeStyleAttribute();

  // 获取自定义样式
  const primaryButtonStyle = customThemeStyle.getCustomStyle('primaryButton');
  console.log('主要按钮样式:', primaryButtonStyle);

  // 获取组合样式
  const combinedStyle = customThemeStyle.getCombinedStyle(['TKButton', 'TKText']);
  console.log('组合样式:', combinedStyle);

  // 使用样式管理器
  const styleManager = ThemeStyleManager.getInstance();
  const buttonStyle = styleManager.getStyle('TKButton');
  console.log('按钮样式:', buttonStyle);

  // 批量获取样式
  const batchStyles = styleManager.getBatchStyles(['TKButton', 'TKText', 'TKPanel']);
  console.log('批量样式:', batchStyles);
}

// 执行演示
demonstrateAdvancedThemeStyle();
```

## 📚 相关资源

### 官方文档
- HarmonyOS响应式编程指南
- @ObservedV2和@Trace装饰器文档

### 参考资料
- 《响应式编程实战》- 响应式编程模式
- 《观察者模式详解》- 观察者模式应用

### 相关文件
- `TKThemeManager.ets` - 主题管理器
- `TKCSSRuleset.ets` - CSS规则集定义
- `TKNotificationCenter.ets` - 通知中心
