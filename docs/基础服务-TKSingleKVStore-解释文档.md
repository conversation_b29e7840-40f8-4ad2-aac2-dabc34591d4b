# TKSingleKVStore

> **文件路径**: `src/main/ets/util/cache/TKSingleKVStore.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 分布式存储、键值对数据库、数据同步、鸿蒙分布式数据管理

## 📋 文件概述

### 功能定位
TKSingleKVStore是HMThinkBaseHar框架的**单实例键值存储类**，提供了基于鸿蒙分布式数据管理的键值对存储功能。它封装了distributedKVStore API，支持单版本数据库、自动序列化、异步操作等功能，为应用提供可靠的分布式数据存储服务。

### 核心职责
- **分布式键值存储**：基于鸿蒙分布式数据管理提供键值对存储
- **单版本数据管理**：使用单版本存储模式，确保数据一致性
- **自动序列化**：自动处理复杂对象的序列化和反序列化
- **异步数据操作**：提供异步的数据存储、读取和删除操作
- **数据库生命周期管理**：自动管理数据库的创建和连接

### 设计模式
- **适配器模式**：封装鸿蒙distributedKVStore API，提供统一接口
- **懒加载模式**：数据库在首次使用时才创建和连接
- **代理模式**：作为底层分布式存储的代理，提供简化接口
- **单例模式**：每个数据库名称对应一个存储实例

## 🔧 核心功能

### 构造方法

#### 构造函数：`constructor(dbName?: string)`
- **功能**: 创建单实例键值存储实例
- **参数**: dbName - 数据库名称（可选，默认'TKStoreKVDB'）
- **初始化**: 创建KVManager实例，准备数据库连接
- **使用场景**: 创建特定用途的分布式存储

### 核心存储方法

#### 方法1：`setData<T>(key: string, data: T)`
- **功能**: 异步存储键值对数据
- **参数**: 
  - key - 数据键名
  - data - 要存储的数据（任意类型）
- **返回值**: Promise<void> - 异步操作结果
- **特性**: 自动序列化，支持复杂对象存储
- **使用场景**: 存储需要分布式同步的数据

#### 方法2：`getData<T>(key: string, defValue?: T)`
- **功能**: 异步读取键值对数据
- **参数**: 
  - key - 数据键名
  - defValue - 默认值（可选）
- **返回值**: Promise<T | undefined> - 读取的数据或默认值
- **特性**: 自动反序列化，类型安全
- **使用场景**: 读取分布式存储的数据

#### 方法3：`deleteData(key: string)`
- **功能**: 异步删除指定键的数据
- **参数**: key - 要删除的数据键名
- **返回值**: Promise<void> - 异步操作结果
- **使用场景**: 清理过期或无效的分布式数据

#### 方法4：`getDBName()`
- **功能**: 获取当前数据库名称
- **返回值**: string - 数据库名称
- **使用场景**: 调试、日志记录、数据库管理

## 💡 技术要点

### 核心算法/逻辑

#### 数据库懒加载机制
```typescript
private async buildKVStore(): Promise<void> {
  if (!this.kvStore) {
    return new Promise<void>((resolve, reject) => {
      if (this.kvManager) {
        this.kvManager.getKVStore<distributedKVStore.SingleKVStore>(this.dbName, {
          createIfMissing: true,        // 不存在时自动创建
          encrypt: false,               // 不启用加密
          backup: false,                // 不启用备份
          autoSync: false,              // 不启用自动同步
          kvStoreType: distributedKVStore.KVStoreType.SINGLE_VERSION,  // 单版本存储
          securityLevel: distributedKVStore.SecurityLevel.S1           // 安全级别S1
        } as distributedKVStore.Options, (error, store: distributedKVStore.SingleKVStore) => {
          if (error) {
            TKLog.error(`[TKSingleKVStore]创建数据库异常，code: ${error.code}， message: ${error.message}`);
            reject();
          } else {
            this.kvStore = store;
            resolve();
          }
        });
      }
    });
  }
}
```

#### 异步数据存储
```typescript
public async setData<T>(key: string, data: T): Promise<void> {
  try {
    await this.buildKVStore();  // 确保数据库已创建
    if (this.kvStore) {
      // 序列化数据并存储
      await this.kvStore.put(key, TKObjectHelper.serialize(data));
    }
  } catch (error) {
    TKLog.error(`[TKSingleKVStore]缓存数据异常，code: ${error.code}， message: ${error.message}`);
  }
}
```

#### 异步数据读取
```typescript
public async getData<T>(key: string, defValue: T | undefined = undefined): Promise<T | undefined> {
  try {
    await this.buildKVStore();  // 确保数据库已创建
    if (this.kvStore) {
      // 读取并反序列化数据
      let dataStr: string = await this.kvStore.get(key) as string;
      let data: T | undefined = TKObjectHelper.deserialize<T>(dataStr) as T ?? defValue;
      return data;
    }
  } catch (error) {
    TKLog.error(`[TKSingleKVStore]获取数据异常，code: ${error.code}， message: ${error.message}`);
  }
  return defValue;
}
```

#### 异步数据删除
```typescript
public async deleteData(key: string): Promise<void> {
  try {
    await this.buildKVStore();  // 确保数据库已创建
    if (this.kvStore) {
      await this.kvStore.delete(key);
    }
  } catch (error) {
    TKLog.error(`[TKSingleKVStore]删除缓存异常，code: ${error.code}， message: ${error.message}`);
  }
}
```

### 实现机制分析

#### 分布式存储特性
- **单版本存储**：使用SINGLE_VERSION模式，确保数据一致性
- **本地存储**：数据存储在本地设备，不进行跨设备同步
- **持久化保证**：数据在应用重启后仍然可用
- **事务支持**：底层支持事务操作，确保数据完整性

#### 数据库配置
- **自动创建**：createIfMissing=true，数据库不存在时自动创建
- **安全级别**：使用S1安全级别，适合一般应用数据
- **同步策略**：autoSync=false，不启用自动同步
- **加密设置**：encrypt=false，不启用数据加密

#### 序列化机制
- **自动序列化**：使用TKObjectHelper自动处理对象序列化
- **类型保持**：通过泛型保持数据类型信息
- **复杂对象支持**：支持嵌套对象、数组等复杂数据结构
- **错误容错**：序列化失败时记录错误并返回默认值

#### 异步操作模式
- **Promise支持**：所有操作都返回Promise，支持async/await
- **错误处理**：完整的异步错误处理机制
- **并发安全**：支持并发操作，底层保证数据一致性
- **性能优化**：异步操作不阻塞主线程

### 性能考虑
- **懒加载**：数据库在首次使用时才创建，减少启动开销
- **异步操作**：所有操作都是异步的，不阻塞主线程
- **内存管理**：及时释放不需要的数据，避免内存泄漏
- **连接复用**：数据库连接创建后复用，减少连接开销

### 错误处理
- **异常捕获**：所有操作都有完整的异常处理
- **错误日志**：详细记录操作失败的错误信息
- **优雅降级**：操作失败时返回默认值，不中断程序
- **资源保护**：确保数据库连接的正确管理

### 最佳实践
- **合理命名**：使用有意义的数据库名称和键名
- **异步处理**：正确使用async/await处理异步操作
- **错误处理**：检查Promise结果，处理可能的异常
- **数据大小**：注意存储数据的大小，避免性能问题

## 🔗 依赖关系

### 依赖的模块
- `distributedKVStore` - 鸿蒙分布式数据管理API，提供底层存储功能
- `TKObjectHelper` - 对象工具，处理序列化和反序列化
- `TKStringHelper` - 字符串工具，处理字符串操作
- `TKContextHelper` - 上下文工具，获取应用上下文
- `TKSystemHelper` - 系统工具，获取应用标识符
- `TKLog` - 日志工具，记录操作日志

### 被依赖情况
- `TKCacheManager` - 缓存管理器，使用TKSingleKVStore进行数据库缓存
- `分布式数据模块` - 使用TKSingleKVStore存储需要分布式同步的数据
- `持久化存储模块` - 使用TKSingleKVStore进行可靠的数据持久化
- `配置管理器` - 使用TKSingleKVStore存储重要的配置数据

### 关联文件
- `TKPreferences.ets` - 偏好设置存储，提供轻量级存储
- `TKObjectHelper.ets` - 对象工具类，提供序列化功能
- `TKCacheManager.ets` - 缓存管理器，使用分布式存储
- `TKAssetStore.ets` - 安全资产存储，提供加密存储

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKSingleKVStore是框架分布式存储的重要组件：
1. **分布式存储**：提供基于鸿蒙分布式数据管理的存储能力
2. **数据可靠性**：确保数据的持久化和一致性
3. **异步操作**：提供高性能的异步数据操作
4. **类型安全**：通过泛型提供类型安全的数据操作

理解TKSingleKVStore有助于掌握分布式数据存储的实现，是处理重要数据持久化的关键工具。

## 🎯 学习建议

### 学习路径
1. **理解分布式存储**：掌握分布式数据管理的基本概念
2. **分析存储配置**：理解不同存储配置的作用和影响
3. **学习异步操作**：掌握异步数据操作的正确使用方式
4. **实践分布式存储**：在实际项目中使用分布式键值存储

### 前置学习
- 分布式数据管理概念
- 鸿蒙distributedKVStore API
- 异步编程和Promise
- 数据序列化和反序列化

### 后续学习
- `TKPreferences.ets` - 对比轻量级存储的差异
- `TKObjectHelper.ets` - 深入理解序列化机制
- `TKCacheManager.ets` - 了解多级缓存的集成

### 实践建议
1. **基础存储测试**：测试不同类型数据的分布式存储
2. **异步操作练习**：练习正确的异步操作模式
3. **性能测试**：测试大数据量的存储性能
4. **错误处理验证**：验证各种异常情况的处理

### 常见问题
1. **问题**: 什么时候使用TKSingleKVStore而不是TKPreferences？
   **解答**: 当需要更可靠的数据持久化、更大的存储容量或者未来可能需要分布式同步时，使用TKSingleKVStore。

2. **问题**: 如何处理异步操作的错误？
   **解答**: 使用try-catch包装await操作，或者使用Promise的catch方法处理错误，确保错误不会导致程序崩溃。

## 📝 代码示例

### 基础使用
```typescript
import { TKSingleKVStore } from '@thinkive/tk-harmony-base';

// 基础分布式存储操作
async function basicKVStoreOperations() {
  // 创建分布式键值存储实例
  const kvStore = new TKSingleKVStore('user_data_store');

  console.log('数据库名称:', kvStore.getDBName());

  try {
    // 存储不同类型的数据
    await kvStore.setData('username', '张三');
    await kvStore.setData('age', 30);
    await kvStore.setData('isActive', true);
    await kvStore.setData('lastLogin', new Date());

    // 存储复杂对象
    const userProfile = {
      id: 12345,
      name: '李四',
      email: '<EMAIL>',
      settings: {
        theme: 'dark',
        language: 'zh-CN',
        notifications: {
          email: true,
          push: false
        }
      },
      tags: ['developer', 'admin'],
      metadata: {
        created: new Date(),
        lastModified: new Date()
      }
    };

    await kvStore.setData('userProfile', userProfile);
    console.log('数据存储完成');

    // 读取数据
    const username = await kvStore.getData<string>('username', '未知用户');
    const age = await kvStore.getData<number>('age', 0);
    const isActive = await kvStore.getData<boolean>('isActive', false);
    const lastLogin = await kvStore.getData<Date>('lastLogin');
    const profile = await kvStore.getData<any>('userProfile');

    console.log('用户名:', username);
    console.log('年龄:', age);
    console.log('活跃状态:', isActive);
    console.log('最后登录:', lastLogin);
    console.log('用户档案:', profile);

    // 删除数据
    await kvStore.deleteData('lastLogin');
    console.log('最后登录时间已删除');

    // 验证删除
    const deletedLoginTime = await kvStore.getData<Date>('lastLogin');
    console.log('删除后的登录时间:', deletedLoginTime); // undefined

  } catch (error) {
    console.error('分布式存储操作失败:', error);
  }
}

// 多数据库管理
async function multiDatabaseOperations() {
  // 创建不同用途的数据库
  const userStore = new TKSingleKVStore('user_database');
  const configStore = new TKSingleKVStore('config_database');
  const cacheStore = new TKSingleKVStore('cache_database');

  try {
    // 用户数据库操作
    await userStore.setData('currentUser', {
      id: 'user_001',
      name: '王五',
      role: 'admin'
    });

    // 配置数据库操作
    await configStore.setData('appSettings', {
      version: '1.0.0',
      theme: 'light',
      language: 'zh-CN',
      features: ['feature1', 'feature2']
    });

    // 缓存数据库操作
    await cacheStore.setData('apiCache', {
      endpoint: '/api/users',
      data: [{ id: 1, name: 'User1' }],
      timestamp: Date.now(),
      ttl: 3600000 // 1小时
    });

    console.log('多数据库操作完成');

    // 读取各数据库的数据
    const currentUser = await userStore.getData('currentUser');
    const appSettings = await configStore.getData('appSettings');
    const apiCache = await cacheStore.getData('apiCache');

    console.log('当前用户:', currentUser);
    console.log('应用设置:', appSettings);
    console.log('API缓存:', apiCache);

  } catch (error) {
    console.error('多数据库操作失败:', error);
  }
}

// 执行示例
basicKVStoreOperations();
multiDatabaseOperations();
```

### 高级用法
```typescript
// 分布式数据管理器
class DistributedDataManager {
  private kvStore: TKSingleKVStore;
  private dataCache: Map<string, any> = new Map();
  private cacheTimeout: Map<string, number> = new Map();

  constructor(dbName: string = 'distributed_data') {
    this.kvStore = new TKSingleKVStore(dbName);
  }

  // 设置数据（带缓存）
  async setData<T>(key: string, data: T, cacheTime: number = 300000): Promise<boolean> {
    try {
      // 存储到分布式数据库
      await this.kvStore.setData(key, data);

      // 更新本地缓存
      this.dataCache.set(key, data);
      this.cacheTimeout.set(key, Date.now() + cacheTime);

      console.log(`数据已存储: ${key}`);
      return true;
    } catch (error) {
      console.error(`数据存储失败: ${key}`, error);
      return false;
    }
  }

  // 获取数据（优先从缓存）
  async getData<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    try {
      // 检查本地缓存
      if (this.dataCache.has(key)) {
        const timeout = this.cacheTimeout.get(key) || 0;
        if (Date.now() < timeout) {
          console.log(`从缓存获取数据: ${key}`);
          return this.dataCache.get(key) as T;
        } else {
          // 缓存过期，清理
          this.dataCache.delete(key);
          this.cacheTimeout.delete(key);
        }
      }

      // 从分布式数据库获取
      console.log(`从数据库获取数据: ${key}`);
      const data = await this.kvStore.getData<T>(key, defaultValue);

      // 更新缓存
      if (data !== undefined) {
        this.dataCache.set(key, data);
        this.cacheTimeout.set(key, Date.now() + 300000); // 5分钟缓存
      }

      return data;
    } catch (error) {
      console.error(`数据获取失败: ${key}`, error);
      return defaultValue;
    }
  }

  // 删除数据
  async deleteData(key: string): Promise<boolean> {
    try {
      // 从分布式数据库删除
      await this.kvStore.deleteData(key);

      // 清理本地缓存
      this.dataCache.delete(key);
      this.cacheTimeout.delete(key);

      console.log(`数据已删除: ${key}`);
      return true;
    } catch (error) {
      console.error(`数据删除失败: ${key}`, error);
      return false;
    }
  }

  // 批量设置数据
  async setBatchData(dataMap: Map<string, any>): Promise<boolean> {
    try {
      const promises: Promise<boolean>[] = [];

      dataMap.forEach((value, key) => {
        promises.push(this.setData(key, value));
      });

      const results = await Promise.all(promises);
      const success = results.every(result => result);

      console.log(`批量设置数据${success ? '成功' : '失败'}`);
      return success;
    } catch (error) {
      console.error('批量设置数据失败:', error);
      return false;
    }
  }

  // 批量获取数据
  async getBatchData<T>(keys: string[]): Promise<Map<string, T | undefined>> {
    const resultMap = new Map<string, T | undefined>();

    try {
      const promises = keys.map(key => this.getData<T>(key));
      const results = await Promise.all(promises);

      keys.forEach((key, index) => {
        resultMap.set(key, results[index]);
      });

      console.log('批量获取数据完成');
    } catch (error) {
      console.error('批量获取数据失败:', error);
    }

    return resultMap;
  }

  // 清理过期缓存
  cleanExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.cacheTimeout.forEach((timeout, key) => {
      if (now >= timeout) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.dataCache.delete(key);
      this.cacheTimeout.delete(key);
    });

    if (expiredKeys.length > 0) {
      console.log(`清理了${expiredKeys.length}个过期缓存`);
    }
  }

  // 获取缓存统计
  getCacheStats(): {
    cacheSize: number,
    hitRate: number,
    expiredCount: number
  } {
    const now = Date.now();
    let expiredCount = 0;

    this.cacheTimeout.forEach(timeout => {
      if (now >= timeout) {
        expiredCount++;
      }
    });

    return {
      cacheSize: this.dataCache.size,
      hitRate: 0, // 需要实际统计
      expiredCount: expiredCount
    };
  }
}

// 用户会话管理器
class UserSessionManager {
  private dataManager: DistributedDataManager;
  private sessionTimeout: number = 30 * 60 * 1000; // 30分钟

  constructor() {
    this.dataManager = new DistributedDataManager('user_sessions');
  }

  // 创建用户会话
  async createSession(userId: string, sessionData: any): Promise<string> {
    const sessionId = this.generateSessionId();
    const session = {
      sessionId: sessionId,
      userId: userId,
      data: sessionData,
      createdAt: Date.now(),
      lastAccessAt: Date.now(),
      expiresAt: Date.now() + this.sessionTimeout
    };

    const success = await this.dataManager.setData(`session_${sessionId}`, session);

    if (success) {
      console.log(`用户会话已创建: ${userId} -> ${sessionId}`);
      return sessionId;
    } else {
      throw new Error('会话创建失败');
    }
  }

  // 获取用户会话
  async getSession(sessionId: string): Promise<any | null> {
    const session = await this.dataManager.getData(`session_${sessionId}`);

    if (!session) {
      return null;
    }

    // 检查会话是否过期
    if (Date.now() > session.expiresAt) {
      await this.deleteSession(sessionId);
      return null;
    }

    // 更新最后访问时间
    session.lastAccessAt = Date.now();
    await this.dataManager.setData(`session_${sessionId}`, session);

    return session;
  }

  // 更新会话数据
  async updateSession(sessionId: string, newData: any): Promise<boolean> {
    const session = await this.getSession(sessionId);

    if (!session) {
      return false;
    }

    session.data = { ...session.data, ...newData };
    session.lastAccessAt = Date.now();

    return await this.dataManager.setData(`session_${sessionId}`, session);
  }

  // 延长会话
  async extendSession(sessionId: string): Promise<boolean> {
    const session = await this.getSession(sessionId);

    if (!session) {
      return false;
    }

    session.expiresAt = Date.now() + this.sessionTimeout;
    session.lastAccessAt = Date.now();

    return await this.dataManager.setData(`session_${sessionId}`, session);
  }

  // 删除会话
  async deleteSession(sessionId: string): Promise<boolean> {
    return await this.dataManager.deleteData(`session_${sessionId}`);
  }

  // 生成会话ID
  private generateSessionId(): string {
    return 'sess_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // 清理过期会话
  async cleanupExpiredSessions(): Promise<void> {
    // 这里需要实现遍历所有会话的逻辑
    // 由于KVStore没有提供遍历所有键的方法，
    // 实际应用中可能需要维护一个会话列表
    console.log('清理过期会话（需要实现具体逻辑）');
  }
}

// 应用配置管理器
class AppConfigManager {
  private dataManager: DistributedDataManager;
  private configCache: Map<string, any> = new Map();

  constructor() {
    this.dataManager = new DistributedDataManager('app_config');
    this.loadDefaultConfigs();
  }

  // 加载默认配置
  private async loadDefaultConfigs(): Promise<void> {
    const defaultConfigs = {
      theme: 'light',
      language: 'zh-CN',
      autoSave: true,
      timeout: 30000,
      maxRetries: 3,
      cacheSize: 100,
      logLevel: 'info'
    };

    for (const [key, value] of Object.entries(defaultConfigs)) {
      const existing = await this.dataManager.getData(key);
      if (existing === undefined) {
        await this.dataManager.setData(key, value);
      }
      this.configCache.set(key, existing || value);
    }

    console.log('默认配置已加载');
  }

  // 获取配置
  async getConfig<T>(key: string, defaultValue?: T): Promise<T | undefined> {
    // 优先从缓存获取
    if (this.configCache.has(key)) {
      return this.configCache.get(key) as T;
    }

    // 从分布式存储获取
    const value = await this.dataManager.getData<T>(key, defaultValue);
    if (value !== undefined) {
      this.configCache.set(key, value);
    }

    return value;
  }

  // 设置配置
  async setConfig<T>(key: string, value: T): Promise<boolean> {
    const success = await this.dataManager.setData(key, value);
    if (success) {
      this.configCache.set(key, value);
      console.log(`配置已更新: ${key} = ${value}`);
    }
    return success;
  }

  // 批量设置配置
  async setBatchConfigs(configs: Record<string, any>): Promise<boolean> {
    const configMap = new Map(Object.entries(configs));
    const success = await this.dataManager.setBatchData(configMap);

    if (success) {
      Object.entries(configs).forEach(([key, value]) => {
        this.configCache.set(key, value);
      });
    }

    return success;
  }

  // 重置配置
  async resetConfig(key: string): Promise<boolean> {
    const success = await this.dataManager.deleteData(key);
    if (success) {
      this.configCache.delete(key);
      console.log(`配置已重置: ${key}`);
    }
    return success;
  }

  // 获取所有配置
  getAllConfigs(): Record<string, any> {
    const configs: Record<string, any> = {};
    this.configCache.forEach((value, key) => {
      configs[key] = value;
    });
    return configs;
  }
}

// 使用示例
async function demonstrateAdvancedKVStore() {
  // 分布式数据管理器示例
  console.log('=== 分布式数据管理器示例 ===');
  const dataManager = new DistributedDataManager('test_data');

  // 设置数据
  await dataManager.setData('user1', { name: '张三', age: 30 });
  await dataManager.setData('user2', { name: '李四', age: 25 });

  // 批量设置
  const batchData = new Map([
    ['config1', { theme: 'dark' }],
    ['config2', { language: 'en' }],
    ['config3', { autoSave: true }]
  ]);
  await dataManager.setBatchData(batchData);

  // 批量获取
  const batchResult = await dataManager.getBatchData(['user1', 'user2', 'config1']);
  console.log('批量获取结果:', batchResult);

  // 缓存统计
  console.log('缓存统计:', dataManager.getCacheStats());

  // 用户会话管理器示例
  console.log('\n=== 用户会话管理器示例 ===');
  const sessionManager = new UserSessionManager();

  // 创建会话
  const sessionId = await sessionManager.createSession('user123', {
    role: 'admin',
    permissions: ['read', 'write', 'delete']
  });

  // 获取会话
  const session = await sessionManager.getSession(sessionId);
  console.log('获取的会话:', session);

  // 更新会话
  await sessionManager.updateSession(sessionId, {
    lastAction: 'login',
    ip: '*************'
  });

  // 应用配置管理器示例
  console.log('\n=== 应用配置管理器示例 ===');
  const configManager = new AppConfigManager();

  // 等待默认配置加载
  await new Promise(resolve => setTimeout(resolve, 100));

  // 获取配置
  const theme = await configManager.getConfig('theme');
  const language = await configManager.getConfig('language');
  console.log('当前主题:', theme);
  console.log('当前语言:', language);

  // 设置配置
  await configManager.setConfig('theme', 'dark');
  await configManager.setConfig('maxRetries', 5);

  // 获取所有配置
  console.log('所有配置:', configManager.getAllConfigs());
}

// 执行演示
demonstrateAdvancedKVStore();
```

## 📚 相关资源

### 官方文档
- HarmonyOS分布式数据管理开发指南
- distributedKVStore API参考文档

### 参考资料
- 《分布式系统原理》- 分布式数据一致性
- 《NoSQL数据库技术》- 键值存储原理

### 相关文件
- `TKPreferences.ets` - 轻量级键值存储
- `TKObjectHelper.ets` - 对象序列化工具
- `TKCacheManager.ets` - 多级缓存管理器
