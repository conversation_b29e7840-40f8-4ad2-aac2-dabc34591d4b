# TKNormalDialog

> **文件路径**: `src/main/ets/components/dialog/TKNormalDialog.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 初级  
> **前置知识**: 鸿蒙组件开发、对话框设计、事件回调、样式配置

## 📋 文件概述

### 功能定位
TKNormalDialog是HMThinkBaseHar框架的**标准对话框组件**，提供了通用的弹窗功能。它采用组件化设计，支持标题、内容、按钮的自定义配置，提供完整的事件回调机制，为应用提供统一的对话框交互体验。

### 核心职责
- **对话框显示**：提供标准的对话框布局和显示功能
- **内容配置**：支持标题、消息内容、按钮文字的自定义配置
- **事件处理**：提供取消、确认、关闭等事件的回调处理
- **样式定制**：支持颜色、尺寸、布局等样式的自定义
- **状态管理**：管理对话框的显示隐藏状态和生命周期

### 设计模式
- **组件模式**：使用@Component装饰器定义可复用对话框组件
- **配置模式**：通过TKNormalDialogOption配置对话框属性
- **回调模式**：通过回调函数处理用户交互事件
- **构建器模式**：提供buildTKNormalDialog构建器函数

## 🔧 核心功能

### 主要配置类：TKNormalDialogOption

#### 属性1：`title: string`
- **功能**: 对话框标题文字
- **类型**: string
- **默认值**: 空字符串
- **使用场景**: 需要显示对话框标题时

#### 属性2：`message: string`
- **功能**: 对话框内容消息
- **类型**: string
- **默认值**: 空字符串
- **使用场景**: 显示对话框的主要内容信息

#### 属性3：`cancelText: string`
- **功能**: 取消按钮文字
- **类型**: string
- **默认值**: 空字符串
- **使用场景**: 需要显示取消按钮时

#### 属性4：`confirmText: string`
- **功能**: 确认按钮文字
- **类型**: string
- **默认值**: "确定"
- **使用场景**: 显示确认按钮，默认总是显示

#### 属性5：`cancel: () => void`
- **功能**: 取消按钮点击回调
- **类型**: Function
- **使用场景**: 处理用户点击取消按钮的逻辑

#### 属性6：`confirm: () => void`
- **功能**: 确认按钮点击回调
- **类型**: Function
- **使用场景**: 处理用户点击确认按钮的逻辑

### 样式配置属性

#### 属性1：`btnHeight: number`
- **功能**: 按钮区域高度
- **默认值**: 50
- **使用场景**: 自定义按钮高度

#### 属性2：`contentMinHeight: number`
- **功能**: 内容区域最小高度
- **默认值**: 120
- **使用场景**: 确保内容区域有足够的显示空间

#### 属性3：`titleColor: ResourceColor`
- **功能**: 标题文字颜色
- **默认值**: "#FF1A9BFF"
- **使用场景**: 自定义标题颜色

#### 属性4：`contentColor: ResourceColor`
- **功能**: 内容文字颜色
- **默认值**: "#FF000000"
- **使用场景**: 自定义内容文字颜色

## 💡 技术要点

### 核心算法/逻辑

#### 按钮宽度自适应算法
```typescript
private btnWidthSpan(): string {
  return TKStringHelper.isNotBlank(this.options.cancelText) && 
         TKStringHelper.isNotBlank(this.options.confirmText) ?
    '50%' : '100%';
}
```

#### 对话框布局结构
```typescript
build() {
  Column() {
    // 内容区域
    Column({ space: 15 }) {
      // 标题（可选）
      if (TKStringHelper.isNotBlank(this.options.title)) {
        Text(this.options.title)
          .fontSize(20)
          .fontColor(this.options.titleColor)
          .textAlign(TextAlign.Center)
          .margin({ left: 5, right: 5, top: 10 })
      }
      
      // 消息内容
      Text(this.options.message)
        .fontSize(16)
        .textAlign(TextAlign.JUSTIFY)
        .fontColor(this.options.contentColor)
        .margin({ left: 15, right: 15, bottom: 10 })
    }
    .justifyContent(FlexAlign.Center)
    .width('100%')
    .constraintSize({ minHeight: this.options.contentMinHeight })

    // 分割线
    Divider().strokeWidth(1)
    
    // 按钮区域
    Row() {
      // 取消按钮（可选）
      if (TKStringHelper.isNotBlank(this.options.cancelText)) {
        // 取消按钮实现
      }
      
      // 按钮间分割线（双按钮时显示）
      if (TKStringHelper.isNotBlank(this.options.cancelText) && 
          TKStringHelper.isNotBlank(this.options.confirmText)) {
        Divider().vertical(true).position({ x: '50%' }).strokeWidth(1)
      }
      
      // 确认按钮
      if (TKStringHelper.isNotBlank(this.options.confirmText)) {
        // 确认按钮实现
      }
    }
    .width('100%')
    .height(this.options.btnHeight)
  }
  .width('80%')
  .borderRadius(4)
  .backgroundColor("#ffffffff")
  .rotate({ angle: this.options.angle })
}
```

#### 按钮事件处理机制
```typescript
// 取消按钮点击处理
.onClick(() => {
  if (this.options.close) {
    this.options.close(); // 关闭对话框
  }
  if (this.options.cancel) {
    this.options.cancel(); // 执行取消回调
  }
})

// 确认按钮点击处理
.onClick(() => {
  if (!this.options.isNoClose) {
    if (this.options.close) {
      this.options.close(); // 关闭对话框
    }
  }
  if (this.options.confirm) {
    this.options.confirm(); // 执行确认回调
  }
})
```

#### 生命周期管理
```typescript
aboutToAppear(options?: TKNormalDialogOption): void {
  this.options = TKObjectHelper.fixDefault(this.options, TKNormalDialogOption, options);
}

aboutToDisappear(): void {
  this.options = new TKNormalDialogOption();
}

aboutToReuse(params: Record<string, Object>): void {
  if (params) {
    let options = params as Object as TKNormalDialogOption;
    this.aboutToAppear(options);
  } else {
    this.aboutToDisappear();
  }
}
```

### 实现机制分析

#### 配置驱动设计
- **默认值处理**：通过TKObjectHelper.fixDefault确保所有配置都有默认值
- **可选显示**：标题和取消按钮根据配置决定是否显示
- **自适应布局**：按钮宽度根据显示的按钮数量自动调整

#### 事件处理流程
1. **用户点击**：用户点击取消或确认按钮
2. **关闭处理**：根据配置决定是否自动关闭对话框
3. **回调执行**：执行相应的回调函数处理业务逻辑

#### 样式系统
- **颜色配置**：支持标题、内容、按钮的颜色自定义
- **尺寸配置**：支持按钮高度、内容最小高度的配置
- **布局配置**：支持旋转角度等特殊布局效果

#### 组件复用机制
- **aboutToReuse**：支持组件复用，提高性能
- **状态重置**：组件销毁时重置状态，避免状态污染
- **参数更新**：复用时更新配置参数

### 性能考虑
- **组件复用**：通过aboutToReuse支持组件复用，减少创建开销
- **条件渲染**：标题和取消按钮按需渲染，减少不必要的组件
- **轻量级设计**：简单的布局结构，渲染性能优秀
- **状态管理**：最小化状态变量，减少重渲染

### 错误处理
- **空值检查**：对回调函数进行空值检查
- **默认值保护**：通过默认值确保组件正常显示
- **异常隔离**：单个回调异常不影响对话框功能
- **状态保护**：通过生命周期方法保护组件状态

### 最佳实践
- **配置集中**：通过TKNormalDialogOption集中管理所有配置
- **回调分离**：分离关闭和业务逻辑回调，提高灵活性
- **样式统一**：提供统一的样式配置，保持视觉一致性
- **生命周期管理**：合理使用生命周期方法管理组件状态

## 🔗 依赖关系

### 依赖的模块
- `TKObjectHelper` - 对象工具，处理配置默认值和对象操作
- `TKStringHelper` - 字符串工具，检查字符串是否为空
- `TKDialogHelper` - 对话框工具，提供对话框显示和管理功能

### 被依赖情况
- `TKDialogHelper` - 对话框工具类，使用TKNormalDialog显示标准对话框
- `各业务模块` - 通过TKDialogHelper.showAlertDialog使用标准对话框
- `其他对话框组件` - 可能继承或参考TKNormalDialog的设计

### 关联文件
- `TKDialogHelper.ets` - 对话框工具类，TKNormalDialog的主要使用者
- `TKLoadingDialog.ets` - 加载对话框组件，类似的对话框实现
- `TKToastDialog.ets` - 吐司对话框组件，轻量级提示对话框

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKNormalDialog是框架UI组件的基础对话框：
1. **通用组件**：提供标准的对话框交互体验
2. **使用频率高**：在需要用户确认的场景中广泛使用
3. **学习难度低**：结构简单，容易理解和使用
4. **扩展性好**：提供丰富的配置选项，支持自定义

理解TKNormalDialog有助于掌握框架的对话框系统，是构建用户交互界面的重要组件。

## 🎯 学习建议

### 学习路径
1. **理解对话框设计**：掌握对话框的基本设计原则和用户体验
2. **分析组件结构**：理解TKNormalDialog的布局和样式设计
3. **学习配置系统**：掌握TKNormalDialogOption的配置方式
4. **实践对话框开发**：使用TKNormalDialog创建各种对话框

### 前置学习
- 鸿蒙组件开发基础
- 布局和样式设计
- 事件处理和回调机制
- 组件生命周期管理

### 后续学习
- `TKDialogHelper.ets` - 学习对话框工具类的使用
- `TKLoadingDialog.ets` - 了解加载对话框的实现
- `TKToastDialog.ets` - 学习轻量级提示对话框

### 实践建议
1. **基础对话框使用**：创建简单的确认对话框
2. **样式定制实验**：测试不同的颜色和尺寸配置
3. **事件处理练习**：实现复杂的对话框交互逻辑
4. **自定义扩展**：基于TKNormalDialog开发自定义对话框

### 常见问题
1. **问题**: 如何创建只有确认按钮的对话框？
   **解答**: 不设置cancelText或设置为空字符串，只设置confirmText，对话框会自动调整为单按钮布局。

2. **问题**: 如何防止对话框自动关闭？
   **解答**: 设置isNoClose为true，确认按钮点击后不会自动关闭对话框，需要在回调中手动调用close方法。

## 📝 代码示例

### 基础使用
```typescript
import { TKDialogHelper, TKNormalDialogOption } from '@thinkive/tk-harmony-base';

// 基础确认对话框
function showBasicDialog() {
  const option: TKNormalDialogOption = {
    title: "提示",
    message: "确定要删除这个文件吗？",
    cancelText: "取消",
    confirmText: "确定",
    cancel: () => {
      console.log('用户点击了取消');
    },
    confirm: () => {
      console.log('用户点击了确定');
      // 执行删除操作
      deleteFile();
    }
  };

  TKDialogHelper.showAlertDialog(option);
}

// 只有确认按钮的提示对话框
function showInfoDialog() {
  const option: TKNormalDialogOption = {
    title: "信息",
    message: "操作已成功完成！",
    confirmText: "知道了",
    confirm: () => {
      console.log('用户已知晓');
    }
  };

  TKDialogHelper.showAlertDialog(option);
}

// 无标题的简单对话框
function showSimpleDialog() {
  const option: TKNormalDialogOption = {
    message: "网络连接失败，请检查网络设置后重试。",
    cancelText: "取消",
    confirmText: "重试",
    cancel: () => {
      console.log('用户选择取消');
    },
    confirm: () => {
      console.log('用户选择重试');
      retryNetworkConnection();
    }
  };

  TKDialogHelper.showAlertDialog(option);
}

function deleteFile() {
  console.log('执行文件删除操作');
}

function retryNetworkConnection() {
  console.log('重新尝试网络连接');
}
```

### 高级用法
```typescript
// 自定义样式的对话框
function showCustomStyledDialog() {
  const option: TKNormalDialogOption = {
    title: "警告",
    message: "此操作不可撤销，确定要继续吗？",
    cancelText: "取消",
    confirmText: "继续",
    titleColor: "#FFFF0000",        // 红色标题
    contentColor: "#FF333333",      // 深灰色内容
    cancelColor: "#FF666666",       // 灰色取消按钮
    confirmColor: "#FFFF0000",      // 红色确认按钮
    btnHeight: 60,                  // 更高的按钮
    contentMinHeight: 150,          // 更高的内容区域
    cancel: () => {
      console.log('用户取消了危险操作');
    },
    confirm: () => {
      console.log('用户确认执行危险操作');
      performDangerousOperation();
    }
  };

  TKDialogHelper.showAlertDialog(option);
}

// 不自动关闭的对话框
function showNonAutoCloseDialog() {
  let dialogContent: any;

  const option: TKNormalDialogOption = {
    title: "处理中",
    message: "正在处理您的请求，请稍候...",
    confirmText: "强制关闭",
    isNoClose: true,  // 不自动关闭
    confirm: () => {
      console.log('用户强制关闭对话框');
      // 手动关闭对话框
      if (dialogContent) {
        dialogContent.close();
      }
    }
  };

  dialogContent = TKDialogHelper.showAlertDialog(option);

  // 模拟异步操作完成后自动关闭
  setTimeout(() => {
    if (dialogContent) {
      dialogContent.close();
      showInfoDialog(); // 显示完成提示
    }
  }, 3000);
}

// 带旋转效果的对话框
function showRotatedDialog() {
  const option: TKNormalDialogOption = {
    title: "特效对话框",
    message: "这是一个带有旋转效果的对话框",
    confirmText: "酷！",
    angle: 5, // 旋转5度
    confirm: () => {
      console.log('用户觉得很酷');
    }
  };

  TKDialogHelper.showAlertDialog(option);
}

function performDangerousOperation() {
  console.log('执行危险操作');
}
```

### 扩展示例
```typescript
// 对话框管理器
class DialogManager {
  private static instance: DialogManager;
  private activeDialogs: Map<string, any> = new Map();

  static getInstance(): DialogManager {
    if (!DialogManager.instance) {
      DialogManager.instance = new DialogManager();
    }
    return DialogManager.instance;
  }

  // 显示确认对话框
  showConfirmDialog(
    title: string,
    message: string,
    onConfirm?: () => void,
    onCancel?: () => void
  ): string {
    const dialogId = `confirm_${Date.now()}`;

    const option: TKNormalDialogOption = {
      title: title,
      message: message,
      cancelText: "取消",
      confirmText: "确定",
      cancel: () => {
        this.activeDialogs.delete(dialogId);
        onCancel?.();
      },
      confirm: () => {
        this.activeDialogs.delete(dialogId);
        onConfirm?.();
      }
    };

    const dialogContent = TKDialogHelper.showAlertDialog(option);
    this.activeDialogs.set(dialogId, dialogContent);

    return dialogId;
  }

  // 显示警告对话框
  showWarningDialog(message: string, onConfirm?: () => void): string {
    const dialogId = `warning_${Date.now()}`;

    const option: TKNormalDialogOption = {
      title: "警告",
      message: message,
      confirmText: "知道了",
      titleColor: "#FFFF6600",
      confirmColor: "#FFFF6600",
      confirm: () => {
        this.activeDialogs.delete(dialogId);
        onConfirm?.();
      }
    };

    const dialogContent = TKDialogHelper.showAlertDialog(option);
    this.activeDialogs.set(dialogId, dialogContent);

    return dialogId;
  }

  // 显示错误对话框
  showErrorDialog(message: string, onConfirm?: () => void): string {
    const dialogId = `error_${Date.now()}`;

    const option: TKNormalDialogOption = {
      title: "错误",
      message: message,
      confirmText: "确定",
      titleColor: "#FFFF0000",
      confirmColor: "#FFFF0000",
      confirm: () => {
        this.activeDialogs.delete(dialogId);
        onConfirm?.();
      }
    };

    const dialogContent = TKDialogHelper.showAlertDialog(option);
    this.activeDialogs.set(dialogId, dialogContent);

    return dialogId;
  }

  // 显示成功对话框
  showSuccessDialog(message: string, onConfirm?: () => void): string {
    const dialogId = `success_${Date.now()}`;

    const option: TKNormalDialogOption = {
      title: "成功",
      message: message,
      confirmText: "确定",
      titleColor: "#FF00AA00",
      confirmColor: "#FF00AA00",
      confirm: () => {
        this.activeDialogs.delete(dialogId);
        onConfirm?.();
      }
    };

    const dialogContent = TKDialogHelper.showAlertDialog(option);
    this.activeDialogs.set(dialogId, dialogContent);

    return dialogId;
  }

  // 关闭指定对话框
  closeDialog(dialogId: string): boolean {
    const dialogContent = this.activeDialogs.get(dialogId);
    if (dialogContent) {
      dialogContent.close();
      this.activeDialogs.delete(dialogId);
      return true;
    }
    return false;
  }

  // 关闭所有对话框
  closeAllDialogs() {
    this.activeDialogs.forEach((dialogContent, dialogId) => {
      dialogContent.close();
    });
    this.activeDialogs.clear();
  }

  // 获取活跃对话框数量
  getActiveDialogCount(): number {
    return this.activeDialogs.size;
  }
}

// 对话框工具类
class DialogUtils {
  private static dialogManager = DialogManager.getInstance();

  // 显示删除确认对话框
  static showDeleteConfirm(itemName: string, onConfirm: () => void) {
    return this.dialogManager.showConfirmDialog(
      "删除确认",
      `确定要删除"${itemName}"吗？此操作不可撤销。`,
      onConfirm
    );
  }

  // 显示保存确认对话框
  static showSaveConfirm(onSave: () => void, onDiscard: () => void) {
    const option: TKNormalDialogOption = {
      title: "保存更改",
      message: "您有未保存的更改，是否要保存？",
      cancelText: "不保存",
      confirmText: "保存",
      cancel: onDiscard,
      confirm: onSave
    };

    return TKDialogHelper.showAlertDialog(option);
  }

  // 显示网络错误对话框
  static showNetworkError(onRetry?: () => void) {
    if (onRetry) {
      return this.dialogManager.showConfirmDialog(
        "网络错误",
        "网络连接失败，请检查网络设置后重试。",
        onRetry
      );
    } else {
      return this.dialogManager.showErrorDialog(
        "网络连接失败，请检查网络设置。"
      );
    }
  }

  // 显示权限请求对话框
  static showPermissionRequest(permission: string, onGrant: () => void) {
    return this.dialogManager.showConfirmDialog(
      "权限请求",
      `应用需要${permission}权限才能正常工作，是否授予？`,
      onGrant
    );
  }

  // 显示版本更新对话框
  static showUpdateDialog(version: string, onUpdate: () => void, onLater?: () => void) {
    const option: TKNormalDialogOption = {
      title: "版本更新",
      message: `发现新版本 ${version}，建议立即更新以获得更好的体验。`,
      cancelText: "稍后",
      confirmText: "立即更新",
      cancel: onLater,
      confirm: onUpdate
    };

    return TKDialogHelper.showAlertDialog(option);
  }
}

// 使用示例
@Component
struct DialogExamplePage {
  private dialogManager = DialogManager.getInstance();

  build() {
    Column({ space: 20 }) {
      Text('对话框示例')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin(20)

      Button('确认对话框')
        .onClick(() => {
          this.dialogManager.showConfirmDialog(
            "确认操作",
            "确定要执行此操作吗？",
            () => console.log('用户确认'),
            () => console.log('用户取消')
          );
        })

      Button('警告对话框')
        .onClick(() => {
          this.dialogManager.showWarningDialog(
            "这是一个警告信息，请注意！",
            () => console.log('用户已知晓警告')
          );
        })

      Button('错误对话框')
        .onClick(() => {
          this.dialogManager.showErrorDialog(
            "操作失败，请重试！",
            () => console.log('用户确认错误信息')
          );
        })

      Button('成功对话框')
        .onClick(() => {
          this.dialogManager.showSuccessDialog(
            "操作成功完成！",
            () => console.log('用户确认成功信息')
          );
        })

      Button('删除确认')
        .onClick(() => {
          DialogUtils.showDeleteConfirm(
            "重要文件.txt",
            () => console.log('执行删除操作')
          );
        })

      Button('网络错误')
        .onClick(() => {
          DialogUtils.showNetworkError(
            () => console.log('重试网络连接')
          );
        })

      Button('关闭所有对话框')
        .onClick(() => {
          this.dialogManager.closeAllDialogs();
        })

      Text(`当前活跃对话框: ${this.dialogManager.getActiveDialogCount()}`)
        .fontSize(14)
        .fontColor(Color.Gray)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .padding(20)
  }
}
```

## 📚 相关资源

### 官方文档
- HarmonyOS对话框开发指南
- ArkUI组件设计规范

### 参考资料
- 《用户界面设计》- 对话框设计原则
- 《移动应用UI设计》- 对话框最佳实践

### 相关文件
- `TKDialogHelper.ets` - 对话框工具类
- `TKLoadingDialog.ets` - 加载对话框组件
- `TKToastDialog.ets` - 吐司对话框组件
