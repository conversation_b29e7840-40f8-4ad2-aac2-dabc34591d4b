# TKNotificationCenter

> **文件路径**: `src/main/ets/base/notification/TKNotificationCenter.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 观察者模式、WeakRef弱引用、Map数据结构、事件分发机制

## 📋 文件概述

### 功能定位
TKNotificationCenter是HMThinkBaseHar框架的**通知中心**，实现了观察者模式的事件分发机制。它采用单例模式设计，管理应用内部的事件监听和通知分发，支持弱引用管理、批量监听、异常处理等企业级功能，为应用提供解耦的事件通信能力。

### 核心职责
- **监听器管理**：注册、移除和管理事件监听器
- **事件分发**：将通知分发给相应的监听器
- **内存管理**：使用WeakRef避免内存泄漏
- **异常处理**：捕获和处理监听器执行异常
- **批量操作**：支持批量添加和移除监听器

### 设计模式
- **观察者模式**：实现事件的发布订阅机制
- **单例模式**：确保全局唯一的通知中心实例
- **弱引用模式**：使用WeakRef管理监听器，避免内存泄漏

## 🔧 核心功能

### 主要API/方法

#### 方法1：`addObserver(observer, handler, name, obj?)`
- **功能**: 添加事件监听器
- **参数**: 
  - observer - 监听对象
  - handler - 处理函数
  - name - 通知名称
  - obj - 关联对象（可选）
- **返回值**: void
- **使用场景**: 注册事件监听器

#### 方法2：`addObservers(observer, handler, notes)`
- **功能**: 批量添加事件监听器
- **参数**: 
  - observer - 监听对象
  - handler - 处理函数
  - notes - 通知名称数组
- **返回值**: void
- **使用场景**: 一次性监听多个事件

#### 方法3：`postNotificationName(name, obj?, userInfo?)`
- **功能**: 发送通知
- **参数**: 
  - name - 通知名称
  - obj - 关联对象（可选）
  - userInfo - 用户信息（可选）
- **返回值**: void
- **使用场景**: 发布事件通知

#### 方法4：`removeObserver(observer, name?, obj?)`
- **功能**: 移除事件监听器
- **参数**: 
  - observer - 监听对象
  - name - 通知名称（可选）
  - obj - 关联对象（可选）
- **返回值**: void
- **使用场景**: 取消事件监听

### 关键属性/配置
- `defaultCenter: TKNotificationCenter` - 默认通知中心实例
- `listenerMap: Map<WeakRef<Object>, Map<TKNotification, TKNotificationHandler>>` - 监听器映射表

## 💡 技术要点

### 核心算法/逻辑

#### 监听器注册机制
```typescript
public addObserver(observer: Object, handler: TKNotificationHandler, name: string, obj?: Object) {
  // 1. 获取或创建观察者的弱引用键
  let observerKey: WeakRef<Object> = this.getObserverKey(observer);
  
  // 2. 获取或创建观察者的监听器映射
  let observerListenerMap: Map<TKNotification, TKNotificationHandler> = 
    this.listenerMap.get(observerKey) ?? new Map<TKNotification, TKNotificationHandler>();
  
  // 3. 移除同名的旧监听器（避免重复注册）
  Array.from(observerListenerMap.keys()).forEach((noti, index) => {
    if (noti.name == name && noti.obj == obj) {
      observerListenerMap.delete(noti);
    }
  });
  
  // 4. 创建新的通知对象和处理器
  let notification: TKNotification = new TKNotification(name, obj);
  handler = handler.bind(observer); // 绑定上下文
  let weakHandler = new WeakRef(handler);
  
  // 5. 存储处理器引用到观察者对象上（防止被垃圾回收）
  (observer as Record<string, Object>)[`TKNotificationHandler#${notification.name}`] = handler;
  
  // 6. 注册弱引用处理器
  observerListenerMap.set(notification, async (note) => {
    weakHandler.deref()?.(note);
  });
  
  // 7. 更新监听器映射
  this.listenerMap.set(observerKey, observerListenerMap);
}
```

#### 事件分发机制
```typescript
public postNotificationName(name: string, obj?: Object, userInfo?: Record<string, Object>) {
  // 1. 创建通知对象
  let notification: TKNotification = new TKNotification(name, obj, userInfo);
  
  // 2. 遍历所有监听器
  this.listenerMap.forEach((observerListenerMap, observer) => {
    // 3. 检查观察者是否仍然存在
    if (observer.deref()) {
      // 4. 遍历观察者的所有监听器
      observerListenerMap.forEach((handler, noti) => {
        // 5. 匹配通知名称
        if (noti.name == notification.name) {
          // 6. 检查对象匹配（如果指定了对象）
          if (noti.obj !== undefined && noti.obj !== null) {
            if (noti.obj == notification.obj) {
              try {
                handler(notification);
              } catch (error) {
                TKLog.error(`监听通知[${name}]处理异常 ~ code: ${error.code} -·- message: ${error.message}`);
              }
            }
          } else {
            // 7. 无对象限制的通知
            try {
              handler(notification);
            } catch (error) {
              TKLog.error(`监听通知[${name}]处理异常 ~ code: ${error.code} -·- message: ${error.message}`);
            }
          }
        }
      });
    }
  });
}
```

#### 弱引用管理机制
```typescript
private getObserverKey(observer: Object): WeakRef<Object> {
  // 1. 获取所有现有的弱引用键
  let keys: Array<WeakRef<Object>> = Array.from(this.listenerMap.keys());
  let observerKey: WeakRef<Object> | undefined = undefined;
  
  // 2. 查找匹配的观察者
  for (let key of keys) {
    if (key.deref() == observer) {
      observerKey = key;
      break;
    }
  }
  
  // 3. 如果没找到，创建新的弱引用
  return observerKey ?? new WeakRef(observer);
}
```

#### 监听器移除机制
```typescript
public removeObserver(observer: Object, name?: string, obj?: Object) {
  // 1. 获取观察者的弱引用键
  let observerKey: WeakRef<Object> = this.getObserverKey(observer);
  let observerListenerMap: Map<TKNotification, TKNotificationHandler> | undefined = 
    this.listenerMap.get(observerKey);
  
  if (observerListenerMap) {
    if (name && name.length > 0) {
      // 2. 移除指定名称的监听器
      Array.from(observerListenerMap.keys()).forEach((noti, index) => {
        if (obj !== undefined && obj !== null) {
          // 精确匹配（名称+对象）
          if (noti.name == name && noti.obj == obj) {
            observerListenerMap?.delete(noti);
          }
        } else {
          // 只匹配名称
          if (noti.name == name) {
            observerListenerMap?.delete(noti);
          }
        }
      });
      
      // 3. 如果没有监听器了，移除整个观察者
      if (observerListenerMap.size == 0) {
        this.listenerMap.delete(observerKey);
      }
    } else {
      // 4. 移除观察者的所有监听器
      this.listenerMap.delete(observerKey);
    }
  }
}
```

### 实现机制分析

#### 弱引用内存管理
- **WeakRef使用**：使用WeakRef包装观察者对象，避免循环引用
- **自动清理**：当观察者被垃圾回收时，弱引用自动失效
- **引用保持**：将处理器存储到观察者对象上，防止过早回收
- **内存安全**：通过deref()检查对象是否仍然存在

#### 双层映射结构
- **外层映射**：WeakRef<Object> -> Map<TKNotification, TKNotificationHandler>
- **内层映射**：TKNotification -> TKNotificationHandler
- **高效查找**：支持按观察者和通知名称快速查找
- **批量操作**：支持按观察者批量移除监听器

#### 异常处理策略
- **异常隔离**：单个监听器异常不影响其他监听器
- **错误日志**：记录异常信息便于调试
- **继续执行**：异常后继续分发给其他监听器
- **优雅降级**：异常情况下保证系统稳定性

### 性能考虑
- **弱引用优化**：避免内存泄漏，自动清理无效监听器
- **批量操作**：支持批量添加监听器，减少重复操作
- **异步处理**：监听器处理函数支持异步执行
- **高效查找**：使用Map数据结构提供O(1)查找性能

### 错误处理
- **空值检查**：对所有参数进行空值验证
- **异常捕获**：捕获监听器执行异常并记录日志
- **状态检查**：通过deref()检查弱引用对象状态
- **优雅降级**：异常情况下保证核心功能正常

### 最佳实践
- **及时移除**：组件销毁时及时移除监听器
- **弱引用管理**：利用弱引用特性避免内存泄漏
- **异常处理**：监听器中添加异常处理逻辑
- **命名规范**：使用统一的通知命名规范

## 🔗 依赖关系

### 依赖的模块
- `TKNotification` - 通知对象，事件数据载体
- `TKLog` - 日志工具，记录异常信息

### 被依赖情况
- `TKAppEngine` - 应用引擎，使用通知中心处理系统事件
- `各业务模块` - 使用通知中心进行模块间通信
- `插件系统` - 使用通知中心进行插件通信
- `WebView组件` - 使用通知中心进行H5与原生通信

### 关联文件
- `TKNotification.ets` - 通知对象定义，数据载体
- `TKJSNotiProxyManager.ets` - JS通知代理管理器
- 各种监听器类 - 通知中心的使用者

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKNotificationCenter是框架事件通信的核心组件：
1. **通信枢纽**：作为应用内事件通信的中央枢纽
2. **解耦工具**：提供模块间解耦的通信方式
3. **内存安全**：通过弱引用机制保证内存安全
4. **扩展性强**：支持灵活的事件监听和分发

理解TKNotificationCenter有助于掌握框架的事件通信机制，是实现模块解耦的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解观察者模式**：掌握发布订阅模式的基本概念
2. **学习弱引用机制**：理解WeakRef的使用和内存管理
3. **分析事件分发流程**：理解从注册到分发的完整流程
4. **实践事件通信**：使用通知中心实现模块间通信

### 前置学习
- 观察者模式的设计原理
- WeakRef弱引用的概念和使用
- Map数据结构的操作
- 异步编程和异常处理

### 后续学习
- `TKNotification.ets` - 了解通知对象的结构
- 各种监听器实现 - 查看通知中心的实际应用
- 插件系统 - 学习通知在插件通信中的应用

### 实践建议
1. **基础监听练习**：注册和移除简单的事件监听器
2. **批量操作实验**：测试批量添加和移除监听器
3. **内存管理测试**：验证弱引用的内存管理效果
4. **异常处理验证**：测试监听器异常的处理机制

### 常见问题
1. **问题**: 为什么使用WeakRef？
   **解答**: WeakRef避免了观察者和通知中心之间的循环引用，当观察者被垃圾回收时，相关的监听器也会自动失效，防止内存泄漏。

2. **问题**: 如何确保监听器不被过早回收？
   **解答**: 框架将处理器函数存储到观察者对象上，确保只要观察者存在，处理器就不会被回收。

## 📝 代码示例

### 基础使用
```typescript
import { TKNotificationCenter, TKNotification } from '@thinkive/tk-harmony-base';

// 获取默认通知中心
const notificationCenter = TKNotificationCenter.defaultCenter;

// 定义通知名称常量
const NOTIFICATION_NAMES = {
  USER_LOGIN: "NOTE_USER_LOGIN",
  USER_LOGOUT: "NOTE_USER_LOGOUT",
  DATA_UPDATED: "NOTE_DATA_UPDATED",
  NETWORK_CHANGED: "NOTE_NETWORK_CHANGED"
};

// 示例观察者类
class UserManager {
  constructor() {
    // 注册监听器
    this.setupNotificationListeners();
  }

  // 设置通知监听
  private setupNotificationListeners() {
    // 监听用户登录事件
    notificationCenter.addObserver(
      this,
      this.handleUserLogin,
      NOTIFICATION_NAMES.USER_LOGIN
    );

    // 监听用户登出事件
    notificationCenter.addObserver(
      this,
      this.handleUserLogout,
      NOTIFICATION_NAMES.USER_LOGOUT
    );

    // 监听数据更新事件
    notificationCenter.addObserver(
      this,
      this.handleDataUpdated,
      NOTIFICATION_NAMES.DATA_UPDATED
    );
  }

  // 处理用户登录
  private handleUserLogin = (notification: TKNotification) => {
    console.log('用户登录通知:', notification.name);

    if (notification.obj) {
      const user = notification.obj as any;
      console.log('登录用户:', user);
    }

    if (notification.userInfo) {
      console.log('登录信息:', notification.userInfo);
    }
  }

  // 处理用户登出
  private handleUserLogout = (notification: TKNotification) => {
    console.log('用户登出通知:', notification.name);
    this.clearUserData();
  }

  // 处理数据更新
  private handleDataUpdated = (notification: TKNotification) => {
    console.log('数据更新通知:', notification.name);

    if (notification.userInfo) {
      const updateType = notification.userInfo.updateType;
      console.log('更新类型:', updateType);
    }
  }

  // 清理用户数据
  private clearUserData() {
    console.log('清理用户数据...');
  }

  // 销毁时移除监听器
  destroy() {
    notificationCenter.removeObserver(this);
  }
}

// 发送通知的示例
class AuthService {
  // 用户登录
  login(username: string, password: string) {
    // 模拟登录逻辑
    const user = { id: 123, name: username, role: 'user' };

    // 发送登录成功通知
    notificationCenter.postNotificationName(
      NOTIFICATION_NAMES.USER_LOGIN,
      user,
      {
        loginTime: Date.now(),
        loginMethod: 'password',
        deviceInfo: 'mobile'
      }
    );
  }

  // 用户登出
  logout() {
    // 发送登出通知
    notificationCenter.postNotificationName(
      NOTIFICATION_NAMES.USER_LOGOUT,
      undefined,
      {
        logoutTime: Date.now(),
        reason: 'user_action'
      }
    );
  }
}

// 使用示例
const userManager = new UserManager();
const authService = new AuthService();

// 模拟用户登录
authService.login('zhangsan', 'password123');

// 模拟用户登出
setTimeout(() => {
  authService.logout();
}, 2000);

// 清理资源
setTimeout(() => {
  userManager.destroy();
}, 5000);
```

### 高级用法
```typescript
// 高级通知管理器
class AdvancedNotificationManager {
  private notificationCenter = TKNotificationCenter.defaultCenter;
  private observers: Set<any> = new Set();

  // 批量注册监听器
  registerBatchListeners(observer: any, handlers: Map<string, Function>) {
    this.observers.add(observer);

    handlers.forEach((handler, notificationName) => {
      this.notificationCenter.addObserver(
        observer,
        handler.bind(observer),
        notificationName
      );
    });
  }

  // 注册带对象过滤的监听器
  registerObjectSpecificListener(
    observer: any,
    handler: Function,
    notificationName: string,
    targetObject: any
  ) {
    this.observers.add(observer);

    this.notificationCenter.addObserver(
      observer,
      handler.bind(observer),
      notificationName,
      targetObject
    );
  }

  // 注册一次性监听器
  registerOnceListener(
    observer: any,
    handler: Function,
    notificationName: string
  ) {
    this.observers.add(observer);

    const onceHandler = (notification: TKNotification) => {
      handler.call(observer, notification);
      // 执行一次后自动移除
      this.notificationCenter.removeObserver(observer, notificationName);
    };

    this.notificationCenter.addObserver(
      observer,
      onceHandler,
      notificationName
    );
  }

  // 注册条件监听器
  registerConditionalListener(
    observer: any,
    handler: Function,
    notificationName: string,
    condition: (notification: TKNotification) => boolean
  ) {
    this.observers.add(observer);

    const conditionalHandler = (notification: TKNotification) => {
      if (condition(notification)) {
        handler.call(observer, notification);
      }
    };

    this.notificationCenter.addObserver(
      observer,
      conditionalHandler,
      notificationName
    );
  }

  // 发送延迟通知
  postDelayedNotification(
    notificationName: string,
    delay: number,
    obj?: any,
    userInfo?: Record<string, any>
  ) {
    setTimeout(() => {
      this.notificationCenter.postNotificationName(notificationName, obj, userInfo);
    }, delay);
  }

  // 发送重复通知
  postRepeatedNotification(
    notificationName: string,
    interval: number,
    maxCount: number,
    obj?: any,
    userInfo?: Record<string, any>
  ): number {
    let count = 0;

    const intervalId = setInterval(() => {
      count++;

      const extendedUserInfo = {
        ...userInfo,
        repeatCount: count,
        maxCount: maxCount
      };

      this.notificationCenter.postNotificationName(
        notificationName,
        obj,
        extendedUserInfo
      );

      if (count >= maxCount) {
        clearInterval(intervalId);
      }
    }, interval);

    return intervalId;
  }

  // 清理所有注册的观察者
  cleanup() {
    this.observers.forEach(observer => {
      this.notificationCenter.removeObserver(observer);
    });
    this.observers.clear();
  }
}

// 通知过滤器
class NotificationFilter {
  private notificationCenter = TKNotificationCenter.defaultCenter;
  private filters: Map<string, Function[]> = new Map();

  // 添加通知过滤器
  addFilter(notificationName: string, filter: (notification: TKNotification) => boolean) {
    const filters = this.filters.get(notificationName) || [];
    filters.push(filter);
    this.filters.set(notificationName, filters);
  }

  // 发送带过滤的通知
  postFilteredNotification(
    notificationName: string,
    obj?: any,
    userInfo?: Record<string, any>
  ) {
    const notification = new TKNotification(notificationName, obj, userInfo);
    const filters = this.filters.get(notificationName) || [];

    // 检查所有过滤器
    const shouldPost = filters.every(filter => filter(notification));

    if (shouldPost) {
      this.notificationCenter.postNotificationName(notificationName, obj, userInfo);
    } else {
      console.log(`通知 ${notificationName} 被过滤器拦截`);
    }
  }
}

// 使用高级功能
class DataService {
  private notificationManager = new AdvancedNotificationManager();
  private notificationFilter = new NotificationFilter();

  constructor() {
    this.setupAdvancedListeners();
    this.setupFilters();
  }

  private setupAdvancedListeners() {
    // 批量注册监听器
    const handlers = new Map([
      ['NOTE_DATA_LOADING', this.handleDataLoading],
      ['NOTE_DATA_LOADED', this.handleDataLoaded],
      ['NOTE_DATA_ERROR', this.handleDataError]
    ]);

    this.notificationManager.registerBatchListeners(this, handlers);

    // 注册一次性监听器
    this.notificationManager.registerOnceListener(
      this,
      this.handleFirstTimeSetup,
      'NOTE_FIRST_TIME_SETUP'
    );

    // 注册条件监听器
    this.notificationManager.registerConditionalListener(
      this,
      this.handleImportantData,
      'NOTE_DATA_UPDATED',
      (notification) => {
        return notification.userInfo?.priority === 'high';
      }
    );
  }

  private setupFilters() {
    // 添加数据更新频率过滤器
    this.notificationFilter.addFilter('NOTE_DATA_UPDATED', (notification) => {
      const lastUpdate = this.getLastUpdateTime();
      const now = Date.now();
      return (now - lastUpdate) > 1000; // 最少间隔1秒
    });
  }

  private handleDataLoading = (notification: TKNotification) => {
    console.log('数据加载中...');
  }

  private handleDataLoaded = (notification: TKNotification) => {
    console.log('数据加载完成');
  }

  private handleDataError = (notification: TKNotification) => {
    console.error('数据加载错误:', notification.userInfo);
  }

  private handleFirstTimeSetup = (notification: TKNotification) => {
    console.log('首次设置完成');
  }

  private handleImportantData = (notification: TKNotification) => {
    console.log('处理重要数据更新');
  }

  private getLastUpdateTime(): number {
    // 模拟获取上次更新时间
    return Date.now() - 500;
  }

  // 发送数据更新通知
  updateData(data: any, priority: string = 'normal') {
    this.notificationFilter.postFilteredNotification(
      'NOTE_DATA_UPDATED',
      data,
      { priority, timestamp: Date.now() }
    );
  }

  // 发送延迟通知
  scheduleDataSync() {
    this.notificationManager.postDelayedNotification(
      'NOTE_DATA_SYNC',
      5000, // 5秒后
      undefined,
      { scheduled: true }
    );
  }

  // 发送重复心跳通知
  startHeartbeat() {
    return this.notificationManager.postRepeatedNotification(
      'NOTE_HEARTBEAT',
      1000, // 每秒
      10,   // 10次
      undefined,
      { source: 'DataService' }
    );
  }

  destroy() {
    this.notificationManager.cleanup();
  }
}

// 使用示例
const dataService = new DataService();

// 测试数据更新
dataService.updateData({ id: 1, value: 'test' }, 'high');
dataService.updateData({ id: 2, value: 'normal' }, 'normal');

// 测试延迟通知
dataService.scheduleDataSync();

// 测试重复通知
const heartbeatId = dataService.startHeartbeat();

// 清理资源
setTimeout(() => {
  dataService.destroy();
}, 15000);
```

## 📚 相关资源

### 官方文档
- TypeScript官方文档 - WeakRef和Map
- 观察者模式设计文档

### 参考资料
- 《设计模式：可复用面向对象软件的基础》- 观察者模式
- 《JavaScript内存管理》- 弱引用和垃圾回收

### 相关文件
- `基础服务-TKNotification-解释文档.md` - 通知对象数据载体
- `TKJSNotiProxyManager.ets` - JS通知代理管理
- 各种监听器实现 - 通知中心的实际应用
