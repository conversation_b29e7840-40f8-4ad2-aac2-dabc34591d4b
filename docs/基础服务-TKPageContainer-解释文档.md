# TKPageContainer

> **文件路径**: `src/main/ets/components/common/TKPageContainer.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 鸿蒙组件开发、生命周期管理、观察者模式、主题切换

## 📋 文件概述

### 功能定位
TKPageContainer是HMThinkBaseHar框架的**页面容器组件**，提供了页面的基础布局容器和生命周期管理功能。它采用组件化设计，支持页面显示隐藏监听、主题切换、路由拦截等企业级功能，为应用页面提供统一的容器基础。

### 核心职责
- **页面容器布局**：提供基础的页面布局容器，支持内容自适应
- **生命周期管理**：管理页面的显示隐藏生命周期事件
- **主题切换支持**：监听主题变化并自动更新样式
- **路由拦截集成**：集成路由拦截器，支持页面级别的拦截处理
- **事件监听管理**：监听页面、滚动等多种事件类型

### 设计模式
- **组件模式**：使用@Component装饰器定义可复用组件
- **观察者模式**：监听主题变化、路由事件等系统通知
- **模板方法模式**：提供可重写的生命周期方法
- **弱引用模式**：使用WeakRef管理页面对象引用

## 🔧 核心功能

### 主要属性

#### 属性1：`isLayoutFitContent: boolean`
- **功能**: 是否根据内容自动撑高
- **类型**: @State boolean
- **默认值**: false
- **使用场景**: 需要容器高度自适应内容时

#### 属性2：`isOnRouterPageShowOrHide: boolean`
- **功能**: 是否监听路由页面展示或隐藏拦截
- **类型**: boolean
- **默认值**: false
- **使用场景**: 需要路由级别拦截处理时

#### 属性3：`page: WeakRef<TKPageContainerInterface>`
- **功能**: 页面容器接口的弱引用
- **类型**: WeakRef<TKPageContainerInterface>
- **使用场景**: 页面生命周期回调处理

#### 属性4：`contentBuilder: () => void`
- **功能**: 页面容器内容构建器
- **类型**: @BuilderParam
- **使用场景**: 自定义页面内容布局

### 主要方法

#### 方法1：`onPageShow(type: TKPageShowHideType)`
- **功能**: 页面显示时的处理逻辑
- **参数**: TKPageShowHideType - 显示类型（Page/Scroll/Other）
- **使用场景**: 页面显示时的初始化和状态更新

#### 方法2：`onPageHide(type: TKPageShowHideType)`
- **功能**: 页面隐藏时的处理逻辑
- **参数**: TKPageShowHideType - 隐藏类型（Page/Scroll/Other）
- **使用场景**: 页面隐藏时的清理和状态保存

## 💡 技术要点

### 核心算法/逻辑

#### 页面可见性监听机制
```typescript
.onVisibleAreaChange([0.0, 1.0], (isVisible: boolean, currentRatio: number) => {
  if (this.isPageEvent) {
    this.isPageEvent = false;
    return;
  }
  
  let pageObj = this.page?.deref();
  let pageShowHideType = this.isScrollEvent ? TKPageShowHideType.Scroll : TKPageShowHideType.Other;
  
  if (!isVisible) {
    if (this.isPageShow) {
      this.onPageHide(pageShowHideType);
      if (pageObj && pageObj.onPageHide) {
        pageObj.onPageHide(pageShowHideType);
      }
    }
  } else if (isVisible) {
    if (!this.isPageShow) {
      this.onPageShow(pageShowHideType);
      if (pageObj && pageObj.onPageShow) {
        pageObj.onPageShow(pageShowHideType);
      }
    }
  }
})
```

#### 主题切换处理机制
```typescript
private freshCss(theme: string) {
  if (this.isPageShow) {
    if (this.theme != theme) {
      this.theme = theme;
      if (this.onThemeChange) {
        // 自定义主题切换处理
        this.onThemeChange(theme, this.styleAttribute);
      } else {
        // 默认样式属性重新创建
        if (this.styleAttribute) {
          if (Array.isArray(this.styleAttribute)) {
            this.styleAttribute.forEach((styleAttribute: TKStyleAttribute, index: number) => {
              let constructor: FunctionConstructor = styleAttribute.constructor as FunctionConstructor;
              let newStyleAttribute: Object = new constructor();
              TKObjectHelper.assign(styleAttribute as Object, newStyleAttribute as Object);
            });
          } else {
            let constructor: FunctionConstructor = this.styleAttribute.constructor as FunctionConstructor;
            let newStyleAttribute: Object = new constructor();
            TKObjectHelper.assign(this.styleAttribute as Object, newStyleAttribute as Object);
          }
        }
      }
    }
  }
}
```

#### 路由拦截器集成
```typescript
private async onRouterPageShow() {
  if (this.isOnRouterPageShowOrHide) {
    let routerFilters: Array<TKRouterFilter> = TKRouterHelper.getRegisterRouterFilters();
    for (let routerFilter of routerFilters) {
      if (routerFilter.pageShow) {
        let isPass: boolean = await routerFilter.pageShow(this.pageName, this.pageInfo);
        if (!isPass) {
          break; // 拦截器返回false时停止后续处理
        }
      }
    }
  }
}
```

#### 路由页面事件监听
```typescript
private handleRouterPageInfoListener: Callback<RouterPageInfo> = async (info: RouterPageInfo) => {
  let curRouterInfo: RouterPageInfo | undefined = this.queryRouterPageInfo();
  if (info.pageId == curRouterInfo?.pageId) {
    this.isPageEvent = true;
    let pageObj = this.page?.deref();
    
    if (info.state == uiObserver.RouterPageState.ON_PAGE_SHOW) {
      if (!this.isPageShow) {
        this.onPageShow(TKPageShowHideType.Page);
        if (pageObj?.onPageShow) {
          pageObj?.onPageShow(TKPageShowHideType.Page);
        }
      }
    } else if (info.state == uiObserver.RouterPageState.ON_PAGE_HIDE) {
      if (this.isPageShow) {
        this.onPageHide(TKPageShowHideType.Page);
        if (pageObj?.onPageHide) {
          pageObj?.onPageHide(TKPageShowHideType.Page);
        }
      }
    }
  }
}
```

### 实现机制分析

#### 多事件源监听
- **可见性变化**：通过onVisibleAreaChange监听组件可见性
- **路由页面事件**：通过uiObserver监听路由页面状态变化
- **滚动事件**：通过uiObserver监听滚动开始和结束
- **主题变化**：通过通知中心监听主题切换事件

#### 事件类型区分
- **Page事件**：由路由页面状态变化触发
- **Scroll事件**：由滚动操作触发的可见性变化
- **Other事件**：其他原因导致的可见性变化

#### 弱引用管理
- **内存安全**：使用WeakRef避免循环引用
- **自动清理**：当页面对象被回收时，弱引用自动失效
- **安全访问**：通过deref()方法安全访问引用对象

#### 样式动态更新
- **构造函数反射**：通过constructor获取样式类的构造函数
- **对象重建**：主题切换时重新创建样式对象
- **属性复制**：使用TKObjectHelper.assign复制属性

### 性能考虑
- **事件去重**：通过isPageEvent标识避免重复事件处理
- **状态缓存**：缓存页面显示状态，避免重复操作
- **弱引用**：使用WeakRef减少内存占用和循环引用
- **条件监听**：只在需要时注册事件监听器

### 错误处理
- **空值检查**：对弱引用对象进行空值检查
- **异常隔离**：单个事件处理异常不影响其他功能
- **状态保护**：通过状态标识防止异常状态
- **资源清理**：组件销毁时自动清理监听器

### 最佳实践
- **接口实现**：通过TKPageContainerInterface定义标准接口
- **生命周期管理**：合理使用aboutToAppear和aboutToDisappear
- **事件监听**：及时注册和注销事件监听器
- **主题适配**：支持动态主题切换功能

## 🔗 依赖关系

### 依赖的模块
- `TKNotificationCenter` - 通知中心，监听主题变化事件
- `TKThemeManager` - 主题管理器，获取当前主题信息
- `TKRouterHelper` - 路由辅助类，获取路由拦截器
- `TKStyleAttribute` - 样式属性类，页面样式定义
- `uiObserver` - UI观察者，监听页面和滚动事件

### 被依赖情况
- `TKWebPage` - Web页面组件，使用页面容器作为基础布局
- `TKVideoPage` - 视频页面组件，使用页面容器管理生命周期
- `各业务页面` - 使用页面容器提供统一的页面基础功能

### 关联文件
- `TKPageContainerInterface.ets` - 页面容器接口定义
- `TKStyleAttribute.ets` - 样式属性基类
- `TKThemeManager.ets` - 主题管理器
- `TKRouterHelper.ets` - 路由辅助类

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKPageContainer是框架页面开发的基础组件：
1. **基础容器**：为页面提供统一的容器和布局基础
2. **生命周期管理**：提供完整的页面生命周期管理功能
3. **主题支持**：内置主题切换支持，提升用户体验
4. **扩展性**：支持自定义内容构建和样式配置

理解TKPageContainer有助于掌握框架的页面开发模式，是构建标准化页面的重要基础。

## 🎯 学习建议

### 学习路径
1. **理解组件开发**：掌握鸿蒙@Component组件的开发方式
2. **分析生命周期**：理解页面显示隐藏的生命周期管理
3. **学习事件监听**：掌握多种事件源的监听和处理
4. **实践页面开发**：使用页面容器构建实际的页面

### 前置学习
- 鸿蒙组件开发基础
- @State、@BuilderParam等装饰器使用
- 观察者模式和事件监听
- WeakRef弱引用概念

### 后续学习
- `TKWeb.ets` - 学习WebView组件的使用
- `TKTitleBar.ets` - 了解标题栏组件
- `TKThemeManager.ets` - 掌握主题管理机制

### 实践建议
1. **基础容器使用**：创建使用页面容器的简单页面
2. **生命周期测试**：测试页面显示隐藏的生命周期回调
3. **主题切换实验**：实现页面的主题切换功能
4. **自定义扩展**：基于页面容器开发自定义页面组件

### 常见问题
1. **问题**: 什么时候使用isLayoutFitContent？
   **解答**: 当页面内容高度不固定，需要容器根据内容自动调整高度时使用，如表单页面、列表页面等。

2. **问题**: 如何实现自定义的页面生命周期处理？
   **解答**: 实现TKPageContainerInterface接口，并将页面对象的弱引用赋值给page属性，容器会自动调用接口方法。

## 📝 代码示例

### 基础使用
```typescript
import { TKPageContainer, TKPageContainerInterface, TKPageShowHideType } from '@thinkive/tk-harmony-base';

// 实现页面容器接口
class MyPageController implements TKPageContainerInterface {
  onPageShow(type?: TKPageShowHideType): void {
    console.log('页面显示:', type);
    switch (type) {
      case TKPageShowHideType.Page:
        console.log('路由页面显示');
        break;
      case TKPageShowHideType.Scroll:
        console.log('滚动导致的显示');
        break;
      case TKPageShowHideType.Other:
        console.log('其他原因导致的显示');
        break;
    }
  }

  onPageHide(type?: TKPageShowHideType): void {
    console.log('页面隐藏:', type);
    // 页面隐藏时的清理工作
    this.savePageState();
  }

  private savePageState() {
    console.log('保存页面状态');
  }
}

// 基础页面组件
@Component
struct MyBasicPage {
  private pageController = new MyPageController();

  build() {
    TKPageContainer({
      page: new WeakRef(this.pageController),
      contentBuilder: () => {
        this.buildPageContent()
      }
    })
  }

  @Builder
  buildPageContent() {
    Column() {
      Text('页面内容')
        .fontSize(16)
        .margin(20)

      Button('测试按钮')
        .onClick(() => {
          console.log('按钮点击');
        })
    }
    .width('100%')
    .height('100%')
  }
}

// 自适应高度页面
@Component
struct MyFitContentPage {
  build() {
    TKPageContainer({
      isLayoutFitContent: true,
      contentBuilder: () => {
        this.buildContent()
      }
    })
  }

  @Builder
  buildContent() {
    Column() {
      Text('标题')
        .fontSize(20)
        .fontWeight(FontWeight.Bold)
        .margin(10)

      Text('这是一个自适应高度的页面容器示例。容器会根据内容的实际高度进行调整。')
        .fontSize(14)
        .margin(10)
        .textAlign(TextAlign.Center)

      Button('确定')
        .margin(20)
    }
    .width('100%')
    .padding(20)
  }
}
```

### 高级用法
```typescript
// 带主题切换的页面
@Component
struct MyThemedPage {
  @State private currentTheme: string = 'light';
  private pageController = new MyPageController();

  build() {
    TKPageContainer({
      page: new WeakRef(this.pageController),
      onThemeChange: (theme: string, styleAttribute?: any) => {
        this.handleThemeChange(theme, styleAttribute);
      },
      contentBuilder: () => {
        this.buildThemedContent()
      }
    })
  }

  private handleThemeChange(theme: string, styleAttribute?: any) {
    console.log('主题切换:', theme);
    this.currentTheme = theme;

    // 自定义主题切换逻辑
    if (theme === 'dark') {
      this.applyDarkTheme();
    } else {
      this.applyLightTheme();
    }
  }

  private applyDarkTheme() {
    console.log('应用暗色主题');
  }

  private applyLightTheme() {
    console.log('应用亮色主题');
  }

  @Builder
  buildThemedContent() {
    Column() {
      Text(`当前主题: ${this.currentTheme}`)
        .fontSize(16)
        .fontColor(this.currentTheme === 'dark' ? Color.White : Color.Black)
        .margin(20)

      Button('切换主题')
        .onClick(() => {
          // 触发主题切换
          const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
          // 这里应该调用主题管理器的切换方法
          console.log('切换到:', newTheme);
        })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.currentTheme === 'dark' ? Color.Black : Color.White)
  }
}

// 带路由拦截的页面
@Component
struct MyInterceptedPage {
  private pageController = new MyPageController();

  build() {
    TKPageContainer({
      page: new WeakRef(this.pageController),
      isOnRouterPageShowOrHide: true,
      pageName: 'MyInterceptedPage',
      pageInfo: {
        level: 'high',
        requireAuth: true
      },
      contentBuilder: () => {
        this.buildContent()
      }
    })
  }

  @Builder
  buildContent() {
    Column() {
      Text('受保护的页面')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .margin(20)

      Text('此页面启用了路由拦截功能')
        .fontSize(14)
        .margin(10)

      Button('敏感操作')
        .onClick(() => {
          console.log('执行敏感操作');
        })
    }
    .width('100%')
    .height('100%')
  }
}
```

### 扩展示例
```typescript
// 页面容器管理器
class PageContainerManager {
  private static instance: PageContainerManager;
  private pageControllers: Map<string, TKPageContainerInterface> = new Map();

  static getInstance(): PageContainerManager {
    if (!PageContainerManager.instance) {
      PageContainerManager.instance = new PageContainerManager();
    }
    return PageContainerManager.instance;
  }

  // 注册页面控制器
  registerPageController(pageId: string, controller: TKPageContainerInterface) {
    this.pageControllers.set(pageId, controller);
  }

  // 获取页面控制器
  getPageController(pageId: string): TKPageContainerInterface | undefined {
    return this.pageControllers.get(pageId);
  }

  // 通知所有页面显示
  notifyAllPagesShow() {
    this.pageControllers.forEach((controller, pageId) => {
      if (controller.onPageShow) {
        controller.onPageShow(TKPageShowHideType.Other);
      }
    });
  }

  // 通知所有页面隐藏
  notifyAllPagesHide() {
    this.pageControllers.forEach((controller, pageId) => {
      if (controller.onPageHide) {
        controller.onPageHide(TKPageShowHideType.Other);
      }
    });
  }

  // 清理页面控制器
  unregisterPageController(pageId: string) {
    this.pageControllers.delete(pageId);
  }
}

// 高级页面控制器
class AdvancedPageController implements TKPageContainerInterface {
  private pageId: string;
  private pageState: 'hidden' | 'showing' | 'shown' = 'hidden';
  private showTime: number = 0;
  private hideTime: number = 0;

  constructor(pageId: string) {
    this.pageId = pageId;
    PageContainerManager.getInstance().registerPageController(pageId, this);
  }

  onPageShow(type?: TKPageShowHideType): void {
    this.pageState = 'showing';
    this.showTime = Date.now();

    console.log(`页面 ${this.pageId} 显示 - 类型: ${type}, 时间: ${this.showTime}`);

    // 延迟标记为已显示
    setTimeout(() => {
      this.pageState = 'shown';
    }, 100);

    // 记录页面访问
    this.recordPageAccess(type);
  }

  onPageHide(type?: TKPageShowHideType): void {
    this.pageState = 'hidden';
    this.hideTime = Date.now();

    const duration = this.hideTime - this.showTime;
    console.log(`页面 ${this.pageId} 隐藏 - 类型: ${type}, 停留时间: ${duration}ms`);

    // 保存页面状态
    this.savePageState();

    // 记录页面停留时间
    this.recordPageDuration(duration, type);
  }

  private recordPageAccess(type?: TKPageShowHideType) {
    const accessRecord = {
      pageId: this.pageId,
      showType: type,
      timestamp: this.showTime,
      userAgent: 'HarmonyOS App'
    };

    console.log('页面访问记录:', accessRecord);
    // 这里可以发送到分析服务
  }

  private recordPageDuration(duration: number, type?: TKPageShowHideType) {
    const durationRecord = {
      pageId: this.pageId,
      hideType: type,
      duration: duration,
      timestamp: this.hideTime
    };

    console.log('页面停留记录:', durationRecord);
    // 这里可以发送到分析服务
  }

  private savePageState() {
    const pageState = {
      pageId: this.pageId,
      state: this.pageState,
      lastShowTime: this.showTime,
      lastHideTime: this.hideTime
    };

    // 保存到本地存储
    console.log('保存页面状态:', pageState);
  }

  // 获取页面状态
  getPageState(): string {
    return this.pageState;
  }

  // 获取页面停留时间
  getStayDuration(): number {
    if (this.pageState === 'shown') {
      return Date.now() - this.showTime;
    }
    return this.hideTime - this.showTime;
  }

  // 销毁时清理
  destroy() {
    PageContainerManager.getInstance().unregisterPageController(this.pageId);
  }
}

// 使用高级页面控制器的组件
@Component
struct MyAdvancedPage {
  private pageController = new AdvancedPageController('MyAdvancedPage');
  @State private stayDuration: number = 0;

  build() {
    TKPageContainer({
      page: new WeakRef(this.pageController),
      contentBuilder: () => {
        this.buildAdvancedContent()
      }
    })
  }

  @Builder
  buildAdvancedContent() {
    Column() {
      Text('高级页面容器示例')
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .margin(20)

      Text(`页面状态: ${this.pageController.getPageState()}`)
        .fontSize(14)
        .margin(10)

      Text(`停留时间: ${this.stayDuration}ms`)
        .fontSize(14)
        .margin(10)

      Button('更新停留时间')
        .onClick(() => {
          this.stayDuration = this.pageController.getStayDuration();
        })
        .margin(10)

      Button('通知所有页面显示')
        .onClick(() => {
          PageContainerManager.getInstance().notifyAllPagesShow();
        })
        .margin(10)
    }
    .width('100%')
    .height('100%')
  }

  aboutToDisappear() {
    this.pageController.destroy();
  }
}
```

## 📚 相关资源

### 官方文档
- HarmonyOS组件开发指南
- ArkUI组件生命周期文档

### 参考资料
- 《鸿蒙应用开发实战》- 组件化开发
- 《设计模式：可复用面向对象软件的基础》- 观察者模式

### 相关文件
- `TKStyleAttribute.ets` - 样式属性基类
- `TKThemeManager.ets` - 主题管理器
- `TKWeb.ets` - WebView组件实现
