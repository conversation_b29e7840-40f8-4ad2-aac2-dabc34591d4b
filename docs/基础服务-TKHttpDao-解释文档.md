# TKHttpDao

> **文件路径**: `src/main/ets/base/mvc/service/dao/http/TKHttpDao.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐⭐ (4星)  
> **学习难度**: 中级  
> **前置知识**: HTTP协议、鸿蒙网络API、单例模式、异步编程、请求管理

## 📋 文件概述

### 功能定位
TKHttpDao是HMThinkBaseHar框架的**HTTP数据访问实现类**，继承自TKBaseHttpDao，提供完整的HTTP请求处理能力。它采用单例模式设计，管理HTTP请求的生命周期，支持请求队列、分组管理、取消机制等企业级功能。

### 核心职责
- **HTTP请求执行**：使用鸿蒙RemoteCommunicationKit执行HTTP请求
- **请求生命周期管理**：管理请求的创建、执行、完成和清理
- **请求队列管理**：维护活跃请求的队列和分组映射
- **会话管理**：管理HTTP会话和连接的创建与销毁
- **请求取消机制**：支持单个请求和批量请求的取消操作

### 设计模式
- **单例模式**：确保全局唯一的HTTP数据访问实例
- **继承模式**：继承TKBaseHttpDao，复用HTTP基础功能
- **策略模式**：支持不同的HTTP方法和配置策略

## 🔧 核心功能

### 主要API/方法

#### 方法1：`shareInstance()`
- **功能**: 获取TKHttpDao的单例实例
- **参数**: 无
- **返回值**: TKHttpDao实例
- **使用场景**: 任何需要进行HTTP数据访问的地方

#### 方法2：`invoke(reqParamVO: TKReqParamVO)`
- **功能**: 执行HTTP请求的入口方法
- **参数**: TKReqParamVO - 请求参数对象
- **返回值**: void
- **使用场景**: 发起HTTP请求时的统一入口

#### 方法3：`clearRequest(flowNo: string)`
- **功能**: 取消并清理指定流水号的HTTP请求
- **参数**: string - 请求流水号
- **返回值**: void
- **使用场景**: 需要取消特定HTTP请求时

#### 方法4：`clearGroup(groupNo: string)`
- **功能**: 清理指定组号的所有HTTP请求
- **参数**: string - 请求组号
- **返回值**: void
- **使用场景**: 需要批量取消某个组的HTTP请求时

### 关键私有方法

#### 方法1：`invokeAsyncHttp(reqParamVO: TKReqParamVO)`
- **功能**: 异步执行HTTP请求的核心实现
- **参数**: TKReqParamVO - 请求参数对象
- **处理内容**: 构建请求、发送请求、管理会话

#### 方法2：`clearGroupRequest(reqParamVO: TKReqParamVO)`
- **功能**: 清理组请求的内部逻辑
- **参数**: TKReqParamVO - 请求参数对象
- **处理内容**: 维护组映射关系

### 关键属性/配置
- `requestMap: Map<string, TKRequest>` - 请求映射表，按流水号索引
- `groupMap: Map<string, Map<string, TKRequest>>` - 分组映射表，按组号管理请求
- `defaultRequesTimeOutSeconds: number` - 默认超时时间（60秒）

## 💡 技术要点

### 核心算法/逻辑

#### HTTP请求执行流程
```typescript
private async invokeAsyncHttp(reqParamVO: TKReqParamVO) {
  try {
    // 1. 构建请求参数
    let url: string = reqParamVO.isURLEncode ? encodeURI(reqParamVO.url) : reqParamVO.url;
    let parameters: Record<string, Object> = await this.getRequestParam(reqParamVO);
    let headers: Record<string, Object> = this.getRequestHeader(reqParamVO);
    let method: string = TKStringHelper.isNotBlank(reqParamVO.httpMethod) ? 
      reqParamVO.httpMethod.toUpperCase() : (reqParamVO.isPost ? "POST" : "GET");
    
    // 2. 构建请求配置
    let configuration: rcp.Configuration = {
      transfer: {
        timeout: {
          connectMs: reqParamVO.timeOut > 0 ? reqParamVO.timeOut * 1000 : 
            (reqParamVO.isUpload ? Number.MAX_VALUE : this.defaultRequesTimeOutSeconds * 1000)
        }
      }
    };
    
    // 3. 创建请求对象和会话
    let reqSource: rcp.Request = new rcp.Request(url, method, headers, content, undefined, undefined, configuration);
    const reqSession = rcp.createSession();
    
    // 4. 发送请求并处理响应
    reqSession.fetch(reqSource).then((response) => {
      this.requestFinished(reqParamVO, response);
    }).catch((error: BusinessError<rcp.Response>) => {
      this.requestFailed(reqParamVO, error.data as rcp.Response, error);
    });
    
    // 5. 存储请求信息
    this.requestMap.set(reqParamVO.flowNo, { reqParamVO, reqSource, reqSession } as TKRequest);
    
    // 6. 处理分组映射
    if (TKStringHelper.isNotBlank(reqParamVO.group)) {
      let groupReqMap: Map<string, TKRequest> = this.groupMap.get(reqParamVO.group) ?? new Map<string, TKRequest>();
      groupReqMap.set(reqParamVO.flowNo, { reqParamVO } as TKRequest);
      this.groupMap.set(reqParamVO.group, groupReqMap);
    }
  } catch (error) {
    TKLog.error(`HTTP请求异常: ${error.message}`);
  }
}
```

#### 请求取消机制
```typescript
public clearRequest(flowNo: string): void {
  try {
    if (TKStringHelper.isNotBlank(flowNo)) {
      let request: TKRequest | undefined = this.requestMap.get(flowNo);
      this.requestMap.delete(flowNo);
      
      if (request) {
        // 取消请求
        request.reqSession.cancel(request.reqSource);
        // 关闭会话
        request.reqSession.close();
        // 清理分组映射
        this.clearGroupRequest(request.reqParamVO);
      }
    }
  } catch (error) {
    TKLog.error(`请求取消异常: ${error.message}`);
  }
}
```

#### 分组管理机制
```typescript
private clearGroupRequest(reqParamVO: TKReqParamVO): void {
  try {
    if (TKStringHelper.isNotBlank(reqParamVO.group)) {
      let groupRequestMap: Map<string, TKRequest> | undefined = this.groupMap.get(reqParamVO.group);
      if (groupRequestMap) {
        groupRequestMap.delete(reqParamVO.flowNo);
        
        // 如果组内没有请求了，清理整个组
        if (groupRequestMap.size == 0) {
          this.clearGroup(reqParamVO.group);
          this.close(reqParamVO);
        }
      }
    }
  } catch (error) {
    TKLog.error(`分组请求清理异常: ${error.message}`);
  }
}
```

### 实现机制分析

#### 单例模式实现
- **私有构造函数**：防止外部直接实例化
- **静态实例管理**：通过静态变量维护唯一实例
- **线程安全**：在单线程环境下保证实例唯一性

#### 请求生命周期管理
1. **请求创建**：构建rcp.Request对象和会话
2. **请求存储**：将请求信息存储到映射表中
3. **请求执行**：通过会话发送请求
4. **响应处理**：处理成功或失败的响应
5. **请求清理**：从映射表中移除并关闭会话

#### 分组管理策略
- **双层映射**：requestMap管理所有请求，groupMap管理分组
- **自动清理**：组内最后一个请求完成时自动清理组
- **批量操作**：支持按组批量取消请求

### 性能考虑
- **连接复用**：通过会话管理实现连接复用
- **内存管理**：请求完成后及时清理映射关系
- **超时控制**：支持自定义超时时间，防止请求阻塞
- **异步处理**：所有HTTP操作都是异步的，不阻塞主线程

### 错误处理
- **网络异常处理**：捕获并处理网络连接异常
- **超时处理**：支持连接超时和读取超时的处理
- **取消异常处理**：请求取消时的异常保护
- **资源清理**：异常情况下确保资源正确释放

### 最佳实践
- **单例使用**：通过shareInstance()获取实例，避免重复创建
- **请求管理**：合理使用分组功能管理相关请求
- **资源清理**：及时清理不需要的请求，避免内存泄漏
- **异常处理**：完善的异常处理机制，确保应用稳定性

## 🔗 依赖关系

### 依赖的模块
- `TKBaseHttpDao` - HTTP数据访问基类，提供基础HTTP功能
- `TKReqParamVO` - 请求参数对象，HTTP请求的数据载体
- `rcp` - 鸿蒙RemoteCommunicationKit，底层网络通信API
- `TKLog` - 日志工具，记录请求和错误信息
- `TKStringHelper` - 字符串工具，参数验证和处理

### 被依赖情况
- `TKDaoFactory` - DAO工厂，创建TKHttpDao实例
- `业务服务类` - 通过DAO工厂获取HTTP数据访问能力
- `网络请求模块` - 使用HTTP DAO进行网络数据访问

### 关联文件
- `TKBaseHttpDao.ets` - HTTP数据访问基类，直接父类
- `TKDaoFactory.ets` - DAO工厂，管理HTTP DAO实例
- `TKReqParamVO.ets` - 请求参数定义，数据载体
- `TKBaseDao.ets` - 数据访问抽象基类，间接父类

## 📊 重要程度评级

### 评级标准
⭐⭐⭐⭐ **重要推荐**: 常用功能，建议掌握

### 本文件评级理由
TKHttpDao是框架网络通信的核心实现：
1. **网络访问基础**：提供完整的HTTP请求处理能力
2. **企业级特性**：支持请求管理、分组、取消等高级功能
3. **性能优化**：通过会话管理和连接复用提升性能
4. **稳定可靠**：完善的错误处理和资源管理机制

理解TKHttpDao是掌握框架网络通信机制的关键，它是数据访问层的核心组件。

## 🎯 学习建议

### 学习路径
1. **理解HTTP基础**：掌握HTTP协议和鸿蒙网络API的基本使用
2. **分析继承关系**：理解与TKBaseHttpDao的继承关系和功能分工
3. **学习请求管理**：掌握请求生命周期和分组管理机制
4. **实践网络请求**：使用HTTP DAO进行实际的网络数据访问

### 前置学习
- HTTP协议基础知识
- 鸿蒙RemoteCommunicationKit使用
- 异步编程和Promise处理
- 单例模式的实现原理

### 后续学习
- `TKBaseHttpDao.ets` - 了解HTTP基础功能的实现
- `TKDaoFactory.ets` - 学习DAO工厂的管理机制
- `TKReqParamVO.ets` - 掌握请求参数的配置方法

### 实践建议
1. **基础请求练习**：使用HTTP DAO发送GET和POST请求
2. **分组管理实验**：创建分组请求并测试批量取消功能
3. **错误处理测试**：模拟网络异常，观察错误处理机制
4. **性能优化实践**：分析请求性能，优化超时和重试策略

### 常见问题
1. **问题**: 为什么要使用单例模式？
   **解答**: HTTP DAO需要管理全局的请求状态和连接资源，单例模式确保资源的统一管理和避免重复创建的开销。

2. **问题**: 请求分组的作用是什么？
   **解答**: 分组允许按业务逻辑组织相关请求，支持批量取消操作，特别适用于页面切换时取消该页面的所有请求。

## 📝 代码示例

### 基础使用
```typescript
import { TKHttpDao, TKReqParamVO, TKDaoType } from '@thinkive/tk-harmony-base';

// 获取HTTP DAO实例
const httpDao = TKHttpDao.shareInstance();

// 创建GET请求
function sendGetRequest() {
  const reqParam = new TKReqParamVO();
  reqParam.flowNo = "GET_001";
  reqParam.url = "https://api.example.com/users";
  reqParam.isPost = false;
  reqParam.protocol = TKDaoType.Http;
  reqParam.timeOut = 30; // 30秒超时

  // 设置请求参数
  reqParam.reqParam = {
    page: 1,
    size: 20,
    status: "active"
  };

  // 设置请求头
  reqParam.headerFieldDic = {
    "Authorization": "Bearer token123",
    "Content-Type": "application/json"
  };

  // 发送请求
  httpDao.invoke(reqParam);
}

// 创建POST请求
function sendPostRequest() {
  const reqParam = new TKReqParamVO();
  reqParam.flowNo = "POST_001";
  reqParam.url = "https://api.example.com/users";
  reqParam.isPost = true;
  reqParam.protocol = TKDaoType.Http;

  // 设置POST数据
  reqParam.reqParam = {
    name: "张三",
    email: "<EMAIL>",
    age: 25
  };

  httpDao.invoke(reqParam);
}

// 取消单个请求
function cancelRequest() {
  httpDao.clearRequest("GET_001");
}
```

### 高级用法
```typescript
// 分组请求管理
class UserPageManager {
  private httpDao = TKHttpDao.shareInstance();
  private pageGroup = "UserPage";

  // 加载用户页面数据
  loadPageData() {
    // 用户基本信息请求
    this.loadUserInfo();
    // 用户订单列表请求
    this.loadUserOrders();
    // 用户收藏列表请求
    this.loadUserFavorites();
  }

  private loadUserInfo() {
    const reqParam = new TKReqParamVO();
    reqParam.flowNo = "USER_INFO_001";
    reqParam.url = "https://api.example.com/user/profile";
    reqParam.group = this.pageGroup; // 设置分组
    reqParam.isPost = false;
    reqParam.timeOut = 15;

    reqParam.reqParam = {
      userId: "12345"
    };

    this.httpDao.invoke(reqParam);
  }

  private loadUserOrders() {
    const reqParam = new TKReqParamVO();
    reqParam.flowNo = "USER_ORDERS_001";
    reqParam.url = "https://api.example.com/user/orders";
    reqParam.group = this.pageGroup; // 同一分组
    reqParam.isPost = false;

    reqParam.reqParam = {
      userId: "12345",
      status: "all",
      page: 1
    };

    this.httpDao.invoke(reqParam);
  }

  private loadUserFavorites() {
    const reqParam = new TKReqParamVO();
    reqParam.flowNo = "USER_FAVORITES_001";
    reqParam.url = "https://api.example.com/user/favorites";
    reqParam.group = this.pageGroup; // 同一分组
    reqParam.isPost = false;

    this.httpDao.invoke(reqParam);
  }

  // 页面销毁时取消所有请求
  onPageDestroy() {
    console.log(`取消页面 ${this.pageGroup} 的所有请求`);
    this.httpDao.clearGroup(this.pageGroup);
  }

  // 刷新页面数据
  refreshPageData() {
    // 先取消现有请求
    this.httpDao.clearGroup(this.pageGroup);
    // 重新加载数据
    setTimeout(() => {
      this.loadPageData();
    }, 100);
  }
}
```

### 扩展示例
```typescript
// HTTP请求管理器，提供更高级的功能
class HttpRequestManager {
  private httpDao = TKHttpDao.shareInstance();
  private requestCallbacks: Map<string, (result: any) => void> = new Map();
  private requestRetryCount: Map<string, number> = new Map();
  private maxRetryCount = 3;

  // 发送带回调的请求
  sendRequestWithCallback(
    url: string,
    params: Record<string, any>,
    callback: (result: any) => void,
    options: {
      method?: 'GET' | 'POST',
      timeout?: number,
      group?: string,
      headers?: Record<string, string>
    } = {}
  ) {
    const flowNo = this.generateFlowNo();

    // 存储回调函数
    this.requestCallbacks.set(flowNo, callback);

    const reqParam = new TKReqParamVO();
    reqParam.flowNo = flowNo;
    reqParam.url = url;
    reqParam.isPost = options.method === 'POST';
    reqParam.timeOut = options.timeout || 30;
    reqParam.group = options.group || '';

    reqParam.reqParam = params;

    if (options.headers) {
      reqParam.headerFieldDic = options.headers;
    }

    this.httpDao.invoke(reqParam);

    return flowNo; // 返回流水号，用于取消请求
  }

  // 发送带重试的请求
  sendRequestWithRetry(
    url: string,
    params: Record<string, any>,
    callback: (result: any) => void,
    maxRetry: number = 3
  ) {
    const flowNo = this.generateFlowNo();
    this.requestRetryCount.set(flowNo, 0);

    const attemptRequest = () => {
      const reqParam = new TKReqParamVO();
      reqParam.flowNo = flowNo;
      reqParam.url = url;
      reqParam.reqParam = params;

      // 设置重试回调
      this.requestCallbacks.set(flowNo, (result) => {
        if (result.errorNo !== 0) {
          const retryCount = this.requestRetryCount.get(flowNo) || 0;
          if (retryCount < maxRetry) {
            console.log(`请求失败，进行第 ${retryCount + 1} 次重试`);
            this.requestRetryCount.set(flowNo, retryCount + 1);
            setTimeout(attemptRequest, 1000 * Math.pow(2, retryCount)); // 指数退避
          } else {
            console.log(`请求失败，已达到最大重试次数 ${maxRetry}`);
            this.requestRetryCount.delete(flowNo);
            callback(result);
          }
        } else {
          this.requestRetryCount.delete(flowNo);
          callback(result);
        }
      });

      this.httpDao.invoke(reqParam);
    };

    attemptRequest();
    return flowNo;
  }

  // 并发请求管理
  sendConcurrentRequests(
    requests: Array<{
      url: string,
      params: Record<string, any>,
      method?: 'GET' | 'POST'
    }>,
    callback: (results: any[]) => void
  ) {
    const results: any[] = new Array(requests.length);
    let completedCount = 0;
    const group = `CONCURRENT_${Date.now()}`;

    requests.forEach((request, index) => {
      const flowNo = this.generateFlowNo();

      this.requestCallbacks.set(flowNo, (result) => {
        results[index] = result;
        completedCount++;

        if (completedCount === requests.length) {
          callback(results);
        }
      });

      const reqParam = new TKReqParamVO();
      reqParam.flowNo = flowNo;
      reqParam.url = request.url;
      reqParam.isPost = request.method === 'POST';
      reqParam.group = group;
      reqParam.reqParam = request.params;

      this.httpDao.invoke(reqParam);
    });

    return group; // 返回组号，用于取消所有请求
  }

  // 取消请求
  cancelRequest(flowNo: string) {
    this.requestCallbacks.delete(flowNo);
    this.requestRetryCount.delete(flowNo);
    this.httpDao.clearRequest(flowNo);
  }

  // 取消分组请求
  cancelGroup(group: string) {
    // 清理相关回调
    for (const [flowNo, callback] of this.requestCallbacks) {
      // 这里需要根据实际情况判断flowNo是否属于该组
      // 简化处理，实际应用中需要维护flowNo与group的映射关系
    }

    this.httpDao.clearGroup(group);
  }

  private generateFlowNo(): string {
    return `REQ_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// 使用示例
const requestManager = new HttpRequestManager();

// 发送单个请求
const flowNo = requestManager.sendRequestWithCallback(
  'https://api.example.com/data',
  { id: 123 },
  (result) => {
    console.log('请求结果:', result);
  },
  {
    method: 'GET',
    timeout: 30,
    group: 'DataPage'
  }
);

// 发送重试请求
requestManager.sendRequestWithRetry(
  'https://api.example.com/unstable',
  { action: 'test' },
  (result) => {
    console.log('重试请求最终结果:', result);
  },
  3 // 最多重试3次
);

// 发送并发请求
const groupId = requestManager.sendConcurrentRequests([
  { url: 'https://api.example.com/user', params: { id: 1 } },
  { url: 'https://api.example.com/orders', params: { userId: 1 } },
  { url: 'https://api.example.com/profile', params: { userId: 1 } }
], (results) => {
  console.log('所有并发请求完成:', results);
});
```

## 📚 相关资源

### 官方文档
- HarmonyOS网络开发指南 - RemoteCommunicationKit
- HTTP协议规范文档

### 参考资料
- 《HTTP权威指南》- HTTP协议深入理解
- 《设计模式：可复用面向对象软件的基础》- 单例模式

### 相关文件
- `基础服务-TKBaseHttpDao-解释文档.md` - HTTP数据访问基类
- `基础服务-TKBaseDao-解释文档.md` - 数据访问抽象基类
- `基础服务-TKDaoFactory-解释文档.md` - DAO工厂管理
