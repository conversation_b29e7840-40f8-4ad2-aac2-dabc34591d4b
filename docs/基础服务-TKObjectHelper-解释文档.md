# TKObjectHelper

> **文件路径**: `src/main/ets/util/data/TKObjectHelper.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中高级  
> **前置知识**: 对象操作、序列化反序列化、深拷贝浅拷贝、TypeScript泛型

## 📋 文件概述

### 功能定位
TKObjectHelper是HMThinkBaseHar框架的**通用对象操作工具类**，提供了完整的对象处理功能。它采用命名空间设计，集成了对象赋值、深拷贝、序列化反序列化、类型转换等功能，支持复杂数据结构（Map、Set、Array等）的处理，为应用提供强大的对象操作服务。

### 核心职责
- **对象赋值拷贝**：提供深度和浅度的对象赋值拷贝功能
- **序列化反序列化**：支持复杂对象的JSON序列化和反序列化
- **对象克隆**：提供完整的对象深度克隆功能
- **类型转换**：支持JSON与对象之间的相互转换
- **复杂数据结构支持**：支持Map、Set、HashMap、TreeMap等复杂数据结构

### 设计模式
- **命名空间模式**：使用namespace封装对象操作功能
- **工厂模式**：根据对象类型创建相应的实例
- **策略模式**：支持不同的拷贝和序列化策略
- **访问者模式**：遍历复杂对象结构进行处理

## 🔧 核心功能

### 类型定义

#### 类型：`TKJson`
- **定义**: `Record<string, Object> | Array<Record<string, Object>> | undefined`
- **用途**: 定义JSON数据的类型结构
- **使用场景**: JSON数据的类型约束和验证

### 对象操作方法

#### 方法1：`assign<T>(target, source, isDeep?, isOnlyOverwrite?)`
- **功能**: 对象赋值拷贝，支持深度拷贝和覆盖模式
- **参数**: 
  - target - 目标对象
  - source - 源对象
  - isDeep - 是否深度拷贝（默认false）
  - isOnlyOverwrite - 是否仅覆盖已存在的属性（默认false）
- **返回值**: T - 赋值后的目标对象
- **使用场景**: 对象属性合并、配置更新

#### 方法2：`copy<T>(source, isDeep?)`
- **功能**: 对象拷贝，创建新对象并复制属性
- **参数**: 
  - source - 源对象
  - isDeep - 是否深度拷贝（默认false）
- **返回值**: T - 拷贝后的新对象
- **使用场景**: 对象复制、避免引用污染

#### 方法3：`clone<T>(obj)`
- **功能**: 对象深度克隆，完全复制对象结构
- **参数**: obj - 要克隆的对象
- **返回值**: T - 克隆后的新对象
- **使用场景**: 完全独立的对象副本

#### 方法4：`fixDefault<T>(targetObj, TClass, sourceObj?)`
- **功能**: 修复对象的默认属性，确保对象完整性
- **参数**: 
  - targetObj - 目标对象
  - TClass - 对象类构造函数
  - sourceObj - 源对象（可选）
- **返回值**: T - 修复后的对象
- **使用场景**: 对象初始化、属性补全

### 序列化方法

#### 方法1：`serialize<T>(obj, isHumpToUnderLine?)`
- **功能**: 对象序列化为JSON字符串
- **参数**: 
  - obj - 要序列化的对象
  - isHumpToUnderLine - 是否驼峰转下划线（默认false）
- **返回值**: string | undefined - 序列化后的JSON字符串
- **使用场景**: 对象存储、网络传输

#### 方法2：`deserialize<T>(jsonString, TClass?, isUnderLineToHump?)`
- **功能**: JSON字符串反序列化为对象
- **参数**: 
  - jsonString - JSON字符串
  - TClass - 目标类构造函数（可选）
  - isUnderLineToHump - 是否下划线转驼峰（默认false）
- **返回值**: T | Array<T> | undefined - 反序列化后的对象
- **使用场景**: 数据恢复、对象重建

### JSON转换方法

#### 方法1：`toJson<T>(obj, isHumpToUnderLine?)`
- **功能**: 对象转换为JSON格式（不序列化为字符串）
- **参数**: 
  - obj - 要转换的对象
  - isHumpToUnderLine - 是否驼峰转下划线（默认false）
- **返回值**: TKJson - JSON格式的对象
- **使用场景**: JSON数据处理、API数据准备

#### 方法2：`toJsonStr<T>(obj)`
- **功能**: 对象转换为JSON字符串
- **参数**: obj - 要转换的对象
- **返回值**: string - JSON字符串
- **使用场景**: 简化的JSON字符串转换

#### 方法3：`toObject<T>(json, TClass?, isUnderLineToHump?, isOnlyOverwrite?)`
- **功能**: JSON转换为对象实例
- **参数**: 
  - json - JSON数据
  - TClass - 目标类构造函数（可选）
  - isUnderLineToHump - 是否下划线转驼峰（默认false）
  - isOnlyOverwrite - 是否仅覆盖（默认false）
- **返回值**: T | Array<T> | undefined - 转换后的对象
- **使用场景**: API数据转换、对象实例化

## 💡 技术要点

### 核心算法/逻辑

#### 深度赋值算法
```typescript
export function assign<T>(target: T, source: T, isDeep: boolean = false, isOnlyOverwrite: boolean = false): T {
  if (source === undefined || source === null || target === undefined || target === null) {
    target = target ?? source as T;
    return target;
  } else if (Array.isArray(source)) {
    target = source as T;
    return target;
  } else if ((source instanceof Set) || (source instanceof HashSet) || (source instanceof TreeSet)) {
    target = source as T;
    return target;
  } else if ((source instanceof Map) || (source instanceof HashMap) || (source instanceof TreeMap)) {
    // Map类型的深度赋值处理
    source.forEach((value: Object, key: string) => {
      const sourceKey: string = key;
      const sourceValue: Object = value;
      if (!isOnlyOverwrite || TKMapHelper.containKey(target, sourceKey)) {
        if ((target instanceof Map) || (target instanceof HashMap) || (target instanceof TreeMap)) {
          const targetValue: Object = (target as Map<string, Object>).get(sourceKey) as Object;
          if (isDeep) {
            (target as Map<string, Object>).set(sourceKey, assign(targetValue, sourceValue, isDeep));
          } else {
            (target as Map<string, Object>).set(sourceKey, sourceValue ?? targetValue);
          }
        }
      }
    });
    return target;
  } else if (!(source instanceof Uint8Array) && typeof source === 'object') {
    // 普通对象的深度赋值处理
    Object.entries(source as Record<string, Object>).forEach((e) => {
      const sourceKey: string = e[0];
      const sourceValue: Object = e[1];
      if (!isOnlyOverwrite || TKMapHelper.containKey(target, sourceKey)) {
        const targetValue: Object = (target as Record<string, Object>)[sourceKey] as Object;
        if (isDeep) {
          (target as Record<string, Object>)[sourceKey] = assign(targetValue, sourceValue, isDeep);
        } else {
          (target as Record<string, Object>)[sourceKey] = sourceValue ?? targetValue;
        }
      }
    });
    return target;
  } else {
    target = source as T;
    return target;
  }
}
```

#### 序列化转换算法
```typescript
function convertSerializeObject(obj: Object, isSerialize: boolean = true): Object {
  if (obj === null || obj === undefined) {
    return obj;
  } else if (Array.isArray(obj)) {
    const jsonArray: Array<Object> = obj.map((item: Object) => convertSerializeObject(item, isSerialize));
    return jsonArray;
  } else if ((obj instanceof Set) || (obj instanceof HashSet) || (obj instanceof TreeSet)) {
    let jsonArray: Array<Object> = new Array<Object>();
    obj.forEach((value: Object) => {
      jsonArray.push(convertSerializeObject(value, isSerialize));
    });
    return isSerialize ? {
      '@dataType': (obj instanceof Set) ? 'Set' : ((obj instanceof HashSet) ? 'HashSet' : 'TreeSet'),
      '@value': jsonArray
    } as Record<string, Object> : jsonArray;
  } else if ((obj instanceof Map) || (obj instanceof HashMap) || (obj instanceof TreeMap)) {
    const json: Record<string, Object> = {};
    obj.forEach((value: Object, key: string) => {
      json[key] = convertSerializeObject(value, isSerialize);
    });
    return isSerialize ? {
      '@dataType': (obj instanceof Map) ? 'Map' : ((obj instanceof HashMap) ? 'HashMap' : 'TreeMap'),
      '@value': json
    } as Record<string, Object> : json;
  } else if (!(obj instanceof Uint8Array) && typeof obj === 'object') {
    const json: Record<string, Object> = {};
    Object.entries(obj as Record<string, Object>).forEach((e) => {
      const key: string = e[0];
      const value: Object = e[1];
      json[key] = convertSerializeObject(value, isSerialize);
    });
    return json;
  } else {
    return obj;
  }
}
```

#### 深度克隆算法
```typescript
export function clone<T>(obj: T): T {
  let source: Object = obj as Object;
  if (source === null || source === undefined) {
    return source as T;
  } else if (Array.isArray(source)) {
    return source.map((value: Object) => clone(value)) as T;
  } else if ((source instanceof Set) || (source instanceof HashSet) || (source instanceof TreeSet)) {
    let target = (source instanceof Set) ? new Set<Object>() :
      ((source instanceof HashSet) ? new HashSet<Object>() : new TreeSet<Object>())
    source.forEach((value: Object) => {
      target.add(clone(value));
    });
    return target as T;
  } else if ((source instanceof Map) || (source instanceof HashMap) || (source instanceof TreeMap)) {
    let target = (source instanceof Map) ? new Map<string, Object>() :
      ((source instanceof HashMap) ? new HashMap<string, Object>() : new TreeMap<string, Object>())
    source.forEach((value: Object, key: string) => {
      target.set(key, clone(value));
    });
    return target as T;
  } else if (!(source instanceof Uint8Array) && typeof source === 'object') {
    let TClass: FunctionConstructor | undefined =
      source.constructor ? source.constructor as FunctionConstructor : undefined;
    let target: Record<string, Object> = TClass ? new TClass() as Object as Record<string, Object> : {};
    Object.entries(source as Record<string, Object>).forEach((e) => {
      const key: string = e[0];
      const value: Object = e[1];
      if (value instanceof WeakRef) {
        target[key] = value; // WeakRef不进行克隆
      } else {
        target[key] = clone(value);
      }
    });
    return target as T;
  } else {
    return source as T;
  }
}
```

### 实现机制分析

#### 复杂数据结构支持
- **Map类型**：支持Map、HashMap、TreeMap的序列化和克隆
- **Set类型**：支持Set、HashSet、TreeSet的序列化和克隆
- **Array类型**：支持数组的深度处理和递归操作
- **Object类型**：支持普通对象的深度操作

#### 类型保持机制
- **构造函数保持**：通过constructor属性保持对象的原始类型
- **泛型支持**：使用TypeScript泛型保持类型安全
- **类型标记**：在序列化时添加@dataType标记保持类型信息
- **类型恢复**：反序列化时根据类型标记恢复原始类型

#### 命名转换支持
- **驼峰转下划线**：支持对象属性名的驼峰转下划线
- **下划线转驼峰**：支持对象属性名的下划线转驼峰
- **递归转换**：对嵌套对象进行递归的命名转换
- **API适配**：适配不同API的命名规范

#### 安全性保证
- **循环引用处理**：避免循环引用导致的无限递归
- **WeakRef处理**：正确处理WeakRef类型的属性
- **异常处理**：完整的异常捕获和错误日志
- **内存保护**：避免大对象操作导致的内存问题

### 性能考虑
- **递归优化**：合理控制递归深度，避免栈溢出
- **内存管理**：及时释放临时对象，避免内存泄漏
- **类型检查优化**：高效的类型检查和分支处理
- **缓存机制**：对频繁使用的类型信息进行缓存

### 错误处理
- **异常捕获**：所有操作都有完整的异常处理
- **错误日志**：详细记录操作失败的错误信息
- **优雅降级**：操作失败时返回合理的默认值
- **类型安全**：确保类型转换的安全性

### 最佳实践
- **深拷贝使用**：需要完全独立副本时使用clone方法
- **浅拷贝使用**：只需要复制第一层属性时使用copy方法
- **序列化选择**：根据数据用途选择合适的序列化方法
- **类型指定**：反序列化时指定目标类型确保类型安全

## 🔗 依赖关系

### 依赖的模块
- `HashMap, HashSet, TreeMap, TreeSet` - 鸿蒙集合类型，支持复杂数据结构
- `TKDataHelper` - 数据工具，处理命名转换和数据处理
- `TKStringHelper` - 字符串工具，处理字符串操作
- `TKMapHelper` - Map工具，处理映射操作
- `TKLog` - 日志工具，记录错误和调试信息

### 被依赖情况
- `TKPreferences` - 偏好设置存储，使用对象工具进行序列化
- `TKSingleKVStore` - 分布式存储，使用对象工具进行序列化
- `TKCacheManager` - 缓存管理器，使用对象工具处理缓存数据
- `各数据模型类` - 使用对象工具进行数据转换和处理

### 关联文件
- `TKDataHelper.ets` - 数据转换工具类
- `TKMapHelper.ets` - Map操作工具类
- `TKStringHelper.ets` - 字符串处理工具类
- `TKValidateHelper.ets` - 数据验证工具类

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKObjectHelper是框架对象处理的核心工具：
1. **基础设施**：为对象操作提供基础功能支持
2. **使用频率高**：在数据处理和存储中广泛使用
3. **功能完整**：提供完整的对象操作解决方案
4. **类型安全**：通过泛型确保类型安全的对象操作

理解TKObjectHelper有助于掌握对象处理的最佳实践，是处理复杂数据结构的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解对象操作**：掌握对象赋值、拷贝、克隆的区别
2. **分析序列化机制**：理解序列化和反序列化的实现原理
3. **学习复杂数据结构**：掌握Map、Set等复杂数据结构的处理
4. **实践对象处理**：在实际项目中使用对象工具

### 前置学习
- TypeScript对象操作基础
- 深拷贝和浅拷贝概念
- JSON序列化和反序列化
- 泛型和类型系统

### 后续学习
- `TKDataHelper.ets` - 学习数据转换工具
- `TKMapHelper.ets` - 了解Map操作工具
- `TKValidateHelper.ets` - 掌握数据验证工具

### 实践建议
1. **对象操作练习**：练习不同类型的对象操作方法
2. **序列化测试**：测试复杂对象的序列化和反序列化
3. **性能对比**：对比不同拷贝方法的性能差异
4. **类型安全验证**：验证泛型类型的安全性

### 常见问题
1. **问题**: 什么时候使用深拷贝，什么时候使用浅拷贝？
   **解答**: 需要完全独立的对象副本时使用深拷贝（clone），只需要复制第一层属性时使用浅拷贝（copy）。

2. **问题**: 如何处理循环引用的对象？
   **解答**: 框架会自动检测和处理循环引用，但建议在设计数据结构时避免循环引用以提高性能。
