# TKModuleDelegate

> **文件路径**: `src/main/ets/base/engine/module/TKModuleDelegate.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐⭐ (4星)  
> **学习难度**: 中级  
> **前置知识**: TypeScript接口设计、模块化架构、发布订阅模式

## 📋 文件概述

### 功能定位
TKModuleDelegate是HMThinkBaseHar框架**模块通信系统的接口规范**，定义了模块间通信的标准协议。它包含三个核心接口，分别规范了模块实现、消息拦截和引擎代理的行为，是实现模块化架构的基础契约。

### 核心职责
- **模块接口规范**：定义模块必须实现的基本接口
- **拦截器协议**：规范消息拦截器的实现标准
- **引擎代理接口**：定义模块引擎的对外服务接口
- **通信协议统一**：确保所有模块遵循统一的通信规范

### 设计模式
- **接口隔离原则**：将不同职责的接口分离，避免接口污染
- **依赖倒置原则**：依赖抽象接口而非具体实现
- **开闭原则**：对扩展开放，对修改封闭

## 🔧 核心功能

### 主要接口定义

#### 接口1：`TKModuleDelegate`
- **功能**: 定义模块的基本接口规范
- **方法**: 
  - `getModuleName(): string` - 获取模块名称
  - `onModuleMessage(message: TKModuleMessage): void` - 处理模块消息
- **使用场景**: 所有参与模块通信的业务模块都必须实现此接口

#### 接口2：`TKModuleMessageFilterDelegate`
- **功能**: 定义消息拦截器的接口规范
- **方法**: 
  - `onFilterModuleMessage(message: TKModuleMessage): boolean` - 拦截并过滤消息
- **返回值**: true表示允许消息通过，false表示拦截消息
- **使用场景**: 实现权限控制、日志记录、消息预处理等功能

#### 接口3：`TKModuleMessageEngineDelegate`
- **功能**: 定义模块引擎的对外服务接口
- **方法**: 
  - `onRegisterMessageModule()` - 注册模块
  - `unRegisterMessageModule()` - 卸载模块
  - `onRegisterModuleMessageFilter()` - 注册拦截器
  - `unRegisterModuleMessageFilter()` - 卸载拦截器
  - `sendModuleMessage()` - 发送模块消息
- **使用场景**: TKModuleEngine实现此接口，提供模块管理和通信服务

### 关键设计特点
- **职责分离**：三个接口各司其职，职责清晰
- **类型安全**：使用TypeScript接口确保类型安全
- **扩展性**：接口设计支持未来功能扩展

## 💡 技术要点

### 核心设计理念

#### 接口隔离原则应用
```typescript
// 模块接口 - 只关注模块基本能力
export interface TKModuleDelegate {
  getModuleName(): string;
  onModuleMessage(message: TKModuleMessage): void;
}

// 拦截器接口 - 只关注消息过滤
export interface TKModuleMessageFilterDelegate {
  onFilterModuleMessage: (message: TKModuleMessage) => boolean;
}

// 引擎接口 - 只关注引擎服务
export interface TKModuleMessageEngineDelegate {
  onRegisterMessageModule(messageModule: TKModuleDelegate): void;
  // ... 其他引擎方法
}
```

#### 依赖倒置实现
```typescript
// 高层模块（TKModuleEngine）依赖抽象接口
export class TKModuleEngine implements TKModuleMessageEngineDelegate {
  private moduleMap: Map<string, WeakRef<TKModuleDelegate>> = new Map();
  private moduleFilters: Array<TKModuleMessageFilterDelegate> = new Array();
  
  // 依赖TKModuleDelegate抽象，而非具体实现
  public onRegisterMessageModule(messageModule: TKModuleDelegate) {
    this.moduleMap.set(messageModule.getModuleName(), new WeakRef(messageModule));
  }
}
```

### 实现机制分析

#### 模块注册流程
1. **实现接口**：业务模块实现TKModuleDelegate接口
2. **注册到引擎**：通过TKModuleEngine注册模块
3. **消息处理**：引擎通过接口方法分发消息
4. **生命周期管理**：支持模块的注册和卸载

#### 拦截器机制
1. **注册拦截器**：实现TKModuleMessageFilterDelegate接口
2. **消息过滤**：在消息分发前进行拦截处理
3. **链式处理**：支持多个拦截器的链式处理
4. **权限控制**：通过返回值控制消息是否继续传递

### 性能考虑
- **接口轻量化**：接口方法简洁，减少调用开销
- **类型检查**：编译时类型检查，避免运行时错误
- **内存友好**：接口不持有状态，减少内存占用
- **扩展性**：接口设计支持功能扩展而不影响现有实现

### 错误处理
- **类型安全**：TypeScript接口提供编译时类型检查
- **空值处理**：接口方法支持可选参数和返回值
- **异常隔离**：接口实现的异常不影响其他模块
- **降级处理**：拦截器异常时可以选择放行或拦截

### 最佳实践
- **单一职责**：每个接口只关注一个方面的功能
- **命名规范**：接口和方法命名清晰表达意图
- **文档完整**：每个接口方法都有详细的注释说明
- **向后兼容**：接口变更时保持向后兼容性

## 🔗 依赖关系

### 依赖的模块
- `TKModuleMessage` - 消息对象定义，接口方法的参数类型

### 被依赖情况
- `TKModuleEngine` - 实现TKModuleMessageEngineDelegate接口
- `各业务模块` - 实现TKModuleDelegate接口
- `拦截器实现` - 实现TKModuleMessageFilterDelegate接口

### 关联文件
- `TKModuleEngine.ets` - 模块引擎，接口的主要实现者
- `TKModuleMessage.ets` - 消息对象，接口方法的参数类型
- `TKAppEngine.ets` - 应用引擎，通过接口使用模块功能

## 📊 重要程度评级

### 评级标准
⭐⭐⭐⭐ **重要推荐**: 常用功能，建议掌握

### 本文件评级理由
TKModuleDelegate是模块化架构的基础接口规范：
1. **架构基础**：定义了模块间通信的基本协议
2. **扩展性保证**：为框架的模块化扩展提供标准接口
3. **类型安全**：通过接口确保模块通信的类型安全
4. **设计规范**：体现了良好的面向对象设计原则

理解这些接口是正确实现模块化功能的前提，是框架扩展开发的必备知识。

## 🎯 学习建议

### 学习路径
1. **理解接口设计原则**：掌握接口隔离和依赖倒置原则
2. **分析接口职责**：理解三个接口的不同职责和使用场景
3. **实践接口实现**：创建自定义模块和拦截器
4. **研究框架集成**：了解接口在框架中的实际应用

### 前置学习
- TypeScript接口设计和实现
- 面向对象设计原则（SOLID原则）
- 模块化架构设计思想
- 发布订阅模式的基本概念

### 后续学习
- `TKModuleEngine.ets` - 了解接口的具体实现
- `TKModuleMessage.ets` - 学习消息对象的设计
- 各业务模块实现 - 查看接口的实际应用

### 实践建议
1. **实现简单模块**：创建一个实现TKModuleDelegate的简单模块
2. **开发拦截器**：实现一个日志记录的消息拦截器
3. **模块通信实验**：在两个自定义模块间进行消息通信
4. **接口扩展练习**：尝试扩展接口功能而不破坏兼容性

### 常见问题
1. **问题**: 为什么要分成三个不同的接口？
   **解答**: 遵循接口隔离原则，不同角色只需要关注自己相关的接口，避免接口污染和不必要的依赖。

2. **问题**: 拦截器返回false后消息会如何处理？
   **解答**: 消息会被拦截，不会继续传递给目标模块，同时会通过回调函数返回拦截错误信息。

## 📝 代码示例

### 基础使用
```typescript
import { TKModuleDelegate, TKModuleMessage } from '@thinkive/tk-harmony-base';

// 实现基本的模块接口
class UserModule implements TKModuleDelegate {
  getModuleName(): string {
    return "UserModule";
  }

  onModuleMessage(message: TKModuleMessage): void {
    console.log(`${this.getModuleName()}收到消息: ${message.funcNo}`);

    // 根据功能号处理不同的消息
    switch (message.funcNo) {
      case "getUserInfo":
        this.handleGetUserInfo(message);
        break;
      case "updateUserInfo":
        this.handleUpdateUserInfo(message);
        break;
      default:
        console.log(`未知消息类型: ${message.funcNo}`);
    }
  }

  private handleGetUserInfo(message: TKModuleMessage) {
    const userInfo = { id: 1, name: "张三", email: "<EMAIL>" };

    // 通过回调返回结果
    if (message.callBackFunc) {
      message.callBackFunc({
        errorNo: 0,
        errorInfo: "成功",
        results: userInfo
      });
    }
  }

  private handleUpdateUserInfo(message: TKModuleMessage) {
    console.log("更新用户信息:", message.param);

    // 模拟更新操作
    if (message.callBackFunc) {
      message.callBackFunc({
        errorNo: 0,
        errorInfo: "更新成功",
        results: { success: true }
      });
    }
  }
}
```

### 高级用法
```typescript
import { TKModuleMessageFilterDelegate, TKModuleMessage } from '@thinkive/tk-harmony-base';

// 实现消息拦截器
class SecurityFilter implements TKModuleMessageFilterDelegate {
  onFilterModuleMessage(message: TKModuleMessage): boolean {
    // 1. 权限检查
    if (this.requiresPermission(message.funcNo)) {
      if (!this.hasPermission(message.funcNo)) {
        console.log(`权限不足，拦截消息: ${message.funcNo}`);
        this.sendErrorResponse(message, "权限不足");
        return false;
      }
    }

    // 2. 参数验证
    if (!this.validateParameters(message)) {
      console.log(`参数验证失败，拦截消息: ${message.funcNo}`);
      this.sendErrorResponse(message, "参数验证失败");
      return false;
    }

    // 3. 频率限制
    if (this.isRateLimited(message.sourceModule, message.funcNo)) {
      console.log(`请求频率过高，拦截消息: ${message.funcNo}`);
      this.sendErrorResponse(message, "请求频率过高");
      return false;
    }

    // 4. 记录日志
    this.logMessage(message);

    return true; // 允许消息通过
  }

  private requiresPermission(funcNo: string): boolean {
    const protectedFunctions = ["deleteUser", "updateUserRole", "accessAdminPanel"];
    return protectedFunctions.includes(funcNo);
  }

  private hasPermission(funcNo: string): boolean {
    // 实际项目中从用户会话或权限系统获取
    return false; // 示例返回
  }

  private validateParameters(message: TKModuleMessage): boolean {
    // 参数验证逻辑
    return message.param !== null && message.param !== undefined;
  }

  private isRateLimited(sourceModule: string, funcNo: string): boolean {
    // 频率限制逻辑
    return false; // 示例返回
  }

  private logMessage(message: TKModuleMessage) {
    console.log(`[消息日志] ${message.sourceModule} -> ${message.targetModule}: ${message.funcNo}`);
  }

  private sendErrorResponse(message: TKModuleMessage, errorInfo: string) {
    if (message.callBackFunc) {
      message.callBackFunc({
        errorNo: -1,
        errorInfo: errorInfo,
        results: null
      });
    }
  }
}
```

### 扩展示例
```typescript
// 扩展模块接口，添加生命周期管理
interface ExtendedModuleDelegate extends TKModuleDelegate {
  onModuleInit?(): void;
  onModuleDestroy?(): void;
  getModuleVersion?(): string;
  getModuleDependencies?(): string[];
}

// 实现扩展接口的高级模块
class AdvancedUserModule implements ExtendedModuleDelegate {
  private isInitialized: boolean = false;
  private moduleVersion: string = "1.0.0";

  getModuleName(): string {
    return "AdvancedUserModule";
  }

  getModuleVersion(): string {
    return this.moduleVersion;
  }

  getModuleDependencies(): string[] {
    return ["DatabaseModule", "CacheModule"];
  }

  onModuleInit(): void {
    console.log(`${this.getModuleName()} 初始化开始`);
    // 初始化逻辑：连接数据库、加载配置等
    this.isInitialized = true;
    console.log(`${this.getModuleName()} 初始化完成`);
  }

  onModuleDestroy(): void {
    console.log(`${this.getModuleName()} 销毁开始`);
    // 清理逻辑：关闭连接、保存状态等
    this.isInitialized = false;
    console.log(`${this.getModuleName()} 销毁完成`);
  }

  onModuleMessage(message: TKModuleMessage): void {
    if (!this.isInitialized) {
      console.log("模块未初始化，拒绝处理消息");
      if (message.callBackFunc) {
        message.callBackFunc({
          errorNo: -1,
          errorInfo: "模块未初始化",
          results: null
        });
      }
      return;
    }

    // 处理消息逻辑
    this.processMessage(message);
  }

  private processMessage(message: TKModuleMessage) {
    console.log(`处理消息: ${message.funcNo}`);
    // 具体的消息处理逻辑
  }
}

// 模块管理器，支持扩展接口
class ModuleManager {
  private modules: Map<string, ExtendedModuleDelegate> = new Map();

  registerModule(module: ExtendedModuleDelegate) {
    // 初始化模块
    if (module.onModuleInit) {
      module.onModuleInit();
    }

    this.modules.set(module.getModuleName(), module);
    console.log(`模块 ${module.getModuleName()} 注册成功`);
  }

  unregisterModule(moduleName: string) {
    const module = this.modules.get(moduleName);
    if (module) {
      // 销毁模块
      if (module.onModuleDestroy) {
        module.onModuleDestroy();
      }

      this.modules.delete(moduleName);
      console.log(`模块 ${moduleName} 卸载成功`);
    }
  }

  getModuleInfo(moduleName: string) {
    const module = this.modules.get(moduleName);
    if (module) {
      return {
        name: module.getModuleName(),
        version: module.getModuleVersion?.() || "unknown",
        dependencies: module.getModuleDependencies?.() || []
      };
    }
    return null;
  }
}
```

## 📚 相关资源

### 官方文档
- TypeScript官方文档 - 接口设计
- 面向对象设计原则 - SOLID原则

### 参考资料
- 《设计模式：可复用面向对象软件的基础》- 接口设计
- 《重构：改善既有代码的设计》- 接口重构

### 相关文件
- `核心架构-TKModuleEngine-解释文档.md` - 接口的具体实现
- `TKModuleMessage.ets` - 消息对象定义
- `核心架构-TKAppEngine-解释文档.md` - 接口的使用者
