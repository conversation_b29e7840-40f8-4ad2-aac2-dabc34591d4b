# TKNotification

> **文件路径**: `src/main/ets/base/notification/TKNotification.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 初级  
> **前置知识**: 观察者模式、事件通知机制、对象传递

## 📋 文件概述

### 功能定位
TKNotification是HMThinkBaseHar框架的**通知对象**，用于在应用内部进行事件通知和消息传递。它是一个简单的数据载体类，封装了通知的基本信息，包括通知名称、关联对象和用户信息，为框架的通知中心提供标准的数据结构。

### 核心职责
- **通知数据封装**：封装通知的名称、对象和用户信息
- **标准化通知格式**：为通知中心提供统一的通知数据结构
- **信息传递载体**：作为应用内部事件通知的数据载体
- **类型安全保证**：通过TypeScript类型系统确保通知数据的类型安全

### 设计模式
- **数据传输对象模式(DTO)**：纯粹的数据载体，不包含业务逻辑
- **值对象模式**：封装相关的数据属性，提供统一的数据结构

## 🔧 核心功能

### 主要属性

#### 属性1：`name: string`
- **功能**: 通知名称，用于标识通知类型
- **类型**: string
- **默认值**: 空字符串
- **使用场景**: 通知中心根据名称匹配监听器

#### 属性2：`obj: Object | undefined`
- **功能**: 通知关联的对象，可选参数
- **类型**: Object | undefined
- **默认值**: undefined
- **使用场景**: 传递与通知相关的特定对象

#### 属性3：`userInfo: Record<string, Object> | undefined`
- **功能**: 用户自定义信息，可选参数
- **类型**: Record<string, Object> | undefined
- **默认值**: undefined
- **使用场景**: 传递额外的通知数据和参数

### 主要方法

#### 构造函数：`constructor(name, obj?, userInfo?)`
- **功能**: 创建通知对象实例
- **参数**: 
  - name - 通知名称（必需）
  - obj - 关联对象（可选）
  - userInfo - 用户信息（可选）
- **使用场景**: 创建通知对象时使用

## 💡 技术要点

### 核心设计理念

#### 简单数据载体
```typescript
export class TKNotification {
  public name: string = "";
  public obj: Object | undefined = undefined;
  public userInfo: Record<string, Object> | undefined = undefined;

  public constructor(name: string, obj?: Object, userInfo?: Record<string, Object>) {
    this.name = name;
    this.obj = obj;
    this.userInfo = userInfo;
  }
}
```

### 实现机制分析

#### 数据封装策略
- **必需参数**：name是唯一必需的参数，确保通知有明确的标识
- **可选参数**：obj和userInfo都是可选的，提供灵活的数据传递能力
- **类型安全**：使用TypeScript类型系统确保数据类型的正确性
- **简单构造**：构造函数直接赋值，没有复杂的初始化逻辑

#### 使用模式
1. **基础通知**：只传递通知名称，用于简单的事件通知
2. **对象通知**：传递关联对象，用于特定对象的状态变化通知
3. **数据通知**：传递用户信息，用于携带额外数据的通知
4. **完整通知**：同时传递对象和用户信息，用于复杂的通知场景

### 性能考虑
- **轻量级设计**：只包含必要的属性，内存占用极小
- **无业务逻辑**：纯数据对象，创建和使用开销最小
- **类型优化**：使用TypeScript类型系统，编译时优化
- **垃圾回收友好**：简单的对象结构，便于垃圾回收

### 错误处理
- **参数验证**：构造函数中直接赋值，不进行复杂验证
- **类型安全**：通过TypeScript类型系统防止类型错误
- **空值处理**：可选参数支持undefined，避免空值异常

### 最佳实践
- **命名规范**：使用有意义的通知名称，便于识别和管理
- **数据精简**：只传递必要的数据，避免过大的对象
- **类型明确**：使用明确的类型定义，提高代码可读性
- **一致性**：在整个应用中保持通知使用的一致性

## 🔗 依赖关系

### 依赖的模块
- 无外部依赖，纯TypeScript类定义

### 被依赖情况
- `TKNotificationCenter` - 通知中心，使用TKNotification作为通知数据载体
- `各业务模块` - 创建和使用TKNotification进行事件通知
- `插件系统` - 使用TKNotification进行插件间通信
- `WebView组件` - 使用TKNotification进行H5与原生的通信

### 关联文件
- `TKNotificationCenter.ets` - 通知中心，TKNotification的主要使用者
- `TKJSNotiProxyManager.ets` - JS通知代理管理器
- 各种监听器类 - 使用TKNotification进行状态通知

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKNotification是框架通知系统的基础数据结构：
1. **基础组件**：作为通知系统的数据载体，是基础但重要的组件
2. **使用频率**：在需要事件通知的场景中会频繁使用
3. **学习难度**：结构简单，容易理解和使用
4. **扩展性**：提供了灵活的数据传递能力

理解TKNotification有助于掌握框架的事件通知机制，是使用通知中心的基础。

## 🎯 学习建议

### 学习路径
1. **理解通知概念**：掌握应用内事件通知的基本概念
2. **分析数据结构**：理解TKNotification的属性和用途
3. **学习使用模式**：掌握不同场景下的通知创建方式
4. **实践通知传递**：结合通知中心实现事件通知功能

### 前置学习
- 观察者模式的基本概念
- TypeScript类和接口定义
- 事件驱动编程思想
- 对象传递和引用概念

### 后续学习
- `TKNotificationCenter.ets` - 学习通知中心的使用
- 各种监听器实现 - 了解通知的实际应用
- 插件系统 - 查看通知在插件通信中的应用

### 实践建议
1. **基础创建练习**：创建不同类型的通知对象
2. **数据传递实验**：测试不同数据类型的传递
3. **通知命名规范**：建立项目的通知命名约定
4. **结合通知中心**：与TKNotificationCenter配合使用

### 常见问题
1. **问题**: 通知名称如何命名？
   **解答**: 建议使用有意义的命名，如"NOTE_USER_LOGIN"、"NOTE_DATA_UPDATED"等，便于识别和管理。

2. **问题**: obj和userInfo有什么区别？
   **解答**: obj通常用于传递与通知相关的特定对象实例，userInfo用于传递额外的键值对数据，两者可以根据需要灵活使用。

## 📝 代码示例

### 基础使用
```typescript
import { TKNotification } from '@thinkive/tk-harmony-base';

// 创建简单通知
function createSimpleNotification() {
  const notification = new TKNotification("NOTE_USER_LOGIN");
  console.log('通知名称:', notification.name);
  console.log('关联对象:', notification.obj);
  console.log('用户信息:', notification.userInfo);
}

// 创建带对象的通知
function createNotificationWithObject() {
  const userObject = {
    id: 123,
    name: "张三",
    email: "<EMAIL>"
  };

  const notification = new TKNotification("NOTE_USER_UPDATED", userObject);
  console.log('通知名称:', notification.name);
  console.log('用户对象:', notification.obj);
}

// 创建带用户信息的通知
function createNotificationWithUserInfo() {
  const userInfo = {
    action: "login",
    timestamp: Date.now(),
    source: "mobile",
    success: true
  };

  const notification = new TKNotification("NOTE_USER_ACTION", undefined, userInfo);
  console.log('通知名称:', notification.name);
  console.log('用户信息:', notification.userInfo);
}

// 创建完整通知
function createCompleteNotification() {
  const dataObject = {
    type: "order",
    id: "ORDER_001"
  };

  const userInfo = {
    operation: "create",
    operator: "admin",
    timestamp: Date.now(),
    details: {
      amount: 299.99,
      currency: "CNY"
    }
  };

  const notification = new TKNotification("NOTE_ORDER_CREATED", dataObject, userInfo);
  console.log('完整通知:', notification);
}

// 执行示例
createSimpleNotification();
createNotificationWithObject();
createNotificationWithUserInfo();
createCompleteNotification();
```

### 高级用法
```typescript
// 通知工厂类，标准化通知创建
class NotificationFactory {
  // 用户相关通知
  static createUserNotification(action: string, user: any, additionalInfo?: Record<string, any>): TKNotification {
    const userInfo = {
      action: action,
      timestamp: Date.now(),
      userId: user.id,
      ...additionalInfo
    };

    return new TKNotification(`NOTE_USER_${action.toUpperCase()}`, user, userInfo);
  }

  // 数据变更通知
  static createDataChangeNotification(dataType: string, changeType: string, data: any): TKNotification {
    const userInfo = {
      dataType: dataType,
      changeType: changeType,
      timestamp: Date.now(),
      dataId: data.id || 'unknown'
    };

    return new TKNotification(`NOTE_DATA_${changeType.toUpperCase()}`, data, userInfo);
  }

  // 系统状态通知
  static createSystemNotification(event: string, status: string, details?: Record<string, any>): TKNotification {
    const userInfo = {
      event: event,
      status: status,
      timestamp: Date.now(),
      ...details
    };

    return new TKNotification(`NOTE_SYSTEM_${event.toUpperCase()}`, undefined, userInfo);
  }

  // 错误通知
  static createErrorNotification(errorCode: string, errorMessage: string, context?: any): TKNotification {
    const userInfo = {
      errorCode: errorCode,
      errorMessage: errorMessage,
      timestamp: Date.now(),
      context: context
    };

    return new TKNotification("NOTE_ERROR_OCCURRED", undefined, userInfo);
  }
}

// 通知常量定义
class NotificationNames {
  // 用户相关
  static readonly USER_LOGIN = "NOTE_USER_LOGIN";
  static readonly USER_LOGOUT = "NOTE_USER_LOGOUT";
  static readonly USER_PROFILE_UPDATED = "NOTE_USER_PROFILE_UPDATED";

  // 数据相关
  static readonly DATA_LOADED = "NOTE_DATA_LOADED";
  static readonly DATA_UPDATED = "NOTE_DATA_UPDATED";
  static readonly DATA_DELETED = "NOTE_DATA_DELETED";

  // 网络相关
  static readonly NETWORK_CONNECTED = "NOTE_NETWORK_CONNECTED";
  static readonly NETWORK_DISCONNECTED = "NOTE_NETWORK_DISCONNECTED";
  static readonly NETWORK_ERROR = "NOTE_NETWORK_ERROR";

  // 应用状态
  static readonly APP_FOREGROUND = "NOTE_APP_FOREGROUND";
  static readonly APP_BACKGROUND = "NOTE_APP_BACKGROUND";
  static readonly APP_PAUSED = "NOTE_APP_PAUSED";
}

// 使用工厂类创建通知
function useNotificationFactory() {
  // 创建用户登录通知
  const user = { id: 123, name: "张三", role: "admin" };
  const loginNotification = NotificationFactory.createUserNotification("login", user, {
    loginMethod: "password",
    deviceInfo: "iPhone 15"
  });

  // 创建数据变更通知
  const orderData = { id: "ORDER_001", amount: 299.99, status: "paid" };
  const dataChangeNotification = NotificationFactory.createDataChangeNotification("order", "updated", orderData);

  // 创建系统状态通知
  const systemNotification = NotificationFactory.createSystemNotification("startup", "completed", {
    version: "1.0.0",
    startupTime: 1500
  });

  // 创建错误通知
  const errorNotification = NotificationFactory.createErrorNotification("NET_001", "网络连接失败", {
    url: "https://api.example.com",
    retryCount: 3
  });

  console.log('用户登录通知:', loginNotification);
  console.log('数据变更通知:', dataChangeNotification);
  console.log('系统状态通知:', systemNotification);
  console.log('错误通知:', errorNotification);
}

useNotificationFactory();
```

### 扩展示例
```typescript
// 通知验证器
class NotificationValidator {
  // 验证通知名称格式
  static validateNotificationName(name: string): boolean {
    // 通知名称应该以NOTE_开头，使用大写字母和下划线
    const namePattern = /^NOTE_[A-Z_]+$/;
    return namePattern.test(name);
  }

  // 验证通知对象
  static validateNotificationObject(obj: any): boolean {
    if (obj === undefined || obj === null) {
      return true; // 允许空值
    }

    // 检查是否为有效对象
    return typeof obj === 'object' && obj.constructor === Object;
  }

  // 验证用户信息
  static validateUserInfo(userInfo: Record<string, any>): boolean {
    if (userInfo === undefined) {
      return true; // 允许空值
    }

    // 检查是否为有效的键值对对象
    return typeof userInfo === 'object' && userInfo !== null;
  }

  // 完整验证通知
  static validateNotification(notification: TKNotification): {
    isValid: boolean,
    errors: string[]
  } {
    const errors: string[] = [];

    if (!notification.name || notification.name.trim() === '') {
      errors.push('通知名称不能为空');
    } else if (!this.validateNotificationName(notification.name)) {
      errors.push('通知名称格式不正确，应该以NOTE_开头并使用大写字母和下划线');
    }

    if (!this.validateNotificationObject(notification.obj)) {
      errors.push('通知对象格式不正确');
    }

    if (!this.validateUserInfo(notification.userInfo)) {
      errors.push('用户信息格式不正确');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
}

// 通知构建器
class NotificationBuilder {
  private name: string = '';
  private obj: any = undefined;
  private userInfo: Record<string, any> = {};

  // 设置通知名称
  setName(name: string): NotificationBuilder {
    this.name = name;
    return this;
  }

  // 设置关联对象
  setObject(obj: any): NotificationBuilder {
    this.obj = obj;
    return this;
  }

  // 添加用户信息
  addUserInfo(key: string, value: any): NotificationBuilder {
    this.userInfo[key] = value;
    return this;
  }

  // 批量设置用户信息
  setUserInfo(userInfo: Record<string, any>): NotificationBuilder {
    this.userInfo = { ...userInfo };
    return this;
  }

  // 添加时间戳
  addTimestamp(): NotificationBuilder {
    this.userInfo.timestamp = Date.now();
    return this;
  }

  // 添加来源信息
  addSource(source: string): NotificationBuilder {
    this.userInfo.source = source;
    return this;
  }

  // 构建通知
  build(): TKNotification {
    if (!this.name) {
      throw new Error('通知名称不能为空');
    }

    const notification = new TKNotification(
      this.name,
      this.obj,
      Object.keys(this.userInfo).length > 0 ? this.userInfo : undefined
    );

    // 验证构建的通知
    const validation = NotificationValidator.validateNotification(notification);
    if (!validation.isValid) {
      throw new Error(`通知验证失败: ${validation.errors.join(', ')}`);
    }

    return notification;
  }

  // 重置构建器
  reset(): NotificationBuilder {
    this.name = '';
    this.obj = undefined;
    this.userInfo = {};
    return this;
  }
}

// 使用构建器创建通知
function useNotificationBuilder() {
  const builder = new NotificationBuilder();

  try {
    // 构建复杂通知
    const notification = builder
      .setName(NotificationNames.USER_PROFILE_UPDATED)
      .setObject({ id: 123, name: "张三" })
      .addUserInfo("operation", "update")
      .addUserInfo("fields", ["name", "email"])
      .addTimestamp()
      .addSource("profile_page")
      .build();

    console.log('构建的通知:', notification);

    // 重置并构建新通知
    const simpleNotification = builder
      .reset()
      .setName(NotificationNames.APP_FOREGROUND)
      .addTimestamp()
      .build();

    console.log('简单通知:', simpleNotification);

  } catch (error) {
    console.error('通知构建失败:', error.message);
  }
}

// 通知工具类
class NotificationUtils {
  // 克隆通知
  static clone(notification: TKNotification): TKNotification {
    return new TKNotification(
      notification.name,
      notification.obj,
      notification.userInfo ? { ...notification.userInfo } : undefined
    );
  }

  // 比较两个通知是否相同
  static equals(notification1: TKNotification, notification2: TKNotification): boolean {
    return notification1.name === notification2.name &&
           notification1.obj === notification2.obj &&
           JSON.stringify(notification1.userInfo) === JSON.stringify(notification2.userInfo);
  }

  // 获取通知摘要
  static getSummary(notification: TKNotification): string {
    const parts = [notification.name];

    if (notification.obj) {
      parts.push(`obj: ${typeof notification.obj}`);
    }

    if (notification.userInfo) {
      const keys = Object.keys(notification.userInfo);
      parts.push(`userInfo: {${keys.join(', ')}}`);
    }

    return parts.join(', ');
  }
}

// 执行扩展示例
useNotificationBuilder();

// 测试工具类
const notification1 = new TKNotification("TEST_NOTE", { id: 1 }, { action: "test" });
const notification2 = NotificationUtils.clone(notification1);

console.log('通知摘要:', NotificationUtils.getSummary(notification1));
console.log('通知相等:', NotificationUtils.equals(notification1, notification2));
```

## 📚 相关资源

### 官方文档
- TypeScript官方文档 - 类和接口
- 观察者模式设计文档

### 参考资料
- 《设计模式：可复用面向对象软件的基础》- 观察者模式
- 《JavaScript设计模式》- 发布订阅模式

### 相关文件
- `基础服务-TKNotificationCenter-解释文档.md` - 通知中心实现
- `TKJSNotiProxyManager.ets` - JS通知代理管理
- 各种监听器实现 - 通知的实际应用场景
