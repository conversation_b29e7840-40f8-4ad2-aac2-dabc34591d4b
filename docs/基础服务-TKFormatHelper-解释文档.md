# TKFormatHelper (验证工具类)

> **文件路径**: `src/main/ets/util/format/TKFormatHelper.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 正则表达式、数据验证、格式校验、密码强度算法

## 📋 文件概述

### 功能定位
TKFormatHelper是HMThinkBaseHar框架的**格式验证和数据校验工具类**，提供了完整的数据格式验证功能。它采用命名空间设计，集成了各种常用的数据格式验证、密码强度检测、身份证验证等功能，支持邮箱、手机号、URL、日期等多种格式的校验，为应用提供全面的数据验证服务。

### 核心职责
- **格式验证**：提供各种数据格式的验证功能（邮箱、手机、身份证等）
- **密码强度检测**：检测密码强度，支持登录密码和交易密码验证
- **数据类型校验**：验证数字、日期、货币等数据类型格式
- **正则表达式验证**：基于正则表达式的通用格式验证
- **统一校验接口**：提供统一的数据校验接口和错误消息

### 设计模式
- **命名空间模式**：使用namespace封装验证功能
- **策略模式**：支持不同的验证策略和规则
- **工厂模式**：根据验证类型创建相应的验证器
- **模板方法模式**：定义验证流程的标准模板

## 🔧 核心功能

### 验证选项枚举

#### 枚举：`TKFormatOption`
- **NotEmpty**: 不为空验证
- **Date**: 日期格式验证
- **DateTime**: 日期时间格式验证
- **AlphaNumeric**: 字母数字验证
- **Email**: 电子邮件格式验证
- **Money**: 货币格式验证
- **Numeric**: 数字格式验证
- **NumberFloat**: 浮点数格式验证
- **Mobile**: 手机号格式验证
- **Phone**: 电话号码验证
- **Tel**: 固定电话验证
- **PostalCode**: 邮政编码验证
- **URL**: 网址格式验证
- **CardID**: 身份证号验证
- **Stock**: 股票代码验证
- **StrongTradePwd**: 强交易密码验证
- **StrongLoginPwd**: 强登录密码验证
- **CnAndEnNumeric**: 中英文数字验证
- **EnNumeric**: 英文数字验证
- **Chinese**: 中文验证

#### 枚举：`TKPasswordStrongLevel`
- **Low**: 低强度密码
- **Mid**: 中等强度密码
- **High**: 高强度密码

### 基础验证方法

#### 方法1：`isFormatRegex(regexStr, str)`
- **功能**: 基于正则表达式的通用格式验证
- **参数**: 
  - regexStr - 正则表达式字符串
  - str - 要验证的字符串
- **返回值**: boolean - 验证结果
- **使用场景**: 自定义格式验证

#### 方法2：`isAlphaNumber(str)`
- **功能**: 验证字符串是否为字母或数字
- **参数**: str - 要验证的字符串
- **返回值**: boolean - 验证结果
- **使用场景**: 用户名、标识符验证

#### 方法3：`isEmail(str)`
- **功能**: 验证邮箱格式
- **参数**: str - 要验证的邮箱字符串
- **返回值**: boolean - 验证结果
- **使用场景**: 邮箱地址验证

#### 方法4：`isMobile(str)`
- **功能**: 验证手机号格式
- **参数**: str - 要验证的手机号字符串
- **返回值**: boolean - 验证结果
- **使用场景**: 手机号码验证

### 高级验证方法

#### 方法1：`isCardID(str)`
- **功能**: 验证身份证号格式和有效性
- **参数**: str - 要验证的身份证号
- **返回值**: boolean - 验证结果
- **特性**: 支持15位和18位身份证，包含校验位验证
- **使用场景**: 身份证号码验证

#### 方法2：`isDate(str)` / `isDateTime(str)`
- **功能**: 验证日期和日期时间格式
- **参数**: str - 要验证的日期字符串
- **返回值**: boolean - 验证结果
- **使用场景**: 日期输入验证

#### 方法3：`isURL(str)`
- **功能**: 验证URL格式
- **参数**: str - 要验证的URL字符串
- **返回值**: boolean - 验证结果
- **使用场景**: 网址链接验证

### 密码强度验证

#### 方法1：`checkPasswordStrength(password)`
- **功能**: 检测密码强度等级
- **参数**: password - 要检测的密码
- **返回值**: TKPasswordStrongLevel - 密码强度等级
- **算法**: 基于字符类型、长度、复杂度的综合评估
- **使用场景**: 登录密码强度检测

#### 方法2：`isStrongTradePassword(password, filter?)`
- **功能**: 验证交易密码强度
- **参数**: 
  - password - 交易密码
  - filter - 过滤字符串数组（可选）
- **返回值**: boolean - 是否为强密码
- **特性**: 支持弱密码过滤，避免使用身份证、手机号等作为密码
- **使用场景**: 交易密码设置验证

### 统一校验接口

#### 方法：`checkValid(srcStr, description, option, filter?)`
- **功能**: 统一的数据格式校验接口
- **参数**: 
  - srcStr - 要校验的字符串
  - description - 字段描述
  - option - 校验选项（TKFormatOption枚举）
  - filter - 过滤数组（可选）
- **返回值**: string - 校验错误消息，空字符串表示验证通过
- **使用场景**: 表单验证、数据校验

## 💡 技术要点

### 核心算法/逻辑

#### 身份证号验证算法
```typescript
export function isCardID(str: string): boolean {
  if (TKStringHelper.isBlank(str)) {
    return false;
  }
  
  // 省份代码字典
  let dic: Record<string, string> = {
    "11": "北京", "12": "天津", "13": "河北", "14": "山西", "15": "内蒙古",
    "21": "辽宁", "22": "吉林", "23": "黑龙江", "31": "上海", "32": "江苏",
    // ... 更多省份代码
  };
  
  // 校验长度和基本格式
  let regexStr: string = "(^\\d{15}$)|(^\\d{17}(\\d|X))$";
  if (!isFormatRegex(regexStr, str)) {
    return false;
  }
  
  // 检查省份代码
  let province: string = str.substring(0, 2);
  if (TKStringHelper.isBlank(dic[province] as string)) {
    return false;
  }
  
  // 校验生日
  if (str.length == 18) {
    let year: number = Number(str.substring(6, 10));
    let month: number = Number(str.substring(10, 12));
    let day: number = Number(str.substring(12, 14));
    
    // 验证日期有效性
    let cardDate: Date = new Date(year, month - 1, day);
    let cardDateStr: string = TKDateHelper.formatDate(cardDate, "YYYYMMDD");
    let oldDateStr: string = str.substring(6, 14);
    
    if (cardDateStr != oldDateStr) {
      return false;
    }
    
    // 验证年龄范围（3-100岁）
    let now: Date = new Date();
    let age: number = now.getFullYear() - year;
    if (!(age >= 3 && age <= 100)) {
      return false;
    }
    
    // 校验位验证
    let arrInt: Array<number> = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    let arrCh: Array<string> = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    let cardTemp: number = 0;
    
    for (let i = 0; i < 17; i++) {
      cardTemp += Number(str.substring(i, i + 1)) * arrInt[i];
    }
    
    let val: string = arrCh[cardTemp % 11];
    if (val != str.charAt(17)) {
      return false;
    }
  }
  
  return true;
}
```

#### 密码强度检测算法
```typescript
export function checkPasswordStrength(password: string): TKPasswordStrongLevel {
  if (TKStringHelper.isBlank(password)) {
    return TKPasswordStrongLevel.Low;
  }
  
  let score: number = 0;
  
  // 长度评分
  if (password.length >= 8) score += 25;
  else if (password.length >= 6) score += 10;
  
  // 字符类型评分
  if (/[a-z]/.test(password)) score += 5;  // 小写字母
  if (/[A-Z]/.test(password)) score += 5;  // 大写字母
  if (/[0-9]/.test(password)) score += 5;  // 数字
  if (/[^A-Za-z0-9]/.test(password)) score += 10;  // 特殊字符
  
  // 复杂度评分
  if (/[a-z].*[A-Z]|[A-Z].*[a-z]/.test(password)) score += 10;  // 大小写混合
  if (/[a-zA-Z].*[0-9]|[0-9].*[a-zA-Z]/.test(password)) score += 10;  // 字母数字混合
  if (password.length >= 12) score += 15;  // 长密码奖励
  
  // 连续字符惩罚
  if (/(.)\1{2,}/.test(password)) score -= 10;  // 连续相同字符
  if (/123|abc|qwe/i.test(password)) score -= 15;  // 常见序列
  
  // 根据评分确定强度等级
  if (score >= 70) return TKPasswordStrongLevel.High;
  else if (score >= 40) return TKPasswordStrongLevel.Mid;
  else return TKPasswordStrongLevel.Low;
}
```

#### 统一校验接口实现
```typescript
export function checkValid(srcStr: string, description: string, option: TKFormatOption, filter?: Array<string>): string {
  let strMsg: string = "";
  
  switch (option) {
    case TKFormatOption.NotEmpty:
      if (TKStringHelper.isBlank(srcStr)) {
        strMsg = `${description}不能为空！\n`;
      }
      break;
      
    case TKFormatOption.Email:
      if (!isEmail(srcStr)) {
        strMsg = `${description}格式错误，请输入正确的邮箱地址！\n`;
      }
      break;
      
    case TKFormatOption.Mobile:
      if (!isMobile(srcStr)) {
        strMsg = `${description}格式错误，请输入正确的手机号码！\n`;
      }
      break;
      
    case TKFormatOption.CardID:
      if (!isCardID(srcStr)) {
        strMsg = `${description}格式错误，请输入正确的身份证号码！\n`;
      }
      break;
      
    case TKFormatOption.StrongLoginPwd:
      if (checkPasswordStrength(srcStr) == TKPasswordStrongLevel.Low) {
        strMsg = `${description}强度太弱,请修改！\n\n`;
      }
      break;
      
    case TKFormatOption.StrongTradePwd:
      if (!isTradePassword(srcStr)) {
        strMsg = `${description}格式错误，请输入6位数字！\n`;
      } else if (!isStrongTradePassword(srcStr, filter)) {
        strMsg = `${description}强度太弱,请修改！\n`;
      }
      break;
      
    // ... 更多验证选项
  }
  
  return strMsg;
}
```

### 实现机制分析

#### 正则表达式验证
- **模式匹配**：使用正则表达式进行精确的格式匹配
- **性能优化**：预编译常用的正则表达式模式
- **国际化支持**：支持中文、英文等多语言字符验证
- **扩展性**：通过正则表达式支持自定义验证规则

#### 身份证验证机制
- **格式验证**：支持15位和18位身份证格式
- **省份验证**：验证省份代码的有效性
- **日期验证**：验证出生日期的合理性
- **校验位算法**：实现18位身份证的校验位验证算法
- **年龄范围**：验证年龄在合理范围内（3-100岁）

#### 密码强度算法
- **多维度评估**：从长度、字符类型、复杂度等多个维度评估
- **动态评分**：根据不同特征给予不同的分数权重
- **安全惩罚**：对弱密码模式进行评分惩罚
- **等级划分**：将评分转换为直观的强度等级

#### 统一接口设计
- **枚举驱动**：通过枚举值驱动不同的验证逻辑
- **错误消息**：提供友好的中文错误提示信息
- **扩展支持**：支持自定义过滤规则和验证参数
- **一致性**：保持所有验证方法的接口一致性

### 性能考虑
- **正则缓存**：缓存编译后的正则表达式对象
- **早期返回**：验证失败时立即返回，避免不必要的计算
- **算法优化**：使用高效的字符串处理和数学计算
- **内存管理**：避免创建不必要的临时对象

### 错误处理
- **输入验证**：检查输入参数的有效性
- **异常捕获**：捕获验证过程中的异常
- **优雅降级**：验证失败时返回明确的错误信息
- **日志记录**：记录验证异常和错误信息

### 最佳实践
- **验证顺序**：按照从简单到复杂的顺序进行验证
- **错误提示**：提供清晰、友好的错误提示信息
- **安全考虑**：密码验证时考虑安全性和用户体验
- **国际化**：支持不同地区的格式验证规则

## 🔗 依赖关系

### 依赖的模块
- `TKStringHelper` - 字符串工具，处理字符串操作和空值检查
- `TKDateHelper` - 日期工具，处理日期格式验证和转换
- `url` - 鸿蒙URL模块，用于URL格式验证

### 被依赖情况
- `表单验证模块` - 使用格式验证进行表单字段验证
- `用户注册模块` - 使用邮箱、手机号等验证
- `密码管理模块` - 使用密码强度检测
- `身份验证模块` - 使用身份证号验证
- `数据输入组件` - 使用各种格式验证

### 关联文件
- `TKStringHelper.ets` - 字符串处理工具
- `TKDateHelper.ets` - 日期处理工具
- `TKDataHelper.ets` - 数据转换工具
- 各种输入组件和表单组件

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKFormatHelper是框架数据验证的重要工具：
1. **数据安全**：确保输入数据的格式正确性和安全性
2. **用户体验**：提供友好的验证错误提示
3. **功能完整**：涵盖常见的数据格式验证需求
4. **易于使用**：提供统一的验证接口和清晰的API

理解TKFormatHelper有助于掌握数据验证的实现和应用，是保证数据质量的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解验证概念**：掌握数据验证的基本概念和重要性
2. **分析正则表达式**：理解各种格式验证的正则表达式
3. **学习算法实现**：掌握身份证验证、密码强度等算法
4. **实践验证应用**：在实际项目中应用数据验证

### 前置学习
- 正则表达式基础
- 数据验证概念
- 字符串处理方法
- 密码安全原理

### 后续学习
- `TKStringHelper.ets` - 学习字符串处理工具
- `TKDataHelper.ets` - 了解数据转换工具
- 表单组件的验证集成

### 实践建议
1. **基础验证测试**：测试各种格式验证方法
2. **自定义验证规则**：创建自定义的验证逻辑
3. **表单集成**：在表单中集成验证功能
4. **性能测试**：测试大量数据的验证性能

### 常见问题
1. **问题**: 如何添加自定义的验证规则？
   **解答**: 可以使用isFormatRegex方法配合自定义正则表达式，或者扩展checkValid方法添加新的验证选项。

2. **问题**: 密码强度检测的标准是什么？
   **解答**: 基于长度、字符类型、复杂度等多个维度评分，70分以上为高强度，40-70分为中等强度，40分以下为低强度。
