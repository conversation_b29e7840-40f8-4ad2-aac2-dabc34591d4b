# TKAesHelper

> **文件路径**: `src/main/ets/util/crypto/TKAesHelper.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: AES加密算法、对称加密、密钥管理、Base64编码

## 📋 文件概述

### 功能定位
TKAesHelper是HMThinkBaseHar框架的**AES加密工具类**，提供了完整的AES对称加密功能。它采用命名空间设计，集成了鸿蒙CryptoArchitectureKit，支持多种AES加密模式、同步异步操作、密钥管理等功能，为应用提供安全可靠的数据加密服务。

### 核心职责
- **AES加密解密**：提供AES128/256的加密和解密功能
- **多模式支持**：支持ECB、CBC等加密模式，以及PKCS7和NoPadding填充
- **密钥管理**：自动处理密钥生成、向量管理和密钥长度适配
- **数据转换**：支持字符串和字节数组的相互转换和Base64编码
- **同步异步**：提供同步和异步两套完整的加密解密API

### 设计模式
- **命名空间模式**：使用namespace封装加密功能，避免全局污染
- **工厂模式**：根据密钥长度自动选择AES128或AES256算法
- **策略模式**：支持不同的加密模式和填充策略
- **适配器模式**：封装鸿蒙CryptoArchitectureKit，提供统一接口

## 🔧 核心功能

### 加密模式枚举

#### 枚举：`TKAesMode`
- **ECB**: 电子密码本模式，使用PKCS7填充
- **CBC**: 密码块链模式，使用PKCS7填充
- **ECB_NOPADDING**: 电子密码本模式，无填充
- **CBC_NOPADDING**: 密码块链模式，无填充

### 异步加密方法

#### 方法1：`stringWithAesEncrypt(content, key, vector?, aesMode?)`
- **功能**: 字符串AES加密，返回Base64编码结果
- **参数**: 
  - content - 要加密的内容（字符串或字节数组）
  - key - 加密密钥（字符串或字节数组）
  - vector - 初始化向量（可选，默认使用密钥）
  - aesMode - 加密模式（可选，默认ECB）
- **返回值**: Promise<string> - Base64编码的加密结果
- **使用场景**: 文本数据的安全传输和存储

#### 方法2：`dataWithAesEncrypt(content, key, vector?, aesMode?)`
- **功能**: 数据AES加密，返回字节数组
- **参数**: 同上
- **返回值**: Promise<Uint8Array> - 加密后的字节数组
- **使用场景**: 二进制数据的加密处理

#### 方法3：`stringWithAesDecrypt(content, key, vector?, aesMode?)`
- **功能**: 字符串AES解密，从Base64解码后解密
- **参数**: 
  - content - 要解密的内容（Base64字符串或字节数组）
  - key - 解密密钥
  - vector - 初始化向量（可选）
  - aesMode - 解密模式（可选）
- **返回值**: Promise<string> - 解密后的字符串
- **使用场景**: 加密文本数据的解密还原

#### 方法4：`dataWithAesDecrypt(content, key, vector?, aesMode?)`
- **功能**: 数据AES解密，返回字节数组
- **参数**: 同上
- **返回值**: Promise<Uint8Array> - 解密后的字节数组
- **使用场景**: 加密二进制数据的解密还原

### 同步加密方法

#### 方法1：`stringWithAesEncryptSync(content, key, vector?, aesMode?)`
- **功能**: 同步字符串AES加密
- **参数**: 同异步版本
- **返回值**: string - Base64编码的加密结果
- **使用场景**: 需要同步处理的加密场景

#### 方法2：`dataWithAesEncryptSync(content, key, vector?, aesMode?)`
- **功能**: 同步数据AES加密
- **参数**: 同异步版本
- **返回值**: Uint8Array - 加密后的字节数组
- **使用场景**: 同步二进制数据加密

#### 方法3：`stringWithAesDecryptSync(content, key, vector?, aesMode?)`
- **功能**: 同步字符串AES解密
- **参数**: 同异步版本
- **返回值**: string - 解密后的字符串
- **使用场景**: 需要同步处理的解密场景

#### 方法4：`dataWithAesDecryptSync(content, key, vector?, aesMode?)`
- **功能**: 同步数据AES解密
- **参数**: 同异步版本
- **返回值**: Uint8Array - 解密后的字节数组
- **使用场景**: 同步二进制数据解密

## 💡 技术要点

### 核心算法/逻辑

#### 算法选择机制
```typescript
function getAlgName(key: string | Uint8Array): string {
  if (key.length <= 16) {
    return 'AES128'
  }
  return 'AES256'
}
```

#### 密钥生成机制
```typescript
async function genSymKeyByData(symKeyData: Uint8Array, algName: string = "AES128"): Promise<cryptoFramework.SymKey> {
  let symKeyBlob: cryptoFramework.DataBlob = { data: symKeyData };
  let aesGenerator = cryptoFramework.createSymKeyGenerator(algName);
  let symKey = await aesGenerator.convertKey(symKeyBlob);
  return symKey;
}
```

#### 初始化向量生成
```typescript
function genIvParamsSpec(dataIv: Uint8Array): cryptoFramework.IvParamsSpec {
  let ivBlob: cryptoFramework.DataBlob = { data: dataIv };
  let ivParamsSpec: cryptoFramework.IvParamsSpec = {
    algName: "IvParamsSpec",
    iv: ivBlob
  };
  return ivParamsSpec;
}
```

#### 核心加解密逻辑
```typescript
async function dataWithAesEncrptAndDecryptData(
  data: Uint8Array, 
  key: string | Uint8Array,
  vector: string | Uint8Array = "", 
  cryptoMode: cryptoFramework.CryptoMode = cryptoFramework.CryptoMode.ENCRYPT_MODE,
  aesMode: TKAesMode = TKAesMode.ECB
): Promise<Uint8Array> {
  let result: Uint8Array = new Uint8Array();
  
  try {
    // 数据类型转换
    let keyData: Uint8Array = key instanceof Uint8Array ? key : TKDataHelper.stringToUint8Array(key);
    let vectorData: Uint8Array = vector instanceof Uint8Array ? vector : TKDataHelper.stringToUint8Array(vector);
    
    // 向量处理
    if (!vectorData || vectorData.length == 0) {
      vectorData = keyData; // 默认使用密钥作为向量
    }
    if (vectorData.length > 16) {
      vectorData = vectorData.subarray(0, 16); // 截取前16字节
    }
    
    // 数据填充处理
    if (aesMode == TKAesMode.ECB_NOPADDING || aesMode == TKAesMode.CBC_NOPADDING) {
      data = TKDataHelper.padUint8Array(data);
    }
    
    // 算法和密钥生成
    let algName: string = getAlgName(key);
    let symKey: cryptoFramework.SymKey = await genSymKeyByData(keyData, algName);
    let iv: cryptoFramework.IvParamsSpec = genIvParamsSpec(vectorData);
    
    // 变换字符串构建
    let transformation: string = "";
    switch (aesMode) {
      case TKAesMode.ECB:
        transformation = `${algName}|ECB|PKCS7`;
        break;
      case TKAesMode.CBC:
        transformation = `${algName}|CBC|PKCS7`;
        break;
      case TKAesMode.ECB_NOPADDING:
        transformation = `${algName}|ECB|NoPadding`;
        break;
      case TKAesMode.CBC_NOPADDING:
        transformation = `${algName}|CBC|NoPadding`;
        break;
    }
    
    // 执行加解密
    let cipher = cryptoFramework.createCipher(transformation);
    await cipher.init(cryptoMode, symKey, iv);
    let plainText: cryptoFramework.DataBlob = { data: data };
    let cipherData = await cipher.doFinal(plainText);
    result = cipherData.data;
    
  } catch (error) {
    TKLog.error(`[TKAesHelper]异步加解密异常，code: ${error.code}， message: ${error.message}`);
  }
  
  return result;
}
```

### 实现机制分析

#### 多模式支持
- **ECB模式**：电子密码本模式，每个块独立加密，适合小数据
- **CBC模式**：密码块链模式，前一块影响后一块，更安全
- **填充策略**：PKCS7自动填充，NoPadding需要手动填充
- **算法适配**：根据密钥长度自动选择AES128或AES256

#### 密钥和向量管理
- **密钥转换**：支持字符串和字节数组两种密钥格式
- **向量处理**：自动处理初始化向量，默认使用密钥
- **长度限制**：向量长度限制为16字节，超出部分自动截取
- **安全性保证**：确保密钥和向量的正确性和安全性

#### 数据处理流程
- **输入验证**：检查数据、密钥的有效性
- **类型转换**：统一转换为Uint8Array进行处理
- **填充处理**：NoPadding模式下自动进行数据填充
- **编码转换**：加密结果自动进行Base64编码

#### 同步异步支持
- **异步版本**：使用Promise，适合大数据量处理
- **同步版本**：直接返回结果，适合小数据量快速处理
- **API一致性**：同步异步版本保持相同的参数和逻辑
- **性能考虑**：根据使用场景选择合适的版本

### 性能考虑
- **算法选择**：根据密钥长度自动选择最适合的算法
- **内存管理**：及时释放临时数据，避免内存泄漏
- **异常处理**：完善的异常捕获，避免程序崩溃
- **缓存优化**：密钥生成器可以复用，减少创建开销

### 错误处理
- **参数验证**：检查输入参数的有效性
- **异常捕获**：捕获加解密过程中的所有异常
- **错误日志**：详细记录错误信息，便于问题排查
- **优雅降级**：加解密失败时返回空数组，不中断程序

### 最佳实践
- **密钥管理**：使用强密钥，定期更换密钥
- **模式选择**：根据安全需求选择合适的加密模式
- **向量使用**：CBC模式下使用随机向量提高安全性
- **数据保护**：加密敏感数据，解密后及时清理

## 🔗 依赖关系

### 依赖的模块
- `cryptoFramework` - 鸿蒙加密框架，提供底层加密功能
- `TKDataHelper` - 数据工具，处理数据类型转换
- `TKStringHelper` - 字符串工具，处理字符串操作
- `TKBase64Helper` - Base64工具，处理编码解码
- `TKLog` - 日志工具，记录错误和调试信息

### 被依赖情况
- `TKCacheManager` - 缓存管理器，使用AES加密缓存数据
- `TKRsaHelper` - RSA工具，结合AES实现混合加密
- `TKComBusV3Client` - 通信客户端，使用AES加密通信数据
- `各业务模块` - 使用AES加密敏感业务数据

### 关联文件
- `TKBase64Helper.ets` - Base64编码工具
- `TKRsaHelper.ets` - RSA加密工具
- `TKPasswordGenerator.ets` - 密码生成器
- `TKDataHelper.ets` - 数据转换工具

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKAesHelper是框架安全功能的重要组成：
1. **数据安全**：为敏感数据提供可靠的加密保护
2. **功能完整**：提供完整的AES加密解决方案
3. **易于使用**：封装复杂的加密逻辑，提供简单易用的API
4. **性能优秀**：基于鸿蒙原生加密框架，性能优秀

理解TKAesHelper有助于掌握数据加密的实现和应用，是保护数据安全的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解AES算法**：掌握AES加密算法的基本原理
2. **分析加密模式**：理解不同加密模式的特点和适用场景
3. **学习密钥管理**：掌握密钥生成、存储和使用的最佳实践
4. **实践加密应用**：在实际项目中应用AES加密功能

### 前置学习
- 对称加密算法基础
- AES加密原理和模式
- 密钥管理和安全实践
- Base64编码原理

### 后续学习
- `TKRsaHelper.ets` - 学习非对称加密的实现
- `TKPasswordGenerator.ets` - 了解安全密钥生成
- `TKBase64Helper.ets` - 掌握编码转换工具

### 实践建议
1. **基础加密测试**：测试不同模式的加密解密功能
2. **性能对比**：对比同步异步版本的性能差异
3. **安全性验证**：验证加密结果的安全性和正确性
4. **集成应用**：在实际应用中集成AES加密功能

### 常见问题
1. **问题**: 如何选择合适的加密模式？
   **解答**: ECB适合小数据块，CBC更安全适合大数据；有填充需求用PKCS7，无填充需求用NoPadding。

2. **问题**: 密钥长度有什么要求？
   **解答**: 密钥长度≤16字节使用AES128，>16字节使用AES256；建议使用16或32字节的强密钥。

## 📝 代码示例

### 基础使用
```typescript
import { TKAesHelper, TKAesMode } from '@thinkive/tk-harmony-base';

// 基础AES加密解密
async function basicAesOperations() {
  const plainText = "Hello, 这是需要加密的敏感数据!";
  const key = "MySecretKey12345"; // 16字节密钥，使用AES128
  const vector = "MyVector12345678"; // 16字节向量

  console.log('原始数据:', plainText);

  // ECB模式加密（默认）
  const encryptedECB = await TKAesHelper.stringWithAesEncrypt(plainText, key);
  console.log('ECB加密结果:', encryptedECB);

  // ECB模式解密
  const decryptedECB = await TKAesHelper.stringWithAesDecrypt(encryptedECB, key);
  console.log('ECB解密结果:', decryptedECB);

  // CBC模式加密（更安全）
  const encryptedCBC = await TKAesHelper.stringWithAesEncrypt(plainText, key, vector, TKAesMode.CBC);
  console.log('CBC加密结果:', encryptedCBC);

  // CBC模式解密
  const decryptedCBC = await TKAesHelper.stringWithAesDecrypt(encryptedCBC, key, vector, TKAesMode.CBC);
  console.log('CBC解密结果:', decryptedCBC);

  // 验证加密解密的正确性
  console.log('ECB加密解密正确:', plainText === decryptedECB);
  console.log('CBC加密解密正确:', plainText === decryptedCBC);
}

// 同步加密解密
function syncAesOperations() {
  const plainText = "同步加密测试数据";
  const key = "SyncKey123456789"; // 16字节密钥

  console.log('同步加密原始数据:', plainText);

  // 同步加密
  const encrypted = TKAesHelper.stringWithAesEncryptSync(plainText, key);
  console.log('同步加密结果:', encrypted);

  // 同步解密
  const decrypted = TKAesHelper.stringWithAesDecryptSync(encrypted, key);
  console.log('同步解密结果:', decrypted);

  console.log('同步加密解密正确:', plainText === decrypted);
}

// 二进制数据加密
async function binaryDataEncryption() {
  // 模拟二进制数据
  const binaryData = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
  const key = "BinaryKey1234567";

  console.log('原始二进制数据:', binaryData);

  // 二进制数据加密
  const encryptedData = await TKAesHelper.dataWithAesEncrypt(binaryData, key);
  console.log('加密后的二进制数据:', encryptedData);

  // 二进制数据解密
  const decryptedData = await TKAesHelper.dataWithAesDecrypt(encryptedData, key);
  console.log('解密后的二进制数据:', decryptedData);

  // 验证数据一致性
  const isEqual = binaryData.every((value, index) => value === decryptedData[index]);
  console.log('二进制数据加密解密正确:', isEqual);
}

// 执行示例
basicAesOperations();
syncAesOperations();
binaryDataEncryption();
```

### 高级用法
```typescript
// AES加密管理器
class AesEncryptionManager {
  private defaultKey: string;
  private defaultVector: string;
  private defaultMode: TKAesMode;

  constructor(key?: string, vector?: string, mode: TKAesMode = TKAesMode.CBC) {
    this.defaultKey = key || this.generateSecureKey();
    this.defaultVector = vector || this.generateSecureVector();
    this.defaultMode = mode;
  }

  // 生成安全密钥
  private generateSecureKey(): string {
    // 生成32字节的强密钥（AES256）
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let key = '';
    for (let i = 0; i < 32; i++) {
      key += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return key;
  }

  // 生成安全向量
  private generateSecureVector(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let vector = '';
    for (let i = 0; i < 16; i++) {
      vector += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return vector;
  }

  // 加密文本
  async encryptText(text: string, key?: string, vector?: string, mode?: TKAesMode): Promise<string> {
    try {
      const encryptKey = key || this.defaultKey;
      const encryptVector = vector || this.defaultVector;
      const encryptMode = mode || this.defaultMode;

      return await TKAesHelper.stringWithAesEncrypt(text, encryptKey, encryptVector, encryptMode);
    } catch (error) {
      console.error('文本加密失败:', error);
      throw new Error('文本加密失败');
    }
  }

  // 解密文本
  async decryptText(encryptedText: string, key?: string, vector?: string, mode?: TKAesMode): Promise<string> {
    try {
      const decryptKey = key || this.defaultKey;
      const decryptVector = vector || this.defaultVector;
      const decryptMode = mode || this.defaultMode;

      return await TKAesHelper.stringWithAesDecrypt(encryptedText, decryptKey, decryptVector, decryptMode);
    } catch (error) {
      console.error('文本解密失败:', error);
      throw new Error('文本解密失败');
    }
  }

  // 加密对象
  async encryptObject(obj: any, key?: string): Promise<string> {
    try {
      const jsonString = JSON.stringify(obj);
      return await this.encryptText(jsonString, key);
    } catch (error) {
      console.error('对象加密失败:', error);
      throw new Error('对象加密失败');
    }
  }

  // 解密对象
  async decryptObject<T>(encryptedText: string, key?: string): Promise<T> {
    try {
      const jsonString = await this.decryptText(encryptedText, key);
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error('对象解密失败:', error);
      throw new Error('对象解密失败');
    }
  }

  // 批量加密
  async encryptBatch(texts: string[], key?: string): Promise<string[]> {
    const results: string[] = [];

    for (const text of texts) {
      try {
        const encrypted = await this.encryptText(text, key);
        results.push(encrypted);
      } catch (error) {
        console.error(`批量加密失败，文本: ${text}`, error);
        results.push(''); // 失败时添加空字符串
      }
    }

    return results;
  }

  // 批量解密
  async decryptBatch(encryptedTexts: string[], key?: string): Promise<string[]> {
    const results: string[] = [];

    for (const encryptedText of encryptedTexts) {
      try {
        const decrypted = await this.decryptText(encryptedText, key);
        results.push(decrypted);
      } catch (error) {
        console.error(`批量解密失败，密文: ${encryptedText}`, error);
        results.push(''); // 失败时添加空字符串
      }
    }

    return results;
  }

  // 获取密钥信息
  getKeyInfo(): {
    keyLength: number,
    vectorLength: number,
    algorithm: string,
    mode: string
  } {
    return {
      keyLength: this.defaultKey.length,
      vectorLength: this.defaultVector.length,
      algorithm: this.defaultKey.length <= 16 ? 'AES128' : 'AES256',
      mode: TKAesMode[this.defaultMode]
    };
  }
}

// 安全数据存储类
class SecureDataStorage {
  private encryptionManager: AesEncryptionManager;
  private storageKey: string = 'secure_data_storage';

  constructor(masterKey?: string) {
    this.encryptionManager = new AesEncryptionManager(masterKey);
  }

  // 安全存储数据
  async storeSecureData(key: string, data: any): Promise<boolean> {
    try {
      // 加密数据
      const encryptedData = await this.encryptionManager.encryptObject(data);

      // 存储到本地（这里模拟存储）
      const storageData = this.getStorageData();
      storageData[key] = encryptedData;
      this.saveStorageData(storageData);

      console.log(`安全数据已存储，键: ${key}`);
      return true;
    } catch (error) {
      console.error('安全数据存储失败:', error);
      return false;
    }
  }

  // 读取安全数据
  async retrieveSecureData<T>(key: string): Promise<T | null> {
    try {
      const storageData = this.getStorageData();
      const encryptedData = storageData[key];

      if (!encryptedData) {
        console.log(`未找到安全数据，键: ${key}`);
        return null;
      }

      // 解密数据
      const decryptedData = await this.encryptionManager.decryptObject<T>(encryptedData);
      console.log(`安全数据已读取，键: ${key}`);
      return decryptedData;
    } catch (error) {
      console.error('安全数据读取失败:', error);
      return null;
    }
  }

  // 删除安全数据
  deleteSecureData(key: string): boolean {
    try {
      const storageData = this.getStorageData();
      if (storageData[key]) {
        delete storageData[key];
        this.saveStorageData(storageData);
        console.log(`安全数据已删除，键: ${key}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('安全数据删除失败:', error);
      return false;
    }
  }

  // 列出所有安全数据键
  listSecureDataKeys(): string[] {
    try {
      const storageData = this.getStorageData();
      return Object.keys(storageData);
    } catch (error) {
      console.error('列出安全数据键失败:', error);
      return [];
    }
  }

  // 模拟获取存储数据
  private getStorageData(): Record<string, string> {
    // 这里应该从实际存储中读取，比如文件或数据库
    return {};
  }

  // 模拟保存存储数据
  private saveStorageData(data: Record<string, string>): void {
    // 这里应该保存到实际存储中
    console.log('存储数据已保存');
  }
}

// 加密通信工具
class EncryptedCommunication {
  private encryptionManager: AesEncryptionManager;

  constructor(sharedKey: string) {
    this.encryptionManager = new AesEncryptionManager(sharedKey);
  }

  // 加密消息
  async encryptMessage(message: any): Promise<string> {
    try {
      const messageData = {
        content: message,
        timestamp: Date.now(),
        checksum: this.calculateChecksum(message)
      };

      return await this.encryptionManager.encryptObject(messageData);
    } catch (error) {
      console.error('消息加密失败:', error);
      throw new Error('消息加密失败');
    }
  }

  // 解密消息
  async decryptMessage(encryptedMessage: string): Promise<any> {
    try {
      const messageData = await this.encryptionManager.decryptObject<{
        content: any,
        timestamp: number,
        checksum: string
      }>(encryptedMessage);

      // 验证校验和
      const expectedChecksum = this.calculateChecksum(messageData.content);
      if (messageData.checksum !== expectedChecksum) {
        throw new Error('消息校验失败');
      }

      // 验证时间戳（可选，防止重放攻击）
      const currentTime = Date.now();
      if (currentTime - messageData.timestamp > 300000) { // 5分钟过期
        throw new Error('消息已过期');
      }

      return messageData.content;
    } catch (error) {
      console.error('消息解密失败:', error);
      throw new Error('消息解密失败');
    }
  }

  // 计算校验和
  private calculateChecksum(data: any): string {
    const jsonString = JSON.stringify(data);
    // 这里应该使用更强的哈希算法，比如SHA-256
    let hash = 0;
    for (let i = 0; i < jsonString.length; i++) {
      const char = jsonString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
  }
}

// 使用示例
async function demonstrateAdvancedAes() {
  // 加密管理器示例
  console.log('=== 加密管理器示例 ===');
  const encryptionManager = new AesEncryptionManager();

  const testText = "这是需要加密的敏感信息";
  const encrypted = await encryptionManager.encryptText(testText);
  const decrypted = await encryptionManager.decryptText(encrypted);

  console.log('原始文本:', testText);
  console.log('加密结果:', encrypted);
  console.log('解密结果:', decrypted);
  console.log('密钥信息:', encryptionManager.getKeyInfo());

  // 对象加密示例
  const testObject = { name: '张三', age: 30, email: '<EMAIL>' };
  const encryptedObject = await encryptionManager.encryptObject(testObject);
  const decryptedObject = await encryptionManager.decryptObject(encryptedObject);

  console.log('原始对象:', testObject);
  console.log('解密对象:', decryptedObject);

  // 安全数据存储示例
  console.log('\n=== 安全数据存储示例 ===');
  const secureStorage = new SecureDataStorage();

  await secureStorage.storeSecureData('user_profile', {
    username: 'admin',
    password: 'secret123',
    permissions: ['read', 'write', 'admin']
  });

  const retrievedProfile = await secureStorage.retrieveSecureData('user_profile');
  console.log('读取的安全数据:', retrievedProfile);

  // 加密通信示例
  console.log('\n=== 加密通信示例 ===');
  const communication = new EncryptedCommunication('SharedSecretKey123456789012345');

  const message = { type: 'user_action', action: 'login', userId: 12345 };
  const encryptedMsg = await communication.encryptMessage(message);
  const decryptedMsg = await communication.decryptMessage(encryptedMsg);

  console.log('原始消息:', message);
  console.log('解密消息:', decryptedMsg);
}

// 执行演示
demonstrateAdvancedAes();
```

## 📚 相关资源

### 官方文档
- HarmonyOS CryptoArchitectureKit开发指南
- AES加密算法标准文档

### 参考资料
- 《现代密码学》- 对称加密算法
- 《密码学工程实践指南》- 加密实现最佳实践

### 相关文件
- `TKRsaHelper.ets` - RSA非对称加密工具
- `TKBase64Helper.ets` - Base64编码工具
- `TKPasswordGenerator.ets` - 密码生成器
