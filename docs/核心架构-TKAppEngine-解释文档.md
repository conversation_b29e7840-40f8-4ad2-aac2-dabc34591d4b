# TKAppEngine - 框架核心引擎

> **文件路径**: `src/main/ets/base/engine/TKAppEngine.ets`
> **所属层级**: 核心架构层
> **重要程度**: ⭐⭐⭐⭐⭐ (5星)
> **学习难度**: 高级
> **前置知识**: 单例模式、观察者模式、TypeScript基础、鸿蒙应用生命周期、Stage模型

> **📖 阅读提示**: 本文档包含多个Mermaid图表，建议在支持Mermaid渲染的环境中查看（如GitHub、GitLab、Typora、VS Code等）以获得最佳阅读体验。

## 📋 文件概述

### 核心定位

TKAppEngine是HMThinkBaseHar框架的**总指挥官**和**控制中心**，在整个框架架构中处于最核心的位置。作为框架的"大脑"，它采用单例模式设计，统一协调各个子系统的启动、运行和停止。

### 重要程度评级

⭐⭐⭐⭐⭐ **核心必学**: 框架核心，必须深入理解

**评级理由**：

1. **生命周期管理**：控制整个框架的启动和停止
2. **资源协调**：统一管理所有子系统的初始化和清理
3. **通信枢纽**：作为模块间通信和插件调用的中央调度器
4. **状态监控**：监听和响应系统级状态变化

## 🏗️ 架构层次图

### 在框架中的核心地位

TKAppEngine在整个框架架构中处于最核心的位置：

```mermaid
%%{init: {'theme':'default', 'themeVariables': {'primaryColor': '#ff6b6b', 'primaryTextColor': '#000', 'primaryBorderColor': '#333', 'lineColor': '#333', 'sectionBkgColor': '#f8f9fa', 'altSectionBkgColor': '#e9ecef', 'gridColor': '#ddd', 'secondaryColor': '#4ecdc4', 'tertiaryColor': '#95a5a6'}}}%%
graph TB
    subgraph APP["🏢 应用层"]
        A1["📱 UIAbility<br/>原生应用能力"]
        A2["🔧 TKUIAbility<br/>框架应用能力"]
        A3["💼 业务模块<br/>具体业务逻辑"]
    end

    subgraph CORE["⚡ 框架核心层"]
        B1["🎯 TKAppEngine<br/>核心引擎控制器"]
        B2["🔄 TKModuleEngine<br/>模块通信引擎"]
        B3["🔌 TKPluginInvokeCenter<br/>插件调用中心"]
    end

    subgraph SERVICE["🛠️ 基础服务层"]
        C1["📋 TKContextHelper<br/>上下文管理器"]
        C2["💾 TKCacheManager<br/>缓存管理器"]
        C3["🗺️ TKRouterPageManager<br/>路由页面管理"]
        C4["📢 TKNotificationCenter<br/>通知中心"]
    end

    subgraph UTIL["🔧 工具类层"]
        D1["📝 TKLog<br/>日志工具"]
        D2["⚙️ TKSystemHelper<br/>系统工具"]
        D3["🌐 TKNetHelper<br/>网络工具"]
        D4["📱 TKDeviceHelper<br/>设备工具"]
    end

    subgraph LISTENER["👂 系统监听层"]
        E1["📡 TKNetworkListener<br/>网络状态监听"]
        E2["🪟 TKWindowListener<br/>窗口状态监听"]
        E3["⌨️ TKKeyboardListener<br/>键盘状态监听"]
        E4["📱 TKFoldDisplayListener<br/>折叠屏监听"]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1

    B1 --> B2
    B1 --> B3
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4

    B1 --> D1
    B1 --> D2
    B1 --> D3
    B1 --> D4

    E1 --> B1
    E2 --> B1
    E3 --> B1
    E4 --> B1

    style B1 fill:#ff6b6b,stroke:#333,stroke-width:6px,color:#fff
    style B2 fill:#4ecdc4,stroke:#333,stroke-width:3px
    style B3 fill:#4ecdc4,stroke:#333,stroke-width:3px
    style C1 fill:#95a5a6,stroke:#333,stroke-width:2px
    style C2 fill:#95a5a6,stroke:#333,stroke-width:2px
    style C3 fill:#95a5a6,stroke:#333,stroke-width:2px
    style C4 fill:#95a5a6,stroke:#333,stroke-width:2px
```

**架构层次说明**：

- **应用层**：业务代码和UIAbility，通过TKAppEngine访问框架功能
- **框架核心层**：🔴 **TKAppEngine作为总控制器**，协调模块引擎和插件中心
- **基础服务层**：提供上下文、缓存、路由、通知等基础服务
- **工具类层**：日志、系统、网络、设备等工具类支持
- **系统监听层**：监听系统级事件，向TKAppEngine报告状态变化

### 核心价值与代码示例

#### 1. 统一控制 - 框架生命周期的统一管理入口

**概念说明**：所有子系统的启动、停止都通过TKAppEngine统一管理，避免分散控制导致的状态不一致。

**代码示例**：

```typescript
// ❌ 错误做法：分散控制各个子系统
export class BadMainAbility extends UIAbility {
  onWindowStageCreate(windowStage: window.WindowStage): void {
    // 分散初始化，难以管理依赖关系和错误处理
    TKLog.init();
    TKCacheManager.shareInstance().startMonitor();
    TKRouterPageManager.shareInstance().startListener();
    TKNetworkListener.shareInstance().startListener();
    // ... 更多子系统初始化
  }
}

// ✅ 正确做法：通过TKAppEngine统一控制
export class GoodMainAbility extends TKUIAbility {
  onWindowStageCreate(windowStage: window.WindowStage): void {
    // 一行代码启动整个框架，内部自动处理所有依赖
    TKAppEngine.shareInstance().start({
      context: this.context,
      finishCallBack: () => {
        console.log('框架启动完成，所有子系统已就绪');
        windowStage.loadContent('pages/Index');
      }
    });
  }

  onDestroy(): void {
    // 一行代码停止整个框架，自动清理所有资源
    TKAppEngine.shareInstance().stop();
  }
}
```

#### 2. 依赖协调 - 确保各子系统按正确顺序启动

**概念说明**：自动处理子系统间的复杂依赖关系，确保启动顺序正确。

**实际场景**：TKRouterPageManager依赖TKContextHelper，TKNetworkListener依赖TKLog等。

**代码示例**：

```typescript
// TKAppEngine内部的依赖协调逻辑
public start(options: TKAppEngineStartOption) {
  // 阶段1: 基础依赖 - 必须最先启动
  TKContextHelper.setCurrentContext(options.context, options.hspModuleName);
  TKLog.init(); // 日志系统优先启动，其他系统需要记录日志

  // 阶段2: 核心服务 - 依赖基础服务
  TKVersionManager.shareInstance().initContext(); // 需要Context
  TKCacheManager.shareInstance().startMonitor();  // 需要Log记录

  // 阶段3: 业务服务 - 依赖核心服务
  TKRouterPageManager.shareInstance().startListener(); // 需要Context和Log

  // 阶段4: 延迟启动 - 避免阻塞主线程
  setTimeout(() => {
    TKNetworkListener.shareInstance().startListener(); // 需要Log记录网络状态
  }, 1000);
}

// 开发者无需关心这些复杂的依赖关系，框架自动处理
```

#### 3. 性能优化 - 分阶段启动策略

**概念说明**：通过同步、延迟、异步三种策略，优化应用启动性能。

**性能对比**：

```typescript
// ❌ 性能问题：所有服务同步启动，阻塞主线程
async function badStartup() {
  const startTime = Date.now();

  await TKContextHelper.init();        // 20ms
  await TKLog.init();                  // 30ms
  await TKCacheManager.startMonitor(); // 50ms
  await TKNetworkListener.start();     // 200ms - 网络检测很慢
  await TKVersionManager.checkUpdate(); // 500ms - 版本检查很慢

  const totalTime = Date.now() - startTime; // 800ms，用户感觉卡顿
  console.log(`启动耗时: ${totalTime}ms`);
}

// ✅ 性能优化：TKAppEngine的分阶段策略
function optimizedStartup() {
  const startTime = Date.now();

  // 同步启动：核心功能，必须立即可用 (100ms)
  TKContextHelper.init();
  TKLog.init();
  TKCacheManager.startMonitor();

  console.log(`核心功能就绪: ${Date.now() - startTime}ms`); // 100ms，用户可以开始使用

  // 延迟启动：非关键功能，1秒后启动
  setTimeout(() => {
    TKNetworkListener.start(); // 不阻塞主线程
  }, 1000);

  // 异步启动：耗时功能，后台执行
  Promise.resolve().then(() => {
    TKVersionManager.checkUpdate(); // 不影响用户体验
  });
}
```

#### 4. 错误隔离 - 单个子系统异常不影响整体运行

**概念说明**：即使某个子系统启动失败，其他系统仍能正常工作。

**代码示例**：

```typescript
// TKAppEngine内部的错误隔离机制
public start(options: TKAppEngineStartOption) {
  try {
    // 核心系统启动失败会抛出异常
    TKContextHelper.setCurrentContext(options.context);
    TKLog.init();
  } catch (error) {
    console.error('核心系统启动失败，无法继续:', error);
    return;
  }

  // 可选系统启动失败不影响整体
  try {
    TKCacheManager.shareInstance().startMonitor();
  } catch (error) {
    console.warn('缓存管理器启动失败，但不影响其他功能:', error);
  }

  try {
    TKNetworkListener.shareInstance().startListener();
  } catch (error) {
    console.warn('网络监听器启动失败，但不影响离线功能:', error);
  }

  // 即使部分系统失败，应用仍可正常使用
  options.finishCallBack?.();
}

// 实际使用中的错误处理
TKAppEngine.shareInstance().start({
  context: this.context,
  finishCallBack: () => {
    // 即使某些子系统启动失败，这个回调仍会执行
    // 应用的核心功能仍然可用
    this.loadMainPage();
  }
});
```

## 📋 核心功能概览与实际应用

### 主要职责与代码示例

#### 1. 框架生命周期管理

**实际场景**：应用启动时需要初始化多个子系统，退出时需要清理资源。

```typescript
// 生命周期管理的实际应用
export class MyApplication extends TKUIAbility {
  onWindowStageCreate(windowStage: window.WindowStage): void {
    const startTime = Date.now();

    TKAppEngine.shareInstance().start({
      context: this.context,
      finishCallBack: () => {
        const initTime = Date.now() - startTime;
        console.log(`应用初始化完成，耗时: ${initTime}ms`);

        // 检查框架状态
        if (TKAppEngine.shareInstance().isRunning()) {
          windowStage.loadContent('pages/Index');
        }
      }
    });
  }

  onDestroy(): void {
    // 自动清理所有子系统资源
    TKAppEngine.shareInstance().stop();
    console.log('应用资源已清理完成');
  }
}
```

#### 2. 插件调用中心 - 50+个内置插件的统一管理

**实际场景**：获取设备信息、网络状态、用户权限等功能。

```typescript
// 插件调用的实际应用
class DeviceInfoService {
  private engine = TKAppEngine.shareInstance();

  // 获取设备基本信息
  async getDeviceInfo() {
    const result = this.engine.callPlugin({
      funcNo: "10001", // 设备信息插件
      param: { includeHardware: true }
    });

    if (result.errorNo === 0) {
      return {
        deviceId: result.data.deviceId,
        osVersion: result.data.osVersion,
        appVersion: result.data.appVersion
      };
    }
    throw new Error(`获取设备信息失败: ${result.errorInfo}`);
  }

  // 检查网络状态
  async checkNetworkStatus() {
    const result = this.engine.callPlugin({
      funcNo: "20001", // 网络状态插件
      param: {}
    });

    return result.errorNo === 0 ? result.data.isConnected : false;
  }

  // 请求用户权限
  async requestPermission(permission: string) {
    const result = this.engine.callPlugin({
      funcNo: "30001", // 权限管理插件
      param: { permission }
    });

    return result.errorNo === 0 ? result.data.granted : false;
  }
}
```

#### 3. 模块通信枢纽 - 跨模块数据传递

**实际场景**：用户模块需要通知订单模块用户登录状态变化。

```typescript
// 模块通信的实际应用
class UserModule {
  private engine = TKAppEngine.shareInstance();

  // 用户登录成功后通知其他模块
  onUserLogin(userInfo: UserInfo) {
    this.engine.sendModuleMessage({
      funcNo: "USER_LOGIN",
      sourceModule: "UserModule",
      targetModule: "OrderModule", // 通知订单模块
      action: TKModuleMessageAction.NSNotify,
      param: {
        userId: userInfo.id,
        userName: userInfo.name,
        loginTime: Date.now()
      }
    });

    // 同时通知统计模块
    this.engine.sendModuleMessage({
      funcNo: "USER_LOGIN",
      sourceModule: "UserModule",
      targetModule: "AnalyticsModule",
      action: TKModuleMessageAction.NSNotify,
      param: { event: "user_login", userId: userInfo.id }
    });
  }
}

class OrderModule implements TKModuleDelegate {
  getModuleName(): string {
    return "OrderModule";
  }

  // 接收用户登录消息
  onReceiveMessage(message: TKModuleMessage): void {
    if (message.funcNo === "USER_LOGIN") {
      const { userId, userName } = message.param;
      console.log(`用户 ${userName} 已登录，开始加载订单数据`);
      this.loadUserOrders(userId);
    }
  }

  private loadUserOrders(userId: string) {
    // 加载用户订单逻辑
  }
}
```

### 设计模式应用与代码实现

#### 1. 单例模式 - 确保全局唯一实例

**问题场景**：如果允许多个TKAppEngine实例，会导致资源冲突和状态不一致。

```typescript
// ❌ 问题：多实例导致的资源冲突
class BadExample {
  init() {
    const engine1 = new TKAppEngine(); // 假设可以直接new
    const engine2 = new TKAppEngine();

    engine1.start({ context: this.context }); // 启动第一个实例
    engine2.start({ context: this.context }); // 启动第二个实例

    // 结果：两个实例都尝试初始化相同的子系统，导致冲突
    // TKLog被初始化两次，TKCacheManager启动两个监控器等
  }
}

// ✅ 解决方案：单例模式确保唯一性
class TKAppEngine {
  private static instance: TKAppEngine | undefined = undefined;
  private _isRunning: boolean = false;

  // 私有构造函数，防止外部直接实例化
  private constructor() {}

  public static shareInstance(): TKAppEngine {
    if (!TKAppEngine.instance) {
      TKAppEngine.instance = new TKAppEngine();
    }
    return TKAppEngine.instance; // 始终返回同一个实例
  }

  public start(options: TKAppEngineStartOption) {
    if (this._isRunning) {
      console.log('框架已在运行，跳过重复启动');
      options.finishCallBack?.();
      return;
    }
    // 启动逻辑...
  }
}

// 正确使用方式
class GoodExample {
  init() {
    const engine1 = TKAppEngine.shareInstance();
    const engine2 = TKAppEngine.shareInstance();

    console.log(engine1 === engine2); // true，同一个实例

    engine1.start({ context: this.context }); // 只会启动一次
    engine2.start({ context: this.context }); // 自动跳过重复启动
  }
}
```

#### 2. 观察者模式 - 松耦合的事件驱动架构

**实际场景**：网络状态变化时，多个模块需要做出响应。

```typescript
// 观察者模式的实际应用
class NetworkStatusObserver {
  constructor() {
    // 注册为网络状态变化的观察者
    TKNotificationCenter.addObserver(
      this,
      'onNetworkChanged',
      'NETWORK_STATUS_CHANGED'
    );
  }

  onNetworkChanged(notification: TKNotification) {
    const isConnected = notification.userInfo?.isConnected;
    if (isConnected) {
      console.log('网络已连接，开始同步数据');
      this.syncData();
    } else {
      console.log('网络已断开，切换到离线模式');
      this.switchToOfflineMode();
    }
  }

  private syncData() {
    // 同步数据逻辑
  }

  private switchToOfflineMode() {
    // 离线模式逻辑
  }
}

// TKAppEngine内部作为事件发布者
class TKNetworkListener {
  private checkNetworkStatus() {
    const isConnected = this.getCurrentNetworkStatus();

    // 发布网络状态变化事件
    TKNotificationCenter.postNotification({
      name: 'NETWORK_STATUS_CHANGED',
      userInfo: { isConnected }
    });
  }
}
```

#### 3. 外观模式 - 简化复杂子系统的访问

**问题场景**：开发者需要直接操作多个复杂的子系统。

```typescript
// ❌ 问题：直接操作复杂子系统
class BadUsage {
  initApp() {
    // 开发者需要了解每个子系统的复杂API
    TKContextHelper.setCurrentContext(this.context, "MyModule");
    TKContextHelper.bindApplicationLifecycle();
    TKVersionManager.shareInstance().initContext();
    TKLog.init();
    TKCacheManager.shareInstance().startMonitor();
    TKRouterPageManager.shareInstance().startListener();
    // ... 还有更多复杂的初始化步骤
  }
}

// ✅ 解决方案：TKAppEngine作为外观，提供简化接口
class GoodUsage {
  initApp() {
    // 一个简单的接口隐藏了所有复杂性
    TKAppEngine.shareInstance().start({
      context: this.context,
      hspModuleName: "MyModule",
      finishCallBack: () => {
        console.log('所有复杂的初始化都已完成');
      }
    });
  }
}
```

## 🔗 依赖关系全景

### 核心依赖图谱

TKAppEngine作为框架核心，依赖12个主要子系统，同时被6个监听器和3个上层模块依赖：

```mermaid
graph TD
    A[TKAppEngine]

    A --> B[TKContextHelper]
    A --> C[TKVersionManager]
    A --> D[TKCacheManager]
    A --> E[TKRouterPageManager]
    A --> F[TKModuleEngine]
    A --> G[TKPluginInvokeCenter]
    A --> H[TKNotificationCenter]
    A --> I[TKLog]
    A --> J[TKSystemHelper]
    A --> K[TKNetHelper]
    A --> L[TKDeviceHelper]

    L1[TKNetworkListener] --> A
    L2[TKWindowListener] --> A
    L3[TKKeyboardListener] --> A
    L4[TKFoldDisplayListener] --> A

    M[UIAbility] --> A
    N[TKUIAbility] --> A
    O[BusinessModules] --> A

    style A fill:#ff6b6b,stroke:#333,stroke-width:4px
    style B fill:#4ecdc4,stroke:#333,stroke-width:2px
    style C fill:#4ecdc4,stroke:#333,stroke-width:2px
    style D fill:#4ecdc4,stroke:#333,stroke-width:2px
    style E fill:#4ecdc4,stroke:#333,stroke-width:2px
    style F fill:#4ecdc4,stroke:#333,stroke-width:2px
    style G fill:#4ecdc4,stroke:#333,stroke-width:2px
```

**依赖关系说明**：

- **⬇️ 正向依赖**：TKAppEngine依赖各个子系统提供的功能
- **⬆️ 反向依赖**：监听器和上层模块依赖TKAppEngine提供的服务
- **🏗️ 核心子系统**：上下文、版本、模块、插件等核心功能
- **📦 基础服务**：缓存、路由、通知等基础服务
- **🔧 工具支持**：日志、系统、网络、设备等工具类

### 启动时序全景

展示TKAppEngine启动过程中与各个子系统的交互顺序：

```mermaid
sequenceDiagram
    participant UI as UIAbility
    participant Engine as TKAppEngine
    participant Context as TKContextHelper
    participant Version as TKVersionManager
    participant Log as TKLog
    participant Cache as TKCacheManager
    participant Router as TKRouterPageManager
    participant Network as TKNetworkListener
    participant Plugin as TKPluginCenter
    participant CB as Callback

    Note over UI,CB: 阶段1: 启动请求
    UI->>Engine: start(options)
    Engine->>Engine: 检查_isRunning状态

    Note over UI,CB: 阶段2: 上下文初始化
    Engine->>Context: setCurrentContext(context, hspModule)
    Context-->>Engine: 上下文设置完成

    Note over UI,CB: 阶段3: 核心服务启动
    Engine->>Context: bindApplicationLifecycle()
    Engine->>Version: initContext()
    Engine->>Log: initLog()
    Engine->>Engine: registerSystemNotification()

    Note over UI,CB: 阶段4: 基础服务启动
    par 同步启动基础服务
        Engine->>Cache: startMonitor()
        Cache-->>Engine: 监控启动完成
    and
        Engine->>Router: startListener()
        Router-->>Engine: 路由监听启动
    end

    Note over UI,CB: 阶段5: 延迟网络服务(1秒后)
    Engine->>Network: startListener() (延迟)
    Network-->>Engine: 网络监听启动

    Note over UI,CB: 阶段6: 异步测速服务
    par 异步启动测速服务
        Engine->>Engine: startTestSpeed()
        Engine->>Engine: startHqPushServer()
    end

    Note over UI,CB: 阶段7: 版本检查(2秒后)
    Engine->>Plugin: callPlugin(funcNo 50203) (延迟)

    Note over UI,CB: 启动完成
    Engine->>CB: finishCallBack()
    CB-->>UI: 启动完成通知
```

**启动阶段详解**：

| 阶段 | 时间范围 | 主要任务 | 执行方式 |
|------|----------|----------|----------|
| **阶段1-2** | 0-20ms | 状态检查和上下文初始化 | 同步执行 |
| **阶段3-4** | 20-100ms | 核心服务和基础服务启动 | 同步执行 |
| **阶段5** | 1000ms后 | 网络服务延迟启动 | 延迟执行 |
| **阶段6** | 异步 | 测速服务并行启动 | 异步执行 |
| **阶段7** | 2000ms后 | 版本检查延迟启动 | 延迟执行 |

**性能优化策略**：

- 🚀 **同步启动**：核心服务优先，确保基础功能可用
- ⏰ **延迟启动**：网络服务延迟1秒，避免阻塞主线程
- 🔄 **异步处理**：测速服务并行执行，提高启动效率
- 📊 **性能监控**：记录启动耗时，通常在100-500ms之间

## 🔧 详细API说明与实际应用

### 启动配置接口详解

#### 接口：`TKAppEngineStartOption`

```typescript
export interface TKAppEngineStartOption {
  context?: common.UIAbilityContext;    // 应用上下文
  hspModuleName?: string;               // HSP模块名称
  finishCallBack?: TKAppEngineStartFinishCallBack; // 完成回调
}
```

**配置参数的实际应用场景**：

```typescript
// 场景1：基础应用启动
TKAppEngine.shareInstance().start({
  context: this.context, // 必需：提供系统能力访问
  finishCallBack: () => {
    // 框架启动完成后的业务逻辑
    this.initUserInterface();
  }
});

// 场景2：HSP模块化应用启动
TKAppEngine.shareInstance().start({
  context: this.context,
  hspModuleName: "BusinessLogicModule", // 指定HSP模块
  finishCallBack: () => {
    console.log('HSP模块已加载，业务逻辑可用');
  }
});

// 场景3：错误处理和重试机制
TKAppEngine.shareInstance().start({
  context: this.context,
  finishCallBack: () => {
    if (TKAppEngine.shareInstance().isRunning()) {
      console.log('框架启动成功');
    } else {
      console.error('框架启动失败，尝试重启');
      this.retryStartup();
    }
  }
});
```

### 核心API方法与实际应用

#### 1. shareInstance() - 单例实例获取

**使用场景对比**：

```typescript
// ❌ 错误：尝试直接实例化（会编译错误）
// const engine = new TKAppEngine(); // 构造函数是私有的

// ✅ 正确：使用单例方法
const engine = TKAppEngine.shareInstance();

// 实际应用：在不同类中获取同一实例
class UserService {
  private engine = TKAppEngine.shareInstance();

  login() {
    // 使用框架功能
    const result = this.engine.callPlugin({
      funcNo: "USER_LOGIN",
      param: { username: "test" }
    });
  }
}

class OrderService {
  private engine = TKAppEngine.shareInstance(); // 同一个实例

  createOrder() {
    // 发送模块消息
    this.engine.sendModuleMessage({
      funcNo: "ORDER_CREATED",
      sourceModule: "OrderService",
      targetModule: "UserService",
      action: 1,
      param: { orderId: "12345" }
    });
  }
}
```

<augment_code_snippet path="src/main/ets/base/engine/TKAppEngine.ets" mode="EXCERPT">

````typescript
public static shareInstance(): TKAppEngine {
  if (!TKAppEngine.instance) {
    TKAppEngine.instance = new TKAppEngine();
  }
  return TKAppEngine.instance;
}
````

</augment_code_snippet>

#### 2. start() - 框架启动与错误处理

**完整的启动流程示例**：

```typescript
class ApplicationManager {
  private startupAttempts = 0;
  private maxRetries = 3;

  async initializeApp() {
    try {
      await this.startFramework();
    } catch (error) {
      console.error('应用初始化失败:', error);
      await this.handleStartupFailure(error);
    }
  }

  private startFramework(): Promise<void> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();

      TKAppEngine.shareInstance().start({
        context: this.context,
        hspModuleName: this.getModuleName(),
        finishCallBack: () => {
          const initTime = Date.now() - startTime;
          console.log(`框架启动成功，耗时: ${initTime}ms`);

          // 验证关键子系统是否正常
          if (this.validateCriticalSystems()) {
            resolve();
          } else {
            reject(new Error('关键子系统验证失败'));
          }
        }
      });

      // 设置启动超时
      setTimeout(() => {
        reject(new Error('框架启动超时'));
      }, 10000);
    });
  }

  private validateCriticalSystems(): boolean {
    const engine = TKAppEngine.shareInstance();

    // 检查框架是否正在运行
    if (!engine.isRunning()) {
      console.error('框架未正常启动');
      return false;
    }

    // 测试插件系统
    try {
      const result = engine.callPlugin({
        funcNo: "SYSTEM_CHECK",
        param: {}
      });
      if (result.errorNo !== 0) {
        console.error('插件系统异常:', result.errorInfo);
        return false;
      }
    } catch (error) {
      console.error('插件调用失败:', error);
      return false;
    }

    return true;
  }

  private async handleStartupFailure(error: Error) {
    this.startupAttempts++;

    if (this.startupAttempts < this.maxRetries) {
      console.log(`启动失败，尝试第${this.startupAttempts + 1}次重启...`);

      // 清理可能的残留状态
      TKAppEngine.shareInstance().stop();

      // 延迟重试
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 递归重试
      await this.initializeApp();
    } else {
      console.error('多次启动失败，进入安全模式');
      this.enterSafeMode();
    }
  }

  private enterSafeMode() {
    // 安全模式：只启动最基本的功能
    console.log('进入安全模式，仅提供基础功能');
  }
}
```

#### 3. stop() - 优雅停止与资源清理

**资源清理的最佳实践**：

```typescript
class AppLifecycleManager {
  private engine = TKAppEngine.shareInstance();
  private cleanupTasks: (() => Promise<void>)[] = [];

  // 注册清理任务
  registerCleanupTask(task: () => Promise<void>) {
    this.cleanupTasks.push(task);
  }

  // 应用退出时的完整清理流程
  async onAppDestroy() {
    console.log('开始应用清理流程...');

    try {
      // 1. 执行自定义清理任务
      await this.executeCleanupTasks();

      // 2. 停止框架引擎
      this.engine.stop();

      // 3. 验证清理结果
      this.validateCleanup();

      console.log('应用清理完成');
    } catch (error) {
      console.error('清理过程中发生错误:', error);
    }
  }

  private async executeCleanupTasks() {
    for (const task of this.cleanupTasks) {
      try {
        await task();
      } catch (error) {
        console.error('清理任务执行失败:', error);
      }
    }
  }

  private validateCleanup() {
    if (this.engine.isRunning()) {
      console.warn('框架仍在运行，可能存在清理问题');
    } else {
      console.log('框架已正确停止');
    }
  }
}

// 使用示例
class MyApplication extends TKUIAbility {
  private lifecycleManager = new AppLifecycleManager();

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // 注册清理任务
    this.lifecycleManager.registerCleanupTask(async () => {
      console.log('清理用户数据缓存');
      await this.clearUserCache();
    });

    this.lifecycleManager.registerCleanupTask(async () => {
      console.log('关闭网络连接');
      await this.closeNetworkConnections();
    });

    // 启动框架
    TKAppEngine.shareInstance().start({
      context: this.context,
      finishCallBack: () => {
        windowStage.loadContent('pages/Index');
      }
    });
  }

  onDestroy(): void {
    // 执行完整的清理流程
    this.lifecycleManager.onAppDestroy();
  }
}
```

#### 4. callPlugin() - 插件调用与错误处理

**插件调用的最佳实践**：

```typescript
class PluginService {
  private engine = TKAppEngine.shareInstance();

  // 安全的插件调用封装
  async callPluginSafely<T>(funcNo: string, param: any): Promise<T> {
    try {
      const result = this.engine.callPlugin({ funcNo, param });

      if (result.errorNo === 0) {
        return result.data as T;
      } else {
        throw new Error(`插件调用失败: ${result.errorInfo} (错误码: ${result.errorNo})`);
      }
    } catch (error) {
      console.error(`插件 ${funcNo} 调用异常:`, error);
      throw error;
    }
  }

  // 具体业务场景：获取用户信息
  async getUserInfo(userId: string): Promise<UserInfo> {
    return this.callPluginSafely<UserInfo>("USER_INFO", { userId });
  }

  // 具体业务场景：上传文件
  async uploadFile(filePath: string): Promise<UploadResult> {
    const result = await this.callPluginSafely<UploadResult>("FILE_UPLOAD", {
      filePath,
      timeout: 30000
    });

    console.log(`文件上传成功: ${result.url}`);
    return result;
  }

  // 具体业务场景：批量插件调用
  async batchPluginCall(requests: PluginRequest[]): Promise<PluginResult[]> {
    const results: PluginResult[] = [];

    for (const request of requests) {
      try {
        const data = await this.callPluginSafely(request.funcNo, request.param);
        results.push({ success: true, data });
      } catch (error) {
        results.push({
          success: false,
          error: error.message,
          funcNo: request.funcNo
        });
      }
    }

    return results;
  }
}

// 使用示例
class UserController {
  private pluginService = new PluginService();

  async handleUserLogin(username: string, password: string) {
    try {
      // 验证用户凭据
      const authResult = await this.pluginService.callPluginSafely("USER_AUTH", {
        username,
        password
      });

      if (authResult.isValid) {
        // 获取用户详细信息
        const userInfo = await this.pluginService.getUserInfo(authResult.userId);

        console.log(`用户 ${userInfo.name} 登录成功`);
        return { success: true, user: userInfo };
      } else {
        return { success: false, message: '用户名或密码错误' };
      }
    } catch (error) {
      console.error('登录过程中发生错误:', error);
      return { success: false, message: '登录失败，请稍后重试' };
    }
  }
}
```

#### 5. sendModuleMessage() - 模块通信实战

**模块通信的完整示例**：

```typescript
// 发送方：用户模块
class UserModule implements TKModuleDelegate {
  private engine = TKAppEngine.shareInstance();

  getModuleName(): string {
    return "UserModule";
  }

  // 用户状态变化时通知其他模块
  onUserStatusChanged(userId: string, status: UserStatus) {
    // 通知订单模块
    this.engine.sendModuleMessage({
      funcNo: "USER_STATUS_CHANGED",
      sourceModule: this.getModuleName(),
      targetModule: "OrderModule",
      action: TKModuleMessageAction.NSNotify,
      param: {
        userId,
        status,
        timestamp: Date.now()
      }
    });

    // 通知推送模块
    this.engine.sendModuleMessage({
      funcNo: "USER_STATUS_CHANGED",
      sourceModule: this.getModuleName(),
      targetModule: "PushModule",
      action: TKModuleMessageAction.NSNotify,
      param: { userId, status }
    });
  }

  // 接收其他模块的消息
  onReceiveMessage(message: TKModuleMessage): void {
    switch (message.funcNo) {
      case "ORDER_COMPLETED":
        this.handleOrderCompleted(message.param);
        break;
      case "PAYMENT_SUCCESS":
        this.handlePaymentSuccess(message.param);
        break;
      default:
        console.warn(`未处理的消息类型: ${message.funcNo}`);
    }
  }

  private handleOrderCompleted(param: any) {
    console.log(`用户 ${param.userId} 的订单 ${param.orderId} 已完成`);
    // 更新用户积分等业务逻辑
  }
}

// 接收方：订单模块
class OrderModule implements TKModuleDelegate {
  private engine = TKAppEngine.shareInstance();

  getModuleName(): string {
    return "OrderModule";
  }

  onReceiveMessage(message: TKModuleMessage): void {
    if (message.funcNo === "USER_STATUS_CHANGED") {
      const { userId, status } = message.param;

      if (status === UserStatus.OFFLINE) {
        console.log(`用户 ${userId} 已离线，暂停订单推送`);
        this.pauseOrderNotifications(userId);
      } else if (status === UserStatus.ONLINE) {
        console.log(`用户 ${userId} 已上线，恢复订单推送`);
        this.resumeOrderNotifications(userId);
      }
    }
  }

  // 订单完成时通知用户模块
  onOrderCompleted(orderId: string, userId: string) {
    this.engine.sendModuleMessage({
      funcNo: "ORDER_COMPLETED",
      sourceModule: this.getModuleName(),
      targetModule: "UserModule",
      action: TKModuleMessageAction.NSNotify,
      param: { orderId, userId, completedAt: Date.now() }
    });
  }
}
```

#### 6. 其他重要方法的实际应用

```typescript
class AppStatusManager {
  private engine = TKAppEngine.shareInstance();

  // 获取应用版本信息
  getAppVersionInfo() {
    const version = this.engine.getVersion();
    console.log(`当前框架版本: ${version}`);

    // 根据版本执行不同逻辑
    if (this.isVersionGreaterThan(version, "2.0.0")) {
      this.enableNewFeatures();
    }
  }

  // 首次安装检测
  handleFirstInstall() {
    if (this.engine.isAppFirstInstall()) {
      console.log('检测到首次安装，执行初始化向导');
      this.showWelcomeGuide();
      this.initDefaultSettings();
    } else {
      console.log('应用已安装过，跳过初始化向导');
      this.loadUserSettings();
    }
  }

  // 后台状态管理
  handleBackgroundState() {
    if (this.engine.isApplicationDidEnterBackground()) {
      console.log('应用在后台运行，暂停非必要任务');
      this.pauseBackgroundTasks();
    } else {
      console.log('应用在前台运行，恢复所有任务');
      this.resumeAllTasks();
    }
  }

  private showWelcomeGuide() {
    // 显示欢迎向导
  }

  private initDefaultSettings() {
    // 初始化默认设置
  }

  private loadUserSettings() {
    // 加载用户设置
  }
}
```

## 💡 实现细节

### 核心算法与逻辑

#### 单例模式实现

采用**线程安全的懒加载单例模式**，确保全局唯一实例：

<augment_code_snippet path="src/main/ets/base/engine/TKAppEngine.ets" mode="EXCERPT">

````typescript
private static instance: TKAppEngine | undefined = undefined;

public static shareInstance(): TKAppEngine {
  if (!TKAppEngine.instance) {
    TKAppEngine.instance = new TKAppEngine();
  }
  return TKAppEngine.instance;
}

private constructor() {
  // 私有构造函数，防止外部直接实例化
}
````

</augment_code_snippet>

#### 7阶段启动策略

TKAppEngine采用**分阶段启动策略**，确保依赖关系正确且性能最优：

<augment_code_snippet path="src/main/ets/base/engine/TKAppEngine.ets" mode="EXCERPT">

````typescript
public start(tkAppEngineStartOption: TKAppEngineStartOption) {
  try {
    if (!this._isRunning) {
      let beginTime: number = new Date().getTime();
      this._isRunning = true;

      // 阶段1: 上下文初始化 - 最高优先级
      TKContextHelper.setCurrentContext(
        tkAppEngineStartOption.context,
        tkAppEngineStartOption.hspModuleName
      );

      // 阶段2: 核心服务启动 - 同步启动
      TKContextHelper.bindApplicationLifecycle();
      TKVersionManager.shareInstance().initContext();
      this.initLog();
      this.registerSystemNotification();

      // 阶段3: 基础服务启动 - 同步启动
      this.isBlurBackground = (TKSystemHelper.getConfig("system.isBlurBackground") !== "0");
      TKCacheManager.shareInstance().startMonitor();
      TKRouterPageManager.shareInstance().startListener();
      TKFoldDisplayListener.shareInstance().startListener();

      // 阶段4: 延迟启动网络服务（1秒后）- 避免阻塞主线程
      setTimeout(() => {
        TKNetHelper.fetchHttpProxy();
        TKNetworkListener.shareInstance().startListener();
        TKWindowListener.shareInstance().startListener();
        TKKeyboardListener.shareInstance().startListener();
        TKKeyBoardVOManager.shareInstance().loadConfig();
      }, 1000);

      // 阶段5: 异步启动站点测速 - 并行处理
      new Promise<void>(async (resolve, reject) => {
        if (this.isAppFirstInstall()) {
          await TKDeviceHelper.getDeviceUUIDASync();
        }
        await this.startTestSpeed();
        resolve();
      }).then(() => {
        let endTime: number = new Date().getTime();
        TKLog.info(`框架初始化完成,耗时[${endTime - beginTime}]毫秒`);
        tkAppEngineStartOption.finishCallBack?.();
      });

      // 阶段6: 启动可选服务
      this.startHqPushServer();

      // 阶段7: 延迟启动版本检查（2秒后）
      setTimeout(() => {
        if (TKSystemHelper.getConfig("update.isOpen") == "1") {
          this.callPlugin({ funcNo: "50203" });
        }
      }, 2000);
    } else {
      // 重复启动直接回调
      tkAppEngineStartOption.finishCallBack?.();
    }
  } catch (error) {
    this._isRunning = false;
    TKLog.error(`框架引擎启动失败: ${error.message}`);
  }
}
````

</augment_code_snippet>

#### 并行测速算法

使用Promise.all实现多个服务的并行测速，提高启动效率：

<augment_code_snippet path="src/main/ets/base/engine/TKAppEngine.ets" mode="EXCERPT">

````typescript
private async startTestSpeed() {
  let testSpeedPromises: Array<Promise<void>> = new Array<Promise<void>>();

  // HTTP服务测速
  if (TKSystemHelper.getConfig("system.isUseServerTestSpeed") == "1") {
    let testSpeedPromise: Promise<void> = new Promise<void>((resolve, reject) => {
      TKHttpRoomServerListener.shareInstance().start(() => {
        resolve();
      })
    });
    testSpeedPromises.push(testSpeedPromise);
  }

  // 网关检测服务测速
  if (TKSystemHelper.getConfig("system.isLinstenGateway") == "1") {
    let testSpeedPromise: Promise<void> = new Promise<void>((resolve, reject) => {
      TKGatewayListener.shareInstance().start(() => {
        this.updateBusConfig(TKBusConfig.shareInstance().updateUrl);
        resolve();
      })
    });
    testSpeedPromises.push(testSpeedPromise);
  }

  // 并行执行所有测速任务
  await Promise.all(testSpeedPromises);
}
````

</augment_code_snippet>

#### 优雅停止机制

按照依赖关系**逆序停止**各个子系统，确保资源正确释放：

<augment_code_snippet path="src/main/ets/base/engine/TKAppEngine.ets" mode="EXCERPT">

````typescript
public stop() {
  try {
    if (this._isRunning) {
      this._isRunning = false;
      // 按照依赖关系逆序停止各个子系统
      TKContextHelper.unbindApplicationLifecycle();
      TKCacheManager.shareInstance().stopMonitor();
      TKFoldDisplayListener.shareInstance().stopListener();
      TKNetworkListener.shareInstance().stopListener();
      TKWindowListener.shareInstance().stopListener();
      TKKeyboardListener.shareInstance().stopListener();

      // 停止网关检测服务
      if (TKSystemHelper.getConfig("system.isLinstenGateway") == "1") {
        TKGatewayListener.shareInstance().stop();
      }

      // 停止行情推送服务
      this.stopHqPushServer();
    }
  } catch (error) {
    TKLog.error(`[框架引擎停止失败]: error code: ${error.code}, message is: ${error.message}`);
  }
}
````

</augment_code_snippet>

### 性能与错误处理

- **延迟初始化**：网络相关服务延迟1秒启动，避免阻塞主线程
- **异步处理**：站点测速使用Promise.all并行处理，提高效率
- **状态缓存**：通过_isRunning标识避免重复操作
- **异常捕获**：start()和stop()方法都有完整的try-catch机制
- **状态恢复**：启动失败时自动重置_isRunning状态
- **日志记录**：所有异常都通过TKLog记录详细信息

## 📝 实际应用场景与代码示例

### 场景1：电商应用的完整启动流程

**业务需求**：电商应用需要在启动时初始化用户系统、商品系统、订单系统等多个模块。

```typescript
// 电商应用的主入口
export class ECommerceApp extends TKUIAbility {
  private userModule: UserModule;
  private productModule: ProductModule;
  private orderModule: OrderModule;

  onWindowStageCreate(windowStage: window.WindowStage): void {
    const startTime = Date.now();

    TKAppEngine.shareInstance().start({
      context: this.context,
      hspModuleName: "ECommerceCore",
      finishCallBack: async () => {
        const initTime = Date.now() - startTime;
        console.log(`框架启动完成，耗时: ${initTime}ms`);

        try {
          // 初始化业务模块
          await this.initBusinessModules();

          // 检查用户登录状态
          await this.checkUserLoginStatus();

          // 加载主页面
          windowStage.loadContent('pages/MainPage');

        } catch (error) {
          console.error('业务模块初始化失败:', error);
          // 降级到基础功能
          windowStage.loadContent('pages/BasicPage');
        }
      }
    });
  }

  private async initBusinessModules() {
    const engine = TKAppEngine.shareInstance();

    // 初始化用户模块
    this.userModule = new UserModule();

    // 初始化商品模块
    this.productModule = new ProductModule();

    // 初始化订单模块
    this.orderModule = new OrderModule();

    // 建立模块间通信
    this.setupModuleCommunication();
  }

  private setupModuleCommunication() {
    // 用户登录成功后，通知其他模块
    this.userModule.onUserLogin = (userInfo) => {
      // 通知商品模块加载个性化推荐
      TKAppEngine.shareInstance().sendModuleMessage({
        funcNo: "USER_LOGIN",
        sourceModule: "UserModule",
        targetModule: "ProductModule",
        action: TKModuleMessageAction.NSNotify,
        param: { userId: userInfo.id, preferences: userInfo.preferences }
      });

      // 通知订单模块加载历史订单
      TKAppEngine.shareInstance().sendModuleMessage({
        funcNo: "USER_LOGIN",
        sourceModule: "UserModule",
        targetModule: "OrderModule",
        action: TKModuleMessageAction.NSNotify,
        param: { userId: userInfo.id }
      });
    };
  }

  private async checkUserLoginStatus() {
    const engine = TKAppEngine.shareInstance();

    // 调用用户认证插件
    const authResult = engine.callPlugin({
      funcNo: "USER_AUTH_CHECK",
      param: {}
    });

    if (authResult.errorNo === 0 && authResult.data.isLoggedIn) {
      console.log('用户已登录，自动恢复会话');
      this.userModule.restoreUserSession(authResult.data.userInfo);
    } else {
      console.log('用户未登录，显示登录页面');
    }
  }
}
```

### 场景2：网络状态变化的响应处理

**业务需求**：当网络状态发生变化时，应用需要自动切换在线/离线模式。

```typescript
// 网络状态管理器
class NetworkStateManager {
  private engine = TKAppEngine.shareInstance();
  private isOnline = true;
  private offlineQueue: any[] = [];

  constructor() {
    this.setupNetworkMonitoring();
  }

  private setupNetworkMonitoring() {
    // 注册网络状态变化监听
    TKNotificationCenter.addObserver(
      this,
      'onNetworkStateChanged',
      'NETWORK_STATE_CHANGED'
    );
  }

  onNetworkStateChanged(notification: TKNotification) {
    const isConnected = notification.userInfo?.isConnected;

    if (isConnected && !this.isOnline) {
      // 网络恢复
      console.log('网络已恢复，切换到在线模式');
      this.switchToOnlineMode();
    } else if (!isConnected && this.isOnline) {
      // 网络断开
      console.log('网络已断开，切换到离线模式');
      this.switchToOfflineMode();
    }
  }

  private switchToOnlineMode() {
    this.isOnline = true;

    // 通知所有模块网络已恢复
    this.engine.sendModuleMessage({
      funcNo: "NETWORK_ONLINE",
      sourceModule: "NetworkStateManager",
      targetModule: "ALL_MODULES",
      action: TKModuleMessageAction.NSNotify,
      param: { timestamp: Date.now() }
    });

    // 处理离线期间的队列
    this.processOfflineQueue();
  }

  private switchToOfflineMode() {
    this.isOnline = false;

    // 通知所有模块网络已断开
    this.engine.sendModuleMessage({
      funcNo: "NETWORK_OFFLINE",
      sourceModule: "NetworkStateManager",
      targetModule: "ALL_MODULES",
      action: TKModuleMessageAction.NSNotify,
      param: { timestamp: Date.now() }
    });
  }

  private processOfflineQueue() {
    console.log(`处理离线队列，共${this.offlineQueue.length}个请求`);

    this.offlineQueue.forEach(async (request) => {
      try {
        await this.retryRequest(request);
      } catch (error) {
        console.error('离线请求重试失败:', error);
      }
    });

    this.offlineQueue = [];
  }

  // 网络请求的智能重试
  async makeNetworkRequest(request: any): Promise<any> {
    if (this.isOnline) {
      try {
        return await this.executeRequest(request);
      } catch (error) {
        if (this.isNetworkError(error)) {
          // 网络错误，加入离线队列
          this.offlineQueue.push(request);
          throw new Error('网络不可用，请求已加入离线队列');
        }
        throw error;
      }
    } else {
      // 离线状态，直接加入队列
      this.offlineQueue.push(request);
      throw new Error('当前离线，请求已加入队列');
    }
  }
}
```

### 场景3：插件系统的高级应用

**业务需求**：实现一个文件管理系统，支持上传、下载、预览等功能。

```typescript
// 文件管理服务
class FileManagerService {
  private engine = TKAppEngine.shareInstance();

  // 文件上传的完整流程
  async uploadFile(filePath: string, options: UploadOptions = {}): Promise<UploadResult> {
    try {
      // 1. 文件预检查
      const checkResult = await this.preCheckFile(filePath);
      if (!checkResult.isValid) {
        throw new Error(`文件检查失败: ${checkResult.reason}`);
      }

      // 2. 获取上传凭证
      const credentialResult = this.engine.callPlugin({
        funcNo: "FILE_UPLOAD_CREDENTIAL",
        param: {
          fileName: checkResult.fileName,
          fileSize: checkResult.fileSize,
          fileType: checkResult.fileType
        }
      });

      if (credentialResult.errorNo !== 0) {
        throw new Error(`获取上传凭证失败: ${credentialResult.errorInfo}`);
      }

      // 3. 执行文件上传
      const uploadResult = this.engine.callPlugin({
        funcNo: "FILE_UPLOAD",
        param: {
          filePath,
          uploadUrl: credentialResult.data.uploadUrl,
          token: credentialResult.data.token,
          onProgress: options.onProgress
        }
      });

      if (uploadResult.errorNo === 0) {
        // 4. 上传成功后的处理
        await this.onUploadSuccess(uploadResult.data);
        return uploadResult.data;
      } else {
        throw new Error(`文件上传失败: ${uploadResult.errorInfo}`);
      }

    } catch (error) {
      console.error('文件上传过程中发生错误:', error);

      // 上传失败的处理
      await this.onUploadFailure(filePath, error);
      throw error;
    }
  }

  private async preCheckFile(filePath: string): Promise<FileCheckResult> {
    // 调用文件检查插件
    const result = this.engine.callPlugin({
      funcNo: "FILE_CHECK",
      param: { filePath }
    });

    if (result.errorNo === 0) {
      return {
        isValid: true,
        fileName: result.data.fileName,
        fileSize: result.data.fileSize,
        fileType: result.data.fileType
      };
    } else {
      return {
        isValid: false,
        reason: result.errorInfo
      };
    }
  }

  private async onUploadSuccess(uploadData: any) {
    // 通知其他模块文件上传成功
    this.engine.sendModuleMessage({
      funcNo: "FILE_UPLOADED",
      sourceModule: "FileManagerService",
      targetModule: "ALL_MODULES",
      action: TKModuleMessageAction.NSNotify,
      param: {
        fileId: uploadData.fileId,
        fileName: uploadData.fileName,
        fileUrl: uploadData.url,
        uploadTime: Date.now()
      }
    });
  }

  private async onUploadFailure(filePath: string, error: Error) {
    // 记录上传失败的文件，用于后续重试
    const failedUpload = {
      filePath,
      error: error.message,
      timestamp: Date.now(),
      retryCount: 0
    };

    // 保存到本地存储，应用重启后可以继续重试
    await this.saveFailedUpload(failedUpload);
  }

  // 批量文件操作
  async batchFileOperation(files: string[], operation: string): Promise<BatchResult> {
    const results: FileOperationResult[] = [];
    const concurrency = 3; // 并发数限制

    // 分批处理文件
    for (let i = 0; i < files.length; i += concurrency) {
      const batch = files.slice(i, i + concurrency);

      const batchPromises = batch.map(async (filePath) => {
        try {
          let result;
          switch (operation) {
            case 'upload':
              result = await this.uploadFile(filePath);
              break;
            case 'delete':
              result = await this.deleteFile(filePath);
              break;
            case 'compress':
              result = await this.compressFile(filePath);
              break;
            default:
              throw new Error(`不支持的操作: ${operation}`);
          }

          return { filePath, success: true, result };
        } catch (error) {
          return { filePath, success: false, error: error.message };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 批次间的延迟，避免过度占用资源
      if (i + concurrency < files.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return {
      total: files.length,
      success: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }
}
```

## 🎯 最佳实践与问题解决方案

### 实际开发中的常见问题与解决方案

#### 问题1：框架启动缓慢，影响用户体验

**问题现象**：

```typescript
// ❌ 问题代码：启动耗时过长
TKAppEngine.shareInstance().start({
  context: this.context,
  finishCallBack: () => {
    // 用户等待了5秒才看到界面
    console.log('启动完成，但用户已经不耐烦了');
  }
});
```

**解决方案**：

```typescript
// ✅ 解决方案：分阶段加载，优先显示界面
class OptimizedStartup {
  async initApp() {
    // 第一阶段：快速启动核心功能，立即显示界面
    await this.quickStart();

    // 第二阶段：后台加载非关键功能
    this.backgroundInit();
  }

  private async quickStart() {
    return new Promise<void>((resolve) => {
      TKAppEngine.shareInstance().start({
        context: this.context,
        finishCallBack: () => {
          // 核心功能就绪，立即显示界面
          this.showMainInterface();
          resolve();
        }
      });
    });
  }

  private backgroundInit() {
    // 在后台异步加载非关键功能
    setTimeout(() => {
      this.loadUserPreferences();
      this.initAnalytics();
      this.preloadResources();
    }, 100);
  }

  private showMainInterface() {
    // 显示基础界面，让用户可以立即开始使用
    console.log('界面已显示，用户可以开始操作');
  }
}
```

#### 问题2：插件调用失败，缺乏有效的错误处理

**问题现象**：

```typescript
// ❌ 问题代码：插件调用失败导致应用崩溃
const result = TKAppEngine.shareInstance().callPlugin({
  funcNo: "USER_INFO",
  param: { userId: "123" }
});

// 直接使用结果，没有错误检查
const userName = result.data.name; // 可能导致崩溃
```

**解决方案**：

```typescript
// ✅ 解决方案：完善的错误处理和降级策略
class RobustPluginService {
  private engine = TKAppEngine.shareInstance();
  private cache = new Map<string, any>();

  async callPluginWithFallback<T>(
    funcNo: string,
    param: any,
    fallbackValue?: T
  ): Promise<T> {
    try {
      // 尝试调用插件
      const result = this.engine.callPlugin({ funcNo, param });

      if (result.errorNo === 0) {
        // 成功时缓存结果
        this.cache.set(`${funcNo}_${JSON.stringify(param)}`, result.data);
        return result.data as T;
      } else {
        console.warn(`插件调用失败: ${result.errorInfo}`);
        return this.handlePluginFailure(funcNo, param, fallbackValue);
      }
    } catch (error) {
      console.error(`插件调用异常: ${error.message}`);
      return this.handlePluginFailure(funcNo, param, fallbackValue);
    }
  }

  private handlePluginFailure<T>(
    funcNo: string,
    param: any,
    fallbackValue?: T
  ): T {
    // 1. 尝试从缓存获取
    const cacheKey = `${funcNo}_${JSON.stringify(param)}`;
    const cachedValue = this.cache.get(cacheKey);
    if (cachedValue) {
      console.log('使用缓存数据作为降级方案');
      return cachedValue as T;
    }

    // 2. 使用提供的降级值
    if (fallbackValue !== undefined) {
      console.log('使用降级值');
      return fallbackValue;
    }

    // 3. 使用默认值
    return this.getDefaultValue<T>(funcNo);
  }

  private getDefaultValue<T>(funcNo: string): T {
    const defaults = {
      'USER_INFO': { name: '未知用户', id: '' },
      'DEVICE_INFO': { model: '未知设备', os: 'HarmonyOS' },
      'NETWORK_STATUS': { isConnected: false, type: 'unknown' }
    };

    return (defaults[funcNo] || {}) as T;
  }

  // 使用示例
  async getUserInfo(userId: string): Promise<UserInfo> {
    return this.callPluginWithFallback<UserInfo>(
      'USER_INFO',
      { userId },
      { name: '游客用户', id: userId } // 降级值
    );
  }
}
```

#### 问题3：模块间通信混乱，消息处理不当

**问题现象**：

```typescript
// ❌ 问题代码：消息处理混乱，难以维护
class BadModuleA {
  onReceiveMessage(message: TKModuleMessage): void {
    // 所有消息都在一个方法中处理，难以维护
    if (message.funcNo === "USER_LOGIN") {
      // 处理用户登录
    } else if (message.funcNo === "ORDER_CREATED") {
      // 处理订单创建
    } else if (message.funcNo === "PAYMENT_SUCCESS") {
      // 处理支付成功
    }
    // ... 更多消息类型
  }
}
```

**解决方案**：

```typescript
// ✅ 解决方案：结构化的消息处理系统
class WellStructuredModule implements TKModuleDelegate {
  private engine = TKAppEngine.shareInstance();
  private messageHandlers = new Map<string, (param: any) => void>();

  constructor() {
    this.initMessageHandlers();
  }

  getModuleName(): string {
    return "WellStructuredModule";
  }

  private initMessageHandlers() {
    // 注册消息处理器
    this.messageHandlers.set("USER_LOGIN", this.handleUserLogin.bind(this));
    this.messageHandlers.set("ORDER_CREATED", this.handleOrderCreated.bind(this));
    this.messageHandlers.set("PAYMENT_SUCCESS", this.handlePaymentSuccess.bind(this));
  }

  onReceiveMessage(message: TKModuleMessage): void {
    const handler = this.messageHandlers.get(message.funcNo);

    if (handler) {
      try {
        handler(message.param);
      } catch (error) {
        console.error(`处理消息 ${message.funcNo} 时发生错误:`, error);
      }
    } else {
      console.warn(`未找到消息 ${message.funcNo} 的处理器`);
    }
  }

  private handleUserLogin(param: any) {
    console.log(`用户 ${param.userId} 已登录`);
    // 具体的用户登录处理逻辑
    this.loadUserData(param.userId);
  }

  private handleOrderCreated(param: any) {
    console.log(`订单 ${param.orderId} 已创建`);
    // 具体的订单创建处理逻辑
    this.updateOrderStatus(param.orderId);
  }

  private handlePaymentSuccess(param: any) {
    console.log(`支付 ${param.paymentId} 成功`);
    // 具体的支付成功处理逻辑
    this.processPayment(param.paymentId);
  }

  // 发送消息的标准化方法
  sendMessage(funcNo: string, targetModule: string, param: any) {
    this.engine.sendModuleMessage({
      funcNo,
      sourceModule: this.getModuleName(),
      targetModule,
      action: TKModuleMessageAction.NSNotify,
      param
    });
  }

  // 广播消息给所有模块
  broadcastMessage(funcNo: string, param: any) {
    this.sendMessage(funcNo, "ALL_MODULES", param);
  }
}
```

#### 问题4：内存泄漏和资源未正确释放

**问题现象**：

```typescript
// ❌ 问题代码：资源未正确释放
class LeakyService {
  private timer: number;
  private listeners: any[] = [];

  start() {
    // 启动定时器但从不清理
    this.timer = setInterval(() => {
      this.doSomething();
    }, 1000);

    // 添加监听器但从不移除
    TKNotificationCenter.addObserver(this, 'onEvent', 'SOME_EVENT');
  }
}
```

**解决方案**：

```typescript
// ✅ 解决方案：完善的资源管理
class ResourceManagedService {
  private timer?: number;
  private listeners: Array<{observer: any, selector: string, name: string}> = [];
  private isDestroyed = false;

  start() {
    if (this.isDestroyed) {
      console.warn('服务已销毁，无法启动');
      return;
    }

    // 启动定时器
    this.timer = setInterval(() => {
      if (!this.isDestroyed) {
        this.doSomething();
      }
    }, 1000);

    // 添加监听器并记录
    this.addObserver(this, 'onNetworkChanged', 'NETWORK_CHANGED');
    this.addObserver(this, 'onUserChanged', 'USER_CHANGED');
  }

  private addObserver(observer: any, selector: string, name: string) {
    TKNotificationCenter.addObserver(observer, selector, name);
    this.listeners.push({ observer, selector, name });
  }

  destroy() {
    if (this.isDestroyed) {
      return;
    }

    this.isDestroyed = true;

    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = undefined;
    }

    // 移除所有监听器
    this.listeners.forEach(({ observer, selector, name }) => {
      TKNotificationCenter.removeObserver(observer, name);
    });
    this.listeners = [];

    console.log('资源已完全清理');
  }

  onNetworkChanged(notification: TKNotification) {
    if (this.isDestroyed) return;
    // 处理网络变化
  }

  onUserChanged(notification: TKNotification) {
    if (this.isDestroyed) return;
    // 处理用户变化
  }
}

// 在应用退出时确保资源清理
export class ResourceAwareApp extends TKUIAbility {
  private services: ResourceManagedService[] = [];

  onDestroy(): void {
    // 清理所有服务
    this.services.forEach(service => service.destroy());
    this.services = [];

    // 停止框架引擎
    TKAppEngine.shareInstance().stop();
  }
}
```

### 性能优化最佳实践

#### 1. 启动性能优化

```typescript
class PerformanceOptimizedApp {
  private criticalServices: string[] = ['UserService', 'SecurityService'];
  private nonCriticalServices: string[] = ['AnalyticsService', 'AdService'];

  async optimizedStartup() {
    const startTime = Date.now();

    // 并行启动关键服务
    const criticalPromises = this.criticalServices.map(service =>
      this.startService(service)
    );

    await Promise.all(criticalPromises);
    console.log(`关键服务启动完成: ${Date.now() - startTime}ms`);

    // 延迟启动非关键服务
    setTimeout(() => {
      this.nonCriticalServices.forEach(service => {
        this.startService(service).catch(error => {
          console.warn(`非关键服务 ${service} 启动失败:`, error);
        });
      });
    }, 2000);
  }
}
```

#### 2. 内存使用优化

```typescript
class MemoryOptimizedService {
  private cache = new Map<string, any>();
  private maxCacheSize = 100;

  cacheData(key: string, data: any) {
    // 实现LRU缓存
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, data);
  }

  getCachedData(key: string): any {
    const data = this.cache.get(key);
    if (data) {
      // 更新访问顺序
      this.cache.delete(key);
      this.cache.set(key, data);
    }
    return data;
  }
}
```

## 🏗️ 鸿蒙特有技术点

### Stage模型适配

TKAppEngine完全适配鸿蒙Stage模型：

```typescript
// Stage模型特性
export interface TKAppEngineStartOption {
  context?: common.UIAbilityContext;  // Stage模型专用上下文
  hspModuleName?: string;             // 动态共享包模块名
  finishCallBack?: TKAppEngineStartFinishCallBack;
}
```

### HSP模块化支持

```typescript
// HSP模块化支持
TKContextHelper.setCurrentContext(context, hspModuleName) {
  if (TKStringHelper.isNotBlank(hspModuleName)) {
    TKContextHelper.hspModuleName = hspModuleName;
    this.loadHspResources(hspModuleName);
  }
}
```

### 系统能力集成

- **权限管理**：集成鸿蒙权限系统
- **系统事件监听**：监听网络、窗口、键盘等系统事件
- **生命周期管理**：完整适配Ability生命周期

## 📚 相关资源

### 官方文档

- [HarmonyOS应用开发指南 - UIAbility生命周期](https://developer.harmonyos.com/cn/docs/documentation/doc-guides-V3/uiability-lifecycle-0000001427902208-V3)
- [TypeScript官方文档 - 类和接口](https://www.typescriptlang.org/docs/handbook/classes.html)

### 设计模式参考

- 《设计模式：可复用面向对象软件的基础》- 单例模式
- 《企业应用架构模式》- 服务层模式

### 相关文档

- `核心架构-TKModuleEngine-解释文档.md` - 模块通信引擎详解
- `核心架构-TKUIAbility-解释文档.md` - 应用能力基类说明
- `基础服务-TKPluginInvokeCenter-解释文档.md` - 插件调用中心介绍

---

## 📋 文档总结

TKAppEngine作为HMThinkBaseHar框架的核心引擎，采用单例模式设计，统一管理框架的生命周期和各子系统的协调。通过7阶段启动策略、插件系统和模块通信机制，为鸿蒙应用提供了完整的基础架构支持。

**核心特性**：

- ⭐ 单例模式确保全局唯一性
- ⭐ 7阶段启动策略优化性能
- ⭐ 完整的插件系统支持
- ⭐ 模块间通信机制
- ⭐ 鸿蒙Stage模型完全适配

理解和掌握TKAppEngine是使用整个框架的基础，建议按照"整体到局部"的学习路径，从架构理解开始，逐步深入到具体的API使用和扩展开发。

## 📚 相关资源

### 官方文档

- HarmonyOS应用开发指南 - UIAbility生命周期
- TypeScript官方文档 - 类和接口

### 参考资料

- 《设计模式：可复用面向对象软件的基础》- 单例模式
- 《企业应用架构模式》- 服务层模式

### 相关文件

- `核心架构-TKModuleEngine-解释文档.md` - 模块通信引擎
- `核心架构-TKUIAbility-解释文档.md` - 应用能力基类
- `基础服务-TKPluginInvokeCenter-解释文档.md` - 插件调用中心
