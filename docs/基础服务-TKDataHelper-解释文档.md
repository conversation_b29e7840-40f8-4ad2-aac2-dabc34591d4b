# TKDataHelper

> **文件路径**: `src/main/ets/util/data/TKDataHelper.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 数据类型转换、编码解码、JSON处理、类型检查

## 📋 文件概述

### 功能定位
TKDataHelper是HMThinkBaseHar框架的**数据处理工具类**，提供了完整的数据转换和处理功能。它采用命名空间设计，集成了类型转换、编码解码、数据验证等功能，支持字符串与字节数组转换、JSON处理、类型检查等操作，为应用提供统一的数据处理服务。

### 核心职责
- **数据类型转换**：提供各种数据类型之间的安全转换
- **编码解码处理**：支持字符串与字节数组的相互转换
- **JSON数据处理**：提供JSON序列化和反序列化功能
- **类型检查验证**：提供数据类型的检查和验证方法
- **数据格式化**：支持数据的格式化和标准化处理

### 设计模式
- **命名空间模式**：使用namespace封装数据处理功能
- **工具类模式**：提供静态方法的工具类设计
- **适配器模式**：封装底层数据转换API，提供统一接口
- **策略模式**：支持不同的数据转换和处理策略

## 🔧 核心功能

### 数据类型转换方法

#### 方法1：`getString(value: any, defaultValue?: string)`
- **功能**: 将任意类型转换为字符串
- **参数**: 
  - value - 要转换的值
  - defaultValue - 默认值（可选）
- **返回值**: string - 转换后的字符串
- **使用场景**: 安全的字符串转换

#### 方法2：`getNumber(value: any, defaultValue?: number)`
- **功能**: 将任意类型转换为数字
- **参数**: 
  - value - 要转换的值
  - defaultValue - 默认值（可选）
- **返回值**: number - 转换后的数字
- **使用场景**: 安全的数字转换

#### 方法3：`getBoolean(value: any, defaultValue?: boolean)`
- **功能**: 将任意类型转换为布尔值
- **参数**: 
  - value - 要转换的值
  - defaultValue - 默认值（可选）
- **返回值**: boolean - 转换后的布尔值
- **使用场景**: 安全的布尔值转换

#### 方法4：`getArray(value: any, defaultValue?: Array<any>)`
- **功能**: 将任意类型转换为数组
- **参数**: 
  - value - 要转换的值
  - defaultValue - 默认值（可选）
- **返回值**: Array<any> - 转换后的数组
- **使用场景**: 安全的数组转换

### 编码解码方法

#### 方法1：`stringToUint8Array(str: string)`
- **功能**: 将字符串转换为字节数组
- **参数**: str - 要转换的字符串
- **返回值**: Uint8Array - 字节数组
- **使用场景**: 文件写入、网络传输等需要字节数据的场景

#### 方法2：`uint8ArrayToString(uint8Array: Uint8Array)`
- **功能**: 将字节数组转换为字符串
- **参数**: uint8Array - 字节数组
- **返回值**: string - 转换后的字符串
- **使用场景**: 文件读取、网络接收等字节数据转换

#### 方法3：`base64Encode(str: string)`
- **功能**: Base64编码
- **参数**: str - 要编码的字符串
- **返回值**: string - Base64编码后的字符串
- **使用场景**: 数据传输、存储等需要编码的场景

#### 方法4：`base64Decode(base64Str: string)`
- **功能**: Base64解码
- **参数**: base64Str - Base64编码的字符串
- **返回值**: string - 解码后的字符串
- **使用场景**: Base64数据的解码处理

### JSON处理方法

#### 方法1：`parseJSON<T>(jsonStr: string, defaultValue?: T)`
- **功能**: 安全的JSON解析
- **参数**: 
  - jsonStr - JSON字符串
  - defaultValue - 解析失败时的默认值
- **返回值**: T - 解析后的对象
- **使用场景**: 安全的JSON数据解析

#### 方法2：`stringifyJSON(obj: any, space?: number)`
- **功能**: 对象转JSON字符串
- **参数**: 
  - obj - 要转换的对象
  - space - 格式化空格数（可选）
- **返回值**: string - JSON字符串
- **使用场景**: 对象序列化为JSON

### 类型检查方法

#### 方法1：`isString(value: any)`
- **功能**: 检查是否为字符串类型
- **参数**: value - 要检查的值
- **返回值**: boolean - 是否为字符串
- **使用场景**: 类型验证

#### 方法2：`isNumber(value: any)`
- **功能**: 检查是否为数字类型
- **参数**: value - 要检查的值
- **返回值**: boolean - 是否为数字
- **使用场景**: 数字类型验证

#### 方法3：`isBoolean(value: any)`
- **功能**: 检查是否为布尔类型
- **参数**: value - 要检查的值
- **返回值**: boolean - 是否为布尔值
- **使用场景**: 布尔类型验证

#### 方法4：`isArray(value: any)`
- **功能**: 检查是否为数组类型
- **参数**: value - 要检查的值
- **返回值**: boolean - 是否为数组
- **使用场景**: 数组类型验证

#### 方法5：`isObject(value: any)`
- **功能**: 检查是否为对象类型
- **参数**: value - 要检查的值
- **返回值**: boolean - 是否为对象
- **使用场景**: 对象类型验证

## 💡 技术要点

### 核心算法/逻辑

#### 安全类型转换实现
```typescript
export function getString(value: any, defaultValue: string = ''): string {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  
  if (typeof value === 'string') {
    return value;
  }
  
  try {
    return String(value);
  } catch (error) {
    return defaultValue;
  }
}

export function getNumber(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  
  if (typeof value === 'number' && !isNaN(value)) {
    return value;
  }
  
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
  }
  
  return defaultValue;
}

export function getBoolean(value: any, defaultValue: boolean = false): boolean {
  if (value === null || value === undefined) {
    return defaultValue;
  }
  
  if (typeof value === 'boolean') {
    return value;
  }
  
  if (typeof value === 'string') {
    const lowerValue = value.toLowerCase();
    if (lowerValue === 'true' || lowerValue === '1') {
      return true;
    }
    if (lowerValue === 'false' || lowerValue === '0') {
      return false;
    }
  }
  
  if (typeof value === 'number') {
    return value !== 0;
  }
  
  return defaultValue;
}
```

#### 编码解码实现
```typescript
export function stringToUint8Array(str: string): Uint8Array {
  if (!str) {
    return new Uint8Array(0);
  }
  
  try {
    const encoder = new TextEncoder();
    return encoder.encode(str);
  } catch (error) {
    // 降级处理
    const bytes = new Uint8Array(str.length);
    for (let i = 0; i < str.length; i++) {
      bytes[i] = str.charCodeAt(i) & 0xFF;
    }
    return bytes;
  }
}

export function uint8ArrayToString(uint8Array: Uint8Array): string {
  if (!uint8Array || uint8Array.length === 0) {
    return '';
  }
  
  try {
    const decoder = new TextDecoder('utf-8');
    return decoder.decode(uint8Array);
  } catch (error) {
    // 降级处理
    let result = '';
    for (let i = 0; i < uint8Array.length; i++) {
      result += String.fromCharCode(uint8Array[i]);
    }
    return result;
  }
}
```

#### JSON处理实现
```typescript
export function parseJSON<T>(jsonStr: string, defaultValue?: T): T | undefined {
  if (!jsonStr || typeof jsonStr !== 'string') {
    return defaultValue;
  }
  
  try {
    return JSON.parse(jsonStr) as T;
  } catch (error) {
    console.error('JSON解析失败:', error);
    return defaultValue;
  }
}

export function stringifyJSON(obj: any, space?: number): string {
  if (obj === null || obj === undefined) {
    return '';
  }
  
  try {
    return JSON.stringify(obj, null, space);
  } catch (error) {
    console.error('JSON序列化失败:', error);
    return '';
  }
}
```

#### 类型检查实现
```typescript
export function isString(value: any): boolean {
  return typeof value === 'string';
}

export function isNumber(value: any): boolean {
  return typeof value === 'number' && !isNaN(value);
}

export function isBoolean(value: any): boolean {
  return typeof value === 'boolean';
}

export function isArray(value: any): boolean {
  return Array.isArray(value);
}

export function isObject(value: any): boolean {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}
```

### 实现机制分析

#### 安全转换策略
- **空值处理**：统一处理null和undefined情况
- **类型检查**：转换前进行类型检查，避免异常
- **默认值机制**：提供默认值，确保函数总是返回有效值
- **异常捕获**：捕获转换过程中的异常，提供降级处理

#### 编码兼容性
- **标准API优先**：优先使用TextEncoder/TextDecoder标准API
- **降级处理**：在标准API不可用时提供降级实现
- **字符编码**：统一使用UTF-8编码处理字符串
- **错误处理**：编码失败时返回空数组或空字符串

#### JSON处理安全
- **异常捕获**：捕获JSON解析和序列化异常
- **类型泛型**：支持泛型类型，提供类型安全
- **默认值支持**：解析失败时返回默认值
- **循环引用处理**：JSON.stringify自动处理循环引用

#### 类型检查精确性
- **严格类型检查**：使用typeof进行精确的类型检查
- **特殊值处理**：正确处理NaN、null、undefined等特殊值
- **数组区分**：正确区分数组和对象类型
- **一致性保证**：所有类型检查方法保持一致的行为

### 性能考虑
- **早期返回**：空值检查后早期返回，避免不必要的处理
- **原生API使用**：充分利用JavaScript原生API的性能优势
- **缓存机制**：对频繁使用的转换结果进行缓存
- **内存管理**：避免创建不必要的临时对象

### 错误处理
- **异常隔离**：单个转换失败不影响其他操作
- **优雅降级**：提供降级处理方案，确保功能可用
- **错误日志**：记录转换失败的详细信息
- **默认值保护**：确保函数总是返回有效的默认值

### 最佳实践
- **类型安全**：使用泛型和类型检查确保类型安全
- **防御性编程**：对所有输入进行验证和处理
- **一致性**：保持API接口的一致性和可预测性
- **文档完整**：提供完整的使用文档和示例

## 🔗 依赖关系

### 依赖的模块
- 无外部依赖，基于JavaScript原生API实现

### 被依赖情况
- `TKFileHelper` - 文件工具使用数据工具进行编码转换
- `TKCacheManager` - 缓存管理器使用数据工具进行序列化
- `TKHttpDao` - HTTP数据访问使用数据工具处理请求和响应数据
- `各种配置管理器` - 使用数据工具进行JSON处理

### 关联文件
- `TKStringHelper.ets` - 字符串工具类，提供字符串处理功能
- `TKObjectHelper.ets` - 对象工具类，提供对象处理功能
- `TKValidateHelper.ets` - 验证工具类，使用数据工具进行类型验证

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKDataHelper是框架数据处理的基础工具：
1. **基础设施**：为数据转换和处理提供基础功能
2. **使用频率高**：在需要数据转换的场景中广泛使用
3. **类型安全**：提供类型安全的数据转换方法
4. **稳定可靠**：完善的错误处理和降级机制

理解TKDataHelper有助于掌握框架的数据处理规范，是处理数据转换的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解数据类型**：掌握JavaScript的数据类型和转换规则
2. **分析转换机制**：理解安全类型转换的实现原理
3. **学习编码解码**：掌握字符串与字节数组的转换
4. **实践数据处理**：在实际开发中使用数据工具

### 前置学习
- JavaScript数据类型基础
- 字符编码和解码概念
- JSON格式和处理
- 类型检查和验证

### 后续学习
- `TKObjectHelper.ets` - 学习对象处理工具
- `TKValidateHelper.ets` - 了解数据验证功能
- `TKStringHelper.ets` - 掌握字符串处理工具

### 实践建议
1. **类型转换练习**：练习各种数据类型的安全转换
2. **编码解码实验**：测试字符串与字节数组的转换
3. **JSON处理测试**：验证JSON序列化和反序列化功能
4. **类型检查验证**：测试各种类型检查方法

### 常见问题
1. **问题**: 如何安全地转换数据类型？
   **解答**: 使用TKDataHelper提供的get系列方法，它们会进行类型检查并提供默认值，避免转换异常。

2. **问题**: 字符串和字节数组转换时需要注意什么？
   **解答**: 注意字符编码问题，框架统一使用UTF-8编码，确保中文等多字节字符的正确处理。

## 📝 代码示例

### 基础使用
```typescript
import { TKDataHelper } from '@thinkive/tk-harmony-base';

// 数据类型转换示例
function dataTypeConversion() {
  // 字符串转换
  console.log('字符串转换:');
  console.log(TKDataHelper.getString(123)); // "123"
  console.log(TKDataHelper.getString(null, '默认值')); // "默认值"
  console.log(TKDataHelper.getString(undefined, '空值')); // "空值"

  // 数字转换
  console.log('\n数字转换:');
  console.log(TKDataHelper.getNumber('123')); // 123
  console.log(TKDataHelper.getNumber('123.45')); // 123.45
  console.log(TKDataHelper.getNumber('abc', 0)); // 0
  console.log(TKDataHelper.getNumber(null, -1)); // -1

  // 布尔值转换
  console.log('\n布尔值转换:');
  console.log(TKDataHelper.getBoolean('true')); // true
  console.log(TKDataHelper.getBoolean('1')); // true
  console.log(TKDataHelper.getBoolean('false')); // false
  console.log(TKDataHelper.getBoolean('0')); // false
  console.log(TKDataHelper.getBoolean(1)); // true
  console.log(TKDataHelper.getBoolean(0)); // false

  // 数组转换
  console.log('\n数组转换:');
  console.log(TKDataHelper.getArray([1, 2, 3])); // [1, 2, 3]
  console.log(TKDataHelper.getArray('not array', [])); // []
  console.log(TKDataHelper.getArray(null, ['默认'])); // ['默认']
}

// 编码解码示例
function encodingDecoding() {
  const testString = "Hello, 世界! 🌍";

  // 字符串转字节数组
  const uint8Array = TKDataHelper.stringToUint8Array(testString);
  console.log('字符串转字节数组:', uint8Array);
  console.log('字节数组长度:', uint8Array.length);

  // 字节数组转字符串
  const decodedString = TKDataHelper.uint8ArrayToString(uint8Array);
  console.log('字节数组转字符串:', decodedString);
  console.log('转换是否正确:', testString === decodedString);

  // Base64编码解码
  const base64Encoded = TKDataHelper.base64Encode(testString);
  console.log('Base64编码:', base64Encoded);

  const base64Decoded = TKDataHelper.base64Decode(base64Encoded);
  console.log('Base64解码:', base64Decoded);
  console.log('Base64转换是否正确:', testString === base64Decoded);
}

// JSON处理示例
function jsonProcessing() {
  const testObject = {
    name: '张三',
    age: 30,
    hobbies: ['读书', '游泳', '编程'],
    address: {
      city: '北京',
      district: '朝阳区'
    }
  };

  // 对象转JSON字符串
  const jsonString = TKDataHelper.stringifyJSON(testObject, 2);
  console.log('对象转JSON:', jsonString);

  // JSON字符串转对象
  const parsedObject = TKDataHelper.parseJSON(jsonString, {});
  console.log('JSON转对象:', parsedObject);

  // 错误的JSON处理
  const invalidJson = '{"name": "张三", "age":}'; // 无效JSON
  const defaultObject = { error: true };
  const result = TKDataHelper.parseJSON(invalidJson, defaultObject);
  console.log('无效JSON解析结果:', result);
}

// 类型检查示例
function typeChecking() {
  const testValues = [
    'hello',
    123,
    true,
    [1, 2, 3],
    { name: 'test' },
    null,
    undefined,
    NaN
  ];

  testValues.forEach((value, index) => {
    console.log(`值 ${index}: ${value}`);
    console.log('  是字符串:', TKDataHelper.isString(value));
    console.log('  是数字:', TKDataHelper.isNumber(value));
    console.log('  是布尔值:', TKDataHelper.isBoolean(value));
    console.log('  是数组:', TKDataHelper.isArray(value));
    console.log('  是对象:', TKDataHelper.isObject(value));
    console.log('---');
  });
}

// 执行示例
dataTypeConversion();
encodingDecoding();
jsonProcessing();
typeChecking();
```

### 高级用法
```typescript
// 数据转换器
class DataConverter {
  // 安全的数据提取
  static extractValue<T>(
    data: any,
    path: string,
    defaultValue: T,
    converter?: (value: any) => T
  ): T {
    try {
      const keys = path.split('.');
      let current = data;

      for (const key of keys) {
        if (current === null || current === undefined) {
          return defaultValue;
        }
        current = current[key];
      }

      if (converter) {
        return converter(current);
      }

      return current !== undefined ? current : defaultValue;
    } catch (error) {
      console.error('数据提取失败:', error);
      return defaultValue;
    }
  }

  // 批量数据转换
  static batchConvert(
    data: Record<string, any>,
    converters: Record<string, (value: any) => any>
  ): Record<string, any> {
    const result: Record<string, any> = {};

    Object.keys(converters).forEach(key => {
      try {
        const converter = converters[key];
        result[key] = converter(data[key]);
      } catch (error) {
        console.error(`转换字段 ${key} 失败:`, error);
        result[key] = data[key];
      }
    });

    return result;
  }

  // 深度克隆对象
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    try {
      const jsonString = TKDataHelper.stringifyJSON(obj);
      return TKDataHelper.parseJSON<T>(jsonString, obj as T);
    } catch (error) {
      console.error('深度克隆失败:', error);
      return obj;
    }
  }

  // 数据类型标准化
  static normalizeData(data: any): {
    type: string,
    value: any,
    isValid: boolean
  } {
    if (TKDataHelper.isString(data)) {
      return { type: 'string', value: data, isValid: true };
    } else if (TKDataHelper.isNumber(data)) {
      return { type: 'number', value: data, isValid: !isNaN(data) };
    } else if (TKDataHelper.isBoolean(data)) {
      return { type: 'boolean', value: data, isValid: true };
    } else if (TKDataHelper.isArray(data)) {
      return { type: 'array', value: data, isValid: true };
    } else if (TKDataHelper.isObject(data)) {
      return { type: 'object', value: data, isValid: true };
    } else {
      return { type: 'unknown', value: data, isValid: false };
    }
  }
}

// 数据验证器
class DataValidator {
  // 验证数据结构
  static validateStructure(
    data: any,
    schema: Record<string, {
      type: string,
      required?: boolean,
      validator?: (value: any) => boolean
    }>
  ): {
    isValid: boolean,
    errors: string[]
  } {
    const errors: string[] = [];

    if (!TKDataHelper.isObject(data)) {
      return {
        isValid: false,
        errors: ['数据必须是对象类型']
      };
    }

    Object.keys(schema).forEach(key => {
      const fieldSchema = schema[key];
      const value = data[key];

      // 检查必填字段
      if (fieldSchema.required && (value === undefined || value === null)) {
        errors.push(`字段 ${key} 是必填的`);
        return;
      }

      // 跳过可选的空字段
      if (value === undefined || value === null) {
        return;
      }

      // 检查类型
      let typeValid = false;
      switch (fieldSchema.type) {
        case 'string':
          typeValid = TKDataHelper.isString(value);
          break;
        case 'number':
          typeValid = TKDataHelper.isNumber(value);
          break;
        case 'boolean':
          typeValid = TKDataHelper.isBoolean(value);
          break;
        case 'array':
          typeValid = TKDataHelper.isArray(value);
          break;
        case 'object':
          typeValid = TKDataHelper.isObject(value);
          break;
      }

      if (!typeValid) {
        errors.push(`字段 ${key} 类型错误，期望 ${fieldSchema.type}`);
        return;
      }

      // 自定义验证
      if (fieldSchema.validator && !fieldSchema.validator(value)) {
        errors.push(`字段 ${key} 验证失败`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  // 验证数组元素类型
  static validateArrayElements(
    array: any[],
    elementType: string
  ): {
    isValid: boolean,
    invalidIndices: number[]
  } {
    const invalidIndices: number[] = [];

    array.forEach((element, index) => {
      let isValidType = false;

      switch (elementType) {
        case 'string':
          isValidType = TKDataHelper.isString(element);
          break;
        case 'number':
          isValidType = TKDataHelper.isNumber(element);
          break;
        case 'boolean':
          isValidType = TKDataHelper.isBoolean(element);
          break;
        case 'object':
          isValidType = TKDataHelper.isObject(element);
          break;
        case 'array':
          isValidType = TKDataHelper.isArray(element);
          break;
      }

      if (!isValidType) {
        invalidIndices.push(index);
      }
    });

    return {
      isValid: invalidIndices.length === 0,
      invalidIndices: invalidIndices
    };
  }
}

// 数据格式化器
class DataFormatter {
  // 格式化API响应数据
  static formatApiResponse(response: any): {
    success: boolean,
    data: any,
    message: string,
    code: number
  } {
    return {
      success: TKDataHelper.getBoolean(response?.success, false),
      data: response?.data || null,
      message: TKDataHelper.getString(response?.message, ''),
      code: TKDataHelper.getNumber(response?.code, -1)
    };
  }

  // 格式化用户输入数据
  static formatUserInput(input: Record<string, any>): Record<string, any> {
    const formatted: Record<string, any> = {};

    Object.keys(input).forEach(key => {
      const value = input[key];

      if (TKDataHelper.isString(value)) {
        // 字符串去除首尾空格
        formatted[key] = value.trim();
      } else if (TKDataHelper.isNumber(value)) {
        // 数字保持原样
        formatted[key] = value;
      } else if (TKDataHelper.isBoolean(value)) {
        // 布尔值保持原样
        formatted[key] = value;
      } else if (TKDataHelper.isArray(value)) {
        // 数组递归格式化
        formatted[key] = value.map(item =>
          TKDataHelper.isObject(item) ? this.formatUserInput(item) : item
        );
      } else if (TKDataHelper.isObject(value)) {
        // 对象递归格式化
        formatted[key] = this.formatUserInput(value);
      } else {
        // 其他类型保持原样
        formatted[key] = value;
      }
    });

    return formatted;
  }

  // 格式化配置数据
  static formatConfigData(config: any): Record<string, any> {
    const defaultConfig = {
      theme: 'light',
      language: 'zh-CN',
      autoSave: true,
      timeout: 30000,
      retryCount: 3
    };

    return {
      theme: TKDataHelper.getString(config?.theme, defaultConfig.theme),
      language: TKDataHelper.getString(config?.language, defaultConfig.language),
      autoSave: TKDataHelper.getBoolean(config?.autoSave, defaultConfig.autoSave),
      timeout: TKDataHelper.getNumber(config?.timeout, defaultConfig.timeout),
      retryCount: TKDataHelper.getNumber(config?.retryCount, defaultConfig.retryCount)
    };
  }
}

// 使用示例
function demonstrateAdvancedDataUtils() {
  // 数据转换器示例
  console.log('=== 数据转换器 ===');
  const testData = {
    user: {
      profile: {
        name: '张三',
        age: '30'
      }
    },
    settings: {
      theme: 'dark'
    }
  };

  const userName = DataConverter.extractValue(
    testData,
    'user.profile.name',
    '未知用户'
  );
  console.log('提取用户名:', userName);

  const userAge = DataConverter.extractValue(
    testData,
    'user.profile.age',
    0,
    (value) => TKDataHelper.getNumber(value)
  );
  console.log('提取用户年龄:', userAge);

  // 批量转换
  const rawData = { name: '李四', age: '25', active: '1' };
  const converted = DataConverter.batchConvert(rawData, {
    name: (v) => TKDataHelper.getString(v),
    age: (v) => TKDataHelper.getNumber(v),
    active: (v) => TKDataHelper.getBoolean(v)
  });
  console.log('批量转换结果:', converted);

  // 数据验证器示例
  console.log('\n=== 数据验证器 ===');
  const userData = {
    name: '王五',
    age: 28,
    email: '<EMAIL>',
    hobbies: ['reading', 'swimming']
  };

  const userSchema = {
    name: { type: 'string', required: true },
    age: { type: 'number', required: true, validator: (v) => v >= 0 && v <= 150 },
    email: { type: 'string', required: false },
    hobbies: { type: 'array', required: false }
  };

  const validation = DataValidator.validateStructure(userData, userSchema);
  console.log('数据验证结果:', validation);

  // 数组元素验证
  const numberArray = [1, 2, '3', 4, 'invalid'];
  const arrayValidation = DataValidator.validateArrayElements(numberArray, 'number');
  console.log('数组验证结果:', arrayValidation);

  // 数据格式化器示例
  console.log('\n=== 数据格式化器 ===');
  const apiResponse = {
    success: 'true',
    data: { id: 1, name: 'test' },
    message: 'Success',
    code: '200'
  };

  const formattedResponse = DataFormatter.formatApiResponse(apiResponse);
  console.log('格式化API响应:', formattedResponse);

  const userInput = {
    name: '  赵六  ',
    age: '35',
    active: 'true',
    tags: ['  tag1  ', '  tag2  ']
  };

  const formattedInput = DataFormatter.formatUserInput(userInput);
  console.log('格式化用户输入:', formattedInput);
}

// 执行演示
demonstrateAdvancedDataUtils();
```

## 📚 相关资源

### 官方文档
- JavaScript数据类型参考
- TextEncoder/TextDecoder API文档

### 参考资料
- 《JavaScript权威指南》- 数据类型和转换
- 《你不知道的JavaScript》- 类型和语法

### 相关文件
- `TKStringHelper.ets` - 字符串处理工具
- `TKObjectHelper.ets` - 对象处理工具
- `TKValidateHelper.ets` - 数据验证工具
