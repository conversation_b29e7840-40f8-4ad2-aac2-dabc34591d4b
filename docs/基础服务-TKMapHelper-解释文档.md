# TKMapHelper

> **文件路径**: `src/main/ets/util/map/TKMapHelper.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: Map数据结构、键值对操作、类型转换、泛型编程

## 📋 文件概述

### 功能定位
TKMapHelper是HMThinkBaseHar框架的**Map操作工具类**，提供了完整的Map数据结构操作功能。它采用命名空间设计，集成了Map数据的读取、写入、合并、类型转换等功能，支持Map、HashMap、TreeMap以及普通对象的统一操作，为应用提供强大的键值对数据处理服务。

### 核心职责
- **类型安全的数据读取**：提供类型安全的Map数据读取方法
- **统一的数据操作接口**：支持Map和普通对象的统一操作
- **数据类型转换**：自动处理不同数据类型的转换和默认值
- **Map数据合并**：支持深度和浅度的Map数据合并
- **键值对管理**：提供键值对的增删改查操作

### 设计模式
- **命名空间模式**：使用namespace封装Map操作功能
- **适配器模式**：统一Map和普通对象的操作接口
- **策略模式**：支持不同的数据类型转换策略
- **模板方法模式**：定义数据操作的标准流程

## 🔧 核心功能

### 数据读取方法

#### 方法1：`getObject<T>(map, key, defaultValue?)`
- **功能**: 获取对象类型数据，支持泛型
- **参数**: 
  - map - Map对象或普通对象
  - key - 数据键名
  - defaultValue - 默认值（可选）
- **返回值**: T - 指定类型的数据
- **使用场景**: 获取复杂对象数据

#### 方法2：`getString(map, key, defaultValue?)`
- **功能**: 获取字符串类型数据
- **参数**: 
  - map - Map对象或普通对象
  - key - 数据键名
  - defaultValue - 默认值（默认空字符串）
- **返回值**: string - 字符串数据
- **使用场景**: 获取文本数据

#### 方法3：`getNumber(map, key, defaultValue?)`
- **功能**: 获取数字类型数据
- **参数**: 
  - map - Map对象或普通对象
  - key - 数据键名
  - defaultValue - 默认值（默认0）
- **返回值**: number - 数字数据
- **使用场景**: 获取数值数据

#### 方法4：`getBoolean(map, key, defaultValue?)`
- **功能**: 获取布尔类型数据
- **参数**: 
  - map - Map对象或普通对象
  - key - 数据键名
  - defaultValue - 默认值（可选）
- **返回值**: boolean - 布尔数据
- **特性**: 智能转换，"0"、""、"false"转为false
- **使用场景**: 获取开关状态数据

#### 方法5：`getJSON(map, key, defaultValue?)`
- **功能**: 获取JSON对象数据
- **参数**: 
  - map - Map对象或普通对象
  - key - 数据键名
  - defaultValue - 默认值（默认空对象）
- **返回值**: Record<string, Object> - JSON对象
- **特性**: 自动解析JSON字符串
- **使用场景**: 获取结构化数据

### 数据写入方法

#### 方法1：`setObject<T>(map, key, value)`
- **功能**: 设置对象类型数据
- **参数**: 
  - map - Map对象或普通对象
  - key - 数据键名
  - value - 要设置的值
- **特性**: 自动识别Map类型和普通对象
- **使用场景**: 统一的数据设置操作

#### 方法2：`deleteObject<T>(map, key)`
- **功能**: 删除指定键的数据
- **参数**: 
  - map - Map对象或普通对象
  - key - 要删除的键名
- **特性**: 对Map使用delete，对对象设置为undefined
- **使用场景**: 数据清理和删除

### 高级操作方法

#### 方法1：`merge<T, S>(target, source, isDeep?)`
- **功能**: Map对象合并，类似putAll方法
- **参数**: 
  - target - 目标Map或对象
  - source - 源Map或对象
  - isDeep - 是否深度合并（默认false）
- **返回值**: T - 合并后的目标对象
- **特性**: 支持深度合并和浅度合并
- **使用场景**: 配置合并、数据整合

#### 方法2：`containKey<T>(map, key)`
- **功能**: 检查是否包含指定键
- **参数**: 
  - map - Map对象或普通对象
  - key - 要检查的键名
- **返回值**: boolean - 是否包含该键
- **使用场景**: 键存在性检查

#### 方法3：`keys<T>(map)`
- **功能**: 获取所有键名
- **参数**: map - Map对象或普通对象
- **返回值**: Array<string> - 所有键名数组
- **使用场景**: 遍历所有键

#### 方法4：`values<T>(map)`
- **功能**: 获取所有值
- **参数**: map - Map对象或普通对象
- **返回值**: Array<Object> - 所有值数组
- **使用场景**: 遍历所有值

## 💡 技术要点

### 核心算法/逻辑

#### 统一数据读取机制
```typescript
export function getObject<T>(map: Map<string, Object | undefined> | Record<string, Object | undefined>, key: string, defaultValue?: T): T {
  let result: Object | undefined = undefined;
  if (map && key) {
    // 自动识别Map类型和普通对象
    result = map instanceof Map ? map.get(key) : map[key];
  }
  if (result !== undefined && result !== null) {
    return result as T;
  } else {
    return defaultValue as T;
  }
}
```

#### 智能布尔值转换
```typescript
export function getBoolean(map: Map<string, Object | undefined> | Record<string, Object | undefined>, key: string, defaultValue?: boolean): boolean {
  let result: Object | undefined = getObject(map, key);
  if (result !== undefined && result !== null) {
    result = result.toString().toLowerCase();
    // 智能转换：0、空字符串、false字符串都转为false
    return (result != "0" && result != "" && result != "false");
  } else {
    return defaultValue as boolean;
  }
}
```

#### 深度合并算法
```typescript
export function merge<T, S>(target: T, source: S, isDeep: boolean = false): T {
  if (source instanceof Map) {
    source.forEach((value: Object, key: string) => {
      const sourceKey: string = key;
      const sourceValue: Object = TKObjectHelper.clone(value);
      const targetValue: Object | undefined = getObject(target as Map<string, Object> | Record<string, Object>, sourceKey);
      
      if (isDeep) {
        if (targetValue === undefined || sourceValue === undefined) {
          setObject(target, sourceKey, sourceValue ?? targetValue);
        } else if (Array.isArray(sourceValue)) {
          setObject(target, sourceKey, sourceValue ?? targetValue);
        } else if (sourceValue instanceof Map && targetValue instanceof Map) {
          // 递归深度合并Map对象
          setObject(target, sourceKey, merge(targetValue, sourceValue, isDeep));
        } else {
          setObject(target, sourceKey, sourceValue ?? targetValue);
        }
      } else {
        setObject(target, sourceKey, sourceValue ?? targetValue);
      }
    });
  } else {
    // 处理普通对象的合并
    Object.entries(source as Record<string, Object>).forEach((e, i) => {
      const sourceKey: string = e[0];
      const sourceValue: Object = TKObjectHelper.clone(e[1]);
      const targetValue: Object | undefined = getObject(target as Map<string, Object> | Record<string, Object>, sourceKey);
      
      if (isDeep) {
        if (targetValue === undefined || sourceValue === undefined) {
          setObject(target, sourceKey, sourceValue ?? targetValue);
        } else if (Array.isArray(sourceValue)) {
          setObject(target, sourceKey, sourceValue ?? targetValue);
        } else if (sourceValue instanceof Map && targetValue instanceof Map) {
          setObject(target, sourceKey, merge(targetValue, sourceValue, isDeep));
        } else {
          setObject(target, sourceKey, sourceValue ?? targetValue);
        }
      } else {
        setObject(target, sourceKey, sourceValue ?? targetValue);
      }
    });
  }
  return target;
}
```

#### 键存在性检查
```typescript
export function containKey<T>(map: T, key: string): boolean {
  let result: boolean = false;
  if ((map instanceof Map) || (map instanceof HashMap) || (map instanceof TreeMap)) {
    // 对于Map类型，遍历检查键
    map.forEach((v: Object, k: string) => {
      if (key == k) {
        result = true;
      }
    });
  } else {
    // 对于普通对象，使用Object.keys检查
    result = Object.keys(map as Object).includes(key);
  }
  return result;
}
```

### 实现机制分析

#### 类型统一处理
- **自动识别**：自动识别Map类型和普通对象，提供统一接口
- **类型安全**：通过泛型确保类型安全的数据操作
- **兼容性**：支持Map、HashMap、TreeMap等多种Map实现
- **一致性**：保持所有操作方法的接口一致性

#### 数据类型转换
- **智能转换**：自动进行数据类型转换，如字符串转数字
- **默认值处理**：提供合理的默认值，避免undefined和null
- **JSON解析**：自动识别和解析JSON字符串
- **布尔值转换**：智能的布尔值转换逻辑

#### 深度操作支持
- **深度合并**：支持嵌套Map和对象的深度合并
- **对象克隆**：使用TKObjectHelper进行对象深度克隆
- **递归处理**：正确处理嵌套结构的递归操作
- **数组处理**：特殊处理数组类型的合并逻辑

#### 性能优化
- **类型检查优化**：高效的类型检查和分支处理
- **内存管理**：合理使用对象克隆，避免内存泄漏
- **遍历优化**：使用高效的遍历方法处理大数据
- **缓存友好**：设计缓存友好的数据访问模式

### 性能考虑
- **类型检查**：使用instanceof进行高效的类型检查
- **遍历优化**：选择最优的遍历方法处理不同数据结构
- **内存使用**：合理控制对象克隆的使用，避免不必要的内存开销
- **缓存策略**：支持数据访问的缓存优化

### 错误处理
- **空值保护**：检查map和key参数的有效性
- **类型安全**：确保类型转换的安全性
- **默认值保护**：提供合理的默认值，避免程序异常
- **异常隔离**：单个操作失败不影响其他操作

### 最佳实践
- **类型指定**：使用泛型指定明确的数据类型
- **默认值设置**：为所有读取操作提供合理的默认值
- **深度合并谨慎使用**：深度合并会增加性能开销，按需使用
- **键名规范**：使用一致的键名规范，便于维护

## 🔗 依赖关系

### 依赖的模块
- `HashMap, TreeMap` - 鸿蒙集合类型，支持不同的Map实现
- `TKObjectHelper` - 对象工具，提供对象克隆功能
- `TKStringHelper` - 字符串工具，处理字符串操作

### 被依赖情况
- `TKObjectHelper` - 对象工具使用Map工具进行映射操作
- `TKRouterHelper` - 路由工具使用Map工具处理路由参数
- `TKSystemHelper` - 系统工具使用Map工具处理配置数据
- `TKFileHelper` - 文件工具使用Map工具处理文件信息
- `各种配置管理器` - 使用Map工具处理配置数据

### 关联文件
- `TKObjectHelper.ets` - 对象操作工具类
- `TKDataHelper.ets` - 数据转换工具类
- `TKStringHelper.ets` - 字符串处理工具类
- 各种使用Map数据结构的业务模块

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKMapHelper是框架Map数据操作的重要工具：
1. **基础设施**：为Map数据操作提供基础功能支持
2. **使用频率高**：在需要处理键值对数据的场景中广泛使用
3. **类型安全**：提供类型安全的Map数据操作方法
4. **统一接口**：统一Map和普通对象的操作接口

理解TKMapHelper有助于掌握Map数据结构的操作技巧，是处理键值对数据的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解Map数据结构**：掌握Map的基本概念和操作方法
2. **分析类型转换机制**：理解自动类型转换的实现原理
3. **学习合并算法**：掌握深度合并和浅度合并的区别
4. **实践Map操作**：在实际项目中使用Map工具

### 前置学习
- Map数据结构基础
- TypeScript泛型编程
- 键值对操作概念
- 数据类型转换

### 后续学习
- `TKObjectHelper.ets` - 学习对象操作工具
- `TKDataHelper.ets` - 了解数据转换工具
- `TKStringHelper.ets` - 掌握字符串处理工具

### 实践建议
1. **基础操作练习**：练习Map数据的读写操作
2. **类型转换测试**：测试不同数据类型的转换效果
3. **合并功能验证**：验证深度合并和浅度合并的差异
4. **性能测试**：测试大数据量的Map操作性能

### 常见问题
1. **问题**: 什么时候使用深度合并，什么时候使用浅度合并？
   **解答**: 当需要合并嵌套的Map或对象时使用深度合并，只需要合并第一层数据时使用浅度合并。深度合并会增加性能开销。

2. **问题**: 如何处理Map中不存在的键？
   **解答**: 所有get方法都支持默认值参数，当键不存在时会返回指定的默认值，避免undefined或null的问题。

## 📝 代码示例

### 基础使用
```typescript
import { TKMapHelper } from '@thinkive/tk-harmony-base';

// 基础Map操作示例
function basicMapOperations() {
  // 创建测试数据
  const userMap = new Map<string, Object>();
  userMap.set('name', '张三');
  userMap.set('age', '30');
  userMap.set('isActive', 'true');
  userMap.set('profile', JSON.stringify({ role: 'admin', department: 'IT' }));

  const userObject = {
    name: '李四',
    age: 25,
    isActive: false,
    profile: { role: 'user', department: 'Sales' }
  };

  console.log('=== Map数据读取 ===');

  // 类型安全的数据读取
  const name1 = TKMapHelper.getString(userMap, 'name', '未知用户');
  const age1 = TKMapHelper.getNumber(userMap, 'age', 0);
  const isActive1 = TKMapHelper.getBoolean(userMap, 'isActive', false);
  const profile1 = TKMapHelper.getJSON(userMap, 'profile', {});

  console.log('Map数据:');
  console.log('  姓名:', name1);
  console.log('  年龄:', age1);
  console.log('  活跃状态:', isActive1);
  console.log('  档案:', profile1);

  console.log('\n=== 普通对象数据读取 ===');

  // 普通对象的统一操作
  const name2 = TKMapHelper.getString(userObject, 'name', '未知用户');
  const age2 = TKMapHelper.getNumber(userObject, 'age', 0);
  const isActive2 = TKMapHelper.getBoolean(userObject, 'isActive', false);
  const profile2 = TKMapHelper.getObject(userObject, 'profile', {});

  console.log('对象数据:');
  console.log('  姓名:', name2);
  console.log('  年龄:', age2);
  console.log('  活跃状态:', isActive2);
  console.log('  档案:', profile2);

  console.log('\n=== 数据写入操作 ===');

  // 统一的数据写入
  TKMapHelper.setObject(userMap, 'email', '<EMAIL>');
  TKMapHelper.setObject(userObject, 'email', '<EMAIL>');

  console.log('Map邮箱:', TKMapHelper.getString(userMap, 'email'));
  console.log('对象邮箱:', TKMapHelper.getString(userObject, 'email'));

  console.log('\n=== 键值操作 ===');

  // 检查键是否存在
  console.log('Map包含name键:', TKMapHelper.containKey(userMap, 'name'));
  console.log('对象包含name键:', TKMapHelper.containKey(userObject, 'name'));

  // 获取所有键和值
  console.log('Map所有键:', TKMapHelper.keys(userMap));
  console.log('对象所有键:', TKMapHelper.keys(userObject));
  console.log('Map所有值:', TKMapHelper.values(userMap));
  console.log('对象所有值:', TKMapHelper.values(userObject));

  // 删除数据
  TKMapHelper.deleteObject(userMap, 'age');
  TKMapHelper.deleteObject(userObject, 'age');

  console.log('删除age后的Map键:', TKMapHelper.keys(userMap));
  console.log('删除age后的对象键:', TKMapHelper.keys(userObject));
}

// 数据合并示例
function mapMergeOperations() {
  console.log('\n=== Map数据合并 ===');

  // 创建目标Map
  const targetMap = new Map<string, Object>();
  targetMap.set('name', '原始用户');
  targetMap.set('age', 20);
  targetMap.set('settings', new Map([['theme', 'light'], ['language', 'zh']]));

  // 创建源Map
  const sourceMap = new Map<string, Object>();
  sourceMap.set('name', '更新用户');
  sourceMap.set('email', '<EMAIL>');
  sourceMap.set('settings', new Map([['theme', 'dark'], ['notifications', true]]));

  console.log('合并前目标Map:', Array.from(targetMap.entries()));
  console.log('源Map:', Array.from(sourceMap.entries()));

  // 浅度合并
  const shallowMerged = new Map(targetMap);
  TKMapHelper.merge(shallowMerged, sourceMap, false);
  console.log('浅度合并结果:', Array.from(shallowMerged.entries()));

  // 深度合并
  const deepMerged = new Map(targetMap);
  TKMapHelper.merge(deepMerged, sourceMap, true);
  console.log('深度合并结果:', Array.from(deepMerged.entries()));

  // 对象合并示例
  const targetObj = {
    name: '原始对象',
    age: 25,
    settings: { theme: 'light', language: 'zh' }
  };

  const sourceObj = {
    name: '更新对象',
    email: '<EMAIL>',
    settings: { theme: 'dark', notifications: true }
  };

  console.log('\n对象合并前:', targetObj);
  console.log('源对象:', sourceObj);

  // 深度合并对象
  const mergedObj = TKMapHelper.merge({ ...targetObj }, sourceObj, true);
  console.log('深度合并对象结果:', mergedObj);
}

// 执行示例
basicMapOperations();
mapMergeOperations();
```

### 高级用法
```typescript
// 配置管理器
class ConfigurationManager {
  private configMap: Map<string, Object> = new Map();
  private defaultConfig: Record<string, Object> = {
    theme: 'light',
    language: 'zh-CN',
    autoSave: true,
    timeout: 30000,
    maxRetries: 3
  };

  constructor() {
    this.loadDefaultConfig();
  }

  // 加载默认配置
  private loadDefaultConfig(): void {
    Object.entries(this.defaultConfig).forEach(([key, value]) => {
      TKMapHelper.setObject(this.configMap, key, value);
    });
    console.log('默认配置已加载');
  }

  // 获取配置值
  getConfig<T>(key: string, defaultValue?: T): T {
    return TKMapHelper.getObject<T>(this.configMap, key, defaultValue);
  }

  // 获取字符串配置
  getStringConfig(key: string, defaultValue: string = ''): string {
    return TKMapHelper.getString(this.configMap, key, defaultValue);
  }

  // 获取数字配置
  getNumberConfig(key: string, defaultValue: number = 0): number {
    return TKMapHelper.getNumber(this.configMap, key, defaultValue);
  }

  // 获取布尔配置
  getBooleanConfig(key: string, defaultValue: boolean = false): boolean {
    return TKMapHelper.getBoolean(this.configMap, key, defaultValue);
  }

  // 设置配置值
  setConfig<T>(key: string, value: T): void {
    TKMapHelper.setObject(this.configMap, key, value);
    console.log(`配置已更新: ${key} = ${value}`);
  }

  // 批量设置配置
  setBatchConfig(configs: Record<string, Object>): void {
    TKMapHelper.merge(this.configMap, configs, false);
    console.log('批量配置已更新');
  }

  // 重置配置
  resetConfig(key: string): void {
    if (TKMapHelper.containKey(this.defaultConfig, key)) {
      const defaultValue = TKMapHelper.getObject(this.defaultConfig, key);
      TKMapHelper.setObject(this.configMap, key, defaultValue);
      console.log(`配置已重置: ${key}`);
    } else {
      TKMapHelper.deleteObject(this.configMap, key);
      console.log(`配置已删除: ${key}`);
    }
  }

  // 获取所有配置
  getAllConfigs(): Record<string, Object> {
    const configs: Record<string, Object> = {};
    TKMapHelper.keys(this.configMap).forEach(key => {
      configs[key] = TKMapHelper.getObject(this.configMap, key);
    });
    return configs;
  }

  // 检查配置是否存在
  hasConfig(key: string): boolean {
    return TKMapHelper.containKey(this.configMap, key);
  }

  // 获取配置统计
  getConfigStats(): {
    totalConfigs: number,
    customConfigs: number,
    defaultConfigs: number
  } {
    const allKeys = TKMapHelper.keys(this.configMap);
    const defaultKeys = TKMapHelper.keys(this.defaultConfig);

    return {
      totalConfigs: allKeys.length,
      customConfigs: allKeys.filter(key => !defaultKeys.includes(key)).length,
      defaultConfigs: defaultKeys.length
    };
  }
}

// 数据转换器
class DataTransformer {
  // API响应数据转换
  static transformApiResponse(response: Record<string, Object>): {
    success: boolean,
    data: any,
    message: string,
    code: number
  } {
    return {
      success: TKMapHelper.getBoolean(response, 'success', false),
      data: TKMapHelper.getObject(response, 'data'),
      message: TKMapHelper.getString(response, 'message', ''),
      code: TKMapHelper.getNumber(response, 'code', 0)
    };
  }

  // 用户数据转换
  static transformUserData(userData: Map<string, Object> | Record<string, Object>): {
    id: number,
    name: string,
    email: string,
    isActive: boolean,
    profile: Record<string, Object>,
    settings: Record<string, Object>
  } {
    return {
      id: TKMapHelper.getNumber(userData, 'id', 0),
      name: TKMapHelper.getString(userData, 'name', ''),
      email: TKMapHelper.getString(userData, 'email', ''),
      isActive: TKMapHelper.getBoolean(userData, 'isActive', false),
      profile: TKMapHelper.getJSON(userData, 'profile', {}),
      settings: TKMapHelper.getJSON(userData, 'settings', {})
    };
  }

  // 表单数据转换
  static transformFormData(formData: Record<string, Object>): Map<string, Object> {
    const resultMap = new Map<string, Object>();

    Object.entries(formData).forEach(([key, value]) => {
      // 处理不同类型的表单数据
      if (typeof value === 'string') {
        // 尝试解析JSON字符串
        try {
          const parsed = JSON.parse(value);
          TKMapHelper.setObject(resultMap, key, parsed);
        } catch {
          TKMapHelper.setObject(resultMap, key, value);
        }
      } else {
        TKMapHelper.setObject(resultMap, key, value);
      }
    });

    return resultMap;
  }

  // 配置数据合并
  static mergeConfigurations(
    baseConfig: Record<string, Object>,
    userConfig: Record<string, Object>,
    environmentConfig: Record<string, Object>
  ): Record<string, Object> {
    // 创建基础配置的副本
    let mergedConfig = { ...baseConfig };

    // 合并用户配置
    mergedConfig = TKMapHelper.merge(mergedConfig, userConfig, true);

    // 合并环境配置（优先级最高）
    mergedConfig = TKMapHelper.merge(mergedConfig, environmentConfig, true);

    return mergedConfig;
  }
}

// 缓存管理器
class CacheManager {
  private cache: Map<string, Object> = new Map();
  private metadata: Map<string, Object> = new Map();

  // 设置缓存
  set(key: string, value: Object, ttl: number = 3600000): void {
    TKMapHelper.setObject(this.cache, key, value);
    TKMapHelper.setObject(this.metadata, key, {
      timestamp: Date.now(),
      ttl: ttl,
      accessCount: 0
    });
    console.log(`缓存已设置: ${key}`);
  }

  // 获取缓存
  get<T>(key: string, defaultValue?: T): T | undefined {
    if (!this.isValid(key)) {
      this.delete(key);
      return defaultValue;
    }

    // 更新访问计数
    const meta = TKMapHelper.getObject(this.metadata, key, {}) as any;
    meta.accessCount = (meta.accessCount || 0) + 1;
    meta.lastAccess = Date.now();
    TKMapHelper.setObject(this.metadata, key, meta);

    return TKMapHelper.getObject<T>(this.cache, key, defaultValue);
  }

  // 检查缓存是否有效
  private isValid(key: string): boolean {
    if (!TKMapHelper.containKey(this.cache, key)) {
      return false;
    }

    const meta = TKMapHelper.getObject(this.metadata, key, {}) as any;
    if (!meta.timestamp || !meta.ttl) {
      return false;
    }

    return (Date.now() - meta.timestamp) < meta.ttl;
  }

  // 删除缓存
  delete(key: string): void {
    TKMapHelper.deleteObject(this.cache, key);
    TKMapHelper.deleteObject(this.metadata, key);
    console.log(`缓存已删除: ${key}`);
  }

  // 清理过期缓存
  cleanup(): void {
    const keys = TKMapHelper.keys(this.cache);
    let cleanedCount = 0;

    keys.forEach(key => {
      if (!this.isValid(key)) {
        this.delete(key);
        cleanedCount++;
      }
    });

    console.log(`清理了${cleanedCount}个过期缓存`);
  }

  // 获取缓存统计
  getStats(): {
    totalItems: number,
    validItems: number,
    expiredItems: number,
    totalSize: number
  } {
    const keys = TKMapHelper.keys(this.cache);
    let validItems = 0;
    let expiredItems = 0;

    keys.forEach(key => {
      if (this.isValid(key)) {
        validItems++;
      } else {
        expiredItems++;
      }
    });

    return {
      totalItems: keys.length,
      validItems: validItems,
      expiredItems: expiredItems,
      totalSize: JSON.stringify(Array.from(this.cache.entries())).length
    };
  }
}

// 使用示例
function demonstrateAdvancedMapHelper() {
  // 配置管理器示例
  console.log('=== 配置管理器示例 ===');
  const configManager = new ConfigurationManager();

  // 获取配置
  console.log('主题配置:', configManager.getStringConfig('theme'));
  console.log('自动保存:', configManager.getBooleanConfig('autoSave'));
  console.log('超时时间:', configManager.getNumberConfig('timeout'));

  // 设置配置
  configManager.setConfig('theme', 'dark');
  configManager.setBatchConfig({
    language: 'en-US',
    maxRetries: 5,
    newFeature: true
  });

  console.log('配置统计:', configManager.getConfigStats());
  console.log('所有配置:', configManager.getAllConfigs());

  // 数据转换器示例
  console.log('\n=== 数据转换器示例 ===');
  const apiResponse = {
    success: 'true',
    data: { id: 1, name: 'test' },
    message: 'Success',
    code: '200'
  };

  const transformedResponse = DataTransformer.transformApiResponse(apiResponse);
  console.log('转换后的API响应:', transformedResponse);

  const userData = new Map([
    ['id', '123'],
    ['name', '张三'],
    ['email', '<EMAIL>'],
    ['isActive', 'true'],
    ['profile', JSON.stringify({ role: 'admin' })],
    ['settings', JSON.stringify({ theme: 'dark' })]
  ]);

  const transformedUser = DataTransformer.transformUserData(userData);
  console.log('转换后的用户数据:', transformedUser);

  // 缓存管理器示例
  console.log('\n=== 缓存管理器示例 ===');
  const cacheManager = new CacheManager();

  // 设置缓存
  cacheManager.set('user:123', { name: '张三', age: 30 }, 5000); // 5秒TTL
  cacheManager.set('config:app', { theme: 'dark', language: 'zh' }, 10000); // 10秒TTL

  // 获取缓存
  console.log('用户缓存:', cacheManager.get('user:123'));
  console.log('配置缓存:', cacheManager.get('config:app'));

  // 缓存统计
  console.log('缓存统计:', cacheManager.getStats());

  // 模拟过期后清理
  setTimeout(() => {
    console.log('\n5秒后清理缓存:');
    cacheManager.cleanup();
    console.log('清理后统计:', cacheManager.getStats());
  }, 6000);
}

// 执行演示
demonstrateAdvancedMapHelper();
```

## 📚 相关资源

### 官方文档
- HarmonyOS集合框架开发指南
- TypeScript泛型编程指南

### 参考资料
- 《数据结构与算法》- Map数据结构原理
- 《TypeScript高级编程》- 泛型和类型安全

### 相关文件
- `TKObjectHelper.ets` - 对象操作工具
- `TKDataHelper.ets` - 数据转换工具
- `TKStringHelper.ets` - 字符串处理工具
