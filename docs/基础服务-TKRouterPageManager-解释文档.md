# TKRouterPageManager

> **文件路径**: `src/main/ets/base/router/TKRouterPageManager.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐⭐ (4星)  
> **学习难度**: 中级  
> **前置知识**: 鸿蒙路由系统、栈数据结构、观察者模式、页面生命周期

## 📋 文件概述

### 功能定位
TKRouterPageManager是HMThinkBaseHar框架的**路由页面管理器**，负责维护应用的页面栈状态和页面生命周期监听。它采用单例模式设计，通过监听系统路由事件来同步维护框架内部的页面栈，为路由功能提供状态管理支持。

### 核心职责
- **页面栈管理**：维护应用页面的栈结构，支持push/pop操作
- **路由事件监听**：监听系统路由变化，同步更新内部状态
- **页面信息封装**：将系统路由信息转换为框架标准格式
- **生命周期跟踪**：跟踪页面的创建和销毁状态
- **导航支持**：同时支持传统路由和Navigation导航

### 设计模式
- **单例模式**：确保全局唯一的页面管理器实例
- **观察者模式**：监听系统路由事件变化
- **栈模式**：使用栈结构管理页面层级关系

## 🔧 核心功能

### 主要API/方法

#### 方法1：`shareInstance()`
- **功能**: 获取TKRouterPageManager的单例实例
- **参数**: 无
- **返回值**: TKRouterPageManager实例
- **使用场景**: 任何需要访问页面管理器的地方

#### 方法2：`startListener()`
- **功能**: 启动路由事件监听器
- **参数**: 无
- **返回值**: Promise<void>
- **使用场景**: 框架初始化时启动页面状态监听

#### 方法3：`push(routerPageInfo: TKRouterPageInfo)`
- **功能**: 将页面信息压入栈中
- **参数**: TKRouterPageInfo - 页面信息对象
- **返回值**: void
- **使用场景**: 页面跳转时记录页面状态

#### 方法4：`pop(urlOrName?: string)`
- **功能**: 从栈中弹出页面信息
- **参数**: string - 页面URL或名称（可选）
- **返回值**: TKRouterPageInfo | undefined
- **使用场景**: 页面返回时移除页面状态

#### 方法5：`get(urlOrName?: string, offsetIndex?: number)`
- **功能**: 获取栈中的页面信息
- **参数**: 
  - urlOrName - 页面URL或名称（可选）
  - offsetIndex - 偏移索引（可选）
- **返回值**: TKRouterPageInfo | undefined
- **使用场景**: 查询特定页面的状态信息

### 关键属性/配置
- `pageStack: ArrayList<TKRouterPageInfo>` - 页面栈，存储页面信息
- `isRunning: boolean` - 监听器运行状态标识

## 💡 技术要点

### 核心算法/逻辑

#### 路由事件监听机制
```typescript
public async startListener() {
  if (!this.isRunning) {
    this.isRunning = true;
    // 监听传统路由页面更新
    uiObserver.on('routerPageUpdate', TKContextHelper.getCurrentUIAbilityContext(),
      this.handleRouterPageInfoListener);
    // 监听Navigation导航切换
    uiObserver.on('navDestinationSwitch', TKContextHelper.getCurrentUIAbilityContext(),
      this.handleNavRouterPageInfoListener);
  }
}
```

#### 页面信息转换处理
```typescript
private handleRouterPageInfoListener = (routerPageInfo: uiObserver.RouterPageInfo) => {
  if (routerPageInfo.state == uiObserver.RouterPageState.ABOUT_TO_APPEAR) {
    // 页面即将出现 - 创建页面信息并入栈
    let tkRouterPageInfo: TKRouterPageInfo = {
      flowNo: TKMapHelper.getString(curRouteState.params, "@flowNo", TKUUIDHelper.uuid()),
      url: curRouteState.path,
      name: curRouteState.name,
      pageId: routerPageInfo.pageId,
      params: curRouteState.params,
      srcPageStyle: srcPageStyle,
      pageStyle: pageStyle
    };
    this.push(tkRouterPageInfo);
  } else if (routerPageInfo.state == uiObserver.RouterPageState.ABOUT_TO_DISAPPEAR) {
    // 页面即将消失 - 从栈中移除页面信息
    let urlOrName: string = this.getRoutePageUrlOrName(routerPageInfo);
    let tkRouterPageInfo: TKRouterPageInfo | undefined = this.pop(urlOrName);
    if (tkRouterPageInfo) {
      TKRouterResultObserverManager.shareInstance().remove(tkRouterPageInfo.flowNo!);
    }
  }
}
```

#### 栈操作核心逻辑
```typescript
// 压栈操作
public push(routerPageInfo: TKRouterPageInfo) {
  this.pageStack.add(routerPageInfo);
}

// 出栈操作（支持按标识查找）
public pop(urlOrName?: string): TKRouterPageInfo | undefined {
  if (TKStringHelper.isNotBlank(urlOrName)) {
    // 从栈顶向下查找匹配的页面
    for (let index = this.pageStack.length - 1; index >= 0; index--) {
      const routerPageInfo: TKRouterPageInfo = this.pageStack[index];
      if (routerPageInfo.flowNo == urlOrName || 
          routerPageInfo.name == urlOrName || 
          routerPageInfo.url == urlOrName) {
        return this.pageStack.removeByIndex(index);
      }
    }
  } else if (this.pageStack.length > 0) {
    // 弹出栈顶页面
    return this.pageStack.removeByIndex(this.pageStack.length - 1);
  }
  return undefined;
}
```

### 性能考虑
- **事件监听优化**：只在需要时启动监听器，避免不必要的性能开销
- **栈操作效率**：使用ArrayList提供高效的栈操作
- **内存管理**：页面销毁时及时清理相关观察者，避免内存泄漏
- **查找优化**：支持多种标识符查找，提高查找效率

### 错误处理
- **监听器状态检查**：启动前检查运行状态，避免重复监听
- **空值处理**：所有栈操作都有空值检查和边界处理
- **异常隔离**：页面状态变化异常不影响其他页面
- **资源清理**：页面销毁时自动清理相关资源

### 最佳实践
- **单例管理**：确保全局唯一的页面状态管理
- **事件驱动**：通过系统事件驱动状态更新，保证一致性
- **信息封装**：将系统路由信息转换为框架标准格式
- **生命周期同步**：与系统页面生命周期保持同步

## 🔗 依赖关系

### 依赖的模块
- `TKRouterPageInfo` - 页面信息定义，栈中存储的数据结构
- `TKRouterResultObserverManager` - 路由结果观察者管理器
- `TKContextHelper` - 上下文辅助工具，获取UIAbility上下文
- `TKStringHelper` - 字符串工具，参数验证
- `TKUUIDHelper` - UUID工具，生成页面流水号
- `uiObserver` - 鸿蒙UI观察者，监听路由事件

### 被依赖情况
- `TKRouterHelper` - 路由辅助类，使用页面管理器查询页面状态
- `各页面组件` - 通过路由系统间接使用页面管理功能
- `路由拦截器` - 可能需要查询当前页面状态

### 关联文件
- `TKRouterHelper.ets` - 路由辅助类，页面管理器的主要使用者
- `TKRouterPageInfo.ets` - 页面信息定义，数据结构规范
- `TKRouterResultObserverManager.ets` - 结果观察者管理器

## 📊 重要程度评级

### 评级标准
⭐⭐⭐⭐ **重要推荐**: 常用功能，建议掌握

### 本文件评级理由
TKRouterPageManager是路由系统的状态管理核心：
1. **状态维护**：维护应用页面栈的完整状态信息
2. **系统集成**：与鸿蒙路由系统深度集成，提供状态同步
3. **功能支持**：为路由功能提供必要的状态查询和管理支持
4. **生命周期管理**：跟踪页面的完整生命周期

理解TKRouterPageManager是掌握框架路由机制的重要环节，它是路由状态管理的基础组件。

## 🎯 学习建议

### 学习路径
1. **理解栈数据结构**：掌握页面栈的基本概念和操作
2. **学习路由监听机制**：了解如何监听系统路由事件
3. **分析页面信息转换**：理解系统路由信息到框架格式的转换
4. **实践状态查询**：学习如何查询和管理页面状态

### 前置学习
- 鸿蒙路由系统基础知识
- 栈数据结构的概念和操作
- 观察者模式的实现原理
- 页面生命周期管理

### 后续学习
- `TKRouterHelper.ets` - 了解路由辅助功能的实现
- `TKRouterPageInfo.ets` - 学习页面信息的数据结构
- `TKRouterResultObserverManager.ets` - 掌握结果回调机制

### 实践建议
1. **监听器实验**：启动监听器，观察页面跳转时的状态变化
2. **栈操作练习**：手动操作页面栈，理解push/pop机制
3. **状态查询测试**：在不同页面查询栈状态，验证数据一致性
4. **生命周期跟踪**：跟踪页面从创建到销毁的完整过程

### 常见问题
1. **问题**: 为什么需要维护独立的页面栈？
   **解答**: 系统路由信息格式固定且功能有限，框架需要扩展的页面信息和状态管理功能，独立的页面栈提供了更灵活的管理能力。

2. **问题**: 页面栈与系统路由栈的关系是什么？
   **解答**: 页面栈是系统路由栈的镜像和扩展，通过监听系统事件保持同步，同时添加了框架特有的信息和功能。

## 📝 代码示例

### 基础使用
```typescript
import { TKRouterPageManager, TKRouterPageInfo } from '@thinkive/tk-harmony-base';

// 获取页面管理器实例
const pageManager = TKRouterPageManager.shareInstance();

// 启动路由监听器（通常在应用初始化时调用）
async function initializeRouterManager() {
  try {
    await pageManager.startListener();
    console.log('路由页面管理器启动成功');
  } catch (error) {
    console.error('路由页面管理器启动失败:', error);
  }
}

// 查询当前页面信息
function getCurrentPageInfo(): TKRouterPageInfo | undefined {
  // 获取栈顶页面（当前页面）
  const currentPage = pageManager.get();
  if (currentPage) {
    console.log('当前页面信息:', {
      flowNo: currentPage.flowNo,
      url: currentPage.url,
      name: currentPage.name,
      params: currentPage.params
    });
    return currentPage;
  } else {
    console.log('未找到当前页面信息');
    return undefined;
  }
}

// 查询特定页面信息
function getPageInfo(pageIdentifier: string): TKRouterPageInfo | undefined {
  // 根据页面URL、名称或流水号查询
  const pageInfo = pageManager.get(pageIdentifier);
  if (pageInfo) {
    console.log(`页面 ${pageIdentifier} 信息:`, pageInfo);
    return pageInfo;
  } else {
    console.log(`未找到页面 ${pageIdentifier}`);
    return undefined;
  }
}
```

### 高级用法
```typescript
// 页面栈状态监控类
class PageStackMonitor {
  private pageManager = TKRouterPageManager.shareInstance();

  // 获取页面栈深度
  getStackDepth(): number {
    let depth = 0;
    while (this.pageManager.get(undefined, -depth)) {
      depth++;
    }
    return depth;
  }

  // 获取完整的页面栈信息
  getFullStackInfo(): TKRouterPageInfo[] {
    const stackInfo: TKRouterPageInfo[] = [];
    let index = 0;

    // 从栈底到栈顶遍历
    while (true) {
      const pageInfo = this.pageManager.get(undefined, -index);
      if (pageInfo) {
        stackInfo.unshift(pageInfo); // 插入到数组开头，保持栈底到栈顶的顺序
        index++;
      } else {
        break;
      }
    }

    return stackInfo;
  }

  // 查找页面在栈中的位置
  findPagePosition(pageIdentifier: string): number {
    const stackInfo = this.getFullStackInfo();
    return stackInfo.findIndex(page =>
      page.flowNo === pageIdentifier ||
      page.name === pageIdentifier ||
      page.url === pageIdentifier
    );
  }

  // 获取前一个页面信息
  getPreviousPageInfo(): TKRouterPageInfo | undefined {
    return this.pageManager.get(undefined, -1);
  }

  // 检查是否可以返回
  canGoBack(): boolean {
    return this.getStackDepth() > 1;
  }

  // 打印页面栈状态（调试用）
  printStackStatus() {
    const stackInfo = this.getFullStackInfo();
    console.log('=== 页面栈状态 ===');
    console.log(`栈深度: ${stackInfo.length}`);

    stackInfo.forEach((page, index) => {
      const position = index === stackInfo.length - 1 ? '(当前)' : '';
      console.log(`${index}: ${page.name || page.url} ${position}`);
      console.log(`    流水号: ${page.flowNo}`);
      console.log(`    参数: ${JSON.stringify(page.params)}`);
    });
    console.log('==================');
  }
}
```

### 扩展示例
```typescript
// 页面状态管理工具类
class PageStateManager {
  private pageManager = TKRouterPageManager.shareInstance();
  private pageStateCache = new Map<string, any>();

  // 保存页面状态
  savePageState(pageIdentifier: string, state: any) {
    this.pageStateCache.set(pageIdentifier, state);
    console.log(`页面 ${pageIdentifier} 状态已保存`);
  }

  // 恢复页面状态
  restorePageState(pageIdentifier: string): any {
    const state = this.pageStateCache.get(pageIdentifier);
    if (state) {
      console.log(`页面 ${pageIdentifier} 状态已恢复`);
      return state;
    } else {
      console.log(`页面 ${pageIdentifier} 无保存状态`);
      return null;
    }
  }

  // 清理已销毁页面的状态
  cleanupDestroyedPageStates() {
    const stackInfo = new PageStackMonitor().getFullStackInfo();
    const activePageIds = new Set(stackInfo.map(page => page.flowNo || page.name || page.url));

    // 清理不在栈中的页面状态
    for (const [pageId] of this.pageStateCache) {
      if (!activePageIds.has(pageId)) {
        this.pageStateCache.delete(pageId);
        console.log(`清理页面 ${pageId} 的状态缓存`);
      }
    }
  }

  // 页面返回时的状态处理
  handlePageReturn(targetPageIdentifier: string, returnData?: any) {
    const targetPage = this.pageManager.get(targetPageIdentifier);
    if (targetPage) {
      // 恢复目标页面状态
      const savedState = this.restorePageState(targetPageIdentifier);

      // 如果有返回数据，合并到状态中
      if (returnData && savedState) {
        const mergedState = { ...savedState, returnData };
        this.savePageState(targetPageIdentifier, mergedState);
      }

      console.log(`页面返回处理完成: ${targetPageIdentifier}`);
      return { targetPage, savedState, returnData };
    } else {
      console.error(`目标页面不存在: ${targetPageIdentifier}`);
      return null;
    }
  }

  // 获取页面导航历史
  getNavigationHistory(): Array<{page: string, timestamp: number}> {
    const stackInfo = new PageStackMonitor().getFullStackInfo();
    return stackInfo.map((page, index) => ({
      page: page.name || page.url || 'unknown',
      timestamp: Date.now() - (stackInfo.length - index - 1) * 1000 // 模拟时间戳
    }));
  }
}

// 使用示例
class MyPageComponent {
  private pageStateManager = new PageStateManager();
  private pageMonitor = new PageStackMonitor();

  onPageAppear() {
    // 页面出现时恢复状态
    const currentPage = TKRouterPageManager.shareInstance().get();
    if (currentPage) {
      const savedState = this.pageStateManager.restorePageState(currentPage.flowNo!);
      if (savedState) {
        this.applyPageState(savedState);
      }
    }

    // 打印当前栈状态（调试）
    this.pageMonitor.printStackStatus();
  }

  onPageDisappear() {
    // 页面消失时保存状态
    const currentPage = TKRouterPageManager.shareInstance().get();
    if (currentPage) {
      const currentState = this.getCurrentPageState();
      this.pageStateManager.savePageState(currentPage.flowNo!, currentState);
    }

    // 清理无效状态
    this.pageStateManager.cleanupDestroyedPageStates();
  }

  private applyPageState(state: any) {
    // 应用页面状态的具体逻辑
    console.log('应用页面状态:', state);
  }

  private getCurrentPageState(): any {
    // 获取当前页面状态的具体逻辑
    return {
      scrollPosition: 0,
      formData: {},
      timestamp: Date.now()
    };
  }
}
```

## 📚 相关资源

### 官方文档
- HarmonyOS路由开发指南
- HarmonyOS UI观察者使用指南

### 参考资料
- 《数据结构与算法》- 栈的实现和应用
- 《设计模式：可复用面向对象软件的基础》- 观察者模式

### 相关文件
- `基础服务-TKRouterHelper-解释文档.md` - 路由辅助功能
- `TKRouterPageInfo.ets` - 页面信息数据结构
- `TKRouterResultObserverManager.ets` - 路由结果管理
