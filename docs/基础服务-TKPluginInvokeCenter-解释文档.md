# TKPluginInvokeCenter

> **文件路径**: `src/main/ets/base/plugin/TKPluginInvokeCenter.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 插件架构、反射机制、XML配置解析、单例模式

## 📋 文件概述

### 功能定位
TKPluginInvokeCenter是HMThinkBaseHar框架的**插件调用中心**，实现了完整的插件管理和调用机制。它采用单例模式设计，负责插件的注册、配置加载、实例管理和调用分发，为框架提供可扩展的插件架构，支持H5与原生的插件通信。

### 核心职责
- **插件注册管理**：注册和管理插件类定义
- **插件配置加载**：从XML配置文件加载插件映射关系
- **插件实例管理**：创建、缓存和管理插件实例
- **插件调用分发**：根据功能号分发插件调用请求
- **多模式支持**：支持同步/异步、H5/原生等多种调用模式

### 设计模式
- **单例模式**：确保全局唯一的插件调用中心实例
- **工厂模式**：根据功能号创建相应的插件实例
- **策略模式**：支持不同类型的插件调用策略
- **注册模式**：支持动态注册插件类定义

## 🔧 核心功能

### 主要API/方法

#### 方法1：`shareInstance()`
- **功能**: 获取TKPluginInvokeCenter的单例实例
- **参数**: 无
- **返回值**: TKPluginInvokeCenter实例
- **使用场景**: 任何需要调用插件的地方

#### 方法2：`callPlugin(pluginInvokeOption: TKPluginInvokeOption)`
- **功能**: 调用插件的核心方法
- **参数**: TKPluginInvokeOption - 插件调用选项
- **返回值**: TKResultVO - 插件执行结果
- **使用场景**: 执行插件功能时的统一入口

#### 方法3：`registerPlugin(plugins: Object)`
- **功能**: 注册插件类定义
- **参数**: Object - 插件类对象集合
- **返回值**: void
- **使用场景**: 框架初始化时注册内置插件

#### 方法4：`isPluginExist(pluginNo: string)`
- **功能**: 检查插件是否存在
- **参数**: string - 插件功能号
- **返回值**: boolean - 是否存在
- **使用场景**: 调用前检查插件可用性

### 关键属性/配置
- `pluginClassMap: Map<string, FunctionConstructor>` - 插件类定义映射表
- `pluginObjectMap: Map<string, TKBasePlugin>` - 插件实例映射表（按appId_funcNo）
- `pluginObjectByFuncNoMap: Map<string, TKBasePlugin>` - 插件实例映射表（按funcNo）
- `pluginMap: Map<string, string>` - 插件配置映射表

## 💡 技术要点

### 核心算法/逻辑

#### 插件调用流程
```typescript
public callPlugin(pluginInvokeOption: TKPluginInvokeOption): TKResultVO {
  try {
    // 1. 提取调用参数
    let funcNo = pluginInvokeOption.funcNo;
    let param = pluginInvokeOption.param ?? {};
    let moduleName = pluginInvokeOption.moduleName;
    let isH5 = pluginInvokeOption.isH5;
    let callBackFunc = pluginInvokeOption.callBackFunc;
    let navComponentOrPathStack = pluginInvokeOption.navComponentOrPathStack;
    
    // 2. 处理功能号和应用ID
    if (TKStringHelper.isBlank(funcNo)) {
      funcNo = TKMapHelper.getString(param, "funcNo");
    }
    let appId: string = TKMapHelper.getString(param, "_TKAppID");
    if (TKStringHelper.isNotBlank(funcNo) && TKStringHelper.isNotBlank(appId)) {
      funcNo = `${appId}_${funcNo}`;
    }
    
    // 3. 获取插件名称
    let pluginName: string | undefined = this.pluginMap.get(funcNo);
    if (TKStringHelper.isBlank(pluginName)) {
      pluginName = 'TKPlugin' + tempFuncNo;
    }
    
    // 4. 获取或创建插件实例
    let plugin: TKBasePlugin | undefined = this.getOrCreatePluginInstance(funcNo, pluginName);
    
    if (plugin) {
      // 5. 设置插件属性
      plugin.moduleName = moduleName;
      plugin.isH5 = TKDataHelper.getBoolean(isH5);
      plugin.callBackFunc = callBackFunc;
      plugin.navComponentOrPathStack = navComponentOrPathStack;
      plugin.flowNo = TKMapHelper.getString(param, "flowNo");
      plugin.isUseJsCallBack = (TKMapHelper.getString(param, "isJsCallBack") == "1");
      
      // 6. 执行插件调用
      TKLog.info(`调用插件(${funcNo})开始,入参为：${TKObjectHelper.toJsonStr(param)}`);
      let pluginResultVO: TKResultVO = plugin.pluginInvoke(param);
      
      // 7. 处理返回结果
      let resultVO: TKResultVO = new TKResultVO();
      resultVO.errorNo = pluginResultVO.errorNo;
      resultVO.errorInfo = pluginResultVO.errorInfo;
      resultVO.results = pluginResultVO.results;
      
      TKLog.info(`调用插件(${funcNo})结束,出参为：${resultVO.toJsonStr()}`);
      return resultVO;
    } else {
      // 8. 插件不存在的错误处理
      TKLog.error(`插件[${pluginName}]对应的类不存在!`);
      let resultVO: TKResultVO = new TKResultVO();
      resultVO.errorNo = -3;
      resultVO.errorInfo = `插件[${pluginName}]对应的类不存在!`;
      return resultVO;
    }
  } catch (error) {
    // 9. 异常处理
    TKLog.error(`插件中心调用插件业务异常:${error.message}`);
    let resultVO: TKResultVO = new TKResultVO();
    resultVO.errorNo = -4;
    resultVO.errorInfo = `插件中心调用插件业务异常:${error.message}`;
    return resultVO;
  }
}
```

#### 插件实例管理机制
```typescript
private getOrCreatePluginInstance(funcNo: string, pluginName: string): TKBasePlugin | undefined {
  // 1. 尝试从缓存获取实例
  let plugin: TKBasePlugin | undefined = this.pluginObjectMap.get(funcNo);
  
  if (!plugin) {
    // 2. 从功能号映射表获取
    plugin = this.pluginObjectByFuncNoMap.get(tempFuncNo);
  }
  
  if (!plugin) {
    // 3. 获取插件类定义
    let pluginClass: FunctionConstructor | undefined = this.pluginClassMap.get(pluginName);
    
    if (pluginClass) {
      // 4. 创建新的插件实例
      plugin = new pluginClass() as TKBasePlugin;
      
      // 5. 根据缓存策略决定是否缓存
      if (plugin.isCache) {
        this.pluginObjectMap.set(funcNo, plugin);
        this.pluginObjectByFuncNoMap.set(tempFuncNo, plugin);
      }
    }
  }
  
  return plugin;
}
```

#### 插件配置加载机制
```typescript
private reloadPluginConfig(configFiles: string) {
  try {
    // 1. 解析配置文件路径
    let configFileArray: Array<string> = configFiles.split(",");
    
    for (let configFile of configFileArray) {
      // 2. 读取XML配置文件
      let configContent: string = TKFileHelper.readRawFileToString(configFile);
      
      if (TKStringHelper.isNotBlank(configContent)) {
        // 3. 解析XML内容
        let xmlDoc = TKXMLHelper.parseXML(configContent);
        let pluginNodes = TKXMLHelper.selectNodes(xmlDoc, "//plugin");
        
        // 4. 遍历插件配置节点
        for (let pluginNode of pluginNodes) {
          let funcNo = TKXMLHelper.getAttributeValue(pluginNode, "funcNo");
          let className = TKXMLHelper.getAttributeValue(pluginNode, "class");
          
          if (TKStringHelper.isNotBlank(funcNo) && TKStringHelper.isNotBlank(className)) {
            // 5. 建立功能号到类名的映射
            this.pluginMap.set(funcNo, className);
          }
        }
      }
    }
  } catch (error) {
    TKLog.error(`加载插件配置异常: ${error.message}`);
  }
}
```

#### 插件注册机制
```typescript
public registerPlugin(plugins: Object) {
  // 遍历插件对象的所有属性
  Object.entries(plugins).forEach((e: Array<Object>, i: number) => {
    let key: string = e[0] as string;
    let plugin = e[1] as FunctionConstructor;
    
    // 将插件类注册到映射表中
    this.pluginClassMap.set(key, plugin);
  });
}
```

### 实现机制分析

#### 多层映射管理
- **类定义映射**：pluginClassMap存储插件类名到构造函数的映射
- **配置映射**：pluginMap存储功能号到类名的映射
- **实例映射**：pluginObjectMap和pluginObjectByFuncNoMap管理插件实例
- **多键索引**：支持按appId_funcNo和funcNo两种方式索引实例

#### 插件生命周期管理
1. **注册阶段**：框架启动时注册所有插件类定义
2. **配置加载**：从XML文件加载插件配置映射
3. **实例创建**：首次调用时创建插件实例
4. **缓存管理**：根据插件的isCache属性决定是否缓存实例
5. **调用执行**：设置插件属性并执行插件逻辑

#### 多模式支持
- **同步/异步模式**：通过isSyncPlugin属性控制
- **H5/原生模式**：通过isH5属性区分调用来源
- **缓存/非缓存模式**：通过isCache属性控制实例生命周期
- **回调模式**：支持JS回调和原生回调两种方式

### 性能考虑
- **实例缓存**：支持插件实例缓存，避免重复创建开销
- **懒加载**：插件实例按需创建，减少内存占用
- **配置缓存**：XML配置解析后缓存映射关系
- **异常隔离**：单个插件异常不影响其他插件

### 错误处理
- **参数验证**：对功能号等关键参数进行验证
- **插件存在性检查**：调用前检查插件是否存在
- **异常捕获**：捕获插件执行异常并返回错误信息
- **日志记录**：详细记录插件调用过程和异常信息

### 最佳实践
- **统一接口**：通过TKPluginInvokeOption提供统一的调用接口
- **配置驱动**：通过XML配置文件管理插件映射关系
- **扩展性设计**：支持动态注册新的插件类
- **错误隔离**：完善的错误处理机制，确保系统稳定性

## 🔗 依赖关系

### 依赖的模块
- `TKBasePlugin` - 插件基类，定义插件接口规范
- `TKResultVO` - 结果对象，插件执行结果的载体
- `TKXMLHelper` - XML工具，解析插件配置文件
- `TKFileHelper` - 文件工具，读取配置文件
- `TKLog` - 日志工具，记录插件调用日志

### 被依赖情况
- `WebView组件` - 使用插件中心处理H5调用的插件
- `业务模块` - 通过插件中心调用各种功能插件
- `TKJSProxyController` - JS代理控制器，H5与原生的桥梁

### 关联文件
- `TKBasePlugin.ets` - 插件基类，所有插件的父类
- `TKPluginIndex.ets` - 插件索引，导出所有插件类
- `SystemPlugin.xml` - 插件配置文件，定义插件映射关系
- 各种具体插件实现 - 如TKPlugin50023等

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKPluginInvokeCenter是框架插件架构的核心组件：
1. **扩展机制**：提供框架功能扩展的核心机制
2. **H5桥接**：支持H5与原生功能的桥接调用
3. **模块化设计**：实现功能的模块化和插件化
4. **配置驱动**：通过配置文件实现灵活的插件管理

理解TKPluginInvokeCenter有助于掌握框架的插件机制，特别是在需要扩展框架功能或实现H5混合开发时。

## 🎯 学习建议

### 学习路径
1. **理解插件架构**：掌握插件模式的设计思想和应用场景
2. **分析调用流程**：理解从插件注册到调用执行的完整流程
3. **学习配置管理**：掌握XML配置文件的解析和映射机制
4. **实践插件开发**：创建自定义插件并注册到插件中心

### 前置学习
- 插件架构和设计模式
- XML文件解析和处理
- 反射机制和动态实例化
- 单例模式的实现原理

### 后续学习
- `TKBasePlugin.ets` - 学习插件基类的设计和使用
- 各种具体插件实现 - 了解插件的实际开发方式
- `TKJSProxyController.ets` - 掌握H5与原生的桥接机制

### 实践建议
1. **基础调用练习**：使用插件中心调用现有插件
2. **插件开发实验**：创建简单的自定义插件
3. **配置文件管理**：修改XML配置文件添加新插件
4. **H5桥接测试**：测试H5调用原生插件的功能

### 常见问题
1. **问题**: 如何添加新的插件？
   **解答**: 需要创建继承TKBasePlugin的插件类，在TKPluginIndex.ets中导出，并在SystemPlugin.xml中配置映射关系。

2. **问题**: 插件实例什么时候会被缓存？
   **解答**: 当插件的isCache属性为true时，实例会被缓存；否则每次调用都会创建新实例。

## 📝 代码示例

### 基础使用
```typescript
import { TKPluginInvokeCenter, TKPluginInvokeOption, TKResultVO } from '@thinkive/tk-harmony-base';

// 获取插件调用中心实例
const pluginCenter = TKPluginInvokeCenter.shareInstance();

// 基础插件调用
function callBasicPlugin() {
  const invokeOption: TKPluginInvokeOption = {
    funcNo: "50023", // 获取设备IP地址插件
    param: {
      flowNo: "FLOW_001"
    }
  };

  const result: TKResultVO = pluginCenter.callPlugin(invokeOption);

  if (result.errorNo === 0) {
    console.log('插件调用成功:', result.results);
    const data = result.results as Record<string, any>;
    console.log('外网IP:', data.ip);
    console.log('内网IP:', data.lip);
  } else {
    console.error('插件调用失败:', result.errorInfo);
  }
}

// 带回调的插件调用
function callPluginWithCallback() {
  const invokeOption: TKPluginInvokeOption = {
    funcNo: "50024", // 获取设备MAC地址插件
    param: {
      flowNo: "FLOW_002"
    },
    callBackFunc: (result: Record<string, Object>) => {
      console.log('插件回调结果:', result);
    }
  };

  const result: TKResultVO = pluginCenter.callPlugin(invokeOption);
  console.log('插件调用结果:', result);
}

// H5调用插件
function callPluginFromH5() {
  const invokeOption: TKPluginInvokeOption = {
    funcNo: "50031", // 获取网络运营商插件
    param: {
      flowNo: "FLOW_003",
      isJsCallBack: "1"
    },
    isH5: true,
    moduleName: "WebModule"
  };

  const result: TKResultVO = pluginCenter.callPlugin(invokeOption);
  console.log('H5插件调用结果:', result);
}

// 检查插件是否存在
function checkPluginExists() {
  const pluginExists = pluginCenter.isPluginExist("50023");
  console.log('插件50023是否存在:', pluginExists);

  const nonExistentPlugin = pluginCenter.isPluginExist("99999");
  console.log('插件99999是否存在:', nonExistentPlugin);
}

// 执行示例
callBasicPlugin();
callPluginWithCallback();
callPluginFromH5();
checkPluginExists();
```

### 高级用法
```typescript
// 插件调用管理器
class PluginCallManager {
  private pluginCenter = TKPluginInvokeCenter.shareInstance();
  private callHistory: Array<{
    funcNo: string,
    timestamp: number,
    result: TKResultVO
  }> = [];

  // 带重试机制的插件调用
  async callPluginWithRetry(
    funcNo: string,
    param: Record<string, any>,
    maxRetries: number = 3
  ): Promise<TKResultVO> {
    let lastError: TKResultVO | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const invokeOption: TKPluginInvokeOption = {
          funcNo: funcNo,
          param: {
            ...param,
            flowNo: `RETRY_${Date.now()}_${attempt}`
          }
        };

        const result = this.pluginCenter.callPlugin(invokeOption);

        if (result.errorNo === 0) {
          // 记录成功调用
          this.recordCall(funcNo, result);
          return result;
        } else {
          lastError = result;
          console.log(`插件调用失败，第 ${attempt} 次尝试:`, result.errorInfo);

          if (attempt < maxRetries) {
            // 等待后重试
            await this.delay(1000 * attempt);
          }
        }
      } catch (error) {
        console.error(`插件调用异常，第 ${attempt} 次尝试:`, error);
        if (attempt === maxRetries) {
          const errorResult = new TKResultVO();
          errorResult.errorNo = -999;
          errorResult.errorInfo = `插件调用异常: ${error.message}`;
          return errorResult;
        }
      }
    }

    return lastError || new TKResultVO();
  }

  // 批量插件调用
  async callMultiplePlugins(
    pluginCalls: Array<{
      funcNo: string,
      param: Record<string, any>
    }>
  ): Promise<Array<TKResultVO>> {
    const results: Array<TKResultVO> = [];

    for (const call of pluginCalls) {
      const invokeOption: TKPluginInvokeOption = {
        funcNo: call.funcNo,
        param: {
          ...call.param,
          flowNo: `BATCH_${Date.now()}_${call.funcNo}`
        }
      };

      const result = this.pluginCenter.callPlugin(invokeOption);
      results.push(result);

      // 记录调用历史
      this.recordCall(call.funcNo, result);
    }

    return results;
  }

  // 条件插件调用
  async callPluginWithCondition(
    funcNo: string,
    param: Record<string, any>,
    condition: () => Promise<boolean>
  ): Promise<TKResultVO | null> {
    const shouldCall = await condition();

    if (shouldCall) {
      const invokeOption: TKPluginInvokeOption = {
        funcNo: funcNo,
        param: {
          ...param,
          flowNo: `CONDITIONAL_${Date.now()}`
        }
      };

      const result = this.pluginCenter.callPlugin(invokeOption);
      this.recordCall(funcNo, result);
      return result;
    } else {
      console.log(`条件不满足，跳过插件 ${funcNo} 的调用`);
      return null;
    }
  }

  // 获取调用历史
  getCallHistory(): Array<{
    funcNo: string,
    timestamp: number,
    result: TKResultVO
  }> {
    return [...this.callHistory];
  }

  // 获取调用统计
  getCallStatistics(): {
    totalCalls: number,
    successCalls: number,
    failedCalls: number,
    successRate: number
  } {
    const totalCalls = this.callHistory.length;
    const successCalls = this.callHistory.filter(call => call.result.errorNo === 0).length;
    const failedCalls = totalCalls - successCalls;
    const successRate = totalCalls > 0 ? (successCalls / totalCalls) * 100 : 0;

    return {
      totalCalls,
      successCalls,
      failedCalls,
      successRate
    };
  }

  // 清理调用历史
  clearHistory() {
    this.callHistory = [];
  }

  private recordCall(funcNo: string, result: TKResultVO) {
    this.callHistory.push({
      funcNo: funcNo,
      timestamp: Date.now(),
      result: result
    });

    // 限制历史记录长度
    if (this.callHistory.length > 100) {
      this.callHistory.shift();
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 插件功能封装器
class DeviceInfoPlugin {
  private callManager = new PluginCallManager();

  // 获取设备网络信息
  async getNetworkInfo(): Promise<{
    ip: string,
    localIp: string,
    mac: string,
    operator: string
  } | null> {
    try {
      const results = await this.callManager.callMultiplePlugins([
        { funcNo: "50023", param: {} }, // IP地址
        { funcNo: "50024", param: {} }, // MAC地址
        { funcNo: "50031", param: {} }  // 网络运营商
      ]);

      if (results.every(result => result.errorNo === 0)) {
        const ipData = results[0].results as Record<string, any>;
        const macData = results[1].results as Record<string, any>;
        const operatorData = results[2].results as Record<string, any>;

        return {
          ip: ipData.ip || '',
          localIp: ipData.lip || '',
          mac: macData.mac || '',
          operator: operatorData.phoneOperator || ''
        };
      } else {
        console.error('获取设备网络信息失败');
        return null;
      }
    } catch (error) {
      console.error('获取设备网络信息异常:', error);
      return null;
    }
  }

  // 检查手势密码状态
  async checkPatternLockStatus(account: string): Promise<boolean> {
    const result = await this.callManager.callPluginWithRetry(
      "50263",
      { account: account },
      2
    );

    if (result.errorNo === 0) {
      const data = result.results as Record<string, any>;
      return data.flag === "1";
    } else {
      console.error('检查手势密码状态失败:', result.errorInfo);
      return false;
    }
  }

  // 获取调用统计信息
  getStatistics() {
    return this.callManager.getCallStatistics();
  }
}

// 使用示例
const pluginCallManager = new PluginCallManager();
const deviceInfoPlugin = new DeviceInfoPlugin();

// 重试调用示例
async function testRetryCall() {
  const result = await pluginCallManager.callPluginWithRetry(
    "50023",
    { test: "retry" },
    3
  );

  console.log('重试调用结果:', result);
}

// 批量调用示例
async function testBatchCall() {
  const results = await pluginCallManager.callMultiplePlugins([
    { funcNo: "50023", param: { type: "ip" } },
    { funcNo: "50024", param: { type: "mac" } }
  ]);

  console.log('批量调用结果:', results);
}

// 条件调用示例
async function testConditionalCall() {
  const result = await pluginCallManager.callPluginWithCondition(
    "50031",
    { test: "conditional" },
    async () => {
      // 模拟条件检查
      return Math.random() > 0.5;
    }
  );

  console.log('条件调用结果:', result);
}

// 设备信息获取示例
async function testDeviceInfo() {
  const networkInfo = await deviceInfoPlugin.getNetworkInfo();
  console.log('设备网络信息:', networkInfo);

  const hasPatternLock = await deviceInfoPlugin.checkPatternLockStatus("testUser");
  console.log('是否设置手势密码:', hasPatternLock);

  const statistics = deviceInfoPlugin.getStatistics();
  console.log('调用统计:', statistics);
}

// 执行测试
testRetryCall();
testBatchCall();
testConditionalCall();
testDeviceInfo();
```

## 📚 相关资源

### 官方文档
- 插件架构设计文档
- XML配置文件规范

### 参考资料
- 《设计模式：可复用面向对象软件的基础》- 工厂模式和策略模式
- 《企业应用架构模式》- 插件架构

### 相关文件
- `TKBasePlugin.ets` - 插件基类定义
- `TKPluginIndex.ets` - 插件索引文件
- `SystemPlugin.xml` - 插件配置文件
