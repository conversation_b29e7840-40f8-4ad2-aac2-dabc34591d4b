# TKModuleMessage

> **文件路径**: `src/main/ets/base/engine/module/TKModuleMessage.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐⭐ (4星)  
> **学习难度**: 中级  
> **前置知识**: TypeScript类设计、枚举类型、回调函数、模块通信概念

## 📋 文件概述

### 功能定位
TKModuleMessage是HMThinkBaseHar框架**模块间通信的数据载体**，定义了消息传递的标准格式和行为类型。它包含消息对象类、动作枚举和回调函数类型，是模块通信系统的核心数据结构。

### 核心职责
- **消息数据封装**：封装模块间传递的所有信息
- **通信行为定义**：通过枚举定义不同的消息动作类型
- **回调机制支持**：提供异步回调函数类型定义
- **路由信息管理**：管理消息的来源、目标和排除规则
- **扩展信息承载**：支持业务参数和用户信息的传递

### 设计模式
- **数据传输对象模式(DTO)**：封装数据传输的结构
- **策略模式**：通过action枚举实现不同的处理策略
- **回调模式**：支持异步结果处理

## 🔧 核心功能

### 主要组件定义

#### 组件1：`TKModuleMessage`类
- **功能**: 模块间消息的数据载体
- **关键属性**: 
  - `funcNo: string` - 消息功能号，标识具体的业务功能
  - `sourceModule: string` - 消息来源模块名称
  - `targetModule: string` - 目标模块名称，支持"*"广播
  - `action: TKModuleMessageAction` - 消息动作类型
  - `param: Record<string, Object>` - 业务参数对象
  - `callBackFunc: TKModuleMessageCallBackFunc` - 回调函数
- **使用场景**: 所有模块间通信都使用此类作为消息载体

#### 组件2：`TKModuleMessageAction`枚举
- **功能**: 定义消息的行为类型
- **枚举值**: 
  - `Open = 0` - 打开模块（页面跳转）
  - `Push = 1` - 打开模块（兼容逻辑）
  - `Close = 2` - 关闭模块（页面返回）
  - `Change = 3` - 切换模块（状态变更）
  - `NSNotify = 4` - 通知模块（数据同步）
- **使用场景**: 根据不同的业务需求选择相应的动作类型

#### 组件3：`TKModuleMessageCallBackFunc`类型
- **功能**: 定义消息回调函数的类型
- **签名**: `(resultVO: TKResultVO) => void`
- **参数**: TKResultVO - 标准化的结果对象
- **使用场景**: 需要异步获取消息处理结果时使用

### 关键设计特点
- **类型安全**：使用TypeScript确保消息结构的类型安全
- **可扩展性**：支持自定义参数和用户信息扩展
- **标准化**：统一的消息格式和回调机制
- **灵活路由**：支持点对点、广播和排除式通信

## 💡 技术要点

### 核心设计理念

#### 消息结构设计
```typescript
export class TKModuleMessage {
  // 消息标识
  public funcNo?: string;              // 功能号
  public sourceModule?: string;        // 来源模块
  public targetModule?: string;        // 目标模块
  
  // 路由控制
  public excludeModules?: Array<string>; // 排除模块列表
  public action?: TKModuleMessageAction; // 动作类型
  
  // 数据载荷
  public param?: Record<string, Object>; // 业务参数
  public userInfo?: Object;             // 扩展信息
  
  // 回调机制
  public callBackFunc?: TKModuleMessageCallBackFunc; // 回调函数
  
  // 特殊控制
  public isGreen?: boolean = false;     // 绿色通道标识
  navComponentOrPathStack?: CustomComponent | NavPathStack; // 导航对象
}
```

#### 动作类型策略
```typescript
export enum TKModuleMessageAction {
  Open = 0,      // 页面跳转类消息
  Push = 1,      // 页面跳转类消息（兼容）
  Close = 2,     // 页面关闭类消息
  Change = 3,    // 状态变更类消息
  NSNotify = 4   // 通知广播类消息
}
```

### 实现机制分析

#### 消息创建和配置
```typescript
// 创建标准消息
const message = new TKModuleMessage();
message.funcNo = "getUserInfo";
message.sourceModule = "OrderModule";
message.targetModule = "UserModule";
message.action = TKModuleMessageAction.NSNotify;
message.param = { userId: "12345" };

// 设置回调处理结果
message.callBackFunc = (result: TKResultVO) => {
  if (result.errorNo === 0) {
    console.log("获取用户信息成功:", result.results);
  } else {
    console.error("获取用户信息失败:", result.errorInfo);
  }
};
```

#### 广播消息机制
```typescript
// 广播消息给所有模块
const broadcastMessage = new TKModuleMessage();
broadcastMessage.funcNo = "systemNotification";
broadcastMessage.sourceModule = "SystemModule";
broadcastMessage.targetModule = "*";  // 广播标识
broadcastMessage.action = TKModuleMessageAction.NSNotify;
broadcastMessage.excludeModules = ["LogModule", "DebugModule"]; // 排除特定模块
broadcastMessage.param = { 
  type: "maintenance", 
  message: "系统将在30分钟后维护" 
};
```

#### 绿色通道机制
```typescript
// 系统级消息使用绿色通道
const systemMessage = new TKModuleMessage();
systemMessage.funcNo = "emergencyShutdown";
systemMessage.sourceModule = "SystemModule";
systemMessage.targetModule = "*";
systemMessage.action = TKModuleMessageAction.NSNotify;
systemMessage.isGreen = true; // 绕过所有拦截器
systemMessage.param = { reason: "紧急维护" };
```

### 性能考虑
- **轻量级设计**：消息对象结构简洁，减少内存占用
- **可选属性**：大部分属性为可选，避免不必要的数据传输
- **类型优化**：使用Record<string, Object>提供灵活的参数结构
- **回调优化**：回调函数为可选，支持单向通信

### 错误处理
- **参数验证**：引擎会验证funcNo和targetModule的有效性
- **回调安全**：回调函数执行前会检查是否存在
- **类型检查**：TypeScript提供编译时类型安全保障
- **异常隔离**：消息处理异常不会影响其他消息

### 最佳实践
- **功能号规范**：使用有意义的功能号命名，便于理解和维护
- **参数结构化**：使用结构化的参数对象，避免参数混乱
- **回调处理**：合理使用回调函数，避免回调地狱
- **动作选择**：根据业务场景选择合适的动作类型

## 🔗 依赖关系

### 依赖的模块
- `TKResultVO` - 结果对象定义，回调函数的参数类型

### 被依赖情况
- `TKModuleEngine` - 使用消息对象进行消息分发
- `TKModuleDelegate` - 接口方法使用消息对象作为参数
- `各业务模块` - 创建和处理消息对象
- `TKAppEngine` - 通过消息对象进行模块通信

### 关联文件
- `TKModuleEngine.ets` - 消息引擎，消息对象的处理者
- `TKModuleDelegate.ets` - 模块接口，消息对象的使用者
- `TKResultVO.ets` - 结果对象，回调函数的参数类型

## 📊 重要程度评级

### 评级标准
⭐⭐⭐⭐ **重要推荐**: 常用功能，建议掌握

### 本文件评级理由
TKModuleMessage是模块通信系统的核心数据结构：
1. **通信基础**：所有模块间通信都依赖此消息格式
2. **标准化载体**：提供统一的数据传输格式
3. **功能完整**：支持多种通信模式和扩展需求
4. **类型安全**：通过TypeScript确保通信的类型安全

理解TKModuleMessage的结构和使用方式是进行模块开发的基础，是框架通信机制的核心组件。

## 🎯 学习建议

### 学习路径
1. **理解消息结构**：掌握消息对象的各个属性和作用
2. **学习动作类型**：理解不同动作类型的使用场景
3. **实践消息创建**：创建不同类型的消息对象
4. **掌握回调机制**：学习异步结果处理的方式

### 前置学习
- TypeScript类和接口设计
- 枚举类型的使用
- 回调函数和异步编程
- 模块化架构的基本概念

### 后续学习
- `TKModuleEngine.ets` - 了解消息的处理流程
- `TKResultVO.ets` - 学习结果对象的结构
- 各业务模块实现 - 查看消息的实际应用

### 实践建议
1. **创建简单消息**：练习创建基本的点对点消息
2. **广播消息实验**：尝试创建广播消息和排除机制
3. **回调函数练习**：实现异步消息处理
4. **动作类型测试**：测试不同动作类型的效果

### 常见问题
1. **问题**: funcNo和action的区别是什么？
   **解答**: funcNo标识具体的业务功能，action标识消息的处理方式。funcNo是业务层面的标识，action是技术层面的处理策略。

2. **问题**: 什么时候使用绿色通道？
   **解答**: 绿色通道用于系统级的紧急消息，需要绕过所有拦截器直接处理，如系统关闭、紧急通知等场景。

## 📝 代码示例

### 基础使用
```typescript
import { TKModuleMessage, TKModuleMessageAction, TKModuleEngine } from '@thinkive/tk-harmony-base';

// 创建基本的点对点消息
function sendUserInfoRequest() {
  const message = new TKModuleMessage();

  // 设置消息基本信息
  message.funcNo = "getUserInfo";
  message.sourceModule = "OrderModule";
  message.targetModule = "UserModule";
  message.action = TKModuleMessageAction.NSNotify;

  // 设置业务参数
  message.param = {
    userId: "12345",
    includeProfile: true,
    includePreferences: false
  };

  // 设置回调函数处理结果
  message.callBackFunc = (result) => {
    if (result.errorNo === 0) {
      const userInfo = result.results;
      console.log("用户信息获取成功:", userInfo);
      // 处理用户信息
      updateOrderUserInfo(userInfo);
    } else {
      console.error("用户信息获取失败:", result.errorInfo);
      // 处理错误情况
      showErrorMessage(result.errorInfo);
    }
  };

  // 发送消息
  TKModuleEngine.shareInstance().sendModuleMessage(message);
}

function updateOrderUserInfo(userInfo: any) {
  console.log("更新订单用户信息:", userInfo);
}

function showErrorMessage(error: string) {
  console.log("显示错误信息:", error);
}
```

### 高级用法
```typescript
// 广播消息示例
class NotificationModule {

  // 发送系统通知给所有模块
  sendSystemNotification(notificationType: string, message: string, excludeModules?: string[]) {
    const notification = new TKModuleMessage();

    notification.funcNo = "systemNotification";
    notification.sourceModule = "NotificationModule";
    notification.targetModule = "*"; // 广播给所有模块
    notification.action = TKModuleMessageAction.NSNotify;

    // 设置通知参数
    notification.param = {
      type: notificationType,
      message: message,
      timestamp: new Date().getTime(),
      priority: "normal"
    };

    // 设置排除模块（可选）
    if (excludeModules && excludeModules.length > 0) {
      notification.excludeModules = excludeModules;
    }

    // 发送广播消息
    TKModuleEngine.shareInstance().sendModuleMessage(notification);
  }

  // 发送页面跳转消息
  navigateToPage(targetPage: string, params?: Record<string, Object>) {
    const navigation = new TKModuleMessage();

    navigation.funcNo = "navigateToPage";
    navigation.sourceModule = "NotificationModule";
    navigation.targetModule = "RouterModule";
    navigation.action = TKModuleMessageAction.Open; // 使用Open动作

    navigation.param = {
      targetPage: targetPage,
      params: params || {},
      animation: "slide"
    };

    // 页面跳转通常不需要回调
    TKModuleEngine.shareInstance().sendModuleMessage(navigation);
  }

  // 发送页面关闭消息
  closePage(pageId?: string) {
    const closeMessage = new TKModuleMessage();

    closeMessage.funcNo = "closePage";
    closeMessage.sourceModule = "NotificationModule";
    closeMessage.targetModule = "RouterModule";
    closeMessage.action = TKModuleMessageAction.Close; // 使用Close动作

    if (pageId) {
      closeMessage.param = { pageId: pageId };
    }

    TKModuleEngine.shareInstance().sendModuleMessage(closeMessage);
  }
}
```

### 扩展示例
```typescript
// 消息工厂类，简化消息创建
class MessageFactory {

  // 创建标准的请求消息
  static createRequest(
    funcNo: string,
    sourceModule: string,
    targetModule: string,
    params?: Record<string, Object>,
    callback?: (result: any) => void
  ): TKModuleMessage {
    const message = new TKModuleMessage();

    message.funcNo = funcNo;
    message.sourceModule = sourceModule;
    message.targetModule = targetModule;
    message.action = TKModuleMessageAction.NSNotify;
    message.param = params || {};

    if (callback) {
      message.callBackFunc = callback;
    }

    return message;
  }

  // 创建广播通知消息
  static createBroadcast(
    funcNo: string,
    sourceModule: string,
    params?: Record<string, Object>,
    excludeModules?: string[]
  ): TKModuleMessage {
    const message = new TKModuleMessage();

    message.funcNo = funcNo;
    message.sourceModule = sourceModule;
    message.targetModule = "*";
    message.action = TKModuleMessageAction.NSNotify;
    message.param = params || {};

    if (excludeModules) {
      message.excludeModules = excludeModules;
    }

    return message;
  }

  // 创建页面导航消息
  static createNavigation(
    sourceModule: string,
    targetPage: string,
    params?: Record<string, Object>,
    isClose: boolean = false
  ): TKModuleMessage {
    const message = new TKModuleMessage();

    message.funcNo = isClose ? "closePage" : "openPage";
    message.sourceModule = sourceModule;
    message.targetModule = "RouterModule";
    message.action = isClose ? TKModuleMessageAction.Close : TKModuleMessageAction.Open;

    message.param = {
      targetPage: targetPage,
      params: params || {}
    };

    return message;
  }

  // 创建系统级绿色通道消息
  static createSystemMessage(
    funcNo: string,
    sourceModule: string,
    params?: Record<string, Object>
  ): TKModuleMessage {
    const message = new TKModuleMessage();

    message.funcNo = funcNo;
    message.sourceModule = sourceModule;
    message.targetModule = "*";
    message.action = TKModuleMessageAction.NSNotify;
    message.param = params || {};
    message.isGreen = true; // 绿色通道

    return message;
  }
}

// 使用消息工厂的示例
class UserModule {
  private moduleEngine = TKModuleEngine.shareInstance();

  // 请求订单信息
  requestOrderInfo(orderId: string) {
    const message = MessageFactory.createRequest(
      "getOrderInfo",
      "UserModule",
      "OrderModule",
      { orderId: orderId },
      (result) => {
        if (result.errorNo === 0) {
          console.log("订单信息:", result.results);
        }
      }
    );

    this.moduleEngine.sendModuleMessage(message);
  }

  // 广播用户状态变更
  broadcastUserStatusChange(userId: string, newStatus: string) {
    const message = MessageFactory.createBroadcast(
      "userStatusChanged",
      "UserModule",
      { userId: userId, status: newStatus },
      ["LogModule"] // 排除日志模块
    );

    this.moduleEngine.sendModuleMessage(message);
  }

  // 导航到用户详情页
  navigateToUserDetail(userId: string) {
    const message = MessageFactory.createNavigation(
      "UserModule",
      "UserDetailPage",
      { userId: userId }
    );

    this.moduleEngine.sendModuleMessage(message);
  }

  // 发送系统紧急通知
  sendEmergencyNotification(message: string) {
    const systemMessage = MessageFactory.createSystemMessage(
      "emergencyNotification",
      "UserModule",
      { message: message, level: "critical" }
    );

    this.moduleEngine.sendModuleMessage(systemMessage);
  }
}
```

## 📚 相关资源

### 官方文档
- TypeScript官方文档 - 类和枚举
- HarmonyOS开发指南 - 模块化开发

### 参考资料
- 《JavaScript设计模式与开发实践》- 数据传输对象模式
- 《企业应用架构模式》- 消息传递模式

### 相关文件
- `核心架构-TKModuleEngine-解释文档.md` - 消息处理引擎
- `基础服务-TKModuleDelegate-解释文档.md` - 模块接口规范
- `TKResultVO.ets` - 回调结果对象定义
