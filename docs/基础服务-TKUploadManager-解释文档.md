# TKUploadManager

> **文件路径**: `src/main/ets/base/network/upload/TKUploadManager.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 文件上传原理、断点续传、分块上传、MD5校验、缓存管理

## 📋 文件概述

### 功能定位
TKUploadManager是HMThinkBaseHar框架的**断点续传上传管理器**，实现了企业级的文件上传功能。它采用单例模式设计，支持分块上传、断点续传、进度监控、MD5校验等高级功能，为大文件上传提供稳定可靠的解决方案。

### 核心职责
- **断点续传上传**：支持大文件的分块上传和断点续传功能
- **文件完整性校验**：使用MD5校验确保文件传输的完整性
- **上传进度监控**：实时监控上传进度并提供回调通知
- **缓存状态管理**：缓存上传状态，支持应用重启后继续上传
- **多种上传方式**：支持文件路径上传和二进制数据上传

### 设计模式
- **单例模式**：确保全局唯一的上传管理器实例
- **分块策略模式**：将大文件分块处理，提高上传效率和稳定性
- **状态缓存模式**：缓存上传状态，实现断点续传

## 🔧 核心功能

### 主要API/方法

#### 方法1：`shareInstance()`
- **功能**: 获取TKUploadManager的单例实例
- **参数**: 无
- **返回值**: TKUploadManager实例
- **使用场景**: 任何需要进行文件上传的地方

#### 方法2：`initContext(appKey: string)`
- **功能**: 初始化上传管理器的应用密钥
- **参数**: string - 应用授权密钥
- **返回值**: void
- **使用场景**: 应用启动时初始化上传功能

#### 方法3：`uploadFile(uploadUrl, filePath, callBackFunc, uploadProgressFunc)`
- **功能**: 根据文件路径上传文件
- **参数**: 
  - uploadUrl - 上传服务器地址
  - filePath - 文件本地路径
  - callBackFunc - 上传结果回调
  - uploadProgressFunc - 上传进度回调（可选）
- **返回值**: Promise<void>
- **使用场景**: 上传本地文件时使用

#### 方法4：`uploadFileData(uploadUrl, fileData, fileName, callBackFunc, uploadProgressFunc)`
- **功能**: 上传二进制文件数据
- **参数**: 
  - uploadUrl - 上传服务器地址
  - fileData - 二进制文件数据
  - fileName - 文件名称
  - callBackFunc - 上传结果回调
  - uploadProgressFunc - 上传进度回调（可选）
- **返回值**: Promise<void>
- **使用场景**: 上传内存中的文件数据时使用

### 关键私有方法

#### 方法1：`uploadChunkFileData(uploadUrl, fileData, fileName, uploadInfo, callBackFunc, uploadProgressFunc)`
- **功能**: 分块上传文件数据的核心实现
- **处理内容**: 分块处理、断点续传、进度计算

### 关键属性/配置
- `TKUploadBlockSize: number` - 上传块大小（1MB）
- `TKUploadCacheFileName: string` - 上传缓存文件名
- `appKey: string` - 应用授权密钥

## 💡 技术要点

### 核心算法/逻辑

#### 断点续传实现流程
```typescript
public async uploadFileData(uploadUrl: string, fileData: Uint8Array, fileName: string,
  callBackFunc: (result: Record<string, Object>) => void, uploadProgressFunc?: (progress: TKLoadInfoVO) => void) {
  
  // 1. 计算文件MD5，用于断点续传标识
  let fileMD5: string = await TKMd5Helper.stringWithMd5(fileData);
  
  // 2. 从缓存中获取上传状态
  let uploadInfo: Record<string, Object> | undefined = 
    TKCacheManager.shareInstance().getFileCacheData(fileMD5, TKUploadManager.TKUploadCacheFileName);
  
  // 3. 验证缓存状态的有效性
  if (uploadInfo && Object.entries(uploadInfo).length > 0) {
    let sequenceNo: number = TKMapHelper.getNumber(uploadInfo, "sequenceNo");
    let totalBlock: number = TKMapHelper.getNumber(uploadInfo, "totalBlock");
    if (sequenceNo < 1 || totalBlock < 1) {
      uploadInfo = undefined; // 无效缓存，重新开始
    }
  }
  
  // 4. 初始化上传信息
  if (!uploadInfo || Object.entries(uploadInfo).length == 0) {
    uploadInfo = {
      fileMD5: fileMD5,
      fileSize: fileData.length,
      uploadSize: 0,
      totalBlock: Math.ceil(fileData.length / TKUploadManager.TKUploadBlockSize),
      sequenceNo: 1
    };
    // 保存到缓存
    TKCacheManager.shareInstance()
      .saveFileCacheData(fileMD5, uploadInfo, 0, false, TKUploadManager.TKUploadCacheFileName);
  }
  
  // 5. 开始分块上传
  await this.uploadChunkFileData(uploadUrl, fileData, fileName, uploadInfo, callBackFunc, uploadProgressFunc);
}
```

#### 分块上传核心逻辑
```typescript
private async uploadChunkFileData(uploadUrl: string, fileData: Uint8Array, fileName: string,
  uploadInfo: Record<string, Object>, callBackFunc: (result: Record<string, Object>) => void,
  uploadProgressFunc?: (progress: TKLoadInfoVO) => void) {
  
  // 1. 获取当前上传状态
  let fileMD5: string = TKMapHelper.getString(uploadInfo, "fileMD5");
  let sequenceNo: number = TKMapHelper.getNumber(uploadInfo, "sequenceNo");
  let totalBlock: number = TKMapHelper.getNumber(uploadInfo, "totalBlock");
  
  // 2. 计算当前块的位置和大小
  let blockBegin: number = ((sequenceNo - 1) * TKUploadManager.TKUploadBlockSize);
  let blockSize: number = Math.min(
    TKUploadManager.TKUploadBlockSize, 
    fileData.length - blockBegin
  );
  let blockData: Uint8Array = fileData.subarray(blockBegin, blockBegin + blockSize);
  
  // 3. 构建文件令牌和请求数据
  let fileTokenStr: string = 
    `"appKey=${this.appKey}fileName=${fileName}fileMD5=${fileMD5}totalBlock=${totalBlock}sequenceNo=${sequenceNo}blockData=`;
  let fileTokenData: Uint8Array = TKDataHelper.stringToUint8Array(fileTokenStr);
  
  // 4. 组装上传数据
  let writeDV: TKWriteDataView = new TKWriteDataView(fileTokenData.length + blockData.length);
  writeDV.putBytes(fileTokenData);
  writeDV.putBytes(blockData);
  
  // 5. 构建请求参数
  let reqParam: Record<string, Object> = {
    fileName: fileName,
    fileMD5: fileMD5,
    totalBlock: totalBlock,
    sequenceNo: sequenceNo,
    "blockData@@F": blockData,
    file_extension: fileName.split(".").pop() ?? "",
    fileToken: await TKMd5Helper.stringWithMd5(new Uint8Array(writeDV.buffer))
  };
  
  // 6. 发送上传请求
  this.service.serviceInvoke(reqParamVO, this.handleUploadResponse);
}
```

#### 上传响应处理
```typescript
// 处理上传响应的核心逻辑
this.service.serviceInvoke(reqParamVO, (resultVO: TKResultVO) => {
  if (resultVO.errorNo == 0) {
    let data: Record<string, Object> = resultVO.results as Record<string, Object>;
    let nextSequenceNo: number = TKMapHelper.getNumber(data, "nextSequenceNo");
    let filePath: string = TKMapHelper.getString(data, "filePath");
    
    if (nextSequenceNo == -1) {
      // 上传完成
      TKCacheManager.shareInstance().deleteFileCacheData(fileMD5, TKUploadManager.TKUploadCacheFileName);
      
      // 最终进度回调
      if (uploadProgressFunc) {
        let loadInfo: TKLoadInfoVO = new TKLoadInfoVO();
        loadInfo.bytesTotal = fileSize;
        loadInfo.bytesLoaded = fileSize;
        uploadProgressFunc(loadInfo);
      }
      
      // 成功回调
      callBackFunc({
        code: resultVO.errorNo,
        msg: resultVO.errorInfo,
        data: filePath
      });
    } else {
      // 继续上传下一块
      uploadInfo["uploadSize"] = ((nextSequenceNo - 1) * TKUploadManager.TKUploadBlockSize) + blockData.length;
      uploadInfo["sequenceNo"] = nextSequenceNo;
      
      // 更新缓存
      TKCacheManager.shareInstance()
        .saveFileCacheData(fileMD5, uploadInfo, 0, false, TKUploadManager.TKUploadCacheFileName);
      
      // 递归上传下一块
      this.uploadChunkFileData(uploadUrl, fileData, fileName, uploadInfo, callBackFunc, uploadProgressFunc);
    }
  } else {
    // 上传失败
    callBackFunc({
      code: resultVO.errorNo,
      msg: resultVO.errorInfo
    });
  }
});
```

### 实现机制分析

#### 断点续传机制
- **MD5标识**：使用文件MD5作为唯一标识，支持断点续传
- **状态缓存**：将上传状态保存到本地缓存，应用重启后可继续
- **块序号管理**：通过sequenceNo管理上传进度，支持从任意块继续
- **完整性校验**：每个块都有MD5校验，确保数据完整性

#### 分块上传策略
- **固定块大小**：每块1MB，平衡传输效率和内存占用
- **顺序上传**：按序号顺序上传，确保服务端正确组装
- **递归处理**：上传完一块后递归处理下一块
- **进度计算**：实时计算上传进度并回调

#### 错误处理机制
- **网络异常**：上传失败时保留状态，支持重新上传
- **数据校验**：通过MD5校验确保数据完整性
- **状态恢复**：从缓存中恢复上传状态，继续未完成的上传

### 性能考虑
- **内存优化**：分块处理避免大文件占用过多内存
- **网络优化**：1MB块大小平衡传输效率和稳定性
- **缓存管理**：及时清理完成的上传缓存，避免存储浪费
- **异步处理**：所有上传操作都是异步的，不阻塞主线程

### 错误处理
- **文件验证**：上传前验证文件是否存在
- **参数校验**：验证上传URL、文件名等必要参数
- **网络异常**：捕获网络异常并提供错误回调
- **状态一致性**：确保缓存状态与实际上传状态一致

### 最佳实践
- **初始化配置**：使用前必须调用initContext设置appKey
- **进度监控**：合理使用进度回调，提供用户友好的上传体验
- **错误处理**：完善的错误处理机制，确保应用稳定性
- **资源管理**：上传完成后及时清理缓存和临时数据

## 🔗 依赖关系

### 依赖的模块
- `TKCacheManager` - 缓存管理器，存储上传状态
- `TKMd5Helper` - MD5工具，文件校验和标识生成
- `TKFileHelper` - 文件操作工具，文件读取和验证
- `TKCommonService` - 通用服务，HTTP请求发送
- `TKLoadInfoVO` - 加载信息对象，进度数据载体

### 被依赖情况
- `业务模块` - 各种需要文件上传功能的业务场景
- `媒体上传` - 图片、视频等媒体文件的上传
- `文档上传` - 文档、附件等文件的上传

### 关联文件
- `TKDownLoadManager.ets` - 下载管理器，对应的下载功能
- `TKCacheManager.ets` - 缓存管理器，状态存储
- `TKCommonService.ets` - 通用服务，网络请求

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKUploadManager是框架的文件上传功能实现：
1. **功能专一**：专门处理文件上传，功能相对独立
2. **使用场景**：主要用于大文件上传、媒体上传等特定场景
3. **技术复杂**：实现了断点续传等高级功能，技术含量较高
4. **扩展性**：提供了完整的上传解决方案，可根据需要扩展

理解TKUploadManager有助于掌握框架的文件上传机制，特别是在需要实现大文件上传功能时。

## 🎯 学习建议

### 学习路径
1. **理解上传基础**：掌握HTTP文件上传的基本原理
2. **分析分块机制**：理解分块上传和断点续传的实现原理
3. **学习状态管理**：掌握上传状态的缓存和恢复机制
4. **实践上传功能**：使用上传管理器实现实际的文件上传

### 前置学习
- HTTP文件上传原理
- 分块传输和断点续传概念
- MD5校验和文件完整性
- 缓存管理基础知识

### 后续学习
- `TKDownLoadManager.ets` - 了解对应的下载功能实现
- `TKCacheManager.ets` - 学习缓存管理的使用
- `TKCommonService.ets` - 掌握网络服务的使用

### 实践建议
1. **基础上传练习**：使用上传管理器上传小文件
2. **大文件上传实验**：测试大文件的分块上传和断点续传
3. **进度监控测试**：观察上传进度的变化和回调
4. **异常处理验证**：模拟网络中断，测试断点续传功能

### 常见问题
1. **问题**: 如何实现断点续传？
   **解答**: 通过文件MD5标识和缓存上传状态，应用重启后可以从缓存中恢复上传进度，继续未完成的上传。

2. **问题**: 上传块大小如何选择？
   **解答**: 框架默认使用1MB块大小，这是在传输效率、内存占用和网络稳定性之间的平衡选择，可根据实际需求调整。

## 📝 代码示例

### 基础使用
```typescript
import { TKUploadManager, TKLoadInfoVO } from '@thinkive/tk-harmony-base';

// 获取上传管理器实例并初始化
const uploadManager = TKUploadManager.shareInstance();
uploadManager.initContext("your-app-key");

// 上传本地文件
function uploadLocalFile() {
  const uploadUrl = "https://api.example.com/upload";
  const filePath = "/data/storage/el2/base/haps/entry/files/document.pdf";

  uploadManager.uploadFile(
    uploadUrl,
    filePath,
    (result) => {
      // 上传结果回调
      if (result.code === 0) {
        console.log('上传成功，文件路径:', result.data);
      } else {
        console.error('上传失败:', result.msg);
      }
    },
    (progress: TKLoadInfoVO) => {
      // 上传进度回调
      const percent = (progress.progress * 100).toFixed(2);
      console.log(`上传进度: ${percent}%`);
      console.log(`已上传: ${progress.bytesLoaded} / 总大小: ${progress.bytesTotal}`);
    }
  );
}

// 上传二进制数据
function uploadBinaryData() {
  const uploadUrl = "https://api.example.com/upload";
  const fileName = "image.jpg";

  // 假设已经获取到文件的二进制数据
  const fileData: Uint8Array = new Uint8Array([/* 文件数据 */]);

  uploadManager.uploadFileData(
    uploadUrl,
    fileData,
    fileName,
    (result) => {
      console.log('上传结果:', result);
    },
    (progress: TKLoadInfoVO) => {
      console.log(`上传进度: ${(progress.progress * 100).toFixed(1)}%`);
    }
  );
}

uploadLocalFile();
```

### 高级用法
```typescript
// 上传管理器包装类，提供更多功能
class AdvancedUploadManager {
  private uploadManager = TKUploadManager.shareInstance();
  private activeUploads: Map<string, {
    fileName: string,
    startTime: number,
    totalSize: number,
    uploadedSize: number
  }> = new Map();

  constructor(appKey: string) {
    this.uploadManager.initContext(appKey);
  }

  // 带重试机制的上传
  uploadWithRetry(
    uploadUrl: string,
    filePath: string,
    maxRetries: number = 3
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      let retryCount = 0;

      const attemptUpload = () => {
        const uploadId = `UPLOAD_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        this.uploadManager.uploadFile(
          uploadUrl,
          filePath,
          (result) => {
            if (result.code === 0) {
              this.activeUploads.delete(uploadId);
              resolve(result.data as string);
            } else {
              retryCount++;
              if (retryCount <= maxRetries) {
                console.log(`上传失败，进行第 ${retryCount} 次重试...`);
                setTimeout(attemptUpload, 1000 * retryCount); // 指数退避
              } else {
                this.activeUploads.delete(uploadId);
                reject(new Error(`上传失败，已重试 ${maxRetries} 次: ${result.msg}`));
              }
            }
          },
          (progress) => {
            const uploadInfo = this.activeUploads.get(uploadId);
            if (uploadInfo) {
              uploadInfo.uploadedSize = progress.bytesLoaded;
              uploadInfo.totalSize = progress.bytesTotal;
            }

            console.log(`重试上传进度: ${(progress.progress * 100).toFixed(1)}%`);
          }
        );

        // 记录上传信息
        this.activeUploads.set(uploadId, {
          fileName: filePath.split('/').pop() || 'unknown',
          startTime: Date.now(),
          totalSize: 0,
          uploadedSize: 0
        });
      };

      attemptUpload();
    });
  }

  // 批量上传
  async uploadMultiple(
    uploadUrl: string,
    filePaths: string[],
    onProgress?: (completed: number, total: number, currentFile: string) => void
  ): Promise<string[]> {
    const results: string[] = [];
    let completed = 0;

    for (const filePath of filePaths) {
      try {
        if (onProgress) {
          onProgress(completed, filePaths.length, filePath);
        }

        const result = await this.uploadWithRetry(uploadUrl, filePath);
        results.push(result);
        completed++;

        console.log(`完成上传 ${completed}/${filePaths.length}: ${filePath}`);
      } catch (error) {
        console.error(`上传失败 ${filePath}:`, error.message);
        results.push(''); // 失败的上传用空字符串占位
      }
    }

    return results;
  }

  // 获取上传统计信息
  getUploadStats(): {
    activeCount: number,
    totalUploaded: number,
    totalSize: number,
    averageSpeed: number
  } {
    let totalUploaded = 0;
    let totalSize = 0;
    let totalTime = 0;
    const now = Date.now();

    for (const [id, info] of this.activeUploads) {
      totalUploaded += info.uploadedSize;
      totalSize += info.totalSize;
      totalTime += now - info.startTime;
    }

    const averageSpeed = totalTime > 0 ? (totalUploaded / (totalTime / 1000)) : 0;

    return {
      activeCount: this.activeUploads.size,
      totalUploaded,
      totalSize,
      averageSpeed
    };
  }

  // 格式化上传速度
  private formatSpeed(bytesPerSecond: number): string {
    if (bytesPerSecond < 1024) {
      return `${bytesPerSecond.toFixed(0)} B/s`;
    } else if (bytesPerSecond < 1024 * 1024) {
      return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
    } else {
      return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
    }
  }
}
```

### 扩展示例
```typescript
// 上传队列管理器
class UploadQueueManager {
  private uploadManager = TKUploadManager.shareInstance();
  private queue: Array<{
    uploadUrl: string,
    filePath: string,
    callback: (result: any) => void,
    progressCallback?: (progress: TKLoadInfoVO) => void,
    priority: number
  }> = [];
  private isProcessing = false;
  private maxConcurrent = 2; // 最大并发上传数
  private activeCount = 0;

  constructor(appKey: string) {
    this.uploadManager.initContext(appKey);
  }

  // 添加上传到队列
  addToQueue(
    uploadUrl: string,
    filePath: string,
    callback: (result: any) => void,
    progressCallback?: (progress: TKLoadInfoVO) => void,
    priority: number = 0
  ) {
    this.queue.push({
      uploadUrl,
      filePath,
      callback,
      progressCallback,
      priority
    });

    // 按优先级排序
    this.queue.sort((a, b) => b.priority - a.priority);

    // 开始处理队列
    this.processQueue();
  }

  // 处理上传队列
  private async processQueue() {
    if (this.isProcessing || this.activeCount >= this.maxConcurrent) {
      return;
    }

    if (this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0 && this.activeCount < this.maxConcurrent) {
      const item = this.queue.shift();
      if (item) {
        this.activeCount++;
        this.processUpload(item);
      }
    }

    this.isProcessing = false;
  }

  // 处理单个上传
  private processUpload(item: {
    uploadUrl: string,
    filePath: string,
    callback: (result: any) => void,
    progressCallback?: (progress: TKLoadInfoVO) => void,
    priority: number
  }) {
    const wrappedCallback = (result: any) => {
      // 调用原始回调
      item.callback(result);

      // 减少活跃计数并继续处理队列
      this.activeCount--;
      this.processQueue();
    };

    this.uploadManager.uploadFile(
      item.uploadUrl,
      item.filePath,
      wrappedCallback,
      item.progressCallback
    );
  }

  // 获取队列状态
  getQueueStatus(): {
    waiting: number,
    active: number,
    maxConcurrent: number
  } {
    return {
      waiting: this.queue.length,
      active: this.activeCount,
      maxConcurrent: this.maxConcurrent
    };
  }

  // 设置最大并发数
  setMaxConcurrent(max: number) {
    this.maxConcurrent = max;
    this.processQueue();
  }

  // 清空队列
  clearQueue() {
    this.queue = [];
  }
}

// 使用示例
const advancedManager = new AdvancedUploadManager("your-app-key");
const queueManager = new UploadQueueManager("your-app-key");

// 重试上传
async function uploadWithRetry() {
  try {
    const result = await advancedManager.uploadWithRetry(
      "https://api.example.com/upload",
      "/path/to/large-file.zip",
      3 // 最多重试3次
    );

    console.log('上传成功:', result);
  } catch (error) {
    console.error('上传失败:', error.message);
  }
}

// 批量上传
async function uploadMultipleFiles() {
  const files = [
    "/path/to/file1.pdf",
    "/path/to/file2.jpg",
    "/path/to/file3.mp4"
  ];

  try {
    const results = await advancedManager.uploadMultiple(
      "https://api.example.com/upload",
      files,
      (completed, total, currentFile) => {
        console.log(`批量上传进度: ${completed}/${total}, 当前文件: ${currentFile}`);
      }
    );

    console.log('批量上传完成:', results);
  } catch (error) {
    console.error('批量上传失败:', error);
  }
}

// 队列上传
function uploadWithQueue() {
  const files = [
    { path: "/path/to/high-priority.pdf", priority: 10 },
    { path: "/path/to/normal-file1.jpg", priority: 5 },
    { path: "/path/to/normal-file2.mp3", priority: 5 },
    { path: "/path/to/low-priority.zip", priority: 1 }
  ];

  files.forEach(file => {
    queueManager.addToQueue(
      "https://api.example.com/upload",
      file.path,
      (result) => {
        console.log(`队列上传完成: ${file.path}, 结果:`, result);
      },
      (progress) => {
        console.log(`${file.path} 上传进度: ${(progress.progress * 100).toFixed(1)}%`);
      },
      file.priority
    );
  });

  console.log('队列状态:', queueManager.getQueueStatus());
}

// 执行示例
uploadWithRetry();
uploadMultipleFiles();
uploadWithQueue();
```

## 📚 相关资源

### 官方文档
- HTTP文件上传规范
- 分块传输编码标准

### 参考资料
- 《HTTP权威指南》- 文件上传原理
- 《大型网站技术架构》- 大文件处理策略

### 相关文件
- `基础服务-TKDownLoadManager-解释文档.md` - 文件下载管理器
- `TKCacheManager.ets` - 缓存管理器
- `TKCommonService.ets` - 通用网络服务
