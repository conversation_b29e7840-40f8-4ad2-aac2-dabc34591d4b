# TKDaoFactory

> **文件路径**: `src/main/ets/base/mvc/service/dao/TKDaoFactory.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐⭐ (4星)  
> **学习难度**: 中级  
> **前置知识**: 工厂模式、命名空间、反射机制、DAO模式

## 📋 文件概述

### 功能定位
TKDaoFactory是HMThinkBaseHar框架的**DAO工厂**，采用工厂模式设计，负责创建和管理不同类型的数据访问对象。它使用命名空间组织功能，支持内置DAO类型的创建和自定义DAO类的注册，为框架提供统一的数据访问对象管理能力。

### 核心职责
- **DAO实例创建**：根据类型创建相应的DAO实例
- **实例缓存管理**：缓存已创建的DAO实例，避免重复创建
- **自定义DAO注册**：支持注册自定义的DAO实现类
- **类型映射管理**：维护DAO类型与实现类的映射关系
- **单例保证**：确保相同类型的DAO只有一个实例

### 设计模式
- **工厂模式**：根据类型参数创建相应的产品对象
- **单例模式**：确保每种类型的DAO只有一个实例
- **注册模式**：支持动态注册新的DAO实现类
- **命名空间模式**：使用namespace组织相关功能

## 🔧 核心功能

### 主要API/方法

#### 方法1：`getDao(daoType: TKDaoType | string)`
- **功能**: 根据类型获取DAO实例，工厂的核心方法
- **参数**: TKDaoType枚举或字符串类型标识
- **返回值**: TKServiceDaoDelegate实例或undefined
- **使用场景**: 需要获取数据访问对象时的统一入口

#### 方法2：`registerDao(daoClass: Function)`
- **功能**: 注册自定义DAO实现类
- **参数**: Function - DAO类的构造函数
- **返回值**: void
- **使用场景**: 需要扩展框架，添加自定义数据访问实现时

### 关键属性/配置
- `daoClassMap: Map<string, FunctionConstructor>` - DAO类定义映射表
- `daoMap: Map<string, TKServiceDaoDelegate>` - DAO实例缓存映射表

## 💡 技术要点

### 核心算法/逻辑

#### 工厂方法实现
```typescript
export function getDao(daoType: TKDaoType | string): TKServiceDaoDelegate | undefined {
  let dao: TKServiceDaoDelegate | undefined = undefined;
  
  if (typeof daoType === "string") {
    // 1. 处理字符串类型（自定义DAO）
    dao = daoMap.get(daoType);
    if (!dao) {
      // 从类定义映射中获取构造函数
      let daoClass: FunctionConstructor | undefined = daoClassMap.get(daoType);
      if (daoClass) {
        // 创建实例
        let instance = new daoClass();
        if (instance instanceof TKBaseDao) {
          dao = instance as TKServiceDaoDelegate;
        }
        // 缓存实例
        daoMap.set(daoType, dao!);
      }
    }
  } else {
    // 2. 处理枚举类型（内置DAO）
    switch (daoType) {
      case TKDaoType.Http: {
        dao = TKHttpDao.shareInstance();
        break;
      }
      default:
        dao = TKBusDao.shareInstance();
        break;
    }
  }
  
  return dao;
}
```

#### 注册机制实现
```typescript
export function registerDao(daoClass: Function) {
  // 使用类名作为键，存储构造函数
  daoClassMap.set(daoClass.name, daoClass as FunctionConstructor);
}
```

### 实现机制分析

#### 双重映射策略
- **类定义映射**：daoClassMap存储类名到构造函数的映射
- **实例缓存映射**：daoMap存储类名到实例的映射
- **懒加载创建**：只有在首次请求时才创建实例
- **实例复用**：后续请求直接返回缓存的实例

#### 类型处理策略
1. **枚举类型处理**：直接调用对应DAO的单例方法
2. **字符串类型处理**：通过映射表查找并创建自定义DAO
3. **实例验证**：确保创建的实例继承自TKBaseDao
4. **缓存管理**：自动缓存创建的实例，避免重复创建

#### 命名空间设计
- **功能封装**：使用namespace封装相关功能
- **全局访问**：提供全局可访问的工厂方法
- **状态隔离**：内部状态与外部隔离，保证安全性

### 性能考虑
- **实例缓存**：避免重复创建相同类型的DAO实例
- **懒加载**：只在需要时创建实例，减少初始化开销
- **类型检查**：编译时类型检查，运行时实例验证
- **内存管理**：通过Map管理实例，支持垃圾回收

### 错误处理
- **类型验证**：确保注册的类继承自TKBaseDao
- **空值处理**：未找到对应DAO时返回undefined
- **实例检查**：创建实例后验证类型正确性
- **异常隔离**：单个DAO创建失败不影响其他DAO

### 最佳实践
- **统一入口**：通过工厂方法获取DAO，避免直接实例化
- **类型安全**：使用枚举类型确保类型安全
- **扩展性**：通过注册机制支持自定义DAO扩展
- **资源管理**：合理利用实例缓存，避免资源浪费

## 🔗 依赖关系

### 依赖的模块
- `TKDaoType` - DAO类型枚举，定义内置DAO类型
- `TKServiceDaoDelegate` - DAO接口定义，规范DAO实现
- `TKHttpDao` - HTTP数据访问实现，内置DAO类型
- `TKBusDao` - Socket数据访问实现，内置DAO类型
- `TKBaseDao` - DAO抽象基类，验证实例类型

### 被依赖情况
- `业务服务类` - 通过工厂获取DAO实例进行数据访问
- `框架初始化模块` - 注册自定义DAO实现
- `数据访问层` - 统一的DAO实例获取入口

### 关联文件
- `TKHttpDao.ets` - HTTP数据访问实现，工厂创建的产品
- `TKBusDao.ets` - Socket数据访问实现，工厂创建的产品
- `TKBaseDao.ets` - DAO抽象基类，产品的基类
- `TKServiceDaoDelegate.ets` - DAO接口定义，产品的接口

## 📊 重要程度评级

### 评级标准
⭐⭐⭐⭐ **重要推荐**: 常用功能，建议掌握

### 本文件评级理由
TKDaoFactory是数据访问层的管理核心：
1. **统一管理**：提供DAO实例的统一创建和管理入口
2. **扩展支持**：支持自定义DAO的注册和使用
3. **性能优化**：通过实例缓存避免重复创建的开销
4. **设计规范**：体现了工厂模式的经典应用

理解TKDaoFactory是掌握框架数据访问管理机制的关键，它是DAO模式实现的重要组件。

## 🎯 学习建议

### 学习路径
1. **理解工厂模式**：掌握工厂模式的设计思想和应用场景
2. **分析实现机制**：理解双重映射和懒加载的实现方式
3. **学习扩展方法**：掌握如何注册和使用自定义DAO
4. **实践工厂使用**：在实际项目中使用工厂获取DAO实例

### 前置学习
- 工厂模式的基本概念和实现
- TypeScript命名空间的使用
- DAO模式的设计原理
- 反射和动态实例化机制

### 后续学习
- `TKHttpDao.ets` - 了解HTTP DAO的具体实现
- `TKBusDao.ets` - 学习Socket DAO的实现方式
- `TKBaseDao.ets` - 掌握DAO基类的设计
- 各业务DAO实现 - 查看实际的DAO应用

### 实践建议
1. **基础使用练习**：通过工厂获取不同类型的DAO实例
2. **自定义DAO开发**：创建自定义DAO并注册到工厂
3. **扩展机制实验**：测试工厂的扩展和缓存机制
4. **性能分析**：分析工厂模式对性能的影响

### 常见问题
1. **问题**: 为什么使用工厂模式而不是直接实例化？
   **解答**: 工厂模式提供了统一的创建接口，支持实例缓存和类型管理，便于扩展和维护，同时隐藏了具体的创建逻辑。

2. **问题**: 自定义DAO如何与框架集成？
   **解答**: 自定义DAO需要继承TKBaseDao并实现TKServiceDaoDelegate接口，然后通过registerDao方法注册到工厂，即可通过getDao方法获取实例。

## 📝 代码示例

### 基础使用
```typescript
import { TKDaoFactory, TKDaoType, TKServiceDaoDelegate } from '@thinkive/tk-harmony-base';

// 获取HTTP DAO实例
function getHttpDao() {
  const httpDao = TKDaoFactory.getDao(TKDaoType.Http);
  if (httpDao) {
    console.log('获取HTTP DAO成功');
    return httpDao;
  } else {
    console.error('获取HTTP DAO失败');
    return null;
  }
}

// 获取Socket DAO实例
function getSocketDao() {
  const socketDao = TKDaoFactory.getDao(TKDaoType.Socket);
  if (socketDao) {
    console.log('获取Socket DAO成功');
    return socketDao;
  } else {
    console.error('获取Socket DAO失败');
    return null;
  }
}

// 获取自定义DAO实例
function getCustomDao(daoName: string) {
  const customDao = TKDaoFactory.getDao(daoName);
  if (customDao) {
    console.log(`获取自定义DAO ${daoName} 成功`);
    return customDao;
  } else {
    console.error(`获取自定义DAO ${daoName} 失败`);
    return null;
  }
}

// 使用示例
const httpDao = getHttpDao();
const socketDao = getSocketDao();
const fileDao = getCustomDao('FileDao');
```

### 高级用法
```typescript
import { TKBaseDao, TKReqParamVO, TKServiceDaoDelegate, TKDaoFactory } from '@thinkive/tk-harmony-base';

// 创建自定义文件DAO
class FileDao extends TKBaseDao {
  private fileOperations: Map<string, any> = new Map();

  public invoke(reqParamVO: TKReqParamVO) {
    // 调用父类预处理
    super.invoke(reqParamVO);

    console.log(`执行文件操作: ${reqParamVO.url}`);

    // 模拟文件操作
    this.performFileOperation(reqParamVO);
  }

  public clearRequest(flowNo: string) {
    console.log(`清理文件操作: ${flowNo}`);
    this.fileOperations.delete(flowNo);
  }

  public clearGroup(groupNo: string) {
    console.log(`清理文件操作组: ${groupNo}`);
    // 清理指定组的文件操作
    for (const [flowNo, operation] of this.fileOperations) {
      if (operation.group === groupNo) {
        this.clearRequest(flowNo);
      }
    }
  }

  private performFileOperation(reqParamVO: TKReqParamVO) {
    // 存储操作信息
    this.fileOperations.set(reqParamVO.flowNo, {
      url: reqParamVO.url,
      params: reqParamVO.reqParam,
      group: reqParamVO.group,
      startTime: Date.now()
    });

    // 模拟异步文件操作
    setTimeout(() => {
      console.log(`文件操作完成: ${reqParamVO.flowNo}`);
      this.fileOperations.delete(reqParamVO.flowNo);
    }, 1000);
  }
}

// 创建自定义缓存DAO
class CacheDao extends TKBaseDao {
  private cache: Map<string, any> = new Map();
  private cacheTimestamps: Map<string, number> = new Map();

  public invoke(reqParamVO: TKReqParamVO) {
    super.invoke(reqParamVO);

    const cacheKey = this.generateCacheKey(reqParamVO);

    // 检查缓存
    if (this.isCacheValid(cacheKey)) {
      const cachedData = this.cache.get(cacheKey);
      console.log(`从缓存获取数据: ${cacheKey}`, cachedData);
      return;
    }

    // 模拟数据获取
    this.fetchData(reqParamVO, cacheKey);
  }

  public clearRequest(flowNo: string) {
    console.log(`清理缓存请求: ${flowNo}`);
  }

  public clearGroup(groupNo: string) {
    console.log(`清理缓存组: ${groupNo}`);
  }

  private generateCacheKey(reqParamVO: TKReqParamVO): string {
    return `${reqParamVO.url}_${JSON.stringify(reqParamVO.reqParam)}`;
  }

  private isCacheValid(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamps.get(cacheKey);
    if (!timestamp) return false;

    const now = Date.now();
    const cacheTime = 5 * 60 * 1000; // 5分钟缓存
    return (now - timestamp) < cacheTime;
  }

  private fetchData(reqParamVO: TKReqParamVO, cacheKey: string) {
    // 模拟数据获取
    setTimeout(() => {
      const data = { id: 1, name: 'test', timestamp: Date.now() };

      // 存储到缓存
      this.cache.set(cacheKey, data);
      this.cacheTimestamps.set(cacheKey, Date.now());

      console.log(`数据获取完成并缓存: ${cacheKey}`, data);
    }, 500);
  }

  // 清理过期缓存
  public clearExpiredCache() {
    const now = Date.now();
    const expireTime = 5 * 60 * 1000; // 5分钟过期

    for (const [key, timestamp] of this.cacheTimestamps) {
      if (now - timestamp > expireTime) {
        this.cache.delete(key);
        this.cacheTimestamps.delete(key);
        console.log(`清理过期缓存: ${key}`);
      }
    }
  }
}

// 注册自定义DAO
TKDaoFactory.registerDao(FileDao);
TKDaoFactory.registerDao(CacheDao);

// 使用自定义DAO
function useCustomDAOs() {
  // 获取文件DAO
  const fileDao = TKDaoFactory.getDao('FileDao');
  if (fileDao) {
    const fileReq = new TKReqParamVO();
    fileReq.flowNo = 'FILE_001';
    fileReq.url = '/files/upload';
    fileReq.reqParam = { fileName: 'document.pdf' };
    fileDao.invoke(fileReq);
  }

  // 获取缓存DAO
  const cacheDao = TKDaoFactory.getDao('CacheDao') as CacheDao;
  if (cacheDao) {
    const cacheReq = new TKReqParamVO();
    cacheReq.flowNo = 'CACHE_001';
    cacheReq.url = '/api/data';
    cacheReq.reqParam = { id: 123 };
    cacheDao.invoke(cacheReq);

    // 定期清理过期缓存
    setInterval(() => {
      cacheDao.clearExpiredCache();
    }, 60000); // 每分钟清理一次
  }
}

useCustomDAOs();
```

### 扩展示例
```typescript
// DAO管理器，提供更高级的DAO管理功能
class DAOManager {
  private daoInstances: Map<string, TKServiceDaoDelegate> = new Map();
  private daoUsageStats: Map<string, number> = new Map();

  // 获取DAO并记录使用统计
  getDAO(daoType: TKDaoType | string): TKServiceDaoDelegate | null {
    const dao = TKDaoFactory.getDao(daoType);
    if (dao) {
      const key = typeof daoType === 'string' ? daoType : TKDaoType[daoType];

      // 记录使用次数
      const currentCount = this.daoUsageStats.get(key) || 0;
      this.daoUsageStats.set(key, currentCount + 1);

      // 缓存实例引用
      this.daoInstances.set(key, dao);

      console.log(`DAO ${key} 使用次数: ${currentCount + 1}`);
      return dao;
    }
    return null;
  }

  // 获取使用统计
  getUsageStats(): Map<string, number> {
    return new Map(this.daoUsageStats);
  }

  // 获取最常用的DAO
  getMostUsedDAO(): string | null {
    let maxCount = 0;
    let mostUsedDAO: string | null = null;

    for (const [daoType, count] of this.daoUsageStats) {
      if (count > maxCount) {
        maxCount = count;
        mostUsedDAO = daoType;
      }
    }

    return mostUsedDAO;
  }

  // 清理所有DAO的请求
  clearAllRequests() {
    for (const [daoType, dao] of this.daoInstances) {
      console.log(`清理 ${daoType} 的所有请求`);
      // 这里需要根据具体DAO实现来清理
      // dao.clearAllRequests(); // 假设有这个方法
    }
  }

  // 重置统计信息
  resetStats() {
    this.daoUsageStats.clear();
    console.log('DAO使用统计已重置');
  }
}

// DAO工厂扩展，添加更多功能
class ExtendedDAOFactory {
  private static registeredDAOs: Set<string> = new Set();

  // 批量注册DAO
  static registerDAOs(daoClasses: Function[]) {
    daoClasses.forEach(daoClass => {
      TKDaoFactory.registerDao(daoClass);
      this.registeredDAOs.add(daoClass.name);
      console.log(`注册DAO: ${daoClass.name}`);
    });
  }

  // 检查DAO是否已注册
  static isDAORegistered(daoName: string): boolean {
    return this.registeredDAOs.has(daoName);
  }

  // 获取所有已注册的DAO名称
  static getRegisteredDAOs(): string[] {
    return Array.from(this.registeredDAOs);
  }

  // 创建DAO实例并进行初始化
  static createAndInitializeDAO(daoType: TKDaoType | string, initConfig?: any): TKServiceDaoDelegate | null {
    const dao = TKDaoFactory.getDao(daoType);
    if (dao && initConfig) {
      // 如果DAO支持初始化配置
      if ('initialize' in dao && typeof dao.initialize === 'function') {
        (dao as any).initialize(initConfig);
        console.log(`DAO ${daoType} 初始化完成`);
      }
    }
    return dao;
  }

  // 验证DAO的健康状态
  static validateDAO(daoType: TKDaoType | string): boolean {
    const dao = TKDaoFactory.getDao(daoType);
    if (!dao) {
      console.error(`DAO ${daoType} 不存在`);
      return false;
    }

    // 检查DAO是否实现了必要的方法
    const requiredMethods = ['invoke', 'clearRequest', 'clearGroup'];
    for (const method of requiredMethods) {
      if (!(method in dao) || typeof (dao as any)[method] !== 'function') {
        console.error(`DAO ${daoType} 缺少必要方法: ${method}`);
        return false;
      }
    }

    console.log(`DAO ${daoType} 验证通过`);
    return true;
  }
}

// 使用示例
const daoManager = new DAOManager();

// 使用DAO管理器
const httpDao = daoManager.getDAO(TKDaoType.Http);
const fileDao = daoManager.getDAO('FileDao');
const cacheDao = daoManager.getDAO('CacheDao');

// 查看使用统计
console.log('DAO使用统计:', daoManager.getUsageStats());
console.log('最常用的DAO:', daoManager.getMostUsedDAO());

// 使用扩展工厂功能
ExtendedDAOFactory.registerDAOs([FileDao, CacheDao]);
console.log('已注册的DAO:', ExtendedDAOFactory.getRegisteredDAOs());

// 验证DAO
ExtendedDAOFactory.validateDAO(TKDaoType.Http);
ExtendedDAOFactory.validateDAO('FileDao');
```

## 📚 相关资源

### 官方文档
- TypeScript官方文档 - 命名空间和模块
- 设计模式文档 - 工厂模式

### 参考资料
- 《设计模式：可复用面向对象软件的基础》- 工厂模式
- 《企业应用架构模式》- 数据访问对象模式

### 相关文件
- `基础服务-TKHttpDao-解释文档.md` - HTTP数据访问实现
- `基础服务-TKBaseDao-解释文档.md` - 数据访问抽象基类
- `TKServiceDaoDelegate.ets` - 数据访问接口规范
