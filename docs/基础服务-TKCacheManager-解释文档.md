# TKCacheManager

> **文件路径**: `src/main/ets/util/cache/TKCacheManager.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中高级  
> **前置知识**: 缓存策略、LRU算法、数据持久化、加密解密、内存管理

## 📋 文件概述

### 功能定位
TKCacheManager是HMThinkBaseHar框架的**缓存管理器**，提供了完整的多级缓存解决方案。它采用单例模式设计，集成了内存缓存、文件缓存、数据库缓存三种存储方式，支持数据加密、过期管理、LRU淘汰等企业级功能，为应用提供高性能的数据缓存服务。

### 核心职责
- **多级缓存管理**：提供内存、文件、数据库三级缓存存储
- **缓存策略控制**：支持LRU淘汰、过期时间、容量限制等策略
- **数据加密保护**：支持AES加密存储敏感数据
- **性能监控优化**：定时监控缓存状态，自动清理过期数据
- **统一接口封装**：提供统一的缓存操作接口，简化使用复杂度

### 设计模式
- **单例模式**：确保全局唯一的缓存管理器实例
- **策略模式**：支持不同的缓存类型和淘汰策略
- **工厂模式**：根据缓存类型创建相应的存储实现
- **观察者模式**：通过定时器监控缓存状态变化

## 🔧 核心功能

### 缓存类型枚举：TKCacheType

#### 类型1：`Mem`
- **功能**: 内存缓存
- **特点**: 访问速度最快，应用重启后丢失
- **使用场景**: 频繁访问的临时数据

#### 类型2：`File`
- **功能**: 文件缓存
- **特点**: 持久化存储，应用重启后保留
- **使用场景**: 需要持久化的配置数据

#### 类型3：`DB`
- **功能**: 数据库缓存
- **特点**: 结构化存储，支持复杂查询
- **使用场景**: 大量结构化数据的缓存

#### 类型4：`Mem_AutoUpdate`
- **功能**: 内存缓存（自动更新）
- **特点**: 命中时自动更新访问时间
- **使用场景**: 需要LRU策略的内存缓存

### 主要配置参数

#### 参数1：`maxMemCacheNum: number`
- **功能**: 最大内存缓存对象个数
- **默认值**: 100
- **使用场景**: 控制内存缓存的数量上限

#### 参数2：`maxMemCacheSize: number`
- **功能**: 最大内存缓存大小（字节）
- **默认值**: 100MB
- **使用场景**: 控制内存缓存的容量上限

#### 参数3：`currentMemCacheSize: number`
- **功能**: 当前内存缓存大小
- **使用场景**: 监控内存使用情况

### 主要方法

#### 方法1：`saveMemeCacheData(key, data, timeOut?, isEncrypt?)`
- **功能**: 保存内存缓存数据
- **参数**: 
  - key - 缓存键
  - data - 缓存数据
  - timeOut - 超时时间（秒，0表示永久）
  - isEncrypt - 是否加密
- **使用场景**: 缓存临时数据到内存

#### 方法2：`saveFileCacheData(key, data, timeOut?, isEncrypt?, fileName?)`
- **功能**: 保存文件缓存数据
- **参数**: 
  - fileName - 文件名称（可选）
- **使用场景**: 持久化缓存数据到文件

#### 方法3：`saveDBCacheData(key, data, timeOut?, isEncrypt?, dbName?)`
- **功能**: 保存数据库缓存数据
- **参数**: 
  - dbName - 数据库名称（可选）
- **使用场景**: 缓存结构化数据到数据库

## 💡 技术要点

### 核心算法/逻辑

#### LRU淘汰机制
```typescript
private async managerCacheObject() {
  try {
    // 检查内存缓存
    let memAllKeys: Array<string> = Array.from(this.memCacheKeys);
    if (memAllKeys && memAllKeys.length > 0) {
      for (let key of memAllKeys) {
        this.getMemCacheData<Object>(key); // 触发过期检查
      }
    }
    
    // 检查是否超过数量限制
    if (this.memCacheKeys.length > this.maxMemCacheNum) {
      // 删除最旧的缓存项
      let removeCount = this.memCacheKeys.length - this.maxMemCacheNum;
      for (let i = 0; i < removeCount; i++) {
        let oldestKey = this.memCacheKeys.shift();
        if (oldestKey) {
          this.memCacheMap.delete(oldestKey);
        }
      }
    }
    
    // 检查是否超过容量限制
    if (this.currentMemCacheSize > this.maxMemCacheSize) {
      // 删除缓存项直到容量合适
      while (this.currentMemCacheSize > this.maxMemCacheSize && this.memCacheKeys.length > 0) {
        let oldestKey = this.memCacheKeys.shift();
        if (oldestKey) {
          let cacheVO = this.memCacheMap.get(oldestKey) as TKCacheVO;
          if (cacheVO) {
            this.currentMemCacheSize -= cacheVO.cacheSize;
            this.memCacheMap.delete(oldestKey);
          }
        }
      }
    }
  } catch (error) {
    TKLog.error(`缓存管理异常: ${error.message}`);
  }
}
```

#### 缓存数据保存机制
```typescript
public saveCacheData(key: string, data: Object, cacheType: TKCacheType = TKCacheType.Mem, 
                    timeOut: number = 0, isEncrypt: boolean = false, fileName: string = ""): void | Promise<void> {
  try {
    // 创建缓存对象
    let cacheVO: TKCacheVO = new TKCacheVO();
    cacheVO.type = cacheType;
    cacheVO.key = key;
    cacheVO.cacheTime = timeOut;
    cacheVO.beginTime = Date.now();
    cacheVO.fileName = fileName;
    
    // 序列化数据
    let dataStr: string = TKObjectHelper.serialize(data) as string;
    cacheVO.cacheSize = dataStr.length;
    
    // 加密处理
    if (isEncrypt) {
      dataStr = TKAesHelper.encrypt(dataStr, this.aesSystemKey());
    }
    
    cacheVO.data = dataStr;
    
    // 根据缓存类型保存
    switch (cacheType) {
      case TKCacheType.Mem:
        this.saveMemCacheVO(key, cacheVO);
        break;
      case TKCacheType.File:
        this.saveFileCacheVO(key, cacheVO, fileName);
        break;
      case TKCacheType.DB:
        return this.saveDBCacheVO(key, cacheVO, fileName);
    }
  } catch (error) {
    TKLog.error(`保存缓存数据异常: ${error.message}`);
  }
}
```

#### 缓存数据获取机制
```typescript
private getDataFromCacheVO<T>(cacheVO: TKCacheVO): T | undefined {
  try {
    // 检查是否过期
    if (cacheVO.cacheTime > 0) {
      let currentTime = Date.now();
      let expireTime = cacheVO.beginTime + (cacheVO.cacheTime * 1000);
      if (currentTime > expireTime) {
        return undefined; // 已过期
      }
    }
    
    // 获取数据
    let dataStr = cacheVO.data as string;
    
    // 解密处理
    if (dataStr.startsWith("AES_")) {
      dataStr = TKAesHelper.decrypt(dataStr.substring(4), this.aesSystemKey());
    }
    
    // 反序列化
    return TKObjectHelper.deserialize<T>(dataStr) as T;
  } catch (error) {
    TKLog.error(`获取缓存数据异常: ${error.message}`);
    return undefined;
  }
}
```

#### 内存缓存管理
```typescript
private saveMemCacheVO(key: string, cacheVO: TKCacheVO) {
  // 如果已存在，先删除旧的
  if (this.memCacheMap.has(key)) {
    let oldCacheVO = this.memCacheMap.get(key) as TKCacheVO;
    this.currentMemCacheSize -= oldCacheVO.cacheSize;
    
    // 从队列中移除
    let index = this.memCacheKeys.indexOf(key);
    if (index >= 0) {
      this.memCacheKeys.splice(index, 1);
    }
  }
  
  // 添加新的缓存
  this.memCacheMap.set(key, cacheVO);
  this.memCacheKeys.push(key);
  this.currentMemCacheSize += cacheVO.cacheSize;
  
  // 检查是否需要淘汰
  this.checkAndEvictCache();
}

private checkAndEvictCache() {
  // 检查数量限制
  while (this.memCacheKeys.length > this.maxMemCacheNum) {
    this.evictOldestCache();
  }
  
  // 检查容量限制
  while (this.currentMemCacheSize > this.maxMemCacheSize && this.memCacheKeys.length > 0) {
    this.evictOldestCache();
  }
}

private evictOldestCache() {
  let oldestKey = this.memCacheKeys.shift();
  if (oldestKey) {
    let cacheVO = this.memCacheMap.get(oldestKey) as TKCacheVO;
    if (cacheVO) {
      this.currentMemCacheSize -= cacheVO.cacheSize;
      this.memCacheMap.delete(oldestKey);
    }
  }
}
```

### 实现机制分析

#### 多级缓存架构
- **内存缓存**：使用Map存储，访问速度最快，支持LRU淘汰
- **文件缓存**：基于TKPreferences实现，持久化存储
- **数据库缓存**：基于TKSingleKVStore实现，支持结构化查询

#### 过期策略
- **时间过期**：支持设置过期时间，自动清理过期数据
- **容量过期**：支持设置最大容量，超出时使用LRU淘汰
- **数量过期**：支持设置最大数量，超出时删除最旧的数据

#### 加密机制
- **AES加密**：使用AES算法加密敏感数据
- **密钥管理**：通过TKPasswordGenerator生成系统密钥
- **透明加解密**：加解密过程对用户透明

#### 监控机制
- **定时监控**：每60秒检查一次缓存状态
- **自动清理**：自动清理过期和超限的缓存数据
- **性能统计**：监控缓存命中率和内存使用情况

### 性能考虑
- **内存管理**：严格控制内存使用量，防止内存泄漏
- **LRU算法**：使用高效的LRU算法进行缓存淘汰
- **异步操作**：数据库操作使用异步方式，不阻塞主线程
- **批量处理**：支持批量清理和批量操作

### 错误处理
- **异常捕获**：所有操作都有完整的异常处理
- **日志记录**：详细记录操作日志和错误信息
- **降级处理**：缓存失败时不影响主业务流程
- **数据保护**：确保数据完整性和一致性

### 最佳实践
- **合理设置容量**：根据应用需求设置合适的缓存容量
- **选择合适的缓存类型**：根据数据特性选择内存、文件或数据库缓存
- **使用加密保护**：对敏感数据启用加密存储
- **定期监控**：启用监控机制，及时清理过期数据

## 🔗 依赖关系

### 依赖的模块
- `TKCacheVO` - 缓存对象，定义缓存数据结构
- `TKPreferences` - 文件存储，实现文件缓存功能
- `TKSingleKVStore` - 数据库存储，实现数据库缓存功能
- `TKAesHelper` - AES加密工具，提供数据加密功能
- `TKPasswordGenerator` - 密码生成器，生成加密密钥

### 被依赖情况
- `TKHttpDao` - HTTP数据访问对象，使用缓存管理器缓存网络数据
- `各业务模块` - 使用缓存管理器缓存业务数据
- `TKImageCacheManager` - 图片缓存管理器，基于缓存管理器实现

### 关联文件
- `TKCacheVO.ets` - 缓存对象定义
- `TKPreferences.ets` - 文件存储实现
- `TKSingleKVStore.ets` - 数据库存储实现
- `TKImageCacheManager.ets` - 图片缓存管理器

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKCacheManager是框架数据缓存的核心组件：
1. **性能优化**：提供多级缓存，显著提升应用性能
2. **使用频率高**：在需要数据缓存的场景中广泛使用
3. **功能完整**：集成了缓存策略、加密、监控等企业级功能
4. **架构重要性**：是数据访问层的重要基础设施

理解TKCacheManager有助于掌握框架的数据缓存机制，是构建高性能应用的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解缓存原理**：掌握缓存的基本概念和LRU算法
2. **分析多级架构**：理解内存、文件、数据库三级缓存的设计
3. **学习缓存策略**：掌握过期时间、容量限制等缓存策略
4. **实践缓存应用**：使用缓存管理器优化应用性能

### 前置学习
- 缓存策略和LRU算法
- 数据序列化和反序列化
- AES加密解密原理
- 内存管理和性能优化

### 后续学习
- `TKPreferences.ets` - 学习文件存储的实现
- `TKSingleKVStore.ets` - 了解数据库存储的使用
- `TKImageCacheManager.ets` - 学习图片缓存的实现

### 实践建议
1. **基础缓存使用**：使用不同类型的缓存存储数据
2. **性能测试**：测试不同缓存策略的性能表现
3. **容量管理实验**：测试缓存容量限制和淘汰机制
4. **加密功能验证**：测试数据加密存储的安全性

### 常见问题
1. **问题**: 如何选择合适的缓存类型？
   **解答**: 内存缓存适合频繁访问的临时数据，文件缓存适合需要持久化的配置数据，数据库缓存适合大量结构化数据。

2. **问题**: 缓存容量如何设置？
   **解答**: 根据应用的内存限制和数据量设置，建议内存缓存不超过可用内存的10-20%，并启用监控机制动态调整。

## 📝 代码示例

### 基础使用
```typescript
import { TKCacheManager, TKCacheType } from '@thinkive/tk-harmony-base';

// 获取缓存管理器实例
const cacheManager = TKCacheManager.shareInstance();

// 基础内存缓存
function basicMemoryCache() {
  // 保存数据到内存缓存
  const userData = {
    id: 123,
    name: "张三",
    email: "<EMAIL>"
  };

  cacheManager.saveMemeCacheData("user_123", userData, 300); // 缓存5分钟

  // 获取内存缓存数据
  const cachedUser = cacheManager.getMemCacheData<any>("user_123");
  if (cachedUser) {
    console.log('缓存命中:', cachedUser);
  } else {
    console.log('缓存未命中或已过期');
  }

  // 删除内存缓存
  cacheManager.deleteMemCacheData("user_123");
}

// 文件缓存示例
function fileCache() {
  // 保存配置数据到文件缓存
  const appConfig = {
    theme: "dark",
    language: "zh-CN",
    autoSave: true
  };

  cacheManager.saveFileCacheData("app_config", appConfig, 0, false, "config"); // 永久缓存

  // 获取文件缓存数据
  const cachedConfig = cacheManager.getFileCacheData<any>("app_config", "config");
  console.log('配置数据:', cachedConfig);
}

// 数据库缓存示例
async function databaseCache() {
  // 保存大量数据到数据库缓存
  const reportData = {
    date: "2024-01-01",
    sales: 10000,
    orders: 150,
    customers: 80
  };

  await cacheManager.saveDBCacheData("daily_report_2024_01_01", reportData, 86400); // 缓存1天

  // 获取数据库缓存数据
  const cachedReport = await cacheManager.getDBCacheData<any>("daily_report_2024_01_01");
  console.log('报表数据:', cachedReport);
}

// 加密缓存示例
function encryptedCache() {
  // 保存敏感数据（加密）
  const sensitiveData = {
    token: "abc123xyz789",
    secret: "my-secret-key",
    password: "user-password"
  };

  cacheManager.saveMemeCacheData("sensitive_data", sensitiveData, 1800, true); // 30分钟，加密存储

  // 获取加密缓存数据
  const decryptedData = cacheManager.getMemCacheData<any>("sensitive_data");
  console.log('解密后的数据:', decryptedData);
}

// 执行示例
basicMemoryCache();
fileCache();
databaseCache();
encryptedCache();
```

### 高级用法
```typescript
// 缓存管理器配置
class CacheConfig {
  static setupCacheManager() {
    const cacheManager = TKCacheManager.shareInstance();

    // 设置内存缓存限制
    cacheManager.setMaxMemCacheNum(200);        // 最多200个对象
    cacheManager.setMaxMemCacheSize(50 * 1024 * 1024); // 最大50MB

    // 启动监控
    cacheManager.startMonitor();

    console.log('缓存管理器配置完成');
  }

  static shutdownCacheManager() {
    const cacheManager = TKCacheManager.shareInstance();

    // 停止监控
    cacheManager.stopMonitor();

    console.log('缓存管理器已关闭');
  }
}

// 缓存策略管理器
class CacheStrategyManager {
  private cacheManager = TKCacheManager.shareInstance();

  // 多级缓存策略
  async getDataWithMultiLevelCache<T>(
    key: string,
    dataLoader: () => Promise<T>,
    options: {
      memCacheTime?: number,
      fileCacheTime?: number,
      dbCacheTime?: number,
      useEncryption?: boolean
    } = {}
  ): Promise<T | undefined> {
    const {
      memCacheTime = 300,      // 内存缓存5分钟
      fileCacheTime = 3600,    // 文件缓存1小时
      dbCacheTime = 86400,     // 数据库缓存1天
      useEncryption = false
    } = options;

    // 1. 尝试从内存缓存获取
    let data = this.cacheManager.getMemCacheData<T>(key);
    if (data) {
      console.log(`内存缓存命中: ${key}`);
      return data;
    }

    // 2. 尝试从文件缓存获取
    data = this.cacheManager.getFileCacheData<T>(key);
    if (data) {
      console.log(`文件缓存命中: ${key}`);
      // 回写到内存缓存
      this.cacheManager.saveMemeCacheData(key, data, memCacheTime, useEncryption);
      return data;
    }

    // 3. 尝试从数据库缓存获取
    data = await this.cacheManager.getDBCacheData<T>(key);
    if (data) {
      console.log(`数据库缓存命中: ${key}`);
      // 回写到文件和内存缓存
      this.cacheManager.saveFileCacheData(key, data, fileCacheTime, useEncryption);
      this.cacheManager.saveMemeCacheData(key, data, memCacheTime, useEncryption);
      return data;
    }

    // 4. 缓存未命中，加载数据
    console.log(`缓存未命中，加载数据: ${key}`);
    try {
      data = await dataLoader();
      if (data) {
        // 保存到所有级别的缓存
        await this.cacheManager.saveDBCacheData(key, data, dbCacheTime, useEncryption);
        this.cacheManager.saveFileCacheData(key, data, fileCacheTime, useEncryption);
        this.cacheManager.saveMemeCacheData(key, data, memCacheTime, useEncryption);
      }
      return data;
    } catch (error) {
      console.error(`数据加载失败: ${key}`, error);
      return undefined;
    }
  }

  // 缓存预热
  async warmupCache(keys: string[], dataLoader: (key: string) => Promise<any>) {
    console.log('开始缓存预热...');

    const promises = keys.map(async (key) => {
      try {
        const data = await dataLoader(key);
        if (data) {
          // 预热到内存缓存
          this.cacheManager.saveMemeCacheData(key, data, 3600); // 1小时
          console.log(`预热完成: ${key}`);
        }
      } catch (error) {
        console.error(`预热失败: ${key}`, error);
      }
    });

    await Promise.all(promises);
    console.log('缓存预热完成');
  }

  // 批量清理缓存
  async clearCacheByPattern(pattern: string) {
    console.log(`清理匹配模式的缓存: ${pattern}`);

    // 这里需要根据实际需求实现模式匹配清理
    // 由于当前API不支持模式匹配，这里提供思路
    console.log('批量清理功能需要扩展API支持');
  }
}

// 缓存监控器
class CacheMonitor {
  private cacheManager = TKCacheManager.shareInstance();
  private hitCount = 0;
  private missCount = 0;
  private startTime = Date.now();

  // 记录缓存命中
  recordHit() {
    this.hitCount++;
  }

  // 记录缓存未命中
  recordMiss() {
    this.missCount++;
  }

  // 获取缓存统计
  getStatistics() {
    const totalRequests = this.hitCount + this.missCount;
    const hitRate = totalRequests > 0 ? (this.hitCount / totalRequests * 100).toFixed(2) : '0.00';
    const runTime = Date.now() - this.startTime;

    return {
      hitCount: this.hitCount,
      missCount: this.missCount,
      totalRequests: totalRequests,
      hitRate: `${hitRate}%`,
      runTime: `${Math.floor(runTime / 1000)}秒`
    };
  }

  // 重置统计
  resetStatistics() {
    this.hitCount = 0;
    this.missCount = 0;
    this.startTime = Date.now();
  }

  // 打印统计报告
  printReport() {
    const stats = this.getStatistics();
    console.log('=== 缓存统计报告 ===');
    console.log(`命中次数: ${stats.hitCount}`);
    console.log(`未命中次数: ${stats.missCount}`);
    console.log(`总请求数: ${stats.totalRequests}`);
    console.log(`命中率: ${stats.hitRate}`);
    console.log(`运行时间: ${stats.runTime}`);
    console.log('==================');
  }
}

// 使用示例
class DataService {
  private cacheStrategy = new CacheStrategyManager();
  private cacheMonitor = new CacheMonitor();

  // 获取用户信息（带缓存）
  async getUserInfo(userId: string) {
    const key = `user_info_${userId}`;

    const data = await this.cacheStrategy.getDataWithMultiLevelCache(
      key,
      async () => {
        this.cacheMonitor.recordMiss();
        // 模拟API调用
        console.log(`从API加载用户信息: ${userId}`);
        return {
          id: userId,
          name: `用户${userId}`,
          email: `user${userId}@example.com`,
          lastLogin: Date.now()
        };
      },
      {
        memCacheTime: 300,    // 内存缓存5分钟
        fileCacheTime: 1800,  // 文件缓存30分钟
        dbCacheTime: 7200,    // 数据库缓存2小时
        useEncryption: false
      }
    );

    if (data) {
      this.cacheMonitor.recordHit();
    }

    return data;
  }

  // 获取敏感数据（加密缓存）
  async getSensitiveData(dataId: string) {
    const key = `sensitive_${dataId}`;

    return await this.cacheStrategy.getDataWithMultiLevelCache(
      key,
      async () => {
        console.log(`从安全API加载敏感数据: ${dataId}`);
        return {
          id: dataId,
          token: `token_${dataId}`,
          secret: `secret_${dataId}`,
          timestamp: Date.now()
        };
      },
      {
        memCacheTime: 600,    // 内存缓存10分钟
        fileCacheTime: 1800,  // 文件缓存30分钟
        dbCacheTime: 3600,    // 数据库缓存1小时
        useEncryption: true   // 启用加密
      }
    );
  }

  // 预热常用数据
  async warmupCommonData() {
    const commonUserIds = ['1', '2', '3', '4', '5'];

    await this.cacheStrategy.warmupCache(
      commonUserIds.map(id => `user_info_${id}`),
      async (key) => {
        const userId = key.replace('user_info_', '');
        return {
          id: userId,
          name: `用户${userId}`,
          email: `user${userId}@example.com`,
          lastLogin: Date.now()
        };
      }
    );
  }

  // 获取缓存统计
  getCacheStatistics() {
    return this.cacheMonitor.getStatistics();
  }

  // 打印缓存报告
  printCacheReport() {
    this.cacheMonitor.printReport();
  }
}

// 使用示例
async function demonstrateCacheUsage() {
  // 配置缓存管理器
  CacheConfig.setupCacheManager();

  const dataService = new DataService();

  // 预热缓存
  await dataService.warmupCommonData();

  // 测试缓存效果
  console.log('=== 测试缓存效果 ===');

  // 第一次访问（缓存未命中）
  await dataService.getUserInfo('1');

  // 第二次访问（内存缓存命中）
  await dataService.getUserInfo('1');

  // 访问敏感数据
  await dataService.getSensitiveData('secret_123');

  // 打印缓存统计
  dataService.printCacheReport();

  // 关闭缓存管理器
  setTimeout(() => {
    CacheConfig.shutdownCacheManager();
  }, 5000);
}

// 执行演示
demonstrateCacheUsage();
```

## 📚 相关资源

### 官方文档
- HarmonyOS数据存储开发指南
- 缓存策略设计最佳实践

### 参考资料
- 《高性能网站建设指南》- 缓存策略
- 《系统设计面试指南》- 缓存系统设计

### 相关文件
- `TKCacheVO.ets` - 缓存对象定义
- `TKPreferences.ets` - 文件存储实现
- `TKSingleKVStore.ets` - 数据库存储实现
