# TKDownLoadManager

> **文件路径**: `src/main/ets/base/network/download/TKDownLoadManager.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 鸿蒙下载API、单例模式、异步编程、文件管理

## 📋 文件概述

### 功能定位
TKDownLoadManager是HMThinkBaseHar框架的**文件下载管理器**，基于鸿蒙系统的request.agent API实现，提供完整的文件下载功能。它采用单例模式设计，支持下载进度监控、状态管理、错误处理等企业级下载功能。

### 核心职责
- **文件下载管理**：使用鸿蒙request.agent API执行文件下载
- **下载进度监控**：实时监控下载进度并提供回调通知
- **下载状态管理**：管理下载的各种状态（等待、进行中、完成、错误等）
- **文件路径管理**：自动管理下载文件的存储路径和文件名
- **错误处理机制**：提供完善的下载错误处理和异常管理

### 设计模式
- **单例模式**：确保全局唯一的下载管理器实例
- **回调模式**：通过回调函数提供下载状态和进度通知
- **策略模式**：支持前台和后台下载模式

## 🔧 核心功能

### 主要API/方法

#### 方法1：`shareInstance()`
- **功能**: 获取TKDownLoadManager的单例实例
- **参数**: 无
- **返回值**: TKDownLoadManager实例
- **使用场景**: 任何需要进行文件下载的地方

#### 方法2：`download(downLoadRequestVO, downLoadCallBack)`
- **功能**: 执行文件下载的核心方法
- **参数**: 
  - TKDownLoadRequestVO - 下载请求对象
  - TKDownLoadCallBack - 下载状态回调函数
- **返回值**: Promise<void>
- **使用场景**: 需要下载文件时的统一入口

### 关键私有方法

#### 方法1：`executeDownloadCallBack(downloadTask, downLoadCallBack, downLoadResultVO)`
- **功能**: 执行下载回调并清理资源
- **参数**: 
  - downloadTask - 下载任务对象
  - downLoadCallBack - 回调函数
  - downLoadResultVO - 下载结果对象
- **处理内容**: 回调执行和任务清理

## 💡 技术要点

### 核心算法/逻辑

#### 下载流程实现
```typescript
public async download(downLoadRequestVO: TKDownLoadRequestVO, downLoadCallBack: TKDownLoadCallBack) {
  // 1. 初始化下载参数
  downLoadRequestVO.sequence = downLoadRequestVO.sequence ?? TKUUIDHelper.uuid();
  downLoadRequestVO.fileName = downLoadRequestVO.fileName ?? await TKMd5Helper.stringWithMd5(downLoadRequestVO.url);
  downLoadRequestVO.header = downLoadRequestVO.header ?? {};
  
  // 2. 构建下载路径和结果对象
  let downloadPath: string = `${TKFileHelper.cacheDir()}/thinkive/download`;
  let downLoadResultVO: TKDownLoadResultVO = {
    sequence: downLoadRequestVO.sequence,
    url: downLoadRequestVO.url,
    fileName: downLoadRequestVO.fileName,
    filePath: `${downloadPath}/${downLoadRequestVO.fileName}`,
    progress: new TKDownLoadProgressVO()
  };
  
  // 3. 参数验证
  if (TKStringHelper.isBlank(downLoadResultVO.url)) {
    downLoadResultVO.errorInfo = '下载url不可为空';
    downLoadResultVO.status = TKDownLoadStatus.ERROR;
    this.executeDownloadCallBack(undefined, downLoadCallBack, downLoadResultVO);
    return;
  }
  
  // 4. 准备下载环境
  TKFileHelper.createFileDir(downloadPath);
  TKFileHelper.deleteFile(downLoadResultVO.filePath);
  
  // 5. 开始下载回调
  downLoadResultVO.status = TKDownLoadStatus.PENDING;
  this.executeDownloadCallBack(undefined, downLoadCallBack, downLoadResultVO);
  
  // 6. 创建下载任务
  let downloadTask: request.agent.Task = await request.agent.create(TKContextHelper.getCurrentContext(), {
    action: request.agent.Action.DOWNLOAD,
    url: downLoadResultVO.url,
    method: 'GET',
    header: downLoadResultVO.header,
    mode: downLoadRequestVO.backgroundMode ? request.agent.Mode.BACKGROUND : request.agent.Mode.FOREGROUND,
    retry: true,
    saveas: downLoadResultVO.filePath,
    overwrite: true
  });
  
  // 7. 注册事件监听器
  this.registerDownloadListeners(downloadTask, downLoadCallBack, downLoadResultVO);
  
  // 8. 启动下载
  await downloadTask.start();
}
```

#### 事件监听机制
```typescript
// 进度监听
downloadTask.on('progress', (progress: request.agent.Progress) => {
  downLoadResultVO.status = TKDownLoadStatus.RUNNING;
  downLoadResultVO.progress.bytesLoaded = progress.processed;
  downLoadResultVO.progress.bytesTotal = progress.sizes[0];
  this.executeDownloadCallBack(downloadTask, downLoadCallBack, downLoadResultVO);
});

// 完成监听
downloadTask.on('completed', (progress: request.agent.Progress) => {
  downLoadResultVO.status = TKDownLoadStatus.FINISHED;
  this.executeDownloadCallBack(downloadTask, downLoadCallBack, downLoadResultVO);
});

// 失败监听
downloadTask.on('failed', (progress: request.agent.Progress) => {
  downLoadResultVO.status = TKDownLoadStatus.ERROR;
  downLoadResultVO.errorInfo = '下载失败';
  this.executeDownloadCallBack(downloadTask, downLoadCallBack, downLoadResultVO);
});
```

#### 资源清理机制
```typescript
private async executeDownloadCallBack(
  downloadTask: request.agent.Task | undefined,
  downLoadCallBack: TKDownLoadCallBack,
  downLoadResultVO: TKDownLoadResultVO
) {
  // 1. 清理下载任务
  if (downloadTask && 
      (downLoadResultVO.status == TKDownLoadStatus.FINISHED || 
       downLoadResultVO.status == TKDownLoadStatus.ERROR)) {
    // 移除事件监听器
    downloadTask.off('progress');
    downloadTask.off('completed');
    downloadTask.off('failed');
    // 移除下载任务
    await request.agent.remove(downloadTask.tid);
  }
  
  // 2. 执行回调
  if (downLoadCallBack) {
    downLoadCallBack(downLoadResultVO);
  }
}
```

### 实现机制分析

#### 单例模式实现
- **私有构造函数**：防止外部直接实例化
- **静态实例管理**：通过静态变量维护唯一实例
- **全局访问点**：提供shareInstance()方法获取实例

#### 文件管理策略
- **自动路径生成**：使用缓存目录下的thinkive/download子目录
- **文件名处理**：支持自定义文件名或使用URL的MD5值
- **文件覆盖**：下载前删除同名文件，确保文件完整性
- **目录创建**：自动创建下载目录

#### 下载状态管理
1. **PENDING**：下载开始，准备阶段
2. **RUNNING**：下载进行中，提供进度信息
3. **FINISHED**：下载完成
4. **ERROR**：下载失败，提供错误信息

### 性能考虑
- **异步处理**：所有下载操作都是异步的，不阻塞主线程
- **事件驱动**：通过事件监听器实现实时状态更新
- **资源管理**：下载完成或失败后自动清理任务和监听器
- **文件覆盖**：避免文件冲突和不完整下载

### 错误处理
- **参数验证**：下载前验证URL等必要参数
- **异常捕获**：捕获下载过程中的异常并转换为错误状态
- **资源清理**：异常情况下确保资源正确释放
- **错误回调**：通过回调函数通知错误信息

### 最佳实践
- **单例使用**：通过shareInstance()获取实例，避免重复创建
- **回调处理**：合理处理各种下载状态的回调
- **文件管理**：注意下载文件的存储位置和清理
- **错误处理**：完善的错误处理机制，确保应用稳定性

## 🔗 依赖关系

### 依赖的模块
- `request` - 鸿蒙下载API，底层下载功能实现
- `TKDownLoadBean` - 下载相关的数据对象定义
- `TKFileHelper` - 文件操作工具，目录创建和文件管理
- `TKMd5Helper` - MD5工具，生成文件名
- `TKUUIDHelper` - UUID工具，生成流水号

### 被依赖情况
- `TKVersionManager` - 版本管理器，使用下载管理器下载更新包
- `业务模块` - 各种需要文件下载功能的业务场景
- `媒体下载` - 图片、视频等媒体文件的下载

### 关联文件
- `TKDownLoadBean.ets` - 下载相关数据对象定义
- `TKFileHelper.ets` - 文件操作工具类
- `TKVersionManager.ets` - 版本管理器，下载功能的使用者

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKDownLoadManager是框架的文件下载功能实现：
1. **功能专一**：专门处理文件下载，功能相对独立
2. **使用场景**：主要用于版本更新、资源下载等特定场景
3. **技术实现**：基于鸿蒙标准API，实现相对简单
4. **扩展性**：提供了基础的下载功能，可根据需要扩展

理解TKDownLoadManager有助于掌握框架的文件下载机制，特别是在需要实现文件下载功能时。

## 🎯 学习建议

### 学习路径
1. **理解下载基础**：掌握鸿蒙request.agent API的基本使用
2. **分析下载流程**：理解从请求创建到文件保存的完整流程
3. **学习状态管理**：掌握下载状态的变化和处理机制
4. **实践下载功能**：使用下载管理器实现实际的文件下载

### 前置学习
- 鸿蒙request.agent API使用
- 异步编程和Promise处理
- 文件系统操作基础
- 单例模式的实现原理

### 后续学习
- `TKUploadManager.ets` - 了解文件上传的实现
- `TKFileHelper.ets` - 学习文件操作工具的使用
- `TKVersionManager.ets` - 查看下载功能的实际应用

### 实践建议
1. **基础下载练习**：使用下载管理器下载简单文件
2. **进度监控实验**：观察下载进度的变化和回调
3. **错误处理测试**：模拟网络异常，测试错误处理机制
4. **扩展功能开发**：基于现有功能添加暂停、恢复等高级功能

### 常见问题
1. **问题**: 下载的文件保存在哪里？
   **解答**: 文件保存在应用缓存目录下的thinkive/download子目录中，可以通过downLoadResultVO.filePath获取完整路径。

2. **问题**: 如何处理下载失败的情况？
   **解答**: 在回调函数中检查downLoadResultVO.status是否为ERROR，并通过downLoadResultVO.errorInfo获取错误信息进行相应处理。

## 📝 代码示例

### 基础使用
```typescript
import { TKDownLoadManager, TKDownLoadRequestVO, TKDownLoadResultVO, TKDownLoadStatus } from '@thinkive/tk-harmony-base';

// 获取下载管理器实例
const downloadManager = TKDownLoadManager.shareInstance();

// 基础文件下载
function downloadFile() {
  const downloadRequest: TKDownLoadRequestVO = {
    url: 'https://example.com/files/document.pdf',
    fileName: 'my-document.pdf',
    header: {
      'Authorization': 'Bearer token123'
    }
  };

  downloadManager.download(downloadRequest, (result: TKDownLoadResultVO) => {
    switch (result.status) {
      case TKDownLoadStatus.PENDING:
        console.log('下载开始准备...');
        break;

      case TKDownLoadStatus.RUNNING:
        const progress = (result.progress.progress * 100).toFixed(2);
        console.log(`下载进度: ${progress}%`);
        console.log(`已下载: ${result.progress.bytesLoaded} / 总大小: ${result.progress.bytesTotal}`);
        break;

      case TKDownLoadStatus.FINISHED:
        console.log('下载完成!');
        console.log(`文件保存路径: ${result.filePath}`);
        break;

      case TKDownLoadStatus.ERROR:
        console.error('下载失败:', result.errorInfo);
        break;
    }
  });
}

// 后台下载模式
function downloadInBackground() {
  const downloadRequest: TKDownLoadRequestVO = {
    url: 'https://example.com/files/large-video.mp4',
    fileName: 'video.mp4',
    backgroundMode: true // 启用后台下载
  };

  downloadManager.download(downloadRequest, (result: TKDownLoadResultVO) => {
    if (result.status === TKDownLoadStatus.FINISHED) {
      console.log('后台下载完成:', result.filePath);
    }
  });
}

downloadFile();
```

### 高级用法
```typescript
// 下载管理器包装类，提供更多功能
class AdvancedDownloadManager {
  private downloadManager = TKDownLoadManager.shareInstance();
  private activeDownloads: Map<string, {
    request: TKDownLoadRequestVO,
    callback: (result: TKDownLoadResultVO) => void,
    startTime: number
  }> = new Map();

  // 带进度显示的下载
  downloadWithProgress(
    url: string,
    fileName?: string,
    onProgress?: (progress: number, speed: string) => void
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const downloadRequest: TKDownLoadRequestVO = {
        url: url,
        fileName: fileName,
        sequence: `DOWNLOAD_${Date.now()}`
      };

      let lastTime = Date.now();
      let lastBytes = 0;

      this.activeDownloads.set(downloadRequest.sequence!, {
        request: downloadRequest,
        callback: () => {},
        startTime: Date.now()
      });

      this.downloadManager.download(downloadRequest, (result: TKDownLoadResultVO) => {
        const downloadInfo = this.activeDownloads.get(result.sequence);

        switch (result.status) {
          case TKDownLoadStatus.RUNNING:
            if (onProgress) {
              // 计算下载速度
              const currentTime = Date.now();
              const timeDiff = (currentTime - lastTime) / 1000; // 秒
              const bytesDiff = result.progress.bytesLoaded - lastBytes;
              const speed = timeDiff > 0 ? this.formatSpeed(bytesDiff / timeDiff) : '0 B/s';

              onProgress(result.progress.progress, speed);

              lastTime = currentTime;
              lastBytes = result.progress.bytesLoaded;
            }
            break;

          case TKDownLoadStatus.FINISHED:
            this.activeDownloads.delete(result.sequence);
            resolve(result.filePath);
            break;

          case TKDownLoadStatus.ERROR:
            this.activeDownloads.delete(result.sequence);
            reject(new Error(result.errorInfo));
            break;
        }
      });
    });
  }

  // 批量下载
  async downloadMultiple(
    downloads: Array<{ url: string, fileName?: string }>,
    onProgress?: (completed: number, total: number) => void
  ): Promise<string[]> {
    const results: string[] = [];
    let completed = 0;

    for (const download of downloads) {
      try {
        const filePath = await this.downloadWithProgress(
          download.url,
          download.fileName,
          (progress, speed) => {
            console.log(`下载 ${download.fileName}: ${(progress * 100).toFixed(1)}% (${speed})`);
          }
        );

        results.push(filePath);
        completed++;

        if (onProgress) {
          onProgress(completed, downloads.length);
        }

        console.log(`完成下载 ${completed}/${downloads.length}: ${download.fileName}`);
      } catch (error) {
        console.error(`下载失败 ${download.fileName}:`, error.message);
        results.push(''); // 失败的下载用空字符串占位
      }
    }

    return results;
  }

  // 获取活跃下载列表
  getActiveDownloads(): Array<{
    sequence: string,
    url: string,
    fileName?: string,
    duration: number
  }> {
    const now = Date.now();
    const activeList: Array<any> = [];

    for (const [sequence, info] of this.activeDownloads) {
      activeList.push({
        sequence: sequence,
        url: info.request.url,
        fileName: info.request.fileName,
        duration: now - info.startTime
      });
    }

    return activeList;
  }

  // 格式化下载速度
  private formatSpeed(bytesPerSecond: number): string {
    if (bytesPerSecond < 1024) {
      return `${bytesPerSecond.toFixed(0)} B/s`;
    } else if (bytesPerSecond < 1024 * 1024) {
      return `${(bytesPerSecond / 1024).toFixed(1)} KB/s`;
    } else {
      return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`;
    }
  }

  // 格式化文件大小
  private formatFileSize(bytes: number): string {
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else if (bytes < 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    } else {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    }
  }
}
```

### 扩展示例
```typescript
// 下载队列管理器
class DownloadQueueManager {
  private downloadManager = TKDownLoadManager.shareInstance();
  private queue: Array<{
    request: TKDownLoadRequestVO,
    callback: (result: TKDownLoadResultVO) => void,
    priority: number
  }> = [];
  private isProcessing = false;
  private maxConcurrent = 3;
  private activeCount = 0;

  // 添加下载到队列
  addToQueue(
    request: TKDownLoadRequestVO,
    callback: (result: TKDownLoadResultVO) => void,
    priority: number = 0
  ) {
    this.queue.push({ request, callback, priority });

    // 按优先级排序
    this.queue.sort((a, b) => b.priority - a.priority);

    // 开始处理队列
    this.processQueue();
  }

  // 处理下载队列
  private async processQueue() {
    if (this.isProcessing || this.activeCount >= this.maxConcurrent) {
      return;
    }

    if (this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0 && this.activeCount < this.maxConcurrent) {
      const item = this.queue.shift();
      if (item) {
        this.activeCount++;
        this.processDownload(item);
      }
    }

    this.isProcessing = false;
  }

  // 处理单个下载
  private processDownload(item: {
    request: TKDownLoadRequestVO,
    callback: (result: TKDownLoadResultVO) => void,
    priority: number
  }) {
    const wrappedCallback = (result: TKDownLoadResultVO) => {
      // 调用原始回调
      item.callback(result);

      // 下载完成或失败时，减少活跃计数并继续处理队列
      if (result.status === TKDownLoadStatus.FINISHED ||
          result.status === TKDownLoadStatus.ERROR) {
        this.activeCount--;
        this.processQueue();
      }
    };

    this.downloadManager.download(item.request, wrappedCallback);
  }

  // 获取队列状态
  getQueueStatus(): {
    waiting: number,
    active: number,
    maxConcurrent: number
  } {
    return {
      waiting: this.queue.length,
      active: this.activeCount,
      maxConcurrent: this.maxConcurrent
    };
  }

  // 设置最大并发数
  setMaxConcurrent(max: number) {
    this.maxConcurrent = max;
    this.processQueue();
  }

  // 清空队列
  clearQueue() {
    this.queue = [];
  }
}

// 使用示例
const advancedManager = new AdvancedDownloadManager();
const queueManager = new DownloadQueueManager();

// 单个文件下载
async function downloadSingleFile() {
  try {
    const filePath = await advancedManager.downloadWithProgress(
      'https://example.com/large-file.zip',
      'download.zip',
      (progress, speed) => {
        console.log(`进度: ${(progress * 100).toFixed(1)}%, 速度: ${speed}`);
      }
    );

    console.log('下载完成:', filePath);
  } catch (error) {
    console.error('下载失败:', error.message);
  }
}

// 批量下载
async function downloadMultipleFiles() {
  const downloads = [
    { url: 'https://example.com/file1.pdf', fileName: 'document1.pdf' },
    { url: 'https://example.com/file2.jpg', fileName: 'image1.jpg' },
    { url: 'https://example.com/file3.mp4', fileName: 'video1.mp4' }
  ];

  try {
    const results = await advancedManager.downloadMultiple(
      downloads,
      (completed, total) => {
        console.log(`批量下载进度: ${completed}/${total}`);
      }
    );

    console.log('批量下载完成:', results);
  } catch (error) {
    console.error('批量下载失败:', error);
  }
}

// 队列下载
function downloadWithQueue() {
  const files = [
    { url: 'https://example.com/high-priority.pdf', priority: 10 },
    { url: 'https://example.com/normal-file1.jpg', priority: 5 },
    { url: 'https://example.com/normal-file2.mp3', priority: 5 },
    { url: 'https://example.com/low-priority.zip', priority: 1 }
  ];

  files.forEach(file => {
    queueManager.addToQueue(
      { url: file.url } as TKDownLoadRequestVO,
      (result) => {
        console.log(`队列下载完成: ${result.fileName}, 状态: ${result.status}`);
      },
      file.priority
    );
  });

  console.log('队列状态:', queueManager.getQueueStatus());
}

// 执行示例
downloadSingleFile();
downloadMultipleFiles();
downloadWithQueue();
```

## 📚 相关资源

### 官方文档
- HarmonyOS网络开发指南 - request.agent API
- HarmonyOS文件管理开发指南

### 参考资料
- 《HTTP权威指南》- 文件下载原理
- 《设计模式：可复用面向对象软件的基础》- 单例模式

### 相关文件
- `基础服务-TKUploadManager-解释文档.md` - 文件上传管理器
- `TKDownLoadBean.ets` - 下载相关数据对象
- `TKFileHelper.ets` - 文件操作工具类
