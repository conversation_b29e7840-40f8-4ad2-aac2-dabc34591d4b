# TKTitleBar

> **文件路径**: `src/main/ets/components/titilebar/TKTitleBar.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 鸿蒙组件开发、状态栏管理、路由导航、主题系统

## 📋 文件概述

### 功能定位
TKTitleBar是HMThinkBaseHar框架的**标题栏组件**，提供了完整的页面标题栏功能。它采用组件化设计，集成了状态栏管理、导航按钮、主题适配等企业级功能，支持全屏模式、自定义样式和灵活的事件处理，为应用提供统一的标题栏体验。

### 核心职责
- **标题栏显示**：提供标准的页面标题栏布局和显示功能
- **状态栏管理**：管理状态栏的显示隐藏和样式配置
- **导航功能**：提供返回、关闭等导航按钮和事件处理
- **全屏适配**：支持全屏模式和安全区域适配
- **主题集成**：集成主题系统，支持动态样式切换

### 设计模式
- **组件模式**：使用@Component装饰器定义可复用标题栏组件
- **控制器模式**：通过TKTitleBarController管理标题栏的行为和状态
- **配置模式**：通过TKTitleBarAttribute和TKTitleBarStyleAttribute配置属性
- **构建器模式**：支持自定义标题栏内容的构建器模式

## 🔧 核心功能

### 主要配置类：TKTitleBarAttribute

#### 属性1：`isLayoutFullScreen: boolean`
- **功能**: 是否全屏显示
- **类型**: boolean
- **默认值**: true
- **使用场景**: 控制页面是否全屏显示

#### 属性2：`title: string`
- **功能**: 标题文本
- **类型**: string
- **默认值**: 空字符串
- **使用场景**: 显示页面标题

#### 属性3：`statusBarEnable: boolean`
- **功能**: 状态栏显示/隐藏
- **类型**: boolean
- **默认值**: true
- **使用场景**: 控制状态栏的显示状态

#### 属性4：`titleBarEnable: boolean`
- **功能**: 标题栏显示/隐藏
- **类型**: boolean
- **默认值**: true
- **使用场景**: 控制标题栏的显示状态

#### 属性5：`backPressEnable: boolean`
- **功能**: 左侧返回按钮显示/隐藏
- **类型**: boolean
- **默认值**: true
- **使用场景**: 控制返回按钮的显示

#### 属性6：`leftBtnMode: string`
- **功能**: 左侧按钮模式
- **类型**: string
- **默认值**: "1"
- **可选值**: "0"(文本)、"1"(图片)、"2"(文本+图片)、"3"(返回图片+关闭文字)
- **使用场景**: 配置左侧按钮的显示模式

### 样式配置类：TKTitleBarStyleAttribute

#### 属性1：`titleColor: ResourceColor`
- **功能**: 标题文字颜色
- **类型**: ResourceColor
- **默认值**: 从主题获取或"#FF000000"
- **使用场景**: 自定义标题颜色

#### 属性2：`titleBgColor: ResourceColor`
- **功能**: 标题栏背景颜色
- **类型**: ResourceColor
- **默认值**: 从主题获取或"#00FFFFFF"
- **使用场景**: 自定义标题栏背景

#### 属性3：`backImage: Resource`
- **功能**: 返回按钮图片
- **类型**: Resource
- **默认值**: 从主题获取或框架默认图片
- **使用场景**: 自定义返回按钮图标

#### 属性4：`statusStyle: string`
- **功能**: 状态栏风格
- **类型**: string
- **可选值**: "0"(黑色图标)、"1"(白色图标)
- **使用场景**: 配置状态栏图标颜色

## 💡 技术要点

### 核心算法/逻辑

#### 标题栏高度计算
```typescript
updateTitleBarHeight() {
  let height: number = 0;
  
  // 全屏模式下计算状态栏高度
  if (this.attribute.isLayoutFullScreen && this.attribute.statusBarEnable) {
    height += this.statusBarHeight;
  }
  
  // 计算标题栏高度
  if (this.attribute.titleBarEnable) {
    height += this.titleBarHeight;
  }
  
  // 回调总高度给外部
  if (this.attribute!.onTitleBarHeight) {
    this.attribute!.onTitleBarHeight!(height);
  }
}
```

#### 标题栏布局结构
```typescript
build() {
  Column() {
    // 状态栏占位区域
    if (this.attribute.isLayoutFullScreen) {
      if (this.attribute.statusBarEnable) {
        Row() {}
        .width('100%')
        .height(px2vp(this.statusBarHeight))
        .backgroundColor(this.styleAttribute.titleBgColor)
      }
    } else {
      if (this.attribute.statusBarEnable) {
        Row() {}
        .width('100%')
        .height('0.01%')
        .backgroundColor(this.styleAttribute.titleBgColor)
        .expandSafeArea([SafeAreaType.SYSTEM], [SafeAreaEdge.TOP])
      }
    }
    
    // 标题栏内容区域
    if (this.attribute.titleBarEnable) {
      Row() {
        if (this.customTitleView) {
          this.customTitleView(this.titleBarController)
        } else {
          this.baseTitleView(this.titleBarController)
        }
      }
      .width('100%')
      .height(px2vp(this.titleBarHeight))
      .backgroundColor(this.styleAttribute.titleBgColor)
      .backgroundImage(this.styleAttribute.titleBgImage)
      .backgroundImageSize(ImageSize.Cover)
    }
  }
  .backgroundColor(this.styleAttribute.titleBgColor)
}
```

#### 基础标题栏视图
```typescript
@Builder
baseTitleView(titleBarController: TKTitleBarController) {
  // 左侧按钮区域（15%宽度）
  Row() {
    // 返回按钮
    if (this.attribute.backPressEnable) {
      if (["1", "2", "3"].includes(this.attribute.leftBtnMode!)) {
        Image(this.styleAttribute.backImage)
          .width(this.styleAttribute.backImageSize?.width)
          .height(this.styleAttribute.backImageSize?.height)
          .objectFit(ImageFit.Contain)
          .colorFilter(drawing.ColorFilter.createBlendModeColorFilter(
            TKColorHelper.colorToARGB((this.styleAttribute.leftBtnColor ?? 
            this.styleAttribute.titleColor) as string),
            drawing.BlendMode.SRC_IN))
          .onClick((event) => {
            this.titleBarController.routerBack(event)
          })
      }
    }
    
    // 关闭按钮
    if (this.attribute.closePressEnable) {
      if (["0", "2", "3"].includes(this.attribute.leftBtnMode!)) {
        Text("关闭")
          .fontFamily('PingFang SC')
          .fontColor((this.styleAttribute.leftBtnColor ?? this.styleAttribute.titleColor))
          .fontSize(19)
          .onClick((event) => {
            this.titleBarController.routerClose(event);
          })
      }
    }
  }
  .width('15%')
  .height('100%')
  .justifyContent(FlexAlign.Start)
  
  // 中间标题区域（70%宽度）
  Row() {
    Text(this.attribute.title)
      .fontFamily('PingFang SC')
      .fontColor(this.styleAttribute.titleColor)
      .minFontSize(5)
      .maxFontSize(19)
      .maxLines(1)
      .textAlign(TextAlign.Center)
  }
  .width('70%')
  .height('100%')
  .justifyContent(FlexAlign.Center)
  
  // 右侧按钮区域（15%宽度）
  Row() {
    // 右侧按钮实现
  }
  .width('15%')
  .height('100%')
  .justifyContent(FlexAlign.End)
}
```

#### 控制器事件处理
```typescript
// 返回事件处理
routerBack: (event?: ClickEvent) => void = (event) => {
  const attribute = this.attribute as TKTitleBarAttribute
  if (attribute?.onBackClick) {
    if (!attribute.onBackClick(event)) {
      TKRouterHelper.back({ navComponentOrPathStack: this.getComponent() });
    }
  } else {
    TKRouterHelper.back({ navComponentOrPathStack: this.getComponent() });
  }
}

// 关闭事件处理
routerClose: (event?: ClickEvent) => void = (event) => {
  const attribute = this.attribute as TKTitleBarAttribute
  if (attribute?.onCloseClick) {
    if (!attribute.onCloseClick(event)) {
      TKRouterHelper.back({ navComponentOrPathStack: this.getComponent() });
    }
  } else {
    TKRouterHelper.back({ navComponentOrPathStack: this.getComponent() });
  }
}
```

### 实现机制分析

#### 全屏模式适配
- **状态栏处理**：全屏模式下手动计算状态栏高度进行占位
- **安全区域**：非全屏模式下使用expandSafeArea自动适配
- **高度回调**：通过onTitleBarHeight回调总高度给外部使用

#### 主题系统集成
- **动态获取**：从TKThemeManager获取主题配置的颜色和图片
- **默认值保护**：提供默认值确保组件正常显示
- **样式继承**：支持通过CSS类名配置样式属性

#### 控制器模式
- **职责分离**：TKTitleBarController负责业务逻辑，组件负责UI渲染
- **事件代理**：控制器处理所有用户交互事件
- **状态管理**：控制器管理标题栏的状态和属性

#### 自定义扩展
- **自定义视图**：通过customTitleView支持完全自定义的标题栏内容
- **构建器模式**：使用@BuilderParam支持灵活的内容构建
- **控制器传递**：将控制器传递给自定义视图，保持功能一致性

### 性能考虑
- **状态栏缓存**：缓存状态栏高度，避免重复计算
- **条件渲染**：根据配置按需渲染组件，减少不必要的渲染
- **弱引用管理**：控制器使用弱引用管理组件，避免循环引用
- **主题优化**：合理使用主题系统，避免频繁的样式计算

### 错误处理
- **空值检查**：对回调函数和配置进行空值检查
- **默认值保护**：提供完整的默认值配置
- **异常隔离**：单个按钮事件异常不影响其他功能
- **状态保护**：通过控制器保护组件状态的一致性

### 最佳实践
- **配置分离**：将属性和样式配置分离，提高可维护性
- **控制器模式**：使用控制器管理复杂的业务逻辑
- **主题集成**：充分利用主题系统实现样式的统一管理
- **自定义支持**：提供灵活的自定义扩展能力

## 🔗 依赖关系

### 依赖的模块
- `TKTitleBarController` - 标题栏控制器，管理标题栏的行为和状态
- `TKWindowHelper` - 窗口工具，处理全屏模式和状态栏配置
- `TKRouterHelper` - 路由工具，处理页面导航和返回
- `TKThemeManager` - 主题管理器，获取主题配置
- `TKColorHelper` - 颜色工具，处理颜色转换和滤镜

### 被依赖情况
- `各页面组件` - 使用TKTitleBar作为页面标题栏
- `TKWebPage` - Web页面使用标题栏组件
- `导航页面` - 需要标题栏的导航页面

### 关联文件
- `TKTitleBarController.ets` - 标题栏控制器实现
- `TKAttributeController.ets` - 属性控制器基类
- `TKWindowHelper.ets` - 窗口管理工具
- `TKThemeManager.ets` - 主题管理器

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKTitleBar是框架UI组件的基础标题栏：
1. **通用组件**：提供标准的页面标题栏功能
2. **使用频率高**：在大多数页面中都需要使用标题栏
3. **功能完整**：集成了状态栏管理、导航、主题等功能
4. **扩展性强**：支持自定义内容和样式配置

理解TKTitleBar有助于掌握框架的页面布局和导航体系，是构建标准化页面的重要组件。

## 🎯 学习建议

### 学习路径
1. **理解标题栏设计**：掌握移动应用标题栏的设计原则和用户体验
2. **分析全屏适配**：理解全屏模式和安全区域的适配机制
3. **学习控制器模式**：掌握控制器模式在组件设计中的应用
4. **实践标题栏开发**：使用TKTitleBar创建各种样式的标题栏

### 前置学习
- 鸿蒙组件开发基础
- 状态栏和安全区域概念
- 路由导航机制
- 主题系统使用

### 后续学习
- `TKTitleBarController.ets` - 学习控制器的实现细节
- `TKWindowHelper.ets` - 了解窗口管理功能
- `TKThemeManager.ets` - 掌握主题系统的使用

### 实践建议
1. **基础标题栏使用**：创建简单的页面标题栏
2. **全屏模式实验**：测试全屏和非全屏模式的适配
3. **自定义样式练习**：实现不同风格的标题栏样式
4. **自定义内容扩展**：使用customTitleView创建自定义标题栏

### 常见问题
1. **问题**: 如何实现自定义的标题栏内容？
   **解答**: 通过customTitleView属性传入自定义的@Builder函数，可以完全自定义标题栏的内容和布局。

2. **问题**: 全屏模式下状态栏如何适配？
   **解答**: 全屏模式下组件会自动计算状态栏高度并创建占位区域，确保内容不被状态栏遮挡。

## 📝 代码示例

### 基础使用
```typescript
import { TKTitleBar, TKTitleBarAttribute, TKTitleBarStyleAttribute } from '@thinkive/tk-harmony-base';

// 基础标题栏
@Component
struct BasicTitleBarPage {
  @State private titleBarAttribute: TKTitleBarAttribute = new TKTitleBarAttribute();
  @State private titleBarStyleAttribute: TKTitleBarStyleAttribute = new TKTitleBarStyleAttribute();

  aboutToAppear() {
    // 配置标题栏属性
    this.titleBarAttribute.title = "基础页面";
    this.titleBarAttribute.backPressEnable = true;
    this.titleBarAttribute.onBackClick = (event) => {
      console.log('返回按钮点击');
      return false; // 返回false使用默认返回行为
    };
  }

  build() {
    Column() {
      TKTitleBar({
        attribute: this.titleBarAttribute,
        styleAttribute: this.titleBarStyleAttribute
      })

      // 页面内容
      Column() {
        Text('页面内容')
          .fontSize(16)
          .margin(20)
      }
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
  }
}

// 自定义样式标题栏
@Component
struct CustomStyledTitleBar {
  @State private titleBarAttribute: TKTitleBarAttribute = new TKTitleBarAttribute();
  @State private titleBarStyleAttribute: TKTitleBarStyleAttribute = new TKTitleBarStyleAttribute();

  aboutToAppear() {
    // 配置标题栏属性
    this.titleBarAttribute.title = "自定义样式";
    this.titleBarAttribute.isLayoutFullScreen = true;
    this.titleBarAttribute.statusBarEnable = true;
    this.titleBarAttribute.rightBtnTxt = "保存";
    this.titleBarAttribute.rightBtnMode = "0"; // 文本模式

    // 配置样式
    this.titleBarStyleAttribute.titleColor = "#FFFFFFFF";
    this.titleBarStyleAttribute.titleBgColor = "#FF007AFF";
    this.titleBarStyleAttribute.statusStyle = "1"; // 白色状态栏图标
    this.titleBarStyleAttribute.leftBtnColor = "#FFFFFFFF";
    this.titleBarStyleAttribute.rightBtnColor = "#FFFFFFFF";

    // 事件处理
    this.titleBarAttribute.onRightBtnClick = (event, action, data) => {
      console.log('保存按钮点击');
      this.saveData();
      return true;
    };
  }

  private saveData() {
    console.log('保存数据');
  }

  build() {
    Column() {
      TKTitleBar({
        attribute: this.titleBarAttribute,
        styleAttribute: this.titleBarStyleAttribute
      })

      // 页面内容
      Text('自定义样式的标题栏')
        .fontSize(16)
        .margin(20)
    }
    .width('100%')
    .height('100%')
  }
}

// 带关闭按钮的标题栏
@Component
struct TitleBarWithClose {
  @State private titleBarAttribute: TKTitleBarAttribute = new TKTitleBarAttribute();

  aboutToAppear() {
    this.titleBarAttribute.title = "模态页面";
    this.titleBarAttribute.leftBtnMode = "3"; // 返回图片+关闭文字
    this.titleBarAttribute.backPressEnable = true;
    this.titleBarAttribute.closePressEnable = true;

    this.titleBarAttribute.onBackClick = (event) => {
      console.log('返回按钮点击');
      return false;
    };

    this.titleBarAttribute.onCloseClick = (event) => {
      console.log('关闭按钮点击');
      // 自定义关闭逻辑
      this.closeModal();
      return true; // 返回true阻止默认行为
    };
  }

  private closeModal() {
    console.log('关闭模态页面');
  }

  build() {
    Column() {
      TKTitleBar({
        attribute: this.titleBarAttribute
      })

      Text('带关闭按钮的标题栏')
        .fontSize(16)
        .margin(20)
    }
    .width('100%')
    .height('100%')
  }
}
```

### 高级用法
```typescript
// 标题栏管理器
class TitleBarManager {
  private static instance: TitleBarManager;

  static getInstance(): TitleBarManager {
    if (!TitleBarManager.instance) {
      TitleBarManager.instance = new TitleBarManager();
    }
    return TitleBarManager.instance;
  }

  // 创建基础标题栏配置
  createBasicTitleBar(title: string): {
    attribute: TKTitleBarAttribute,
    styleAttribute: TKTitleBarStyleAttribute
  } {
    const attribute = new TKTitleBarAttribute();
    const styleAttribute = new TKTitleBarStyleAttribute();

    attribute.title = title;
    attribute.backPressEnable = true;
    attribute.isLayoutFullScreen = true;
    attribute.statusBarEnable = true;

    return { attribute, styleAttribute };
  }

  // 创建深色主题标题栏
  createDarkTitleBar(title: string): {
    attribute: TKTitleBarAttribute,
    styleAttribute: TKTitleBarStyleAttribute
  } {
    const { attribute, styleAttribute } = this.createBasicTitleBar(title);

    styleAttribute.titleColor = "#FFFFFFFF";
    styleAttribute.titleBgColor = "#FF1C1C1E";
    styleAttribute.statusStyle = "1";
    styleAttribute.leftBtnColor = "#FFFFFFFF";

    return { attribute, styleAttribute };
  }

  // 创建透明标题栏
  createTransparentTitleBar(title: string): {
    attribute: TKTitleBarAttribute,
    styleAttribute: TKTitleBarStyleAttribute
  } {
    const { attribute, styleAttribute } = this.createBasicTitleBar(title);

    styleAttribute.titleBgColor = "#00000000";
    styleAttribute.titleColor = "#FF000000";

    return { attribute, styleAttribute };
  }

  // 创建带右侧按钮的标题栏
  createTitleBarWithRightButton(
    title: string,
    buttonText: string,
    onButtonClick: (event?: ClickEvent, action?: string, data?: Record<string, Object>) => boolean
  ): {
    attribute: TKTitleBarAttribute,
    styleAttribute: TKTitleBarStyleAttribute
  } {
    const { attribute, styleAttribute } = this.createBasicTitleBar(title);

    attribute.rightBtnTxt = buttonText;
    attribute.rightBtnMode = "0";
    attribute.onRightBtnClick = onButtonClick;

    return { attribute, styleAttribute };
  }
}

// 自定义标题栏内容
@Component
struct CustomTitleBarContent {
  @State private titleBarAttribute: TKTitleBarAttribute = new TKTitleBarAttribute();
  @State private currentStep: number = 1;
  @State private totalSteps: number = 3;

  aboutToAppear() {
    this.titleBarAttribute.title = "自定义内容";
    this.titleBarAttribute.isLayoutFullScreen = true;
  }

  @Builder
  customTitleView(titleBarController: any) {
    Row() {
      // 左侧返回按钮
      Image($r('app.media.back_icon'))
        .width(20)
        .height(20)
        .onClick(() => {
          titleBarController.routerBack();
        })
        .margin({ left: 15 })

      // 中间进度指示器
      Row() {
        ForEach(Array.from({ length: this.totalSteps }, (_, i) => i + 1), (step: number) => {
          Circle({ width: 8, height: 8 })
            .fill(step <= this.currentStep ? "#FF007AFF" : "#FFCCCCCC")
            .margin({ horizontal: 4 })
        })
      }
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)

      // 右侧步骤文字
      Text(`${this.currentStep}/${this.totalSteps}`)
        .fontSize(14)
        .fontColor("#FF666666")
        .margin({ right: 15 })
    }
    .width('100%')
    .height('100%')
    .alignItems(VerticalAlign.Center)
  }

  build() {
    Column() {
      TKTitleBar({
        attribute: this.titleBarAttribute,
        customTitleView: this.customTitleView
      })

      // 页面内容
      Column() {
        Text(`当前步骤: ${this.currentStep}`)
          .fontSize(18)
          .margin(20)

        Button('下一步')
          .onClick(() => {
            if (this.currentStep < this.totalSteps) {
              this.currentStep++;
            }
          })
          .margin(10)

        Button('上一步')
          .onClick(() => {
            if (this.currentStep > 1) {
              this.currentStep--;
            }
          })
      }
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
    }
    .width('100%')
    .height('100%')
  }
}

// 动态标题栏
@Component
struct DynamicTitleBar {
  @State private titleBarAttribute: TKTitleBarAttribute = new TKTitleBarAttribute();
  @State private titleBarStyleAttribute: TKTitleBarStyleAttribute = new TKTitleBarStyleAttribute();
  @State private isDarkMode: boolean = false;
  @State private pageTitle: string = "动态标题栏";

  aboutToAppear() {
    this.updateTitleBarStyle();

    this.titleBarAttribute.onRightBtnClick = (event, action, data) => {
      this.toggleTheme();
      return true;
    };
  }

  private updateTitleBarStyle() {
    this.titleBarAttribute.title = this.pageTitle;
    this.titleBarAttribute.rightBtnTxt = this.isDarkMode ? "浅色" : "深色";
    this.titleBarAttribute.rightBtnMode = "0";

    if (this.isDarkMode) {
      this.titleBarStyleAttribute.titleColor = "#FFFFFFFF";
      this.titleBarStyleAttribute.titleBgColor = "#FF1C1C1E";
      this.titleBarStyleAttribute.statusStyle = "1";
      this.titleBarStyleAttribute.leftBtnColor = "#FFFFFFFF";
      this.titleBarStyleAttribute.rightBtnColor = "#FFFFFFFF";
    } else {
      this.titleBarStyleAttribute.titleColor = "#FF000000";
      this.titleBarStyleAttribute.titleBgColor = "#FFFFFFFF";
      this.titleBarStyleAttribute.statusStyle = "0";
      this.titleBarStyleAttribute.leftBtnColor = "#FF000000";
      this.titleBarStyleAttribute.rightBtnColor = "#FF007AFF";
    }
  }

  private toggleTheme() {
    this.isDarkMode = !this.isDarkMode;
    this.updateTitleBarStyle();
  }

  build() {
    Column() {
      TKTitleBar({
        attribute: this.titleBarAttribute,
        styleAttribute: this.titleBarStyleAttribute
      })

      Column() {
        Text(`当前主题: ${this.isDarkMode ? '深色' : '浅色'}`)
          .fontSize(16)
          .fontColor(this.isDarkMode ? "#FFFFFFFF" : "#FF000000")
          .margin(20)

        Button('更改标题')
          .onClick(() => {
            this.pageTitle = `新标题 ${Date.now()}`;
            this.updateTitleBarStyle();
          })
      }
      .layoutWeight(1)
      .justifyContent(FlexAlign.Center)
      .backgroundColor(this.isDarkMode ? "#FF000000" : "#FFFFFFFF")
    }
    .width('100%')
    .height('100%')
  }
}

// 使用示例
@Component
struct TitleBarExamplePage {
  @State private currentExample: number = 0;
  private titleBarManager = TitleBarManager.getInstance();

  build() {
    Column() {
      // 根据当前示例显示不同的标题栏
      if (this.currentExample === 0) {
        BasicTitleBarPage()
      } else if (this.currentExample === 1) {
        CustomStyledTitleBar()
      } else if (this.currentExample === 2) {
        TitleBarWithClose()
      } else if (this.currentExample === 3) {
        CustomTitleBarContent()
      } else if (this.currentExample === 4) {
        DynamicTitleBar()
      }

      // 底部切换按钮
      Row() {
        Button('基础')
          .onClick(() => this.currentExample = 0)
        Button('样式')
          .onClick(() => this.currentExample = 1)
        Button('关闭')
          .onClick(() => this.currentExample = 2)
        Button('自定义')
          .onClick(() => this.currentExample = 3)
        Button('动态')
          .onClick(() => this.currentExample = 4)
      }
      .justifyContent(FlexAlign.SpaceEvenly)
      .width('100%')
      .padding(10)
      .backgroundColor("#FFF0F0F0")
    }
    .width('100%')
    .height('100%')
  }
}
```

## 📚 相关资源

### 官方文档
- HarmonyOS状态栏和导航栏开发指南
- ArkUI安全区域适配文档

### 参考资料
- 《移动应用UI设计》- 标题栏设计规范
- 《用户界面设计》- 导航设计原则

### 相关文件
- `TKTitleBarController.ets` - 标题栏控制器
- `TKWindowHelper.ets` - 窗口管理工具
- `TKThemeManager.ets` - 主题管理器
