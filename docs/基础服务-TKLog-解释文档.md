# TKLog

> **文件路径**: `src/main/ets/util/logger/TKLog.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 初级  
> **前置知识**: 日志系统概念、文件操作、命名空间使用

## 📋 文件概述

### 功能定位
TKLog是HMThinkBaseHar框架的**日志工具类**，提供了完整的日志记录功能。它采用命名空间设计，集成了鸿蒙hilog系统和文件日志，支持多种日志级别、双重输出、格式化记录等功能，为应用提供统一的日志管理服务。

### 核心职责
- **日志级别管理**：支持debug、info、warn、error等多种日志级别
- **双重输出机制**：同时支持控制台输出和文件输出
- **日志格式化**：提供统一的日志格式和时间戳
- **文件日志管理**：支持日志文件的写入、读取和清理
- **性能优化**：基于鸿蒙hilog系统，提供高性能日志记录

### 设计模式
- **命名空间模式**：使用namespace封装日志功能，避免全局污染
- **策略模式**：支持不同的日志输出策略（控制台、文件）
- **单例模式**：全局统一的日志配置和管理
- **工厂模式**：根据日志类型选择相应的输出方式

## 🔧 核心功能

### 日志级别配置

#### 级别1：`debug`
- **数值**: 1
- **用途**: 调试信息，开发阶段使用
- **使用场景**: 详细的程序执行流程跟踪

#### 级别2：`info`
- **数值**: 2
- **用途**: 一般信息，记录程序正常运行状态
- **使用场景**: 关键操作的成功执行记录

#### 级别3：`warn`
- **数值**: 3
- **用途**: 警告信息，潜在问题提示
- **使用场景**: 非致命错误或异常情况

#### 级别4：`error`
- **数值**: 4
- **用途**: 错误信息，程序异常或失败
- **使用场景**: 异常捕获和错误处理

#### 级别5：`off`
- **数值**: 5
- **用途**: 关闭日志输出
- **使用场景**: 生产环境关闭日志

### 日志输出类型

#### 类型1：`console`
- **功能**: 控制台输出
- **特点**: 基于鸿蒙hilog系统，性能优秀
- **使用场景**: 开发调试阶段

#### 类型2：`file`
- **功能**: 文件输出
- **特点**: 持久化存储，支持日志分析
- **使用场景**: 生产环境日志收集

#### 类型3：`console,file`
- **功能**: 双重输出
- **特点**: 同时输出到控制台和文件
- **使用场景**: 开发和测试环境

### 主要方法

#### 方法1：`setLogLevel(logLevel: string)`
- **功能**: 设置日志级别
- **参数**: logLevel - 日志级别字符串
- **使用场景**: 根据环境配置日志输出级别

#### 方法2：`setLogType(logType: string)`
- **功能**: 设置日志输出类型
- **参数**: logType - 输出类型字符串
- **使用场景**: 配置日志输出方式

#### 方法3：`debug/info/warn/error(format, ...args)`
- **功能**: 记录不同级别的日志
- **参数**: format - 格式化字符串，args - 参数列表
- **使用场景**: 在代码中记录各种级别的日志信息

## 💡 技术要点

### 核心算法/逻辑

#### 日志级别控制机制
```typescript
export function debug(format: string, ...args: Object[]) {
  if (level <= 1) { // 只有当前级别允许时才输出
    if (hilog.isLoggable(domain, tag, hilog.LogLevel.DEBUG)) {
      if (type.includes("console")) {
        hilog.debug(domain, tag, format, args);
      }
      if (type.includes("file")) {
        writeLog("DEBUG", format, args);
      }
    }
  }
}
```

#### 双重输出机制
```typescript
// 控制台输出
if (type.includes("console")) {
  hilog.info(domain, tag, format, args);
}

// 文件输出
if (type.includes("file")) {
  writeLog("INFO", format, args);
}
```

#### 文件日志写入机制
```typescript
export function writeLog(level: string, format: string, ...args: Object[]) {
  const logPath: string = `${TKFileHelper.tempDir()}/thinkive/log/temp.log`;
  let content: string = 
    `${TKDateHelper.formatDate(new Date(), "YYYY-MM-DD HH:mm:ss:SSS")} [${level}] ${util.format(format, args)}\n`;
  TKFileHelper.writeFile(content, logPath, true); // 追加模式写入
}
```

#### 日志级别设置逻辑
```typescript
export function setLogLevel(logLevel: string) {
  if (logLevel) {
    switch (logLevel) {
      case 'debug':
        level = 1;
        break;
      case 'info':
        level = 2;
        break;
      case 'warn':
        level = 3;
        break;
      case 'error':
        level = 4;
        break;
      case 'off':
        level = 5;
        break;
      default:
        level = 0; // 默认输出所有级别
    }
  }
}
```

#### 日志文件管理
```typescript
// 清理日志文件
export function clearLogFile() {
  const logPath: string = `${TKFileHelper.tempDir()}/thinkive/log/temp.log`;
  TKFileHelper.deleteFile(logPath);
}

// 读取日志文件
export function readLogFile(): string {
  const logPath: string = `${TKFileHelper.tempDir()}/thinkive/log/temp.log`;
  return TKDataHelper.uint8ArrayToString(TKFileHelper.readFile(logPath));
}
```

### 实现机制分析

#### 命名空间设计
- **全局访问**：通过TKLog命名空间提供全局访问
- **功能封装**：将所有日志相关功能封装在一个命名空间内
- **配置集中**：日志配置统一管理，避免分散配置
- **API简洁**：提供简洁易用的API接口

#### hilog集成
- **系统集成**：基于鸿蒙hilog系统，性能优秀
- **域标识**：使用固定的domain(0xffff)和tag("Thinkive")
- **级别检查**：通过hilog.isLoggable检查日志级别
- **格式化支持**：支持格式化字符串和参数列表

#### 文件日志机制
- **路径管理**：统一的日志文件路径管理
- **追加写入**：使用追加模式避免覆盖历史日志
- **格式统一**：统一的时间戳和级别格式
- **编码处理**：正确处理字符编码和换行符

#### 双重输出策略
- **类型检查**：通过字符串包含检查支持多种输出类型
- **独立控制**：控制台和文件输出可以独立控制
- **性能考虑**：避免不必要的格式化和写入操作

### 性能考虑
- **级别过滤**：在方法入口进行级别过滤，避免不必要的处理
- **hilog优化**：基于鸿蒙hilog系统，底层优化
- **延迟格式化**：只有在需要输出时才进行格式化
- **文件缓存**：文件写入使用系统缓存机制

### 错误处理
- **异常隔离**：日志记录异常不影响主业务流程
- **静默失败**：日志操作失败时静默处理
- **资源保护**：文件操作异常时保护系统资源
- **降级处理**：文件日志失败时仍可使用控制台日志

### 最佳实践
- **级别合理使用**：根据信息重要性选择合适的日志级别
- **格式化字符串**：使用格式化字符串提高性能和可读性
- **敏感信息保护**：避免在日志中记录敏感信息
- **生产环境配置**：生产环境适当提高日志级别

## 🔗 依赖关系

### 依赖的模块
- `hilog` - 鸿蒙日志系统，提供底层日志功能
- `TKFileHelper` - 文件工具，处理日志文件操作
- `TKDateHelper` - 日期工具，格式化时间戳
- `TKDataHelper` - 数据工具，处理字符编码转换
- `util` - 鸿蒙工具库，提供字符串格式化功能

### 被依赖情况
- `框架所有模块` - 几乎所有框架模块都使用TKLog记录日志
- `TKCacheManager` - 缓存管理器使用日志记录操作信息
- `TKHttpDao` - HTTP数据访问使用日志记录请求信息
- `各种插件` - 插件系统使用日志记录执行状态

### 关联文件
- `TKFileHelper.ets` - 文件工具类，提供文件操作支持
- `TKDateHelper.ets` - 日期工具类，提供时间格式化
- `TKDataHelper.ets` - 数据工具类，提供编码转换
- `TKStringHelper.ets` - 字符串工具类，提供字符串处理

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKLog是框架调试和运维的重要工具：
1. **调试支持**：为开发调试提供重要的信息输出
2. **运维监控**：为生产环境提供日志收集和分析
3. **问题排查**：帮助快速定位和解决问题
4. **使用频率高**：在框架的各个模块中广泛使用

理解TKLog有助于掌握框架的调试和监控机制，是开发和运维的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解日志系统**：掌握日志系统的基本概念和作用
2. **分析级别控制**：理解日志级别的设计和使用场景
3. **学习输出机制**：掌握控制台和文件输出的实现
4. **实践日志应用**：在实际开发中合理使用日志功能

### 前置学习
- 日志系统的基本概念
- 鸿蒙hilog系统的使用
- 文件操作和路径管理
- 命名空间的概念和使用

### 后续学习
- `TKFileHelper.ets` - 学习文件操作的实现
- `TKDateHelper.ets` - 了解日期格式化功能
- `TKDataHelper.ets` - 掌握数据转换工具

### 实践建议
1. **基础日志使用**：在代码中添加不同级别的日志
2. **配置实验**：测试不同的日志级别和输出类型
3. **文件日志管理**：实践日志文件的读取和清理
4. **性能测试**：测试日志对应用性能的影响

### 常见问题
1. **问题**: 如何在生产环境配置日志？
   **解答**: 生产环境建议设置日志级别为"warn"或"error"，输出类型设置为"file"，避免敏感信息泄露。

2. **问题**: 日志文件会无限增长吗？
   **解答**: 当前实现会持续追加到同一文件，建议在应用中定期调用clearLogFile()清理，或实现日志轮转机制。

## 📝 代码示例

### 基础使用
```typescript
import { TKLog } from '@thinkive/tk-harmony-base';

// 基础日志记录
function basicLogging() {
  // 调试信息
  TKLog.debug("用户登录流程开始，用户ID: %{public}s", "12345");

  // 一般信息
  TKLog.info("用户登录成功，用户名: %{public}s", "张三");

  // 警告信息
  TKLog.warn("用户密码即将过期，剩余天数: %{public}d", 3);

  // 错误信息
  TKLog.error("用户登录失败，错误代码: %{public}d，错误信息: %{public}s", 401, "密码错误");
}

// 配置日志级别和输出类型
function configureLogging() {
  // 设置日志级别为info，只输出info及以上级别的日志
  TKLog.setLogLevel("info");

  // 设置输出类型为控制台和文件双重输出
  TKLog.setLogType("console,file");

  // 测试不同级别的日志
  TKLog.debug("这条debug日志不会输出"); // 被过滤
  TKLog.info("这条info日志会输出");      // 会输出
  TKLog.warn("这条warn日志会输出");      // 会输出
  TKLog.error("这条error日志会输出");    // 会输出
}

// 文件日志管理
function fileLogManagement() {
  // 写入一些日志
  TKLog.info("开始文件日志测试");
  TKLog.warn("这是一条警告信息");
  TKLog.error("这是一条错误信息");

  // 读取日志文件内容
  const logContent = TKLog.readLogFile();
  console.log("日志文件内容:", logContent);

  // 清理日志文件
  TKLog.clearLogFile();
  console.log("日志文件已清理");
}

// 执行示例
basicLogging();
configureLogging();
fileLogManagement();
```

### 高级用法
```typescript
// 日志管理器
class LogManager {
  private static instance: LogManager;
  private logLevel: string = "info";
  private logType: string = "console";

  static getInstance(): LogManager {
    if (!LogManager.instance) {
      LogManager.instance = new LogManager();
    }
    return LogManager.instance;
  }

  // 初始化日志配置
  initialize(config: {
    level?: string,
    type?: string,
    enableFileRotation?: boolean
  } = {}) {
    this.logLevel = config.level || "info";
    this.logType = config.type || "console";

    TKLog.setLogLevel(this.logLevel);
    TKLog.setLogType(this.logType);

    if (config.enableFileRotation) {
      this.startLogRotation();
    }

    TKLog.info("日志管理器初始化完成，级别: %{public}s，类型: %{public}s",
               this.logLevel, this.logType);
  }

  // 启动日志轮转
  private startLogRotation() {
    // 每小时检查一次日志文件大小
    setInterval(() => {
      this.rotateLogIfNeeded();
    }, 60 * 60 * 1000);
  }

  // 日志轮转检查
  private rotateLogIfNeeded() {
    try {
      const logContent = TKLog.readLogFile();
      const logSize = logContent.length;

      // 如果日志文件超过1MB，进行轮转
      if (logSize > 1024 * 1024) {
        TKLog.info("日志文件大小超过限制，开始轮转");

        // 备份当前日志
        this.backupCurrentLog();

        // 清理当前日志文件
        TKLog.clearLogFile();

        TKLog.info("日志轮转完成");
      }
    } catch (error) {
      TKLog.error("日志轮转检查失败: %{public}s", error.message);
    }
  }

  // 备份当前日志
  private backupCurrentLog() {
    try {
      const logContent = TKLog.readLogFile();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `${TKFileHelper.tempDir()}/thinkive/log/backup_${timestamp}.log`;

      TKFileHelper.writeFile(logContent, backupPath, false);
      TKLog.info("日志已备份到: %{public}s", backupPath);
    } catch (error) {
      TKLog.error("日志备份失败: %{public}s", error.message);
    }
  }

  // 获取日志统计信息
  getLogStatistics(): {
    fileSize: number,
    lineCount: number,
    lastModified: string
  } {
    try {
      const logContent = TKLog.readLogFile();
      const lines = logContent.split('\n').filter(line => line.trim().length > 0);

      return {
        fileSize: logContent.length,
        lineCount: lines.length,
        lastModified: new Date().toISOString()
      };
    } catch (error) {
      TKLog.error("获取日志统计失败: %{public}s", error.message);
      return {
        fileSize: 0,
        lineCount: 0,
        lastModified: ""
      };
    }
  }

  // 搜索日志
  searchLogs(keyword: string, level?: string): string[] {
    try {
      const logContent = TKLog.readLogFile();
      const lines = logContent.split('\n');

      return lines.filter(line => {
        const matchesKeyword = line.includes(keyword);
        const matchesLevel = !level || line.includes(`[${level.toUpperCase()}]`);
        return matchesKeyword && matchesLevel;
      });
    } catch (error) {
      TKLog.error("搜索日志失败: %{public}s", error.message);
      return [];
    }
  }

  // 导出日志
  exportLogs(format: 'txt' | 'json' = 'txt'): string {
    try {
      const logContent = TKLog.readLogFile();

      if (format === 'json') {
        const lines = logContent.split('\n').filter(line => line.trim().length > 0);
        const logEntries = lines.map(line => {
          const match = line.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}:\d{3}) \[(\w+)\] (.+)$/);
          if (match) {
            return {
              timestamp: match[1],
              level: match[2],
              message: match[3]
            };
          }
          return { timestamp: "", level: "", message: line };
        });

        return JSON.stringify(logEntries, null, 2);
      }

      return logContent;
    } catch (error) {
      TKLog.error("导出日志失败: %{public}s", error.message);
      return "";
    }
  }
}

// 性能监控日志
class PerformanceLogger {
  private startTimes: Map<string, number> = new Map();

  // 开始性能监控
  startTiming(operation: string) {
    this.startTimes.set(operation, Date.now());
    TKLog.debug("开始性能监控: %{public}s", operation);
  }

  // 结束性能监控
  endTiming(operation: string) {
    const startTime = this.startTimes.get(operation);
    if (startTime) {
      const duration = Date.now() - startTime;
      this.startTimes.delete(operation);

      if (duration > 1000) {
        TKLog.warn("性能警告 - %{public}s 耗时: %{public}d ms", operation, duration);
      } else {
        TKLog.info("性能监控 - %{public}s 耗时: %{public}d ms", operation, duration);
      }
    } else {
      TKLog.error("性能监控错误 - 未找到操作: %{public}s", operation);
    }
  }

  // 监控函数执行
  async monitorFunction<T>(
    operation: string,
    func: () => Promise<T> | T
  ): Promise<T> {
    this.startTiming(operation);
    try {
      const result = await func();
      this.endTiming(operation);
      return result;
    } catch (error) {
      this.endTiming(operation);
      TKLog.error("操作执行失败 - %{public}s: %{public}s", operation, error.message);
      throw error;
    }
  }
}

// 使用示例
class UserService {
  private logManager = LogManager.getInstance();
  private performanceLogger = new PerformanceLogger();

  constructor() {
    // 初始化日志配置
    this.logManager.initialize({
      level: "info",
      type: "console,file",
      enableFileRotation: true
    });
  }

  // 用户登录
  async login(username: string, password: string) {
    return this.performanceLogger.monitorFunction("用户登录", async () => {
      TKLog.info("用户登录开始，用户名: %{public}s", username);

      try {
        // 模拟登录验证
        await this.validateCredentials(username, password);

        TKLog.info("用户登录成功，用户名: %{public}s", username);
        return { success: true, message: "登录成功" };
      } catch (error) {
        TKLog.error("用户登录失败，用户名: %{public}s，错误: %{public}s",
                   username, error.message);
        return { success: false, message: error.message };
      }
    });
  }

  // 验证用户凭据
  private async validateCredentials(username: string, password: string) {
    TKLog.debug("开始验证用户凭据");

    // 模拟异步验证
    await new Promise(resolve => setTimeout(resolve, 100));

    if (username === "admin" && password === "password") {
      TKLog.debug("用户凭据验证成功");
    } else {
      throw new Error("用户名或密码错误");
    }
  }

  // 获取日志报告
  getLogReport() {
    const stats = this.logManager.getLogStatistics();
    TKLog.info("生成日志报告，文件大小: %{public}d 字节，行数: %{public}d",
               stats.fileSize, stats.lineCount);

    return {
      statistics: stats,
      recentErrors: this.logManager.searchLogs("ERROR", "error"),
      recentWarnings: this.logManager.searchLogs("WARN", "warn")
    };
  }

  // 导出日志
  exportLogData(format: 'txt' | 'json' = 'txt') {
    TKLog.info("开始导出日志数据，格式: %{public}s", format);
    return this.logManager.exportLogs(format);
  }
}

// 使用示例
async function demonstrateLogging() {
  const userService = new UserService();

  // 测试登录
  await userService.login("admin", "password");
  await userService.login("user", "wrongpassword");

  // 生成日志报告
  const report = userService.getLogReport();
  console.log("日志报告:", report);

  // 导出日志
  const logData = userService.exportLogData("json");
  console.log("导出的日志数据:", logData);
}

// 执行演示
demonstrateLogging();
```

## 📚 相关资源

### 官方文档
- HarmonyOS hilog开发指南
- 日志系统设计最佳实践

### 参考资料
- 《高性能日志系统设计》- 日志系统架构
- 《系统监控与运维》- 日志分析和管理

### 相关文件
- `TKFileHelper.ets` - 文件操作工具
- `TKDateHelper.ets` - 日期格式化工具
- `TKDataHelper.ets` - 数据转换工具
