# TKPreferences

> **文件路径**: `src/main/ets/util/cache/TKPreferences.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 键值对存储、数据持久化、序列化反序列化、文件操作

## 📋 文件概述

### 功能定位
TKPreferences是HMThinkBaseHar框架的**偏好设置存储类**，提供了轻量级的键值对存储功能。它基于鸿蒙dataPreferences API，集成了自动序列化、大数据文件存储、智能缓存等功能，支持任意类型数据的持久化存储，为应用提供简单易用的配置和数据存储服务。

### 核心职责
- **键值对存储**：提供简单的键值对数据存储和读取功能
- **自动序列化**：自动处理复杂对象的序列化和反序列化
- **智能存储策略**：小数据使用Preferences，大数据自动转为文件存储
- **数据持久化**：确保数据在应用重启后仍然可用
- **类型安全**：支持泛型，提供类型安全的数据操作

### 设计模式
- **适配器模式**：封装鸿蒙dataPreferences API，提供统一接口
- **策略模式**：根据数据大小选择不同的存储策略
- **代理模式**：作为底层存储API的代理，提供增强功能
- **模板方法模式**：定义数据操作的标准流程

## 🔧 核心功能

### 构造方法

#### 构造函数：`constructor(fileName?: string)`
- **功能**: 创建偏好设置存储实例
- **参数**: fileName - 存储文件名（可选，默认'TKPreferencesFile'）
- **初始化**: 自动创建或获取对应的Preferences实例
- **使用场景**: 创建特定用途的配置存储

### 核心存储方法

#### 方法1：`setData<T>(key: string, data: T)`
- **功能**: 存储键值对数据
- **参数**: 
  - key - 数据键名
  - data - 要存储的数据（任意类型）
- **特性**: 自动序列化，大数据自动转文件存储
- **使用场景**: 保存配置、缓存数据、用户偏好等

#### 方法2：`getData<T>(key: string, defValue?: T)`
- **功能**: 读取键值对数据
- **参数**: 
  - key - 数据键名
  - defValue - 默认值（可选）
- **返回值**: T | undefined - 读取的数据或默认值
- **特性**: 自动反序列化，支持文件存储数据读取
- **使用场景**: 读取配置、获取缓存数据

#### 方法3：`deleteData(key: string)`
- **功能**: 删除指定键的数据
- **参数**: key - 要删除的数据键名
- **特性**: 同时删除Preferences和文件存储中的数据
- **使用场景**: 清理过期数据、重置配置

#### 方法4：`getFileName()`
- **功能**: 获取当前存储文件名
- **返回值**: string - 文件名
- **使用场景**: 调试、日志记录、文件管理

## 💡 技术要点

### 核心算法/逻辑

#### 智能存储策略
```typescript
public setData<T>(key: string, data: T) {
  try {
    if (this.preferences) {
      let value: string = TKObjectHelper.serialize(data) as string;
      
      // 检查数据大小，超过8KB自动转为文件存储
      if (value.length > 8000) {
        TKLog.warn(`[${this.fileName}]存储[${key}]的内容过大[${value.length}]，自动转为自定义文件存储`);
        
        // 使用文件存储
        let filePath: string = this.getLocalFilePath();
        let fileData: Record<string, Object | undefined> = this.getLocalFileData(filePath);
        fileData[key] = value;
        TKFileHelper.writeFile(JSON.stringify(fileData), filePath);
        
        // 清理Preferences中的数据
        this.preferences.deleteSync(key);
        this.preferences.flush();
      } else {
        // 使用Preferences存储
        this.preferences.putSync(key, value);
        this.preferences.flush();
      }
    }
  } catch (error) {
    TKLog.error(`[TKPreferences]缓存数据异常，code: ${error.code}， message: ${error.message}`);
  }
}
```

#### 数据读取机制
```typescript
public getData<T>(key: string, defValue: T | undefined = undefined): T | undefined {
  try {
    if (this.preferences) {
      // 首先尝试从Preferences读取
      let dataStr: string = this.preferences.getSync(key, "") as string;
      
      // 如果Preferences中没有，尝试从文件读取
      if (TKStringHelper.isBlank(dataStr)) {
        let filePath: string = this.getLocalFilePath();
        let fileData: Record<string, Object | undefined> = this.getLocalFileData(filePath);
        dataStr = TKMapHelper.getString(fileData, key);
      }
      
      // 反序列化数据
      let data: T | undefined = TKObjectHelper.deserialize<T>(dataStr) as T ?? defValue;
      return data;
    }
    return defValue;
  } catch (error) {
    TKLog.error(`[TKPreferences]获取数据异常，code: ${error.code}， message: ${error.message}`);
    return undefined;
  }
}
```

#### 文件存储路径管理
```typescript
private getLocalFilePath(): string {
  let filePath: string = TKFileHelper.cacheDir() + "/" + this.fileName;
  return filePath;
}

private getLocalFileData(filePath: string): Record<string, Object | undefined> {
  let fileContent: string = TKDataHelper.uint8ArrayToString(TKFileHelper.readFile(filePath));
  let fileData: Record<string, Object | undefined> = 
    TKStringHelper.isNotBlank(fileContent) ? JSON.parse(fileContent) : {};
  return fileData;
}
```

#### 数据删除机制
```typescript
public deleteData(key: string) {
  try {
    if (this.preferences) {
      // 删除Preferences中的数据
      this.preferences.deleteSync(key);
      this.preferences.flush();
      
      // 删除文件存储中的数据
      let filePath: string = this.getLocalFilePath();
      let fileData: Record<string, Object | undefined> = this.getLocalFileData(filePath);
      if (Object.entries(fileData).length > 0) {
        fileData[key] = undefined;
        TKFileHelper.writeFile(JSON.stringify(fileData), filePath);
      }
    }
  } catch (error) {
    TKLog.error(`[TKPreferences]删除数据异常，code: ${error.code}， message: ${error.message}`);
  }
}
```

### 实现机制分析

#### 双重存储策略
- **Preferences存储**：适合小数据（<8KB），读写速度快
- **文件存储**：适合大数据（≥8KB），无大小限制
- **自动切换**：根据数据大小自动选择存储方式
- **透明访问**：读取时自动从两种存储中查找数据

#### 序列化机制
- **自动序列化**：使用TKObjectHelper自动处理对象序列化
- **类型保持**：通过泛型保持数据类型信息
- **复杂对象支持**：支持嵌套对象、数组等复杂数据结构
- **错误容错**：序列化失败时记录错误并返回默认值

#### 文件管理
- **路径统一**：使用缓存目录存储文件数据
- **JSON格式**：文件数据使用JSON格式存储，便于调试
- **增量更新**：文件数据采用增量更新方式，避免全量重写
- **错误处理**：文件操作失败时不影响Preferences操作

#### 性能优化
- **同步操作**：使用同步API提供即时的数据一致性
- **缓存刷新**：及时调用flush()确保数据持久化
- **内存管理**：避免大数据在内存中长期驻留
- **错误隔离**：单个操作失败不影响其他数据

### 性能考虑
- **存储选择**：根据数据大小智能选择最优存储方式
- **同步操作**：使用同步API确保数据一致性
- **内存优化**：大数据自动转为文件存储，减少内存占用
- **批量操作**：支持多个键值对的批量处理

### 错误处理
- **异常捕获**：所有操作都有完整的异常处理
- **错误日志**：详细记录操作失败的错误信息
- **优雅降级**：操作失败时返回默认值，不中断程序
- **数据保护**：确保部分失败不影响其他数据

### 最佳实践
- **合理命名**：使用有意义的键名和文件名
- **数据大小**：注意数据大小，避免频繁的存储策略切换
- **类型安全**：使用泛型确保数据类型安全
- **错误处理**：检查返回值，处理可能的异常情况

## 🔗 依赖关系

### 依赖的模块
- `dataPreferences` - 鸿蒙偏好设置API，提供底层存储功能
- `TKObjectHelper` - 对象工具，处理序列化和反序列化
- `TKFileHelper` - 文件工具，处理文件存储操作
- `TKDataHelper` - 数据工具，处理数据转换
- `TKStringHelper` - 字符串工具，处理字符串操作
- `TKMapHelper` - Map工具，处理映射数据操作
- `TKContextHelper` - 上下文工具，获取应用上下文
- `TKLog` - 日志工具，记录操作日志

### 被依赖情况
- `TKCacheManager` - 缓存管理器，使用TKPreferences进行文件缓存
- `各配置管理器` - 使用TKPreferences存储应用配置
- `用户偏好模块` - 使用TKPreferences存储用户设置
- `数据持久化模块` - 使用TKPreferences进行轻量级数据存储

### 关联文件
- `TKSingleKVStore.ets` - 单实例键值存储，提供分布式存储
- `TKObjectHelper.ets` - 对象工具类，提供序列化功能
- `TKFileHelper.ets` - 文件工具类，提供文件操作
- `TKCacheManager.ets` - 缓存管理器，使用偏好设置存储

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKPreferences是框架数据存储的重要组件：
1. **配置存储**：为应用配置和用户偏好提供持久化存储
2. **轻量级存储**：提供简单易用的键值对存储方案
3. **智能优化**：自动选择最优存储策略，平衡性能和容量
4. **类型安全**：通过泛型提供类型安全的数据操作

理解TKPreferences有助于掌握应用数据持久化的实现，是处理配置和偏好设置的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解键值对存储**：掌握键值对存储的基本概念和应用
2. **分析存储策略**：理解双重存储策略的设计和实现
3. **学习序列化机制**：掌握对象序列化和反序列化的处理
4. **实践数据存储**：在实际项目中使用偏好设置存储

### 前置学习
- 键值对存储概念
- 数据序列化和反序列化
- 鸿蒙dataPreferences API
- 文件操作基础

### 后续学习
- `TKSingleKVStore.ets` - 学习分布式键值存储
- `TKObjectHelper.ets` - 深入理解序列化机制
- `TKCacheManager.ets` - 了解高级缓存管理

### 实践建议
1. **基础存储测试**：测试不同类型数据的存储和读取
2. **大数据处理**：测试大数据的自动文件存储机制
3. **配置管理应用**：实现应用配置的持久化存储
4. **性能对比**：对比不同存储策略的性能差异

### 常见问题
1. **问题**: 什么时候数据会自动转为文件存储？
   **解答**: 当序列化后的数据大小超过8KB时，会自动转为文件存储，以优化性能和内存使用。

2. **问题**: 如何确保数据的类型安全？
   **解答**: 使用泛型方法getData<T>()和setData<T>()，并在读取时提供合适的默认值，确保类型安全。

## 📝 代码示例

### 基础使用
```typescript
import { TKPreferences } from '@thinkive/tk-harmony-base';

// 基础偏好设置操作
function basicPreferencesOperations() {
  // 创建偏好设置实例
  const preferences = new TKPreferences('app_settings');

  console.log('存储文件名:', preferences.getFileName());

  // 存储不同类型的数据
  preferences.setData('username', '张三');
  preferences.setData('age', 30);
  preferences.setData('isVip', true);
  preferences.setData('loginTime', new Date());

  // 存储复杂对象
  const userProfile = {
    id: 12345,
    name: '李四',
    email: '<EMAIL>',
    preferences: {
      theme: 'dark',
      language: 'zh-CN',
      notifications: true
    },
    tags: ['developer', 'admin']
  };
  preferences.setData('userProfile', userProfile);

  // 读取数据
  const username = preferences.getData<string>('username', '未知用户');
  const age = preferences.getData<number>('age', 0);
  const isVip = preferences.getData<boolean>('isVip', false);
  const loginTime = preferences.getData<Date>('loginTime');
  const profile = preferences.getData<any>('userProfile');

  console.log('用户名:', username);
  console.log('年龄:', age);
  console.log('VIP状态:', isVip);
  console.log('登录时间:', loginTime);
  console.log('用户档案:', profile);

  // 删除数据
  preferences.deleteData('loginTime');

  // 验证删除
  const deletedLoginTime = preferences.getData<Date>('loginTime');
  console.log('删除后的登录时间:', deletedLoginTime); // undefined
}

// 大数据存储测试
function largeDataStorageTest() {
  const preferences = new TKPreferences('large_data_test');

  // 创建大数据对象（超过8KB）
  const largeData = {
    id: 'large_data_001',
    content: 'A'.repeat(10000), // 10KB的字符串
    metadata: {
      created: new Date(),
      size: 10000,
      type: 'test_data'
    },
    items: Array.from({ length: 1000 }, (_, i) => ({
      id: i,
      name: `Item ${i}`,
      value: Math.random()
    }))
  };

  console.log('存储大数据对象...');
  preferences.setData('largeData', largeData);

  console.log('读取大数据对象...');
  const retrievedLargeData = preferences.getData<any>('largeData');

  console.log('大数据验证:', {
    原始大小: JSON.stringify(largeData).length,
    读取成功: retrievedLargeData !== undefined,
    内容匹配: retrievedLargeData?.content === largeData.content,
    项目数量: retrievedLargeData?.items?.length
  });
}

// 执行示例
basicPreferencesOperations();
largeDataStorageTest();
```

### 高级用法
```typescript
// 配置管理器
class ConfigurationManager {
  private preferences: TKPreferences;
  private configCache: Map<string, any> = new Map();

  constructor(configName: string = 'app_config') {
    this.preferences = new TKPreferences(configName);
    this.loadAllConfigs();
  }

  // 加载所有配置到缓存
  private loadAllConfigs() {
    // 这里可以预加载一些常用配置
    const commonConfigs = ['theme', 'language', 'autoSave', 'timeout'];

    commonConfigs.forEach(key => {
      const value = this.preferences.getData(key);
      if (value !== undefined) {
        this.configCache.set(key, value);
      }
    });

    console.log('配置已加载到缓存');
  }

  // 设置配置
  setConfig<T>(key: string, value: T): void {
    try {
      this.preferences.setData(key, value);
      this.configCache.set(key, value);
      console.log(`配置已设置: ${key} = ${value}`);
    } catch (error) {
      console.error(`设置配置失败: ${key}`, error);
    }
  }

  // 获取配置
  getConfig<T>(key: string, defaultValue?: T): T | undefined {
    // 优先从缓存获取
    if (this.configCache.has(key)) {
      return this.configCache.get(key) as T;
    }

    // 从存储获取
    const value = this.preferences.getData<T>(key, defaultValue);
    if (value !== undefined) {
      this.configCache.set(key, value);
    }

    return value;
  }

  // 删除配置
  deleteConfig(key: string): void {
    this.preferences.deleteData(key);
    this.configCache.delete(key);
    console.log(`配置已删除: ${key}`);
  }

  // 批量设置配置
  setBatchConfigs(configs: Record<string, any>): void {
    Object.entries(configs).forEach(([key, value]) => {
      this.setConfig(key, value);
    });
  }

  // 获取所有缓存的配置
  getAllCachedConfigs(): Record<string, any> {
    const configs: Record<string, any> = {};
    this.configCache.forEach((value, key) => {
      configs[key] = value;
    });
    return configs;
  }

  // 重置所有配置
  resetAllConfigs(): void {
    const keys = Array.from(this.configCache.keys());
    keys.forEach(key => {
      this.deleteConfig(key);
    });
    console.log('所有配置已重置');
  }

  // 导出配置
  exportConfigs(): string {
    const configs = this.getAllCachedConfigs();
    return JSON.stringify(configs, null, 2);
  }

  // 导入配置
  importConfigs(configJson: string): boolean {
    try {
      const configs = JSON.parse(configJson);
      this.setBatchConfigs(configs);
      console.log('配置导入成功');
      return true;
    } catch (error) {
      console.error('配置导入失败:', error);
      return false;
    }
  }
}

// 用户偏好管理器
class UserPreferencesManager {
  private preferences: TKPreferences;
  private userId: string;

  constructor(userId: string) {
    this.userId = userId;
    this.preferences = new TKPreferences(`user_prefs_${userId}`);
  }

  // 设置主题偏好
  setThemePreference(theme: 'light' | 'dark' | 'auto'): void {
    this.preferences.setData('theme', theme);
    console.log(`用户 ${this.userId} 主题设置为: ${theme}`);
  }

  // 获取主题偏好
  getThemePreference(): 'light' | 'dark' | 'auto' {
    return this.preferences.getData<'light' | 'dark' | 'auto'>('theme', 'auto');
  }

  // 设置语言偏好
  setLanguagePreference(language: string): void {
    this.preferences.setData('language', language);
    console.log(`用户 ${this.userId} 语言设置为: ${language}`);
  }

  // 获取语言偏好
  getLanguagePreference(): string {
    return this.preferences.getData<string>('language', 'zh-CN');
  }

  // 设置通知偏好
  setNotificationPreferences(notifications: {
    email: boolean,
    push: boolean,
    sms: boolean,
    marketing: boolean
  }): void {
    this.preferences.setData('notifications', notifications);
    console.log(`用户 ${this.userId} 通知偏好已更新`);
  }

  // 获取通知偏好
  getNotificationPreferences(): any {
    return this.preferences.getData('notifications', {
      email: true,
      push: true,
      sms: false,
      marketing: false
    });
  }

  // 设置应用偏好
  setAppPreferences(prefs: {
    autoSave: boolean,
    autoLogin: boolean,
    rememberPassword: boolean,
    sessionTimeout: number
  }): void {
    this.preferences.setData('appPreferences', prefs);
    console.log(`用户 ${this.userId} 应用偏好已更新`);
  }

  // 获取应用偏好
  getAppPreferences(): any {
    return this.preferences.getData('appPreferences', {
      autoSave: true,
      autoLogin: false,
      rememberPassword: false,
      sessionTimeout: 30
    });
  }

  // 记录用户行为
  recordUserBehavior(action: string, data?: any): void {
    const behavior = {
      action: action,
      data: data,
      timestamp: new Date().toISOString(),
      userId: this.userId
    };

    // 获取现有行为记录
    const behaviors = this.preferences.getData<any[]>('userBehaviors', []);
    behaviors.push(behavior);

    // 只保留最近100条记录
    if (behaviors.length > 100) {
      behaviors.splice(0, behaviors.length - 100);
    }

    this.preferences.setData('userBehaviors', behaviors);
  }

  // 获取用户行为记录
  getUserBehaviors(limit: number = 10): any[] {
    const behaviors = this.preferences.getData<any[]>('userBehaviors', []);
    return behaviors.slice(-limit);
  }

  // 清理用户数据
  clearUserData(): void {
    this.preferences.deleteData('theme');
    this.preferences.deleteData('language');
    this.preferences.deleteData('notifications');
    this.preferences.deleteData('appPreferences');
    this.preferences.deleteData('userBehaviors');
    console.log(`用户 ${this.userId} 数据已清理`);
  }

  // 获取用户偏好摘要
  getUserPreferencesSummary(): any {
    return {
      userId: this.userId,
      theme: this.getThemePreference(),
      language: this.getLanguagePreference(),
      notifications: this.getNotificationPreferences(),
      appPreferences: this.getAppPreferences(),
      behaviorCount: this.getUserBehaviors(1000).length
    };
  }
}

// 应用状态管理器
class ApplicationStateManager {
  private preferences: TKPreferences;

  constructor() {
    this.preferences = new TKPreferences('app_state');
  }

  // 保存应用状态
  saveApplicationState(state: {
    currentPage: string,
    navigationStack: string[],
    userSession: any,
    temporaryData: any
  }): void {
    const appState = {
      ...state,
      timestamp: Date.now(),
      version: '1.0.0'
    };

    this.preferences.setData('applicationState', appState);
    console.log('应用状态已保存');
  }

  // 恢复应用状态
  restoreApplicationState(): any {
    const state = this.preferences.getData('applicationState');

    if (state) {
      // 检查状态是否过期（24小时）
      const now = Date.now();
      const stateAge = now - (state.timestamp || 0);
      const maxAge = 24 * 60 * 60 * 1000; // 24小时

      if (stateAge > maxAge) {
        console.log('应用状态已过期，清理旧状态');
        this.clearApplicationState();
        return null;
      }

      console.log('应用状态已恢复');
      return state;
    }

    return null;
  }

  // 清理应用状态
  clearApplicationState(): void {
    this.preferences.deleteData('applicationState');
    console.log('应用状态已清理');
  }

  // 保存崩溃信息
  saveCrashInfo(crashInfo: {
    error: string,
    stack: string,
    timestamp: number,
    userAgent: string,
    appVersion: string
  }): void {
    const crashes = this.preferences.getData<any[]>('crashReports', []);
    crashes.push(crashInfo);

    // 只保留最近10次崩溃记录
    if (crashes.length > 10) {
      crashes.splice(0, crashes.length - 10);
    }

    this.preferences.setData('crashReports', crashes);
    console.log('崩溃信息已保存');
  }

  // 获取崩溃报告
  getCrashReports(): any[] {
    return this.preferences.getData<any[]>('crashReports', []);
  }
}

// 使用示例
function demonstrateAdvancedPreferences() {
  // 配置管理器示例
  console.log('=== 配置管理器示例 ===');
  const configManager = new ConfigurationManager();

  // 设置应用配置
  configManager.setBatchConfigs({
    theme: 'dark',
    language: 'zh-CN',
    autoSave: true,
    timeout: 30000,
    maxRetries: 3
  });

  // 读取配置
  console.log('主题配置:', configManager.getConfig('theme'));
  console.log('语言配置:', configManager.getConfig('language'));
  console.log('所有配置:', configManager.getAllCachedConfigs());

  // 用户偏好管理器示例
  console.log('\n=== 用户偏好管理器示例 ===');
  const userPrefs = new UserPreferencesManager('user_12345');

  // 设置用户偏好
  userPrefs.setThemePreference('dark');
  userPrefs.setLanguagePreference('en-US');
  userPrefs.setNotificationPreferences({
    email: true,
    push: false,
    sms: true,
    marketing: false
  });

  // 记录用户行为
  userPrefs.recordUserBehavior('login', { method: 'password' });
  userPrefs.recordUserBehavior('view_page', { page: 'dashboard' });
  userPrefs.recordUserBehavior('click_button', { button: 'save' });

  // 获取用户偏好摘要
  console.log('用户偏好摘要:', userPrefs.getUserPreferencesSummary());

  // 应用状态管理器示例
  console.log('\n=== 应用状态管理器示例 ===');
  const stateManager = new ApplicationStateManager();

  // 保存应用状态
  stateManager.saveApplicationState({
    currentPage: 'dashboard',
    navigationStack: ['home', 'profile', 'dashboard'],
    userSession: { userId: '12345', token: 'abc123' },
    temporaryData: { searchQuery: 'test', filters: ['active'] }
  });

  // 恢复应用状态
  const restoredState = stateManager.restoreApplicationState();
  console.log('恢复的应用状态:', restoredState);
}

// 执行演示
demonstrateAdvancedPreferences();
```

## 📚 相关资源

### 官方文档
- HarmonyOS数据管理开发指南
- dataPreferences API参考文档

### 参考资料
- 《移动应用数据存储》- 移动端存储策略
- 《高性能数据存储》- 存储优化技术

### 相关文件
- `TKSingleKVStore.ets` - 分布式键值存储
- `TKObjectHelper.ets` - 对象序列化工具
- `TKCacheManager.ets` - 缓存管理器
