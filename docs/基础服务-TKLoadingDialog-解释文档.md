# TKLoadingDialog

> **文件路径**: `src/main/ets/components/dialog/TKLoadingDialog.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 初级  
> **前置知识**: 鸿蒙组件开发、动画效果、定时器使用、进度条组件

## 📋 文件概述

### 功能定位
TKLoadingDialog是HMThinkBaseHar框架的**加载对话框组件**，提供了标准的加载状态显示功能。它采用组件化设计，集成了动态进度条和定时器，为应用提供统一的加载状态提示体验，支持自定义提示文字和旋转效果。

### 核心职责
- **加载状态显示**：提供标准的加载状态可视化反馈
- **动态进度动画**：通过定时器驱动的环形进度条显示加载动画
- **自定义配置**：支持提示文字、旋转角度等自定义配置
- **生命周期管理**：管理定时器的启动和停止，避免内存泄漏
- **组件复用**：支持组件复用机制，提高性能

### 设计模式
- **组件模式**：使用@Component装饰器定义可复用加载对话框组件
- **配置模式**：通过TKLoadingDialogOption配置对话框属性
- **定时器模式**：使用TKTimer驱动进度条动画
- **构建器模式**：提供buildTKLoadingDialog构建器函数

## 🔧 核心功能

### 主要配置类：TKLoadingDialogOption

#### 属性1：`tip: string`
- **功能**: 加载提示文字
- **类型**: string
- **默认值**: "加载中..."
- **使用场景**: 显示加载状态的提示信息

#### 属性2：`angle: number`
- **功能**: 对话框旋转角度
- **类型**: number
- **默认值**: 0
- **使用场景**: 需要特殊视觉效果时

#### 属性3：`isDisappear: boolean`
- **功能**: 是否隐藏对话框
- **类型**: boolean
- **默认值**: false
- **使用场景**: 控制对话框的显示隐藏状态

### 主要组件属性

#### 属性1：`value: number`
- **功能**: 进度条当前值
- **类型**: @State number
- **默认值**: 0
- **使用场景**: 驱动进度条动画

#### 属性2：`timer: TKTimer`
- **功能**: 动画定时器
- **类型**: TKTimer
- **使用场景**: 控制进度条动画的更新频率

## 💡 技术要点

### 核心算法/逻辑

#### 定时器驱动的动画机制
```typescript
aboutToAppear(options?: TKLoadingDialogOption): void {
  this.options = TKObjectHelper.fixDefault(this.options, TKLoadingDialogOption, options);
  this.value = 0;
  
  // 创建定时器，每100ms更新一次进度
  if (!this.timer) {
    this.timer = new TKTimer(100, this, this.handleTimer);
  }
  this.timer.start();
}

private handleTimer() {
  // 进度值循环递增，形成连续动画效果
  this.value = (this.value + 10) % 100;
}
```

#### 组件布局结构
```typescript
build() {
  Column({ space: 5 }) {
    // 环形进度条
    Progress({ 
      value: this.value, 
      total: 100, 
      type: ProgressType.ScaleRing 
    })
    .width(35)
    .height(35)
    .backgroundColor(Color.White)
    .style({ 
      strokeWidth: 5, 
      scaleCount: 10, 
      scaleWidth: 5 
    })
    
    // 提示文字
    Text(this.options.tip)
      .fontSize(14)
      .fontWeight(FontWeight.Bold)
      .fontColor(Color.White)
      .textAlign(TextAlign.JUSTIFY)
  }
  .rotate({ angle: this.options.angle })
  .backgroundColor(Color.Black)
  .width(100)
  .height(100)
  .justifyContent(FlexAlign.Center)
  .borderRadius(10)
  .opacity(0.8);
}
```

#### 生命周期管理
```typescript
aboutToDisappear(): void {
  // 停止定时器，释放资源
  if (this.timer) {
    this.timer.stop();
    this.timer = undefined;
  }
  // 重置配置
  this.options = new TKLoadingDialogOption();
}

aboutToReuse(params: Record<string, Object>): void {
  let options = params as Object as TKLoadingDialogOption;
  if (options && !options.isDisappear) {
    this.aboutToAppear(options);
  } else {
    this.aboutToDisappear();
  }
}
```

### 实现机制分析

#### 动画实现原理
- **定时器驱动**：使用TKTimer每100ms更新一次进度值
- **循环动画**：进度值从0到100循环递增，形成连续的动画效果
- **环形进度条**：使用ProgressType.ScaleRing类型的Progress组件
- **视觉效果**：通过刻度样式和颜色配置增强视觉效果

#### 组件复用机制
- **aboutToReuse**：支持组件复用，避免重复创建开销
- **状态重置**：复用时重新初始化定时器和进度值
- **资源管理**：及时清理定时器资源，避免内存泄漏

#### 样式设计
- **半透明背景**：黑色背景配合0.8透明度，提供良好的视觉层次
- **圆角设计**：10px圆角，符合现代UI设计规范
- **固定尺寸**：100x100的固定尺寸，保证一致的视觉体验
- **白色进度条**：白色进度条在黑色背景上形成良好对比

#### 定时器管理
- **创建时机**：在aboutToAppear时创建定时器
- **销毁时机**：在aboutToDisappear时停止并清理定时器
- **异常处理**：TKTimer内部包含异常处理机制
- **性能优化**：100ms的更新频率平衡了动画流畅度和性能

### 性能考虑
- **定时器优化**：使用合适的更新频率，避免过度渲染
- **组件复用**：通过aboutToReuse支持组件复用，减少创建开销
- **资源清理**：及时清理定时器资源，避免内存泄漏
- **轻量级设计**：简单的布局结构，渲染性能优秀

### 错误处理
- **定时器保护**：检查定时器是否存在，避免重复创建
- **状态保护**：通过生命周期方法保护组件状态
- **异常隔离**：定时器异常不影响组件正常显示
- **资源安全**：确保定时器资源得到正确释放

### 最佳实践
- **配置集中**：通过TKLoadingDialogOption集中管理所有配置
- **生命周期管理**：合理使用生命周期方法管理定时器
- **资源清理**：及时清理定时器等资源，避免内存泄漏
- **视觉一致性**：提供统一的加载状态视觉体验

## 🔗 依赖关系

### 依赖的模块
- `TKObjectHelper` - 对象工具，处理配置默认值和对象操作
- `TKTimer` - 定时器工具，驱动进度条动画效果

### 被依赖情况
- `TKDialogHelper` - 对话框工具类，使用TKLoadingDialog显示加载对话框
- `各业务模块` - 通过TKDialogHelper.createLoadingDialog使用加载对话框
- `TKWeb组件` - 在WebView加载时使用加载对话框

### 关联文件
- `TKDialogHelper.ets` - 对话框工具类，TKLoadingDialog的主要使用者
- `TKTimer.ets` - 定时器工具类，提供动画驱动能力
- `TKWebLoadingDialog.ets` - Web专用加载对话框，类似的实现
- `TKProgressDialog.ets` - 进度对话框组件，相关的对话框实现

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKLoadingDialog是框架UI组件的基础加载对话框：
1. **通用组件**：提供标准的加载状态显示功能
2. **使用频率高**：在需要显示加载状态的场景中广泛使用
3. **学习难度低**：结构简单，容易理解和使用
4. **动画效果**：集成了动态动画效果，提升用户体验

理解TKLoadingDialog有助于掌握框架的加载状态管理，是构建良好用户体验的重要组件。

## 🎯 学习建议

### 学习路径
1. **理解加载状态设计**：掌握加载状态的用户体验设计原则
2. **分析动画实现**：理解定时器驱动的动画实现机制
3. **学习组件复用**：掌握组件复用的生命周期管理
4. **实践加载对话框开发**：使用TKLoadingDialog创建各种加载场景

### 前置学习
- 鸿蒙组件开发基础
- Progress组件的使用
- 定时器和动画原理
- 组件生命周期管理

### 后续学习
- `TKTimer.ets` - 学习定时器工具类的使用
- `TKDialogHelper.ets` - 了解对话框工具类的管理
- `TKProgressDialog.ets` - 学习进度对话框的实现

### 实践建议
1. **基础加载对话框使用**：创建简单的加载提示
2. **动画效果实验**：测试不同的动画参数配置
3. **生命周期管理练习**：实现正确的资源管理
4. **自定义扩展**：基于TKLoadingDialog开发自定义加载组件

### 常见问题
1. **问题**: 如何自定义加载提示文字？
   **解答**: 通过TKLoadingDialogOption的tip属性设置自定义提示文字，如"正在处理中..."。

2. **问题**: 为什么需要在aboutToDisappear中停止定时器？
   **解答**: 定时器会持续运行消耗资源，组件销毁时必须停止定时器，否则会造成内存泄漏和不必要的性能消耗。

## 📝 代码示例

### 基础使用
```typescript
import { TKDialogHelper, TKLoadingDialogOption } from '@thinkive/tk-harmony-base';

// 基础加载对话框
function showBasicLoading() {
  const loading = TKDialogHelper.createLoadingDialog();
  loading.open();

  // 模拟异步操作
  setTimeout(() => {
    loading.close();
    console.log('加载完成');
  }, 3000);
}

// 自定义提示文字的加载对话框
function showCustomLoading() {
  const option: TKLoadingDialogOption = {
    tip: "正在处理中..."
  };

  const loading = TKDialogHelper.createLoadingDialog(option);
  loading.open();

  // 模拟数据处理
  processData().then(() => {
    loading.close();
    console.log('数据处理完成');
  });
}

// 带旋转效果的加载对话框
function showRotatedLoading() {
  const option: TKLoadingDialogOption = {
    tip: "加载中...",
    angle: 10 // 旋转10度
  };

  const loading = TKDialogHelper.createLoadingDialog(option);
  loading.open();

  setTimeout(() => {
    loading.close();
  }, 2000);
}

async function processData() {
  // 模拟异步数据处理
  return new Promise(resolve => {
    setTimeout(resolve, 2000);
  });
}
```

### 高级用法
```typescript
// 加载对话框管理器
class LoadingManager {
  private static instance: LoadingManager;
  private activeLoadings: Map<string, any> = new Map();

  static getInstance(): LoadingManager {
    if (!LoadingManager.instance) {
      LoadingManager.instance = new LoadingManager();
    }
    return LoadingManager.instance;
  }

  // 显示加载对话框
  showLoading(
    id: string,
    tip: string = "加载中...",
    angle: number = 0
  ): string {
    // 如果已存在相同ID的加载框，先关闭
    this.hideLoading(id);

    const option: TKLoadingDialogOption = {
      tip: tip,
      angle: angle
    };

    const loading = TKDialogHelper.createLoadingDialog(option);
    loading.open();

    this.activeLoadings.set(id, loading);
    console.log(`显示加载对话框: ${id} - ${tip}`);

    return id;
  }

  // 隐藏指定加载对话框
  hideLoading(id: string): boolean {
    const loading = this.activeLoadings.get(id);
    if (loading) {
      loading.close();
      this.activeLoadings.delete(id);
      console.log(`隐藏加载对话框: ${id}`);
      return true;
    }
    return false;
  }

  // 更新加载提示
  updateLoadingTip(id: string, newTip: string): boolean {
    const loading = this.activeLoadings.get(id);
    if (loading) {
      // 重新创建加载框以更新提示
      const option: TKLoadingDialogOption = {
        tip: newTip
      };

      loading.close();
      const newLoading = TKDialogHelper.createLoadingDialog(option);
      newLoading.open();

      this.activeLoadings.set(id, newLoading);
      console.log(`更新加载提示: ${id} - ${newTip}`);
      return true;
    }
    return false;
  }

  // 隐藏所有加载对话框
  hideAllLoadings() {
    this.activeLoadings.forEach((loading, id) => {
      loading.close();
      console.log(`关闭加载对话框: ${id}`);
    });
    this.activeLoadings.clear();
  }

  // 获取活跃加载框数量
  getActiveLoadingCount(): number {
    return this.activeLoadings.size;
  }

  // 检查是否有指定ID的加载框
  hasLoading(id: string): boolean {
    return this.activeLoadings.has(id);
  }
}

// 异步操作包装器
class AsyncOperationWrapper {
  private loadingManager = LoadingManager.getInstance();

  // 包装异步操作，自动显示和隐藏加载框
  async wrapWithLoading<T>(
    operation: () => Promise<T>,
    loadingId: string,
    loadingTip: string = "处理中..."
  ): Promise<T> {
    try {
      this.loadingManager.showLoading(loadingId, loadingTip);
      const result = await operation();
      return result;
    } finally {
      this.loadingManager.hideLoading(loadingId);
    }
  }

  // 包装多步骤异步操作
  async wrapMultiStepOperation<T>(
    steps: Array<{
      operation: () => Promise<any>,
      tip: string
    }>,
    loadingId: string
  ): Promise<T[]> {
    const results: T[] = [];

    try {
      for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        const tip = `${step.tip} (${i + 1}/${steps.length})`;

        this.loadingManager.showLoading(loadingId, tip);
        const result = await step.operation();
        results.push(result);

        // 短暂延迟以显示进度变化
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      return results;
    } finally {
      this.loadingManager.hideLoading(loadingId);
    }
  }
}

// 使用示例
class DataService {
  private loadingManager = LoadingManager.getInstance();
  private asyncWrapper = new AsyncOperationWrapper();

  // 基础数据加载
  async loadUserData(userId: string) {
    return this.asyncWrapper.wrapWithLoading(
      async () => {
        // 模拟API调用
        await this.delay(2000);
        return { id: userId, name: "用户名", email: "<EMAIL>" };
      },
      "load_user",
      "正在加载用户信息..."
    );
  }

  // 多步骤数据同步
  async syncAllData() {
    const steps = [
      {
        operation: () => this.syncUserData(),
        tip: "同步用户数据"
      },
      {
        operation: () => this.syncSettings(),
        tip: "同步设置信息"
      },
      {
        operation: () => this.syncFiles(),
        tip: "同步文件数据"
      }
    ];

    return this.asyncWrapper.wrapMultiStepOperation(steps, "sync_all");
  }

  // 文件上传
  async uploadFile(file: any) {
    const loadingId = "upload_file";

    try {
      this.loadingManager.showLoading(loadingId, "准备上传...");

      // 模拟文件准备
      await this.delay(500);

      this.loadingManager.updateLoadingTip(loadingId, "正在上传...");

      // 模拟文件上传
      await this.delay(3000);

      this.loadingManager.updateLoadingTip(loadingId, "处理中...");

      // 模拟服务器处理
      await this.delay(1000);

      return { success: true, fileId: "file_123" };
    } finally {
      this.loadingManager.hideLoading(loadingId);
    }
  }

  private async syncUserData() {
    await this.delay(1000);
    return "用户数据同步完成";
  }

  private async syncSettings() {
    await this.delay(800);
    return "设置同步完成";
  }

  private async syncFiles() {
    await this.delay(1200);
    return "文件同步完成";
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 使用示例组件
@Component
struct LoadingExamplePage {
  private loadingManager = LoadingManager.getInstance();
  private dataService = new DataService();

  build() {
    Column({ space: 20 }) {
      Text('加载对话框示例')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin(20)

      Button('基础加载')
        .onClick(() => {
          showBasicLoading();
        })

      Button('自定义提示')
        .onClick(() => {
          showCustomLoading();
        })

      Button('旋转效果')
        .onClick(() => {
          showRotatedLoading();
        })

      Button('加载用户数据')
        .onClick(async () => {
          try {
            const userData = await this.dataService.loadUserData("123");
            console.log('用户数据:', userData);
          } catch (error) {
            console.error('加载失败:', error);
          }
        })

      Button('同步所有数据')
        .onClick(async () => {
          try {
            const results = await this.dataService.syncAllData();
            console.log('同步结果:', results);
          } catch (error) {
            console.error('同步失败:', error);
          }
        })

      Button('上传文件')
        .onClick(async () => {
          try {
            const result = await this.dataService.uploadFile({ name: "test.txt" });
            console.log('上传结果:', result);
          } catch (error) {
            console.error('上传失败:', error);
          }
        })

      Button('关闭所有加载框')
        .onClick(() => {
          this.loadingManager.hideAllLoadings();
        })

      Text(`当前活跃加载框: ${this.loadingManager.getActiveLoadingCount()}`)
        .fontSize(14)
        .fontColor(Color.Gray)
    }
    .width('100%')
    .height('100%')
    .justifyContent(FlexAlign.Center)
    .padding(20)
  }
}
```

## 📚 相关资源

### 官方文档
- HarmonyOS Progress组件开发指南
- ArkUI动画效果实现

### 参考资料
- 《用户界面设计》- 加载状态设计原则
- 《移动应用UI设计》- 加载动画最佳实践

### 相关文件
- `TKTimer.ets` - 定时器工具类
- `TKDialogHelper.ets` - 对话框工具类
- `TKProgressDialog.ets` - 进度对话框组件
