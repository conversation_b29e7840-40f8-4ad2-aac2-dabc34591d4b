# TKThemeManager

> **文件路径**: `src/main/ets/base/theme/TKThemeManager.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: CSS解析、主题系统、单例模式、通知机制

## 📋 文件概述

### 功能定位
TKThemeManager是HMThinkBaseHar框架的**主题管理器**，提供了完整的主题切换和样式管理功能。它采用单例模式设计，集成了CSS解析、主题缓存、通知机制等功能，支持多主题配置、动态切换、样式热更新等企业级功能，为应用提供统一的主题管理服务。

### 核心职责
- **主题配置管理**：管理多套主题配置，支持主题的加载和切换
- **CSS解析引擎**：解析CSS文件，将样式规则转换为组件可用的样式对象
- **主题缓存机制**：缓存已解析的主题配置，提高主题切换性能
- **通知广播系统**：主题切换时广播通知，实现UI的响应式更新
- **样式查询服务**：提供样式查询接口，支持组件获取主题样式

### 设计模式
- **单例模式**：确保全局唯一的主题管理器实例
- **观察者模式**：通过通知中心实现主题变更的观察者模式
- **策略模式**：支持不同的主题配置策略和CSS解析策略
- **缓存模式**：使用Map缓存已解析的主题配置，提高性能

## 🔧 核心功能

### 主题管理方法

#### 方法1：`initThemeContext()`
- **功能**: 初始化主题环境
- **执行时机**: 应用启动时调用
- **处理逻辑**: 从缓存或配置中加载默认主题
- **使用场景**: 应用初始化阶段

#### 方法2：`set theme(theme: string)`
- **功能**: 设置当前主题
- **参数**: theme - 主题名称
- **处理逻辑**: 解析主题CSS，缓存配置，广播通知
- **使用场景**: 用户切换主题时

#### 方法3：`get theme()`
- **功能**: 获取当前主题名称
- **返回值**: string - 当前主题名称
- **使用场景**: 查询当前使用的主题

#### 方法4：`clearCache()`
- **功能**: 清理主题缓存
- **执行时机**: 应用退出或重置时
- **处理逻辑**: 根据配置决定是否保留主题设置
- **使用场景**: 应用清理阶段

### 样式查询方法

#### 方法1：`getCssRulesetByClassName(className: string)`
- **功能**: 根据类名获取CSS规则集
- **参数**: className - CSS类名
- **返回值**: TKCSSRuleset - CSS规则集对象
- **使用场景**: 组件获取特定样式

#### 方法2：`getCssRulesetMap()`
- **功能**: 获取完整的CSS规则集映射
- **返回值**: Map<string, TKCSSRuleset> - 完整的样式映射
- **使用场景**: 批量样式处理或样式管理

### 核心配置常量

#### 常量1：`CACHE_SYSTEM_THEME`
- **值**: "tk_system_theme"
- **用途**: 主题缓存的键名
- **使用场景**: 主题持久化存储

#### 常量2：`NOTE_THEME_CHANGED`
- **值**: "note_theme_changed"
- **用途**: 主题变更通知名称
- **使用场景**: 主题变更事件广播

## 💡 技术要点

### 核心算法/逻辑

#### CSS解析引擎
```typescript
private async parseThemeCSS(theme: string): Promise<Map<string, TKCSSRuleset>> {
  let themeConfig: Map<string, TKCSSRuleset> = new Map<string, TKCSSRuleset>();
  let themePathConfig: string = TKSystemHelper.getConfig(`theme.${theme}`);
  
  // 处理主题路径配置
  if (TKStringHelper.isNotBlank(themePathConfig) && !themePathConfig.includes(".css")) {
    themePathConfig = TKSystemHelper.getConfig(`theme.${themePathConfig}`);
  }
  
  if (TKStringHelper.isNotBlank(themePathConfig)) {
    let themePaths: Array<string> = themePathConfig.split("|");
    
    for (let tempThemePath of themePaths) {
      // 解析每个CSS文件
      let themeCss: string = TKDataHelper.uint8ArrayToString(TKFileHelper.readFile(themePath, moduleContext));
      
      if (TKStringHelper.isNotBlank(themeCss)) {
        // 匹配类定义的正则表达式
        const regex: RegExp = /\.([0-9a-zA-Z-_]+)\s*\{([^\}]+)\}/g;
        let match: RegExpExecArray | null = null;
        
        while ((match = regex.exec(themeCss)) !== null) {
          const className: string = TKStringHelper.trim(match[1]);
          const classProperties: string = TKStringHelper.trim(match[2]);
          
          // 解析类属性和值
          const classResult: TKCSSRuleset = new TKCSSRuleset();
          const classRegex: RegExp = /([0-9a-zA-Z-_]+)\s*:\s*([^;]+)/g;
          let classMatch: RegExpExecArray | null = null;
          
          while ((classMatch = classRegex.exec(classProperties)) !== null) {
            let property: string = TKStringHelper.trim(classMatch[1]);
            let value: string = TKStringHelper.trim(classMatch[2]);
            
            // 根据属性类型设置对应的样式值
            this.setStyleProperty(classResult, property, value);
          }
          
          themeConfig.set(className, classResult);
        }
      }
    }
  }
  
  return themeConfig;
}
```

#### 主题切换机制
```typescript
public set theme(theme: string) {
  if (TKStringHelper.isNotBlank(theme) && theme != this._theme) {
    this._theme = theme;
    
    // 缓存主题设置
    TKCacheManager.shareInstance().saveFileCacheData(TKThemeManager.CACHE_SYSTEM_THEME, this._theme);
    
    // 检查主题是否已缓存
    let themeConfig: Map<string, TKCSSRuleset> | undefined = this.themeMap.get(this._theme);
    
    if (!themeConfig) {
      // 异步解析新主题
      this.parseThemeCSS(this._theme).then((themeConfig) => {
        this.themeMap.set(theme, themeConfig);
        // 广播主题变更通知
        TKNotificationCenter.defaultCenter.postNotificationName(TKThemeManager.NOTE_THEME_CHANGED, this._theme);
      });
    } else {
      // 直接广播通知
      TKNotificationCenter.defaultCenter.postNotificationName(TKThemeManager.NOTE_THEME_CHANGED, this._theme);
    }
  }
}
```

#### 样式属性映射
```typescript
private setStyleProperty(classResult: TKCSSRuleset, property: string, value: string) {
  switch (property) {
    case "width":
      classResult.width = this.parseLength(value);
      break;
    case "height":
      classResult.height = this.parseLength(value);
      break;
    case "backgroundColor":
      classResult.backgroundColor = value;
      break;
    case "color":
    case "fontColor":
      classResult.fontColor = value;
      break;
    case "fontSize":
      classResult.fontSize = this.parseLength(value);
      break;
    case "borderRadius":
      classResult.borderRadius = this.parseLength(value);
      break;
    case "image":
      classResult.image = TKImageHelper.getImageResource(value);
      break;
    case "selectedImage":
      classResult.selectedImage = TKImageHelper.getImageResource(value);
      break;
    // ... 更多属性映射
  }
}
```

### 实现机制分析

#### 单例模式实现
- **静态实例**：使用静态变量保存唯一实例
- **延迟初始化**：在首次调用时创建实例
- **线程安全**：通过条件检查确保单例性
- **全局访问**：提供静态方法获取实例

#### CSS解析机制
- **正则表达式解析**：使用正则表达式解析CSS语法
- **分层解析**：先解析类定义，再解析属性值
- **类型转换**：将CSS字符串值转换为对应的鸿蒙类型
- **错误容错**：解析失败时使用默认值

#### 缓存策略
- **内存缓存**：使用Map缓存已解析的主题配置
- **文件缓存**：通过TKCacheManager持久化主题设置
- **懒加载**：主题配置按需解析和加载
- **缓存更新**：主题切换时更新缓存内容

#### 通知机制
- **事件驱动**：主题变更时发送通知事件
- **解耦设计**：通过通知中心实现组件间解耦
- **响应式更新**：组件监听通知自动更新样式
- **异步处理**：支持异步主题解析和通知

### 性能考虑
- **缓存优化**：已解析的主题配置缓存在内存中
- **懒加载**：主题配置按需解析，避免启动时的性能开销
- **异步解析**：CSS解析使用异步方式，不阻塞主线程
- **正则优化**：使用高效的正则表达式进行CSS解析

### 错误处理
- **解析容错**：CSS解析失败时使用默认样式
- **文件容错**：主题文件不存在时回退到默认主题
- **类型安全**：样式值转换时进行类型检查
- **异常隔离**：单个样式解析失败不影响其他样式

### 最佳实践
- **主题配置规范**：建议使用标准的CSS语法定义主题
- **性能优化**：合理使用主题缓存，避免频繁解析
- **组件集成**：通过TKThemeStyleAttribute实现组件的主题响应
- **配置管理**：使用配置文件管理主题路径和设置

## 🔗 依赖关系

### 依赖的模块
- `TKCacheManager` - 缓存管理器，用于主题设置的持久化
- `TKNotificationCenter` - 通知中心，用于主题变更事件广播
- `TKFileHelper` - 文件工具，用于读取CSS主题文件
- `TKSystemHelper` - 系统工具，用于获取主题配置
- `TKImageHelper` - 图片工具，用于处理主题中的图片资源

### 被依赖情况
- `TKThemeStyleAttribute` - 主题样式属性类，监听主题变更
- `各UI组件` - 通过主题管理器获取样式配置
- `TKTitleBar` - 标题栏组件使用主题样式
- `TKPatternLock` - 手势密码组件使用主题样式

### 关联文件
- `TKThemeStyleAttribute.ets` - 主题样式属性类
- `TKCSSRuleset.ets` - CSS规则集定义
- `TKStyleAttribute.ets` - 样式属性基类
- 主题CSS文件 - 具体的主题样式定义

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKThemeManager是框架主题系统的核心：
1. **主题管理**：提供完整的主题切换和管理功能
2. **样式统一**：确保应用UI样式的统一性和一致性
3. **用户体验**：支持动态主题切换，提升用户体验
4. **扩展性强**：支持自定义主题和样式扩展

理解TKThemeManager有助于掌握框架的主题系统，是实现个性化UI的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解主题概念**：掌握主题系统的基本概念和作用
2. **分析CSS解析**：理解CSS解析引擎的实现原理
3. **学习缓存机制**：掌握主题缓存和性能优化策略
4. **实践主题开发**：创建自定义主题和样式

### 前置学习
- CSS语法和样式概念
- 单例模式设计原理
- 通知机制和观察者模式
- 文件操作和缓存管理

### 后续学习
- `TKThemeStyleAttribute.ets` - 学习主题样式属性的使用
- `TKCSSRuleset.ets` - 了解CSS规则集的定义
- 各UI组件的主题集成方式

### 实践建议
1. **基础主题使用**：学习如何切换和使用预定义主题
2. **自定义主题开发**：创建自己的主题CSS文件
3. **组件主题集成**：在自定义组件中集成主题支持
4. **性能优化实验**：测试主题缓存和切换性能

### 常见问题
1. **问题**: 如何创建自定义主题？
   **解答**: 创建CSS文件定义样式规则，在配置中注册主题路径，使用标准CSS语法定义组件样式。

2. **问题**: 主题切换后组件样式没有更新怎么办？
   **解答**: 确保组件使用了TKThemeStyleAttribute或监听了主题变更通知，检查CSS类名是否正确匹配。

## 📝 代码示例

### 基础使用
```typescript
import { TKThemeManager } from '@thinkive/tk-harmony-base';

// 主题管理基础操作
function basicThemeOperations() {
  // 获取主题管理器实例
  const themeManager = TKThemeManager.shareInstance();

  // 初始化主题环境（通常在应用启动时调用）
  themeManager.initThemeContext();

  // 获取当前主题
  const currentTheme = themeManager.theme;
  console.log('当前主题:', currentTheme);

  // 切换主题
  themeManager.theme = 'dark'; // 切换到深色主题
  themeManager.theme = 'light'; // 切换到浅色主题

  // 获取特定组件的样式
  const buttonStyle = themeManager.getCssRulesetByClassName('TKButton');
  console.log('按钮样式:', buttonStyle);

  // 获取完整的样式映射
  const allStyles = themeManager.getCssRulesetMap();
  console.log('所有样式:', allStyles);

  // 清理缓存（通常在应用退出时调用）
  themeManager.clearCache();
}

// 主题切换监听
function themeChangeListener() {
  // 监听主题变更通知
  TKNotificationCenter.defaultCenter.addObserver(
    this,
    (notification) => {
      console.log('主题已切换到:', notification.object);
      // 在这里处理主题变更后的逻辑
      updateUIForThemeChange();
    },
    TKThemeManager.NOTE_THEME_CHANGED
  );
}

// 更新UI以响应主题变更
function updateUIForThemeChange() {
  const themeManager = TKThemeManager.shareInstance();

  // 获取新主题的样式
  const titleBarStyle = themeManager.getCssRulesetByClassName('TKTitleBar');
  const buttonStyle = themeManager.getCssRulesetByClassName('TKButton');

  // 应用新样式到UI组件
  console.log('应用新的标题栏样式:', titleBarStyle.backgroundColor);
  console.log('应用新的按钮样式:', buttonStyle.fontColor);
}

// 执行示例
basicThemeOperations();
themeChangeListener();
```

### 高级用法
```typescript
// 主题管理器扩展
class ThemeController {
  private themeManager: TKThemeManager;
  private supportedThemes: string[] = ['light', 'dark', 'blue', 'green'];

  constructor() {
    this.themeManager = TKThemeManager.shareInstance();
    this.initializeThemes();
  }

  // 初始化主题系统
  private initializeThemes() {
    // 初始化主题环境
    this.themeManager.initThemeContext();

    // 监听主题变更
    TKNotificationCenter.defaultCenter.addObserver(
      this,
      this.onThemeChanged.bind(this),
      TKThemeManager.NOTE_THEME_CHANGED
    );

    console.log('主题系统初始化完成');
  }

  // 主题变更处理
  private onThemeChanged(notification: TKNotification) {
    const newTheme = notification.object as string;
    console.log(`主题已切换到: ${newTheme}`);

    // 记录主题切换日志
    this.logThemeChange(newTheme);

    // 通知其他模块主题已变更
    this.notifyThemeChange(newTheme);
  }

  // 获取支持的主题列表
  getSupportedThemes(): string[] {
    return [...this.supportedThemes];
  }

  // 获取当前主题
  getCurrentTheme(): string {
    return this.themeManager.theme;
  }

  // 切换主题（带验证）
  switchTheme(themeName: string): boolean {
    if (!this.supportedThemes.includes(themeName)) {
      console.error(`不支持的主题: ${themeName}`);
      return false;
    }

    if (this.themeManager.theme === themeName) {
      console.log(`主题 ${themeName} 已经是当前主题`);
      return true;
    }

    try {
      this.themeManager.theme = themeName;
      return true;
    } catch (error) {
      console.error(`切换主题失败: ${error.message}`);
      return false;
    }
  }

  // 获取主题样式
  getThemeStyle(className: string): TKCSSRuleset {
    return this.themeManager.getCssRulesetByClassName(className);
  }

  // 批量获取样式
  getBatchStyles(classNames: string[]): Map<string, TKCSSRuleset> {
    const styles = new Map<string, TKCSSRuleset>();

    classNames.forEach(className => {
      const style = this.themeManager.getCssRulesetByClassName(className);
      styles.set(className, style);
    });

    return styles;
  }

  // 检查主题是否可用
  isThemeAvailable(themeName: string): boolean {
    return this.supportedThemes.includes(themeName);
  }

  // 重置到默认主题
  resetToDefaultTheme() {
    const defaultTheme = 'light'; // 或从配置中获取
    this.switchTheme(defaultTheme);
  }

  // 记录主题切换日志
  private logThemeChange(themeName: string) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 主题切换: ${themeName}`);
  }

  // 通知其他模块主题变更
  private notifyThemeChange(themeName: string) {
    // 可以在这里添加额外的通知逻辑
    // 比如保存用户偏好、统计主题使用情况等
  }
}

// 主题样式工具类
class ThemeStyleUtils {
  private static themeManager = TKThemeManager.shareInstance();

  // 获取颜色值
  static getColor(className: string, property: 'color' | 'backgroundColor' | 'borderColor' = 'color'): ResourceColor | undefined {
    const style = this.themeManager.getCssRulesetByClassName(className);

    switch (property) {
      case 'color':
        return style.fontColor || style.color;
      case 'backgroundColor':
        return style.backgroundColor;
      case 'borderColor':
        return style.borderColor;
      default:
        return undefined;
    }
  }

  // 获取尺寸值
  static getSize(className: string, property: 'width' | 'height' | 'fontSize' = 'fontSize'): Length | undefined {
    const style = this.themeManager.getCssRulesetByClassName(className);

    switch (property) {
      case 'width':
        return style.width;
      case 'height':
        return style.height;
      case 'fontSize':
        return style.fontSize;
      default:
        return undefined;
    }
  }

  // 获取图片资源
  static getImage(className: string, state: 'normal' | 'selected' | 'highlighted' | 'disabled' = 'normal'): Resource | undefined {
    const style = this.themeManager.getCssRulesetByClassName(className);

    switch (state) {
      case 'normal':
        return style.image;
      case 'selected':
        return style.selectedImage;
      case 'highlighted':
        return style.highlightedImage;
      case 'disabled':
        return style.disabledImage;
      default:
        return undefined;
    }
  }

  // 创建样式对象
  static createStyleObject(className: string): {
    colors: any,
    sizes: any,
    images: any
  } {
    const style = this.themeManager.getCssRulesetByClassName(className);

    return {
      colors: {
        primary: style.fontColor || style.color,
        background: style.backgroundColor,
        border: style.borderColor,
        selected: style.selectedColor,
        highlighted: style.highlightedColor,
        disabled: style.disabledColor
      },
      sizes: {
        width: style.width,
        height: style.height,
        fontSize: style.fontSize,
        borderRadius: style.borderRadius,
        borderWidth: style.borderWidth
      },
      images: {
        normal: style.image,
        selected: style.selectedImage,
        highlighted: style.highlightedImage,
        disabled: style.disabledImage
      }
    };
  }
}

// 自定义组件主题集成示例
@Component
struct CustomThemeComponent {
  @State private themeController: ThemeController = new ThemeController();
  @State private currentTheme: string = '';

  aboutToAppear() {
    this.currentTheme = this.themeController.getCurrentTheme();

    // 监听主题变更
    TKNotificationCenter.defaultCenter.addObserver(
      this,
      (notification) => {
        this.currentTheme = notification.object as string;
      },
      TKThemeManager.NOTE_THEME_CHANGED
    );
  }

  build() {
    Column() {
      // 显示当前主题
      Text(`当前主题: ${this.currentTheme}`)
        .fontSize(ThemeStyleUtils.getSize('TKText', 'fontSize'))
        .fontColor(ThemeStyleUtils.getColor('TKText', 'color'))
        .margin(10)

      // 主题切换按钮
      Row() {
        this.themeController.getSupportedThemes().forEach(theme => {
          Button(theme)
            .backgroundColor(ThemeStyleUtils.getColor('TKButton', 'backgroundColor'))
            .fontColor(ThemeStyleUtils.getColor('TKButton', 'color'))
            .margin(5)
            .onClick(() => {
              this.themeController.switchTheme(theme);
            })
        })
      }
      .justifyContent(FlexAlign.SpaceEvenly)
      .width('100%')
      .margin(20)

      // 样式展示区域
      Column() {
        Text('样式展示')
          .fontSize(ThemeStyleUtils.getSize('TKTitle', 'fontSize'))
          .fontColor(ThemeStyleUtils.getColor('TKTitle', 'color'))
          .margin(10)

        Row() {
          Image(ThemeStyleUtils.getImage('TKIcon', 'normal'))
            .width(50)
            .height(50)
            .margin(10)

          Text('主题化文本')
            .fontSize(ThemeStyleUtils.getSize('TKContent', 'fontSize'))
            .fontColor(ThemeStyleUtils.getColor('TKContent', 'color'))
            .margin(10)
        }
        .justifyContent(FlexAlign.Center)
      }
      .backgroundColor(ThemeStyleUtils.getColor('TKPanel', 'backgroundColor'))
      .borderRadius(ThemeStyleUtils.getSize('TKPanel', 'borderRadius') as number)
      .padding(20)
      .margin(20)
    }
    .width('100%')
    .height('100%')
    .backgroundColor(ThemeStyleUtils.getColor('TKBackground', 'backgroundColor'))
  }
}

// 使用示例
function demonstrateThemeManagement() {
  // 创建主题控制器
  const themeController = new ThemeController();

  // 获取支持的主题
  const themes = themeController.getSupportedThemes();
  console.log('支持的主题:', themes);

  // 切换主题
  themes.forEach(theme => {
    console.log(`切换到主题: ${theme}`);
    themeController.switchTheme(theme);

    // 获取主题样式
    const buttonStyle = themeController.getThemeStyle('TKButton');
    console.log(`${theme} 主题下的按钮样式:`, buttonStyle);
  });

  // 批量获取样式
  const batchStyles = themeController.getBatchStyles(['TKButton', 'TKText', 'TKPanel']);
  console.log('批量样式:', batchStyles);

  // 使用样式工具类
  const buttonColor = ThemeStyleUtils.getColor('TKButton', 'backgroundColor');
  const titleSize = ThemeStyleUtils.getSize('TKTitle', 'fontSize');
  const iconImage = ThemeStyleUtils.getImage('TKIcon', 'normal');

  console.log('按钮颜色:', buttonColor);
  console.log('标题大小:', titleSize);
  console.log('图标图片:', iconImage);
}

// 执行演示
demonstrateThemeManagement();
```

## 📚 相关资源

### 官方文档
- HarmonyOS主题开发指南
- CSS样式参考文档

### 参考资料
- 《移动应用主题设计》- 主题系统设计原理
- 《CSS权威指南》- CSS语法和解析

### 相关文件
- `TKThemeStyleAttribute.ets` - 主题样式属性类
- `TKCSSRuleset.ets` - CSS规则集定义
- `TKNotificationCenter.ets` - 通知中心
