# TKFileHelper

> **文件路径**: `src/main/ets/util/file/TKFileHelper.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 中级  
> **前置知识**: 文件系统概念、路径管理、文件操作、编码转换

## 📋 文件概述

### 功能定位
TKFileHelper是HMThinkBaseHar框架的**文件操作工具类**，提供了完整的文件管理功能。它采用命名空间设计，集成了鸿蒙文件系统API，支持文件创建、读写、删除、目录管理、路径处理等功能，为应用提供统一的文件操作服务。

### 核心职责
- **目录路径管理**：提供各种应用目录的路径获取和管理
- **文件基础操作**：支持文件的创建、删除、读取、写入等基础操作
- **目录管理**：支持目录的创建、删除、遍历等操作
- **路径处理**：提供路径格式化、验证、转换等功能
- **文件信息获取**：支持文件大小、存在性检查等信息获取

### 设计模式
- **命名空间模式**：使用namespace封装文件操作功能
- **工具类模式**：提供静态方法的工具类设计
- **适配器模式**：封装鸿蒙文件系统API，提供统一接口
- **策略模式**：支持不同的文件操作策略和编码方式

## 🔧 核心功能

### 目录路径管理

#### 方法1：`cacheDir()`
- **功能**: 获取缓存目录路径
- **返回值**: string - 缓存目录路径
- **使用场景**: 存储临时缓存文件

#### 方法2：`tempDir()`
- **功能**: 获取临时目录路径
- **返回值**: string - 临时目录路径
- **使用场景**: 存储临时文件，应用退出时可能被清理

#### 方法3：`fileDir()`
- **功能**: 获取文件目录路径
- **返回值**: string - 文件目录路径
- **使用场景**: 存储应用私有文件

#### 方法4：`databaseDir()`
- **功能**: 获取数据库目录路径
- **返回值**: string - 数据库目录路径
- **使用场景**: 存储数据库文件

#### 方法5：`resourceDir()`
- **功能**: 获取资源目录路径
- **返回值**: string - 资源目录路径
- **使用场景**: 访问应用资源文件

### 文件基础操作

#### 方法1：`createFile(filePath: string)`
- **功能**: 创建文件
- **参数**: filePath - 文件路径
- **返回值**: boolean - 创建是否成功
- **使用场景**: 创建新文件，自动创建父目录

#### 方法2：`deleteFile(filePath: string)`
- **功能**: 删除文件
- **参数**: filePath - 文件路径
- **返回值**: boolean - 删除是否成功
- **使用场景**: 删除不需要的文件

#### 方法3：`readFile(filePath: string)`
- **功能**: 读取文件内容
- **参数**: filePath - 文件路径
- **返回值**: Uint8Array - 文件内容字节数组
- **使用场景**: 读取文件的二进制内容

#### 方法4：`writeFile(content: string, filePath: string, isAppend?: boolean)`
- **功能**: 写入文件内容
- **参数**: 
  - content - 要写入的内容
  - filePath - 文件路径
  - isAppend - 是否追加模式
- **返回值**: boolean - 写入是否成功
- **使用场景**: 写入文本内容到文件

#### 方法5：`fileSize(filePath: string)`
- **功能**: 获取文件大小
- **参数**: filePath - 文件路径
- **返回值**: number - 文件大小（字节），-1表示失败
- **使用场景**: 检查文件大小

### 目录管理

#### 方法1：`createDir(dirPath: string)`
- **功能**: 创建目录
- **参数**: dirPath - 目录路径
- **返回值**: boolean - 创建是否成功
- **使用场景**: 创建新目录，支持递归创建

#### 方法2：`deleteDir(dirPath: string)`
- **功能**: 删除目录
- **参数**: dirPath - 目录路径
- **返回值**: boolean - 删除是否成功
- **使用场景**: 删除目录及其所有内容

#### 方法3：`listFiles(dirPath: string)`
- **功能**: 列出目录中的文件
- **参数**: dirPath - 目录路径
- **返回值**: Array<string> - 文件名列表
- **使用场景**: 遍历目录内容

## 💡 技术要点

### 核心算法/逻辑

#### 路径格式化机制
```typescript
export function formatFilePath(filePath: string) {
  return TKStringHelper.replace(filePath, `file://${TKSystemHelper.getAppIdentifier()}`, "");
}

function buildDirPath(dirPath: string): string {
  if (dirPath.endsWith("/")) {
    dirPath = dirPath.substring(0, dirPath.length - 1);
  }
  return dirPath;
}
```

#### 文件创建机制
```typescript
export function createFile(filePath: string): boolean {
  let result: boolean = true;
  try {
    if (TKStringHelper.isNotBlank(filePath)) {
      filePath = formatFilePath(filePath);
      if (!fs.accessSync(filePath)) {
        // 创建父目录
        let index: number = filePath.lastIndexOf("/");
        let fileDir = filePath.substring(0, index);
        if (!fs.accessSync(fileDir)) {
          fs.mkdirSync(fileDir, true); // 递归创建目录
        }
        // 创建文件
        let file = fs.openSync(filePath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
        fs.closeSync(file);
      }
    }
  } catch (error) {
    TKLog.error(`[TKFileHelper]创建文件异常，code: ${error.code}， message: ${error.message}`);
    result = false;
  }
  return result;
}
```

#### 文件读写机制
```typescript
// 读取文件
export function readFile(filePath: string): Uint8Array {
  try {
    filePath = formatFilePath(filePath);
    if (fs.accessSync(filePath)) {
      let file = fs.openSync(filePath, fs.OpenMode.READ_ONLY);
      let arrayBuffer = new ArrayBuffer(fs.statSync(file.fd).size);
      fs.readSync(file.fd, arrayBuffer);
      fs.closeSync(file);
      return new Uint8Array(arrayBuffer);
    }
  } catch (error) {
    TKLog.error(`[TKFileHelper]读取文件异常，code: ${error.code}， message: ${error.message}`);
  }
  return new Uint8Array(0);
}

// 写入文件
export function writeFile(content: string, filePath: string, isAppend: boolean = false): boolean {
  let result: boolean = true;
  try {
    if (TKStringHelper.isNotBlank(content) && TKStringHelper.isNotBlank(filePath)) {
      filePath = formatFilePath(filePath);
      
      // 确保文件存在
      if (!fs.accessSync(filePath)) {
        createFile(filePath);
      }
      
      // 转换内容为字节数组
      let uint8Array: Uint8Array = TKDataHelper.stringToUint8Array(content);
      
      // 选择打开模式
      let openMode = isAppend ? 
        (fs.OpenMode.READ_WRITE | fs.OpenMode.APPEND) : 
        (fs.OpenMode.READ_WRITE | fs.OpenMode.TRUNC);
      
      let file = fs.openSync(filePath, openMode);
      fs.writeSync(file.fd, uint8Array.buffer);
      fs.closeSync(file);
    }
  } catch (error) {
    TKLog.error(`[TKFileHelper]写入文件异常，code: ${error.code}， message: ${error.message}`);
    result = false;
  }
  return result;
}
```

#### 目录操作机制
```typescript
// 创建目录
export function createDir(dirPath: string): boolean {
  let result: boolean = true;
  try {
    if (TKStringHelper.isNotBlank(dirPath)) {
      dirPath = formatFilePath(dirPath);
      if (!fs.accessSync(dirPath)) {
        fs.mkdirSync(dirPath, true); // 递归创建
      }
    }
  } catch (error) {
    TKLog.error(`[TKFileHelper]创建目录异常，code: ${error.code}， message: ${error.message}`);
    result = false;
  }
  return result;
}

// 列出文件
export function listFiles(dirPath: string): Array<string> {
  let result: Array<string> = new Array<string>();
  try {
    if (TKStringHelper.isNotBlank(dirPath)) {
      dirPath = formatFilePath(dirPath);
      if (fs.accessSync(dirPath)) {
        let listFileOptions: ListFileOptions = {
          recursion: false,
          listNum: 0,
          filter: {
            suffix: [],
            displayName: [],
            mimeType: [],
            fileSizeOver: 0,
            lastModifiedAfter: new Date(0).getTime()
          }
        };
        result = fs.listFileSync(dirPath, listFileOptions);
      }
    }
  } catch (error) {
    TKLog.error(`[TKFileHelper]列出文件异常，code: ${error.code}， message: ${error.message}`);
  }
  return result;
}
```

### 实现机制分析

#### 上下文管理
- **UIAbilityContext获取**：通过TKContextHelper获取当前应用上下文
- **目录路径统一**：所有目录路径都通过上下文获取，确保正确性
- **路径标准化**：统一处理路径格式，移除末尾斜杠

#### 错误处理策略
- **异常捕获**：所有文件操作都有完整的异常处理
- **日志记录**：详细记录操作失败的错误码和消息
- **优雅降级**：操作失败时返回合理的默认值
- **资源保护**：确保文件句柄正确关闭

#### 路径处理机制
- **格式化处理**：移除file://协议前缀和应用标识符
- **相对路径支持**：支持相对路径和绝对路径
- **路径验证**：检查路径有效性和文件存在性
- **目录自动创建**：文件操作时自动创建必要的父目录

#### 编码转换
- **字符串转换**：通过TKDataHelper进行字符串和字节数组转换
- **编码统一**：统一使用UTF-8编码处理文本文件
- **二进制支持**：支持二进制文件的读写操作

### 性能考虑
- **文件句柄管理**：及时关闭文件句柄，避免资源泄漏
- **缓冲区优化**：使用合适的缓冲区大小进行文件操作
- **批量操作**：支持批量文件操作，减少系统调用
- **路径缓存**：缓存常用目录路径，避免重复获取

### 错误处理
- **文件不存在**：读取不存在的文件时返回空数组
- **权限不足**：处理文件权限不足的情况
- **磁盘空间不足**：处理磁盘空间不足的写入失败
- **路径无效**：处理无效路径的情况

### 最佳实践
- **路径规范化**：使用工具类提供的路径获取方法
- **异常处理**：调用文件操作方法时检查返回值
- **资源清理**：及时清理不需要的临时文件
- **权限管理**：合理使用不同的目录存储不同类型的文件

## 🔗 依赖关系

### 依赖的模块
- `TKContextHelper` - 上下文工具，获取应用上下文
- `TKLog` - 日志工具，记录操作日志和错误信息
- `TKStringHelper` - 字符串工具，处理路径和内容
- `TKDataHelper` - 数据工具，处理编码转换
- `fs` - 鸿蒙文件系统API，底层文件操作

### 被依赖情况
- `TKCacheManager` - 缓存管理器，使用文件工具进行文件缓存
- `TKLog` - 日志工具，使用文件工具写入日志文件
- `TKPreferences` - 偏好设置，使用文件工具进行配置存储
- `各种配置管理器` - 使用文件工具读写配置文件

### 关联文件
- `TKContextHelper.ets` - 上下文工具类
- `TKDataHelper.ets` - 数据转换工具类
- `TKStringHelper.ets` - 字符串处理工具类
- `TKXMLHelper.ets` - XML文件处理工具类

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKFileHelper是框架文件操作的基础工具：
1. **基础设施**：为其他模块提供文件操作的基础能力
2. **使用频率高**：在需要文件操作的场景中广泛使用
3. **功能完整**：提供完整的文件和目录操作功能
4. **稳定可靠**：完善的错误处理和资源管理

理解TKFileHelper有助于掌握框架的文件管理机制，是处理文件操作的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解文件系统**：掌握文件系统的基本概念和操作
2. **分析目录结构**：理解应用目录的分类和用途
3. **学习文件操作**：掌握文件的创建、读写、删除等操作
4. **实践文件管理**：使用文件工具实现文件管理功能

### 前置学习
- 文件系统基本概念
- 鸿蒙文件系统API
- 路径管理和处理
- 编码转换原理

### 后续学习
- `TKDataHelper.ets` - 学习数据转换工具的使用
- `TKXMLHelper.ets` - 了解XML文件处理
- `TKPreferences.ets` - 学习配置文件管理

### 实践建议
1. **基础文件操作**：练习文件的创建、读写、删除
2. **目录管理实验**：测试目录的创建和遍历
3. **路径处理练习**：理解不同目录的用途和路径获取
4. **错误处理验证**：测试各种异常情况的处理

### 常见问题
1. **问题**: 如何选择合适的目录存储文件？
   **解答**: 临时文件用tempDir，缓存文件用cacheDir，持久化文件用fileDir，数据库文件用databaseDir。

2. **问题**: 文件操作失败如何处理？
   **解答**: 检查返回值，查看日志中的错误信息，确认路径正确性和权限，必要时进行重试或降级处理。

## 📝 代码示例

### 基础使用
```typescript
import { TKFileHelper } from '@thinkive/tk-harmony-base';

// 基础文件操作
function basicFileOperations() {
  // 获取各种目录路径
  const cacheDir = TKFileHelper.cacheDir();
  const tempDir = TKFileHelper.tempDir();
  const fileDir = TKFileHelper.fileDir();

  console.log('缓存目录:', cacheDir);
  console.log('临时目录:', tempDir);
  console.log('文件目录:', fileDir);

  // 创建文件
  const filePath = `${fileDir}/test.txt`;
  const created = TKFileHelper.createFile(filePath);
  console.log('文件创建结果:', created);

  // 写入文件内容
  const content = "Hello, HarmonyOS!";
  const written = TKFileHelper.writeFile(content, filePath);
  console.log('文件写入结果:', written);

  // 读取文件内容
  const fileData = TKFileHelper.readFile(filePath);
  const readContent = TKDataHelper.uint8ArrayToString(fileData);
  console.log('读取的内容:', readContent);

  // 获取文件大小
  const size = TKFileHelper.fileSize(filePath);
  console.log('文件大小:', size, '字节');

  // 删除文件
  const deleted = TKFileHelper.deleteFile(filePath);
  console.log('文件删除结果:', deleted);
}

// 目录操作
function directoryOperations() {
  const baseDir = TKFileHelper.fileDir();
  const testDir = `${baseDir}/testDir`;

  // 创建目录
  const dirCreated = TKFileHelper.createDir(testDir);
  console.log('目录创建结果:', dirCreated);

  // 在目录中创建文件
  const file1 = `${testDir}/file1.txt`;
  const file2 = `${testDir}/file2.txt`;

  TKFileHelper.writeFile("文件1内容", file1);
  TKFileHelper.writeFile("文件2内容", file2);

  // 列出目录中的文件
  const files = TKFileHelper.listFiles(testDir);
  console.log('目录中的文件:', files);

  // 删除目录
  const dirDeleted = TKFileHelper.deleteDir(testDir);
  console.log('目录删除结果:', dirDeleted);
}

// 执行示例
basicFileOperations();
directoryOperations();
```

### 高级用法
```typescript
// 文件管理器
class FileManager {
  private baseDir: string;

  constructor(subDir: string = 'app_data') {
    this.baseDir = `${TKFileHelper.fileDir()}/${subDir}`;
    this.ensureBaseDir();
  }

  // 确保基础目录存在
  private ensureBaseDir() {
    if (!TKFileHelper.createDir(this.baseDir)) {
      throw new Error(`无法创建基础目录: ${this.baseDir}`);
    }
  }

  // 保存JSON数据
  saveJson(fileName: string, data: any): boolean {
    try {
      const content = JSON.stringify(data, null, 2);
      const filePath = `${this.baseDir}/${fileName}.json`;
      return TKFileHelper.writeFile(content, filePath);
    } catch (error) {
      console.error('保存JSON失败:', error);
      return false;
    }
  }

  // 读取JSON数据
  loadJson<T>(fileName: string): T | null {
    try {
      const filePath = `${this.baseDir}/${fileName}.json`;
      const fileData = TKFileHelper.readFile(filePath);

      if (fileData.length === 0) {
        return null;
      }

      const content = TKDataHelper.uint8ArrayToString(fileData);
      return JSON.parse(content) as T;
    } catch (error) {
      console.error('读取JSON失败:', error);
      return null;
    }
  }

  // 保存文本文件
  saveText(fileName: string, content: string, append: boolean = false): boolean {
    const filePath = `${this.baseDir}/${fileName}`;
    return TKFileHelper.writeFile(content, filePath, append);
  }

  // 读取文本文件
  loadText(fileName: string): string {
    try {
      const filePath = `${this.baseDir}/${fileName}`;
      const fileData = TKFileHelper.readFile(filePath);
      return TKDataHelper.uint8ArrayToString(fileData);
    } catch (error) {
      console.error('读取文本失败:', error);
      return '';
    }
  }

  // 复制文件
  copyFile(sourceFileName: string, targetFileName: string): boolean {
    try {
      const sourcePath = `${this.baseDir}/${sourceFileName}`;
      const targetPath = `${this.baseDir}/${targetFileName}`;

      const fileData = TKFileHelper.readFile(sourcePath);
      if (fileData.length === 0) {
        return false;
      }

      const content = TKDataHelper.uint8ArrayToString(fileData);
      return TKFileHelper.writeFile(content, targetPath);
    } catch (error) {
      console.error('复制文件失败:', error);
      return false;
    }
  }

  // 移动文件
  moveFile(sourceFileName: string, targetFileName: string): boolean {
    if (this.copyFile(sourceFileName, targetFileName)) {
      const sourcePath = `${this.baseDir}/${sourceFileName}`;
      return TKFileHelper.deleteFile(sourcePath);
    }
    return false;
  }

  // 获取文件列表
  getFileList(): string[] {
    return TKFileHelper.listFiles(this.baseDir);
  }

  // 获取文件信息
  getFileInfo(fileName: string): {
    exists: boolean,
    size: number,
    path: string
  } {
    const filePath = `${this.baseDir}/${fileName}`;
    const size = TKFileHelper.fileSize(filePath);

    return {
      exists: size !== -1,
      size: size,
      path: filePath
    };
  }

  // 清理目录
  cleanup(): boolean {
    return TKFileHelper.deleteDir(this.baseDir);
  }
}

// 日志文件管理器
class LogFileManager {
  private logDir: string;
  private maxLogFiles: number = 5;
  private maxLogSize: number = 1024 * 1024; // 1MB

  constructor() {
    this.logDir = `${TKFileHelper.tempDir()}/logs`;
    TKFileHelper.createDir(this.logDir);
  }

  // 写入日志
  writeLog(level: string, message: string) {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} [${level}] ${message}\n`;
    const currentLogFile = this.getCurrentLogFile();

    TKFileHelper.writeFile(logEntry, currentLogFile, true);
    this.rotateLogsIfNeeded();
  }

  // 获取当前日志文件
  private getCurrentLogFile(): string {
    const today = new Date().toISOString().split('T')[0];
    return `${this.logDir}/app_${today}.log`;
  }

  // 日志轮转
  private rotateLogsIfNeeded() {
    const currentLogFile = this.getCurrentLogFile();
    const size = TKFileHelper.fileSize(currentLogFile);

    if (size > this.maxLogSize) {
      this.archiveCurrentLog();
      this.cleanupOldLogs();
    }
  }

  // 归档当前日志
  private archiveCurrentLog() {
    const currentLogFile = this.getCurrentLogFile();
    const timestamp = Date.now();
    const archiveFile = `${this.logDir}/app_${timestamp}.log`;

    const logData = TKFileHelper.readFile(currentLogFile);
    const content = TKDataHelper.uint8ArrayToString(logData);

    TKFileHelper.writeFile(content, archiveFile);
    TKFileHelper.deleteFile(currentLogFile);
  }

  // 清理旧日志
  private cleanupOldLogs() {
    const logFiles = TKFileHelper.listFiles(this.logDir);
    const sortedFiles = logFiles.sort().reverse();

    // 保留最新的几个日志文件
    for (let i = this.maxLogFiles; i < sortedFiles.length; i++) {
      const filePath = `${this.logDir}/${sortedFiles[i]}`;
      TKFileHelper.deleteFile(filePath);
    }
  }

  // 获取日志文件列表
  getLogFiles(): string[] {
    return TKFileHelper.listFiles(this.logDir);
  }

  // 读取日志文件
  readLogFile(fileName: string): string {
    const filePath = `${this.logDir}/${fileName}`;
    const fileData = TKFileHelper.readFile(filePath);
    return TKDataHelper.uint8ArrayToString(fileData);
  }
}

// 配置文件管理器
class ConfigManager {
  private configDir: string;
  private configs: Map<string, any> = new Map();

  constructor() {
    this.configDir = `${TKFileHelper.fileDir()}/configs`;
    TKFileHelper.createDir(this.configDir);
    this.loadAllConfigs();
  }

  // 加载所有配置
  private loadAllConfigs() {
    const configFiles = TKFileHelper.listFiles(this.configDir);

    configFiles.forEach(fileName => {
      if (fileName.endsWith('.json')) {
        const configName = fileName.replace('.json', '');
        const config = this.loadConfig(configName);
        if (config) {
          this.configs.set(configName, config);
        }
      }
    });
  }

  // 加载配置
  private loadConfig(configName: string): any {
    try {
      const filePath = `${this.configDir}/${configName}.json`;
      const fileData = TKFileHelper.readFile(filePath);

      if (fileData.length === 0) {
        return null;
      }

      const content = TKDataHelper.uint8ArrayToString(fileData);
      return JSON.parse(content);
    } catch (error) {
      console.error(`加载配置失败: ${configName}`, error);
      return null;
    }
  }

  // 保存配置
  saveConfig(configName: string, config: any): boolean {
    try {
      const content = JSON.stringify(config, null, 2);
      const filePath = `${this.configDir}/${configName}.json`;
      const success = TKFileHelper.writeFile(content, filePath);

      if (success) {
        this.configs.set(configName, config);
      }

      return success;
    } catch (error) {
      console.error(`保存配置失败: ${configName}`, error);
      return false;
    }
  }

  // 获取配置
  getConfig(configName: string, defaultValue: any = null): any {
    return this.configs.get(configName) || defaultValue;
  }

  // 设置配置项
  setConfigValue(configName: string, key: string, value: any): boolean {
    let config = this.configs.get(configName) || {};
    config[key] = value;
    return this.saveConfig(configName, config);
  }

  // 获取配置项
  getConfigValue(configName: string, key: string, defaultValue: any = null): any {
    const config = this.configs.get(configName);
    return config ? (config[key] || defaultValue) : defaultValue;
  }

  // 删除配置
  deleteConfig(configName: string): boolean {
    const filePath = `${this.configDir}/${configName}.json`;
    const success = TKFileHelper.deleteFile(filePath);

    if (success) {
      this.configs.delete(configName);
    }

    return success;
  }

  // 获取所有配置名称
  getConfigNames(): string[] {
    return Array.from(this.configs.keys());
  }
}

// 使用示例
async function demonstrateFileManagement() {
  // 文件管理器示例
  const fileManager = new FileManager('my_app');

  // 保存和读取JSON数据
  const userData = { name: '张三', age: 30, city: '北京' };
  fileManager.saveJson('user', userData);

  const loadedUser = fileManager.loadJson<any>('user');
  console.log('加载的用户数据:', loadedUser);

  // 保存和读取文本文件
  fileManager.saveText('notes.txt', '这是第一行笔记\n', false);
  fileManager.saveText('notes.txt', '这是第二行笔记\n', true);

  const notes = fileManager.loadText('notes.txt');
  console.log('笔记内容:', notes);

  // 日志管理器示例
  const logManager = new LogFileManager();
  logManager.writeLog('INFO', '应用启动');
  logManager.writeLog('WARN', '内存使用率较高');
  logManager.writeLog('ERROR', '网络连接失败');

  // 配置管理器示例
  const configManager = new ConfigManager();

  // 保存应用配置
  configManager.saveConfig('app', {
    theme: 'dark',
    language: 'zh-CN',
    autoSave: true
  });

  // 读取配置
  const theme = configManager.getConfigValue('app', 'theme', 'light');
  console.log('当前主题:', theme);

  // 更新配置
  configManager.setConfigValue('app', 'theme', 'light');

  console.log('文件管理演示完成');
}

// 执行演示
demonstrateFileManagement();
```

## 📚 相关资源

### 官方文档
- HarmonyOS文件管理开发指南
- 文件系统API参考文档

### 参考资料
- 《文件系统设计与实现》- 文件系统原理
- 《移动应用数据管理》- 移动端文件管理最佳实践

### 相关文件
- `TKDataHelper.ets` - 数据转换工具
- `TKContextHelper.ets` - 上下文管理工具
- `TKStringHelper.ets` - 字符串处理工具
