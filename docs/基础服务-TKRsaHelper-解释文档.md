# TKRsaHelper

> **文件路径**: `src/main/ets/util/crypto/TKRsaHelper.ets`  
> **所属层级**: 基础服务层  
> **重要程度**: ⭐⭐⭐ (3星)  
> **学习难度**: 高级  
> **前置知识**: RSA算法、非对称加密、数字签名、证书管理

## 📋 文件概述

### 功能定位
TKRsaHelper是HMThinkBaseHar框架的**RSA加密工具类**，提供了完整的RSA非对称加密功能。它采用命名空间设计，集成了鸿蒙CryptoArchitectureKit和证书框架，支持RSA加密解密、数字签名验证、证书处理、密钥管理等功能，为应用提供企业级的非对称加密服务。

### 核心职责
- **RSA加密解密**：提供RSA公钥加密、私钥解密的完整功能
- **数字签名**：支持RSA私钥签名和公钥验证签名
- **证书处理**：支持X.509证书的解析、验证和公钥提取
- **密钥管理**：支持RSA密钥对生成、导入和格式转换
- **分块处理**：自动处理大数据的分块加密解密

### 设计模式
- **命名空间模式**：使用namespace封装RSA功能，避免全局污染
- **工厂模式**：根据密钥类型和长度创建相应的加密器
- **策略模式**：支持不同的填充模式和签名算法
- **适配器模式**：封装鸿蒙加密框架，提供统一接口

## 🔧 核心功能

### 常量定义

#### 常量1：`MAX_ENCRYPT_BLOCK = 117`
- **用途**: RSA最大加密明文大小（字节）
- **说明**: 1024位RSA密钥的最大加密块大小
- **使用场景**: 分块加密时的块大小限制

#### 常量2：`MAX_DECRYPT_BLOCK = 128`
- **用途**: RSA最大解密密文大小（字节）
- **说明**: 1024位RSA密钥的最大解密块大小
- **使用场景**: 分块解密时的块大小限制

### 核心加密方法

#### 方法1：`stringWithRsaEncrypt(content, publicKey)`
- **功能**: 使用公钥加密字符串，返回Base64编码结果
- **参数**: 
  - content - 要加密的字符串内容
  - publicKey - RSA公钥（PEM格式或证书）
- **返回值**: Promise<string> - Base64编码的加密结果
- **使用场景**: 敏感信息的安全传输

#### 方法2：`dataWithRsaEncrypt(data, publicKey)`
- **功能**: 使用公钥加密字节数组
- **参数**: 
  - data - 要加密的字节数组
  - publicKey - RSA公钥
- **返回值**: Promise<Uint8Array> - 加密后的字节数组
- **使用场景**: 二进制数据的加密处理

#### 方法3：`stringWithRsaDecrypt(content, privateKey)`
- **功能**: 使用私钥解密字符串
- **参数**: 
  - content - 要解密的Base64字符串
  - privateKey - RSA私钥（PEM格式）
- **返回值**: Promise<string> - 解密后的字符串
- **使用场景**: 加密数据的解密还原

#### 方法4：`dataWithRsaDecrypt(data, privateKey)`
- **功能**: 使用私钥解密字节数组
- **参数**: 
  - data - 要解密的字节数组
  - privateKey - RSA私钥
- **返回值**: Promise<Uint8Array> - 解密后的字节数组
- **使用场景**: 加密二进制数据的解密

### 数字签名方法

#### 方法1：`stringWithRsaSign(content, privateKey)`
- **功能**: 使用私钥对字符串进行数字签名
- **参数**: 
  - content - 要签名的字符串内容
  - privateKey - RSA私钥
- **返回值**: Promise<string> - Base64编码的签名结果
- **使用场景**: 数据完整性和身份认证

#### 方法2：`stringWithRsaVerify(content, signature, publicKey)`
- **功能**: 使用公钥验证字符串的数字签名
- **参数**: 
  - content - 原始字符串内容
  - signature - Base64编码的签名
  - publicKey - RSA公钥
- **返回值**: Promise<boolean> - 签名验证结果
- **使用场景**: 签名验证和数据完整性检查

### 证书处理方法

#### 方法1：`getPublicKeyFromCert(certData)`
- **功能**: 从X.509证书中提取RSA公钥
- **参数**: certData - 证书数据（PEM或DER格式）
- **返回值**: Promise<cryptoFramework.PubKey> - 提取的公钥
- **使用场景**: 证书验证和公钥提取

#### 方法2：`verifyCertificate(certData, caCertData?)`
- **功能**: 验证X.509证书的有效性
- **参数**: 
  - certData - 要验证的证书数据
  - caCertData - CA证书数据（可选）
- **返回值**: Promise<boolean> - 证书验证结果
- **使用场景**: 证书链验证和信任建立

### 密钥管理方法

#### 方法1：`generateRsaKeyPair(keySize?)`
- **功能**: 生成RSA密钥对
- **参数**: keySize - 密钥长度（默认2048位）
- **返回值**: Promise<cryptoFramework.KeyPair> - 生成的密钥对
- **使用场景**: 密钥对生成和初始化

#### 方法2：`exportPublicKeyToPem(publicKey)`
- **功能**: 将公钥导出为PEM格式
- **参数**: publicKey - RSA公钥对象
- **返回值**: Promise<string> - PEM格式的公钥
- **使用场景**: 密钥交换和存储

#### 方法3：`importPublicKeyFromPem(pemData)`
- **功能**: 从PEM格式导入公钥
- **参数**: pemData - PEM格式的公钥数据
- **返回值**: Promise<cryptoFramework.PubKey> - 导入的公钥
- **使用场景**: 密钥导入和恢复

## 💡 技术要点

### 核心算法/逻辑

#### 分块加密处理
```typescript
async function encryptLargeData(data: Uint8Array, publicKey: cryptoFramework.PubKey): Promise<Uint8Array> {
  const encryptedBlocks: Uint8Array[] = [];
  
  // 分块处理大数据
  for (let i = 0; i < data.length; i += MAX_ENCRYPT_BLOCK) {
    const block = data.slice(i, i + MAX_ENCRYPT_BLOCK);
    const encryptedBlock = await encryptBlock(block, publicKey);
    encryptedBlocks.push(encryptedBlock);
  }
  
  // 合并加密块
  const totalLength = encryptedBlocks.reduce((sum, block) => sum + block.length, 0);
  const result = new Uint8Array(totalLength);
  let offset = 0;
  
  for (const block of encryptedBlocks) {
    result.set(block, offset);
    offset += block.length;
  }
  
  return result;
}
```

#### 证书公钥提取
```typescript
export async function getPublicKeyFromCert(certData: string | Uint8Array): Promise<cryptoFramework.PubKey> {
  try {
    // 转换证书数据格式
    const certBytes = typeof certData === 'string' ? 
      TKBase64Helper.dataWithBase64Decode(certData) : certData;
    
    // 创建证书对象
    const certBlob: certFramework.EncodingBlob = {
      data: certBytes,
      encodingFormat: certFramework.EncodingFormat.FORMAT_DER
    };
    
    // 解析证书
    const x509Cert = certFramework.createX509Cert(certBlob);
    
    // 提取公钥
    const publicKeyBlob = x509Cert.getPublicKey();
    
    // 转换为加密框架公钥
    const keyGenerator = cryptoFramework.createAsyKeyGenerator('RSA2048');
    const publicKey = await keyGenerator.convertKey(publicKeyBlob);
    
    return publicKey.pubKey;
  } catch (error) {
    TKLog.error(`[TKRsaHelper]证书公钥提取失败: ${error.message}`);
    throw error;
  }
}
```

#### 数字签名生成
```typescript
export async function stringWithRsaSign(content: string, privateKey: string): Promise<string> {
  try {
    const data = TKDataHelper.stringToUint8Array(content);
    
    // 导入私钥
    const privKey = await importPrivateKeyFromPem(privateKey);
    
    // 创建签名器
    const signer = cryptoFramework.createSign('RSA2048|PKCS1|SHA256');
    await signer.init(privKey);
    
    // 执行签名
    const signatureData = await signer.sign({ data: data });
    
    // 返回Base64编码的签名
    return TKBase64Helper.stringWithBase64Encode(signatureData.data);
  } catch (error) {
    TKLog.error(`[TKRsaHelper]RSA签名失败: ${error.message}`);
    throw error;
  }
}
```

### 实现机制分析

#### 非对称加密特性
- **公钥加密**：使用公钥加密数据，只有对应私钥才能解密
- **私钥解密**：使用私钥解密数据，确保数据安全性
- **密钥分离**：公钥可以公开分发，私钥必须安全保存
- **安全性高**：基于大数分解难题，安全性远高于对称加密

#### 分块处理机制
- **块大小限制**：RSA加密有明文长度限制，需要分块处理
- **自动分块**：工具自动将大数据分割为合适的块大小
- **块合并**：解密时自动合并所有解密块
- **性能优化**：合理的块大小平衡安全性和性能

#### 证书集成
- **X.509支持**：完整支持X.509证书标准
- **公钥提取**：从证书中提取RSA公钥用于加密
- **证书验证**：验证证书的有效性和信任链
- **格式兼容**：支持PEM和DER两种证书格式

#### 数字签名机制
- **完整性保证**：通过签名确保数据未被篡改
- **身份认证**：通过签名验证数据来源的真实性
- **不可否认**：签名者无法否认已签名的数据
- **算法选择**：使用SHA256哈希和PKCS1填充

### 性能考虑
- **算法选择**：RSA2048提供足够安全性和合理性能
- **分块优化**：合理的分块大小减少加密次数
- **内存管理**：及时释放大数据块，避免内存泄漏
- **异步处理**：使用异步API避免阻塞主线程

### 错误处理
- **参数验证**：检查密钥格式和数据有效性
- **异常捕获**：捕获加密解密过程中的所有异常
- **错误日志**：详细记录错误信息便于调试
- **优雅降级**：操作失败时抛出明确的错误信息

### 最佳实践
- **密钥管理**：私钥安全存储，公钥可以公开分发
- **证书验证**：使用前验证证书的有效性和信任链
- **混合加密**：结合AES实现高效的混合加密方案
- **签名验证**：重要数据传输时使用数字签名

## 🔗 依赖关系

### 依赖的模块
- `cryptoFramework` - 鸿蒙加密框架，提供RSA加密功能
- `certFramework` - 鸿蒙证书框架，提供证书处理功能
- `TKDataHelper` - 数据工具，处理数据类型转换
- `TKBase64Helper` - Base64工具，处理编码解码
- `TKLog` - 日志工具，记录错误和调试信息

### 被依赖情况
- `TKComBusV3Client` - 通信客户端，使用RSA进行密钥交换
- `TKPasswordGenerator` - 密码生成器，可能使用RSA保护密钥
- `各安全模块` - 使用RSA进行身份认证和数据保护
- `证书验证模块` - 使用RSA验证数字证书

### 关联文件
- `TKAesHelper.ets` - AES加密工具，配合实现混合加密
- `TKBase64Helper.ets` - Base64编码工具
- `TKPasswordGenerator.ets` - 密码生成器
- `TKHexHelper.ets` - 十六进制编码工具

## 📊 重要程度评级

### 评级标准
⭐⭐⭐ **一般了解**: 特定场景使用，了解即可

### 本文件评级理由
TKRsaHelper是框架高级安全功能的重要组成：
1. **高级安全**：提供企业级的非对称加密和数字签名功能
2. **证书支持**：完整的X.509证书处理和验证能力
3. **身份认证**：支持基于RSA的身份认证和授权
4. **数据完整性**：通过数字签名确保数据完整性

理解TKRsaHelper有助于掌握高级加密技术的应用，是实现企业级安全方案的重要工具。

## 🎯 学习建议

### 学习路径
1. **理解RSA算法**：掌握RSA非对称加密的基本原理
2. **分析数字签名**：理解数字签名的生成和验证机制
3. **学习证书体系**：掌握X.509证书和PKI体系
4. **实践混合加密**：结合AES实现高效的混合加密方案

### 前置学习
- 非对称加密算法基础
- RSA算法原理和应用
- 数字签名和证书概念
- PKI公钥基础设施

### 后续学习
- `TKAesHelper.ets` - 学习对称加密的配合使用
- `TKPasswordGenerator.ets` - 了解安全密钥生成
- 证书管理和PKI体系应用

### 实践建议
1. **基础加密测试**：测试RSA加密解密的基本功能
2. **签名验证实验**：实践数字签名的生成和验证
3. **证书处理练习**：处理X.509证书的解析和验证
4. **混合加密实现**：结合AES实现完整的加密方案

### 常见问题
1. **问题**: RSA加密有什么限制？
   **解答**: RSA加密有明文长度限制，1024位密钥最多加密117字节，需要分块处理大数据。

2. **问题**: 如何选择RSA密钥长度？
   **解答**: 2048位是当前推荐的最小长度，4096位提供更高安全性但性能较低，根据安全需求选择。

## 📝 代码示例

### 基础使用
```typescript
import { TKRsaHelper } from '@thinkive/tk-harmony-base';

// 基础RSA加密解密
async function basicRsaOperations() {
  // 生成RSA密钥对
  const keyPair = await TKRsaHelper.generateRsaKeyPair(2048);

  // 导出公钥和私钥为PEM格式
  const publicKeyPem = await TKRsaHelper.exportPublicKeyToPem(keyPair.pubKey);
  const privateKeyPem = await TKRsaHelper.exportPrivateKeyToPem(keyPair.priKey);

  console.log('公钥PEM:', publicKeyPem);
  console.log('私钥PEM:', privateKeyPem);

  // 要加密的敏感数据
  const sensitiveData = "这是需要RSA加密的敏感信息";
  console.log('原始数据:', sensitiveData);

  // 使用公钥加密
  const encryptedData = await TKRsaHelper.stringWithRsaEncrypt(sensitiveData, publicKeyPem);
  console.log('加密结果:', encryptedData);

  // 使用私钥解密
  const decryptedData = await TKRsaHelper.stringWithRsaDecrypt(encryptedData, privateKeyPem);
  console.log('解密结果:', decryptedData);

  // 验证加密解密的正确性
  console.log('加密解密正确:', sensitiveData === decryptedData);
}

// 数字签名和验证
async function digitalSignatureOperations() {
  // 生成密钥对
  const keyPair = await TKRsaHelper.generateRsaKeyPair(2048);
  const publicKeyPem = await TKRsaHelper.exportPublicKeyToPem(keyPair.pubKey);
  const privateKeyPem = await TKRsaHelper.exportPrivateKeyToPem(keyPair.priKey);

  // 要签名的数据
  const dataToSign = "这是需要数字签名的重要数据";
  console.log('原始数据:', dataToSign);

  // 使用私钥生成数字签名
  const signature = await TKRsaHelper.stringWithRsaSign(dataToSign, privateKeyPem);
  console.log('数字签名:', signature);

  // 使用公钥验证签名
  const isValid = await TKRsaHelper.stringWithRsaVerify(dataToSign, signature, publicKeyPem);
  console.log('签名验证结果:', isValid);

  // 测试篡改数据的签名验证
  const tamperedData = "这是被篡改的数据";
  const isTamperedValid = await TKRsaHelper.stringWithRsaVerify(tamperedData, signature, publicKeyPem);
  console.log('篡改数据签名验证:', isTamperedValid);
}

// 证书处理
async function certificateOperations() {
  // 模拟证书数据（实际应用中从文件或网络获取）
  const certPem = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMTcwODI4MDkxNjAyWhcNMTgwODI4MDkxNjAyWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3xrUXgHBmg7Qjux6o4+5qX5oBUeIQK
-----END CERTIFICATE-----`;

  try {
    // 从证书提取公钥
    const publicKey = await TKRsaHelper.getPublicKeyFromCert(certPem);
    console.log('从证书提取的公钥:', publicKey);

    // 验证证书
    const isValidCert = await TKRsaHelper.verifyCertificate(certPem);
    console.log('证书验证结果:', isValidCert);

    // 使用证书公钥加密数据
    const testData = "使用证书公钥加密的数据";
    const encrypted = await TKRsaHelper.stringWithRsaEncrypt(testData, certPem);
    console.log('使用证书公钥加密:', encrypted);

  } catch (error) {
    console.error('证书处理失败:', error.message);
  }
}

// 执行示例
basicRsaOperations();
digitalSignatureOperations();
certificateOperations();
```

### 高级用法
```typescript
// RSA密钥管理器
class RsaKeyManager {
  private keyPairs: Map<string, cryptoFramework.KeyPair> = new Map();
  private publicKeys: Map<string, string> = new Map();
  private privateKeys: Map<string, string> = new Map();

  // 生成并存储密钥对
  async generateAndStoreKeyPair(keyId: string, keySize: number = 2048): Promise<boolean> {
    try {
      const keyPair = await TKRsaHelper.generateRsaKeyPair(keySize);

      // 导出PEM格式
      const publicKeyPem = await TKRsaHelper.exportPublicKeyToPem(keyPair.pubKey);
      const privateKeyPem = await TKRsaHelper.exportPrivateKeyToPem(keyPair.priKey);

      // 存储密钥
      this.keyPairs.set(keyId, keyPair);
      this.publicKeys.set(keyId, publicKeyPem);
      this.privateKeys.set(keyId, privateKeyPem);

      console.log(`密钥对已生成并存储，ID: ${keyId}`);
      return true;
    } catch (error) {
      console.error(`密钥对生成失败，ID: ${keyId}`, error);
      return false;
    }
  }

  // 导入密钥对
  async importKeyPair(keyId: string, publicKeyPem: string, privateKeyPem: string): Promise<boolean> {
    try {
      const publicKey = await TKRsaHelper.importPublicKeyFromPem(publicKeyPem);
      const privateKey = await TKRsaHelper.importPrivateKeyFromPem(privateKeyPem);

      const keyPair: cryptoFramework.KeyPair = {
        pubKey: publicKey,
        priKey: privateKey
      };

      this.keyPairs.set(keyId, keyPair);
      this.publicKeys.set(keyId, publicKeyPem);
      this.privateKeys.set(keyId, privateKeyPem);

      console.log(`密钥对已导入，ID: ${keyId}`);
      return true;
    } catch (error) {
      console.error(`密钥对导入失败，ID: ${keyId}`, error);
      return false;
    }
  }

  // 获取公钥
  getPublicKey(keyId: string): string | undefined {
    return this.publicKeys.get(keyId);
  }

  // 获取私钥
  getPrivateKey(keyId: string): string | undefined {
    return this.privateKeys.get(keyId);
  }

  // 使用指定密钥加密
  async encryptWithKey(keyId: string, data: string): Promise<string> {
    const publicKey = this.getPublicKey(keyId);
    if (!publicKey) {
      throw new Error(`未找到密钥，ID: ${keyId}`);
    }

    return await TKRsaHelper.stringWithRsaEncrypt(data, publicKey);
  }

  // 使用指定密钥解密
  async decryptWithKey(keyId: string, encryptedData: string): Promise<string> {
    const privateKey = this.getPrivateKey(keyId);
    if (!privateKey) {
      throw new Error(`未找到密钥，ID: ${keyId}`);
    }

    return await TKRsaHelper.stringWithRsaDecrypt(encryptedData, privateKey);
  }

  // 使用指定密钥签名
  async signWithKey(keyId: string, data: string): Promise<string> {
    const privateKey = this.getPrivateKey(keyId);
    if (!privateKey) {
      throw new Error(`未找到密钥，ID: ${keyId}`);
    }

    return await TKRsaHelper.stringWithRsaSign(data, privateKey);
  }

  // 使用指定密钥验证签名
  async verifyWithKey(keyId: string, data: string, signature: string): Promise<boolean> {
    const publicKey = this.getPublicKey(keyId);
    if (!publicKey) {
      throw new Error(`未找到密钥，ID: ${keyId}`);
    }

    return await TKRsaHelper.stringWithRsaVerify(data, signature, publicKey);
  }

  // 列出所有密钥ID
  listKeyIds(): string[] {
    return Array.from(this.keyPairs.keys());
  }

  // 删除密钥对
  removeKeyPair(keyId: string): boolean {
    const removed = this.keyPairs.delete(keyId) &&
                   this.publicKeys.delete(keyId) &&
                   this.privateKeys.delete(keyId);

    if (removed) {
      console.log(`密钥对已删除，ID: ${keyId}`);
    }

    return removed;
  }
}

// 混合加密系统（RSA + AES）
class HybridEncryptionSystem {
  private rsaKeyManager: RsaKeyManager;

  constructor() {
    this.rsaKeyManager = new RsaKeyManager();
  }

  // 初始化系统
  async initialize(keyId: string = 'default'): Promise<void> {
    await this.rsaKeyManager.generateAndStoreKeyPair(keyId, 2048);
    console.log('混合加密系统初始化完成');
  }

  // 混合加密（RSA加密AES密钥，AES加密数据）
  async hybridEncrypt(data: string, recipientKeyId: string): Promise<{
    encryptedData: string,
    encryptedKey: string,
    algorithm: string
  }> {
    try {
      // 生成随机AES密钥
      const aesKey = this.generateRandomKey(32); // 256位AES密钥

      // 使用AES加密数据
      const encryptedData = await TKAesHelper.stringWithAesEncrypt(data, aesKey);

      // 使用RSA加密AES密钥
      const encryptedKey = await this.rsaKeyManager.encryptWithKey(recipientKeyId, aesKey);

      return {
        encryptedData: encryptedData,
        encryptedKey: encryptedKey,
        algorithm: 'RSA+AES'
      };
    } catch (error) {
      console.error('混合加密失败:', error);
      throw error;
    }
  }

  // 混合解密
  async hybridDecrypt(
    encryptedData: string,
    encryptedKey: string,
    recipientKeyId: string
  ): Promise<string> {
    try {
      // 使用RSA解密AES密钥
      const aesKey = await this.rsaKeyManager.decryptWithKey(recipientKeyId, encryptedKey);

      // 使用AES解密数据
      const decryptedData = await TKAesHelper.stringWithAesDecrypt(encryptedData, aesKey);

      return decryptedData;
    } catch (error) {
      console.error('混合解密失败:', error);
      throw error;
    }
  }

  // 生成随机密钥
  private generateRandomKey(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let key = '';
    for (let i = 0; i < length; i++) {
      key += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return key;
  }

  // 获取公钥用于密钥交换
  getPublicKey(keyId: string): string | undefined {
    return this.rsaKeyManager.getPublicKey(keyId);
  }
}

// 数字签名验证系统
class DigitalSignatureSystem {
  private rsaKeyManager: RsaKeyManager;
  private trustedKeys: Set<string> = new Set();

  constructor() {
    this.rsaKeyManager = new RsaKeyManager();
  }

  // 添加信任的密钥
  addTrustedKey(keyId: string, publicKeyPem: string): void {
    this.rsaKeyManager.importKeyPair(keyId, publicKeyPem, '');
    this.trustedKeys.add(keyId);
    console.log(`已添加信任密钥，ID: ${keyId}`);
  }

  // 签名数据
  async signData(data: any, signerKeyId: string): Promise<{
    data: any,
    signature: string,
    timestamp: number,
    signer: string
  }> {
    try {
      const dataString = JSON.stringify(data);
      const timestamp = Date.now();
      const signatureData = `${dataString}|${timestamp}|${signerKeyId}`;

      const signature = await this.rsaKeyManager.signWithKey(signerKeyId, signatureData);

      return {
        data: data,
        signature: signature,
        timestamp: timestamp,
        signer: signerKeyId
      };
    } catch (error) {
      console.error('数据签名失败:', error);
      throw error;
    }
  }

  // 验证签名数据
  async verifySignedData(signedData: {
    data: any,
    signature: string,
    timestamp: number,
    signer: string
  }): Promise<{
    isValid: boolean,
    isTrusted: boolean,
    isExpired: boolean,
    details: string
  }> {
    try {
      const { data, signature, timestamp, signer } = signedData;

      // 检查签名者是否可信
      const isTrusted = this.trustedKeys.has(signer);

      // 检查时间戳是否过期（24小时）
      const isExpired = Date.now() - timestamp > 24 * 60 * 60 * 1000;

      // 重构签名数据
      const dataString = JSON.stringify(data);
      const signatureData = `${dataString}|${timestamp}|${signer}`;

      // 验证签名
      const isValid = await this.rsaKeyManager.verifyWithKey(signer, signatureData, signature);

      let details = '';
      if (!isValid) {
        details = '签名验证失败';
      } else if (!isTrusted) {
        details = '签名者不在信任列表中';
      } else if (isExpired) {
        details = '签名已过期';
      } else {
        details = '签名验证成功';
      }

      return {
        isValid: isValid,
        isTrusted: isTrusted,
        isExpired: isExpired,
        details: details
      };
    } catch (error) {
      console.error('签名验证失败:', error);
      return {
        isValid: false,
        isTrusted: false,
        isExpired: true,
        details: `验证异常: ${error.message}`
      };
    }
  }

  // 列出信任的密钥
  listTrustedKeys(): string[] {
    return Array.from(this.trustedKeys);
  }

  // 移除信任的密钥
  removeTrustedKey(keyId: string): boolean {
    const removed = this.trustedKeys.delete(keyId);
    if (removed) {
      console.log(`已移除信任密钥，ID: ${keyId}`);
    }
    return removed;
  }
}

// 使用示例
async function demonstrateAdvancedRsa() {
  // RSA密钥管理器示例
  console.log('=== RSA密钥管理器示例 ===');
  const keyManager = new RsaKeyManager();

  // 生成密钥对
  await keyManager.generateAndStoreKeyPair('user1', 2048);
  await keyManager.generateAndStoreKeyPair('user2', 2048);

  console.log('密钥列表:', keyManager.listKeyIds());

  // 加密解密测试
  const testData = "这是RSA加密测试数据";
  const encrypted = await keyManager.encryptWithKey('user1', testData);
  const decrypted = await keyManager.decryptWithKey('user1', encrypted);

  console.log('原始数据:', testData);
  console.log('解密数据:', decrypted);
  console.log('加密解密正确:', testData === decrypted);

  // 混合加密系统示例
  console.log('\n=== 混合加密系统示例 ===');
  const hybridSystem = new HybridEncryptionSystem();
  await hybridSystem.initialize('hybrid_key');

  const largeData = "这是一个很长的数据，需要使用混合加密来处理。".repeat(100);
  const hybridEncrypted = await hybridSystem.hybridEncrypt(largeData, 'hybrid_key');
  const hybridDecrypted = await hybridSystem.hybridDecrypt(
    hybridEncrypted.encryptedData,
    hybridEncrypted.encryptedKey,
    'hybrid_key'
  );

  console.log('混合加密算法:', hybridEncrypted.algorithm);
  console.log('混合加密解密正确:', largeData === hybridDecrypted);

  // 数字签名系统示例
  console.log('\n=== 数字签名系统示例 ===');
  const signatureSystem = new DigitalSignatureSystem();

  // 生成签名密钥
  await keyManager.generateAndStoreKeyPair('signer1', 2048);
  const signerPublicKey = keyManager.getPublicKey('signer1');
  signatureSystem.addTrustedKey('signer1', signerPublicKey!);

  // 签名数据
  const importantData = { message: '重要通知', amount: 10000, recipient: 'user123' };
  const signedData = await signatureSystem.signData(importantData, 'signer1');

  // 验证签名
  const verificationResult = await signatureSystem.verifySignedData(signedData);

  console.log('签名数据:', signedData);
  console.log('验证结果:', verificationResult);
}

// 执行演示
demonstrateAdvancedRsa();
```

## 📚 相关资源

### 官方文档
- HarmonyOS CryptoArchitectureKit开发指南
- HarmonyOS证书框架开发指南

### 参考资料
- 《现代密码学》- 非对称加密算法
- 《PKI技术与应用》- 公钥基础设施

### 相关文件
- `TKAesHelper.ets` - AES对称加密工具
- `TKBase64Helper.ets` - Base64编码工具
- `TKPasswordGenerator.ets` - 密码生成器
