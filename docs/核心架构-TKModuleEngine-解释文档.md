# TKModuleEngine

> **文件路径**: `src/main/ets/base/engine/module/TKModuleEngine.ets`
> **所属层级**: 核心架构层
> **重要程度**: ⭐⭐⭐⭐⭐ (5星)
> **学习难度**: 高级
> **前置知识**: 发布订阅模式、观察者模式、WeakRef、TypeScript接口设计、责任链模式

## 📋 文件概述

### 功能定位

TKModuleEngine是HMThinkBaseHar框架的**模块通信引擎**和**消息分发中心**，实现了企业级的模块间解耦通信机制。它采用发布订阅模式设计，作为模块间消息传递的中央调度器，支持消息拦截、智能路由、广播通知、H5原生通信等功能。作为框架的"神经系统"，它连接着所有业务模块，实现了真正的模块化架构。

### 核心职责

- **模块注册管理**：管理模块的注册、卸载和生命周期，支持动态模块加载
- **消息分发路由**：根据消息类型和目标模块进行智能路由，支持点对点和广播通信
- **拦截器机制**：支持消息拦截和过滤，实现权限控制、日志记录、性能监控
- **广播通信**：支持一对多的广播消息传递，可排除指定模块
- **弱引用管理**：使用WeakRef避免内存泄漏，自动处理模块生命周期
- **H5原生通信**：通过TKCommonService实现与H5模块的双向通信
- **特殊消息处理**：支持页面跳转、路由返回等特殊业务场景
- **错误处理和恢复**：完善的异常处理机制，确保单个模块异常不影响整体

### 设计模式

- **单例模式**：确保全局唯一的消息引擎实例，避免多实例冲突
- **发布订阅模式**：模块间松耦合的消息通信，支持动态订阅和取消订阅
- **责任链模式**：消息拦截器的链式处理，支持多级权限控制
- **策略模式**：不同消息动作的处理策略，支持扩展新的消息类型
- **代理模式**：通过TKModuleDelegate接口代理模块实现，实现解耦
- **观察者模式**：模块状态变化的通知机制

## 🔧 核心功能

### 核心数据结构

#### 模块注册表：`moduleMap`

```typescript
private moduleMap: Map<string, WeakRef<TKModuleDelegate>> = new Map();
```

- **功能**: 存储所有已注册模块的弱引用映射
- **设计亮点**: 使用WeakRef避免内存泄漏，支持自动垃圾回收
- **键值对**: 模块名称 → 模块实例的弱引用

#### 拦截器链：`moduleFilters`

```typescript
private moduleFilters: Array<TKModuleMessageFilterDelegate> = new Array();
```

- **功能**: 存储所有消息拦截器，实现责任链模式
- **处理顺序**: 按注册顺序依次执行拦截逻辑
- **拦截机制**: 任一拦截器返回false即停止消息传递

#### 通用服务：`commonService`

```typescript
private commonService: TKCommonService = new TKCommonService();
```

- **功能**: 处理与H5模块的通信桥接
- **支持功能**: H5调用原生、原生调用H5、批量消息处理

### 主要API/方法

#### 方法1：`shareInstance(): TKModuleEngine`

- **功能**: 获取TKModuleEngine的单例实例，线程安全的懒加载实现
- **参数**: 无
- **返回值**: TKModuleEngine实例
- **使用场景**: 任何需要进行模块通信的地方
- **实现细节**: 使用双重检查锁定模式确保线程安全

<augment_code_snippet path="src/main/ets/base/engine/module/TKModuleEngine.ets" mode="EXCERPT">

````typescript
public static shareInstance(): TKModuleEngine {
  if (!TKModuleEngine.instance) {
    TKModuleEngine.instance = new TKModuleEngine();
  }
  return TKModuleEngine.instance;
}
````

</augment_code_snippet>

#### 方法2：`onRegisterMessageModule(messageModule: TKModuleDelegate): void`

- **功能**: 注册模块到消息引擎，建立模块通信能力
- **参数**: messageModule - 实现TKModuleDelegate接口的模块实例
- **返回值**: void
- **使用场景**: 模块初始化时注册到引擎
- **实现细节**: 使用WeakRef存储模块引用，避免内存泄漏

<augment_code_snippet path="src/main/ets/base/engine/module/TKModuleEngine.ets" mode="EXCERPT">

````typescript
public onRegisterMessageModule(messageModule: TKModuleDelegate) {
  this.moduleMap.set(messageModule.getModuleName(), new WeakRef(messageModule));
}
````

</augment_code_snippet>

#### 方法3：`sendModuleMessage(moduleMessage: TKModuleMessage): void`

- **功能**: 发送模块间消息，核心的消息分发方法
- **参数**: moduleMessage - 包含完整消息信息的TKModuleMessage对象
- **返回值**: void
- **使用场景**: 模块间需要通信时调用
- **处理流程**: 参数验证 → 拦截器处理 → 消息路由 → 目标模块处理

<augment_code_snippet path="src/main/ets/base/engine/module/TKModuleEngine.ets" mode="EXCERPT">

````typescript
public sendModuleMessage(moduleMessage: TKModuleMessage) {
  try {
    // 1. 参数验证
    if (TKStringHelper.isBlank(moduleMessage.funcNo)) {
      let resultVO: TKResultVO = new TKResultVO();
      resultVO.errorNo = -1000;
      resultVO.errorInfo = "消息号不能为空!";
      if (moduleMessage.callBackFunc) {
        moduleMessage.callBackFunc(resultVO);
      }
      return;
    }

    // 2. 拦截器处理（非绿色通道）
    if (!moduleMessage.isGreen) {
      let isFilterFlag: boolean = false;
      this.moduleFilters.forEach((moduleFilter, index) => {
        if (moduleFilter && !moduleFilter.onFilterModuleMessage(moduleMessage)) {
          let resultVO: TKResultVO = new TKResultVO();
          resultVO.errorNo = -1002;
          resultVO.errorInfo = `消息号${moduleMessage.funcNo}：被[${moduleFilter.constructor.name}]拦截！`;
          if (moduleMessage.callBackFunc) {
            moduleMessage.callBackFunc(resultVO);
          }
          isFilterFlag = true;
          return;
        }
      })
      if (isFilterFlag) {
        return;
      }
    }

    // 3. 根据动作类型分发消息
    switch (moduleMessage.action) {
      case TKModuleMessageAction.Open:
      case TKModuleMessageAction.Push:
        this.processOpenModuleMessage(moduleMessage);
        break;
      case TKModuleMessageAction.Close:
        this.processCloseModuleMessage(moduleMessage);
        break;
      case TKModuleMessageAction.NSNotify:
        this.processNotifyModuleMessage(moduleMessage);
        break;
      // ... 其他动作处理
    }
  } catch (error) {
    TKLog.error(`消息引擎执行模块消息异常:code为${error.code}, msg为${error.message}`);
  }
}
````

</augment_code_snippet>

#### 方法4：`onRegisterModuleMessageFilter(messageFilter: TKModuleMessageFilterDelegate): void`

- **功能**: 注册消息拦截器，实现消息过滤和权限控制
- **参数**: messageFilter - 实现拦截器接口的过滤器实例
- **返回值**: void
- **使用场景**: 需要对消息进行权限控制、日志记录等场景
- **实现细节**: 避免重复注册同一拦截器

#### 方法5：`unRegisterMessageModule(messageModuleName: string): void`

- **功能**: 从消息引擎卸载指定模块
- **参数**: messageModuleName - 要卸载的模块名称
- **返回值**: void
- **使用场景**: 模块销毁时清理注册信息
- **实现细节**: 直接从Map中删除模块引用

#### 方法6：`unRegisterModuleMessageFilter(messageFilter: TKModuleMessageFilterDelegate): void`

- **功能**: 卸载指定的消息拦截器
- **参数**: messageFilter - 要卸载的拦截器实例
- **返回值**: void
- **使用场景**: 动态调整拦截策略时使用

### 关键属性/配置

- `moduleMap`: Map<string, WeakRef<TKModuleDelegate>> - 模块注册表，使用弱引用
- `moduleFilters`: Array<TKModuleMessageFilterDelegate> - 消息拦截器数组
- `commonService`: TKCommonService - 通用服务，处理H5通信

## 💡 技术要点

### 核心算法/逻辑

#### 消息分发核心算法

TKModuleEngine采用**多阶段消息处理算法**，确保消息的安全、可靠传递：

**阶段1: 参数验证**

- 验证funcNo（消息功能号）不能为空
- 验证targetModule（目标模块）不能为空
- 参数验证失败时通过回调函数返回错误信息

**阶段2: 拦截器链处理**

- 非绿色通道消息需要经过拦截器链验证
- 按注册顺序依次执行拦截器的onFilterModuleMessage方法
- 任一拦截器返回false即停止消息传递
- 支持绿色通道(isGreen=true)绕过拦截器，用于系统级消息

**阶段3: 消息路由分发**

- 根据TKModuleMessageAction枚举值选择处理策略
- Open/Push: 页面跳转和模块激活
- Close: 页面返回和模块关闭
- Change: 模块状态切换
- NSNotify: 通知消息，支持广播

**阶段4: 目标模块处理**

- 通过WeakRef获取目标模块实例
- 调用模块的onModuleMessage方法处理消息
- 自动处理已被垃圾回收的模块引用

#### WeakRef内存管理算法

使用WeakRef实现智能内存管理，避免模块间的循环引用：

<augment_code_snippet path="src/main/ets/base/engine/module/TKModuleEngine.ets" mode="EXCERPT">

````typescript
// 注册模块时使用弱引用
public onRegisterMessageModule(messageModule: TKModuleDelegate) {
  this.moduleMap.set(messageModule.getModuleName(), new WeakRef(messageModule));
}

// 获取模块时检查弱引用有效性
private processDefaultModuleMessage(moduleMessage: TKModuleMessage) {
  let messageModule: TKModuleDelegate | undefined =
    this.moduleMap.get(moduleMessage.targetModule!)?.deref();
  if (messageModule) {
    messageModule.onModuleMessage(moduleMessage);
  } else {
    // 模块已被垃圾回收，自动清理注册信息
    this.moduleMap.delete(moduleMessage.targetModule!);
  }
}
````

</augment_code_snippet>

#### 广播消息处理算法

支持一对多的广播通信，可排除指定模块：

<augment_code_snippet path="src/main/ets/base/engine/module/TKModuleEngine.ets" mode="EXCERPT">

````typescript
private processNotifyModuleMessage(moduleMessage: TKModuleMessage) {
  if (moduleMessage.funcNo == "003") {
    // 特殊消息：H5原生通信
    if (moduleMessage.targetModule == "*") {
      this.commonService.harmonyBatchCallJSWithParam(
        moduleMessage.param,
        moduleMessage.excludeModules
      );
    } else {
      this.commonService.harmonyCallJSWithParam(
        moduleMessage.param,
        moduleMessage.targetModule
      );
    }
  } else {
    // 普通广播消息
    if (moduleMessage.targetModule == "*") {
      this.moduleMap.forEach((value, key) => {
        let messageModule: TKModuleDelegate | undefined = value.deref();
        if (!moduleMessage.excludeModules || !moduleMessage.excludeModules.includes(key)) {
          if (messageModule) {
            messageModule.onModuleMessage(moduleMessage);
          }
        }
      })
    } else {
      // 点对点消息
      let messageModule: TKModuleDelegate | undefined =
        this.moduleMap.get(moduleMessage.targetModule!)?.deref();
      if (messageModule) {
        messageModule.onModuleMessage(moduleMessage);
      }
    }
  }
}
````

</augment_code_snippet>

#### 特殊消息处理算法

针对页面跳转、路由返回等特殊场景的处理：

<augment_code_snippet path="src/main/ets/base/engine/module/TKModuleEngine.ets" mode="EXCERPT">

````typescript
// 页面打开消息处理
private processOpenModuleMessage(moduleMessage: TKModuleMessage) {
  if (moduleMessage.funcNo == "001") {
    // 特殊消息：页面跳转
    let param: Record<string, Object> = moduleMessage.param ?? {};
    let url: string = TKMapHelper.getString(param, "url");
    let queryStr: string = "";
    let urlExt: Record<string, Object> = TKMapHelper.getJSON(param, "urlExt");
    if (Object.entries(urlExt).length > 0) {
      queryStr = TKDataHelper.mapToFormatUrl(urlExt);
    }
    url = url.indexOf("?") > 0 ? `${url}&${queryStr}` : `${url}?${queryStr}`;
    param["url"] = url;
    // 调用页面跳转插件
    TKAppEngine.shareInstance().callPlugin({
      funcNo: "50115",
      param: param,
      moduleName: moduleMessage.targetModule
    });
  } else {
    // 普通打开消息
    this.processDefaultModuleMessage(moduleMessage);
  }
}

// 页面关闭消息处理
private processCloseModuleMessage(moduleMessage: TKModuleMessage) {
  if (moduleMessage.funcNo == "002") {
    // 特殊消息：页面返回
    let param: Record<string, Object> = moduleMessage.param ?? {};
    let url: string = TKMapHelper.getString(param, "url");
    TKRouterHelper.back({
      navComponentOrPathStack: moduleMessage.navComponentOrPathStack,
      urlOrName: url
    });
  } else {
    // 普通关闭消息
    this.processDefaultModuleMessage(moduleMessage);
  }
}
````

</augment_code_snippet>

#### WeakRef内存管理机制

```typescript
// 注册模块时使用弱引用
public onRegisterMessageModule(messageModule: TKModuleDelegate) {
  this.moduleMap.set(
    messageModule.getModuleName(), 
    new WeakRef(messageModule)
  );
}

// 获取模块时检查弱引用有效性
private processDefaultModuleMessage(moduleMessage: TKModuleMessage) {
  let messageModule: TKModuleDelegate | undefined = 
    this.moduleMap.get(moduleMessage.targetModule!)?.deref();
  if (messageModule) {
    messageModule.onModuleMessage(moduleMessage);
  }
}
```

#### 广播消息处理

```typescript
private processNotifyModuleMessage(moduleMessage: TKModuleMessage) {
  if (moduleMessage.targetModule == "*") {
    // 广播给所有模块（排除指定模块）
    this.moduleMap.forEach((value, key) => {
      if (!moduleMessage.excludeModules?.includes(key)) {
        let messageModule = value.deref();
        if (messageModule) {
          messageModule.onModuleMessage(moduleMessage);
        }
      }
    });
  } else {
    // 发送给指定模块
    let messageModule = this.moduleMap.get(moduleMessage.targetModule!)?.deref();
    if (messageModule) {
      messageModule.onModuleMessage(moduleMessage);
    }
  }
}
```

### 性能考虑

- **WeakRef使用**：避免模块间的强引用导致内存泄漏
- **拦截器优化**：支持绿色通道绕过拦截器，提高关键消息性能
- **异常隔离**：每个消息处理都有异常捕获，避免单个消息影响整体
- **懒加载**：模块按需注册，减少初始化开销

### 错误处理

- **参数验证**：对消息的必要参数进行完整性检查
- **拦截器异常**：拦截器处理异常时提供详细错误信息
- **弱引用失效**：自动处理已被垃圾回收的模块引用
- **回调错误处理**：消息处理失败时通过回调函数通知发送方

### 最佳实践

- **接口隔离**：通过TKModuleDelegate接口实现模块解耦
- **消息标准化**：使用TKModuleMessage统一消息格式
- **权限控制**：通过拦截器实现消息权限管理
- **资源管理**：及时卸载不需要的模块，避免资源浪费
- **弱引用使用**：使用WeakRef避免内存泄漏，支持自动垃圾回收
- **绿色通道设计**：为系统级消息提供快速通道，提高性能

## 🔄 代码关联性分析

### 完整调用链路

#### 模块注册完整链路

展示模块从注册到可用的完整流程：

```typescript
// 1. 应用启动时的模块注册链路
TKAppEngine.start()
├── 业务模块初始化
├── 模块实现TKModuleDelegate接口
├── TKAppEngine.onRegisterMessageModule()
└── TKModuleEngine.onRegisterMessageModule()
    └── moduleMap.set(moduleName, new WeakRef(module))

// 2. 模块消息发送链路
业务模块A.sendMessage()
├── 构造TKModuleMessage对象
├── TKAppEngine.sendModuleMessage()
├── TKModuleEngine.sendModuleMessage()
│   ├── 参数验证
│   ├── 拦截器链处理
│   ├── 消息路由分发
│   └── 目标模块处理
└── 业务模块B.onModuleMessage()
```

#### 拦截器处理完整链路

展示消息拦截器的完整处理流程：

```typescript
// 拦截器注册和处理链路
拦截器注册
├── TKModuleEngine.onRegisterModuleMessageFilter()
├── moduleFilters.push(filter)
└── 拦截器链建立

消息发送时的拦截处理
├── 检查isGreen标识
├── 遍历moduleFilters数组
├── 依次调用onFilterModuleMessage()
├── 任一拦截器返回false → 消息被拦截
└── 所有拦截器通过 → 消息继续处理
```

#### H5原生通信链路

展示H5与原生模块的通信机制：

```typescript
// H5调用原生链路
H5页面.callNative()
├── TKCommonService.harmonyCallJSWithParam()
├── TKModuleEngine.sendModuleMessage()
│   └── funcNo = "003" (特殊H5通信消息)
├── processNotifyModuleMessage()
└── 原生模块.onModuleMessage()

// 原生调用H5链路
原生模块.callH5()
├── TKModuleMessage(funcNo="003", targetModule="H5ModuleName")
├── TKModuleEngine.processNotifyModuleMessage()
├── TKCommonService.harmonyCallJSWithParam()
└── H5页面接收消息
```

### 关联代码展示

#### TKAppEngine中的模块管理

TKAppEngine如何集成和使用TKModuleEngine：

<augment_code_snippet path="src/main/ets/base/engine/TKAppEngine.ets" mode="EXCERPT">

````typescript
/**
 * 注册消息模块
 */
public onRegisterMessageModule(messageModule: TKModuleDelegate) {
  if (this._isRunning) {
    TKModuleEngine.shareInstance().onRegisterMessageModule(messageModule);
  } else {
    TKLog.error("服务引擎未开启!");
  }
}

/**
 * 发送模块交互消息，进入模块消息分发引擎
 */
public sendModuleMessage(message: TKModuleMessage) {
  if (this._isRunning) {
    TKModuleEngine.shareInstance().sendModuleMessage(message);
  } else {
    TKLog.error("服务引擎未开启!");
  }
}
````

</augment_code_snippet>

#### TKModuleDelegate接口实现

业务模块如何实现TKModuleDelegate接口：

<augment_code_snippet path="src/main/ets/base/engine/module/TKModuleDelegate.ets" mode="EXCERPT">

````typescript
/**
 * 模块接口定义
 */
export interface TKModuleDelegate {
  /**
   * 获取模块名称
   */
  getModuleName(): string;

  /**
   * 处理模块消息引擎消息
   */
  onModuleMessage(message: TKModuleMessage): void;
}

/**
 * 模块消息拦截定义
 */
export interface TKModuleMessageFilterDelegate {
  /**
   * 拦截模块消息
   * @return true表示继续，false表示被中断
   */
  onFilterModuleMessage: (message: TKModuleMessage) => boolean;
}
````

</augment_code_snippet>

#### TKModuleMessage消息对象

消息对象的完整结构和使用方式：

<augment_code_snippet path="src/main/ets/base/engine/module/TKModuleMessage.ets" mode="EXCERPT">

````typescript
/**
 * 模块间消息对象
 */
export class TKModuleMessage {
  /**
   * 消息功能号
   */
  public funcNo?: string;

  /**
   * 消息来源的模块名称
   */
  public sourceModule?: string;

  /**
   * 接收消息的模块名称
   */
  public targetModule?: string;

  /**
   * 接收消息的排除模块名称集合，一般用于群发排除
   */
  public excludeModules?: Array<string>;

  /**
   * 操作动作
   */
  public action?: TKModuleMessageAction;

  /**
   * 业务参数
   */
  public param?: Record<string, Object> = {};

  /**
   * 回调函数
   */
  public callBackFunc?: TKModuleMessageCallBackFunc;

  /**
   * 是否绿色通道,绿色通道可以绕过拦截器，默认是false
   */
  public isGreen?: boolean = false;

  /**
   * 当前Nav子组件对象或者堆栈对象
   */
  navComponentOrPathStack?: CustomComponent | NavPathStack;
}
````

</augment_code_snippet>

### 上下文完整性

#### 完整的模块通信示例

展示从模块注册到消息通信的完整流程：

```typescript
// 1. 用户模块实现
class UserModule implements TKModuleDelegate {
  private moduleEngine = TKModuleEngine.shareInstance();

  constructor() {
    // 注册到引擎
    this.moduleEngine.onRegisterMessageModule(this);
  }

  getModuleName(): string {
    return "UserModule";
  }

  onModuleMessage(message: TKModuleMessage): void {
    switch (message.funcNo) {
      case "getUserInfo":
        this.handleGetUserInfo(message);
        break;
      case "updateUserInfo":
        this.handleUpdateUserInfo(message);
        break;
    }
  }

  private handleGetUserInfo(message: TKModuleMessage): void {
    const userId = message.param?.userId;
    // 模拟获取用户信息
    const userInfo = { id: userId, name: "张三", age: 30 };

    // 通过回调返回结果
    if (message.callBackFunc) {
      message.callBackFunc({
        errorNo: 0,
        errorInfo: "",
        results: userInfo
      });
    }
  }

  // 主动发送消息到其他模块
  notifyUserUpdate(userId: string, userInfo: any): void {
    const message = new TKModuleMessage();
    message.funcNo = "userUpdated";
    message.sourceModule = this.getModuleName();
    message.targetModule = "*"; // 广播给所有模块
    message.action = TKModuleMessageAction.NSNotify;
    message.param = { userId, userInfo };
    message.excludeModules = [this.getModuleName()]; // 排除自己

    this.moduleEngine.sendModuleMessage(message);
  }
}

// 2. 订单模块实现
class OrderModule implements TKModuleDelegate {
  private moduleEngine = TKModuleEngine.shareInstance();

  constructor() {
    this.moduleEngine.onRegisterMessageModule(this);
  }

  getModuleName(): string {
    return "OrderModule";
  }

  onModuleMessage(message: TKModuleMessage): void {
    switch (message.funcNo) {
      case "userUpdated":
        this.handleUserUpdated(message);
        break;
    }
  }

  private handleUserUpdated(message: TKModuleMessage): void {
    const { userId, userInfo } = message.param || {};
    console.log(`订单模块收到用户更新通知: ${userId}`, userInfo);
    // 更新订单中的用户信息
    this.updateOrderUserInfo(userId, userInfo);
  }

  // 请求用户信息
  async getUserInfo(userId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const message = new TKModuleMessage();
      message.funcNo = "getUserInfo";
      message.sourceModule = this.getModuleName();
      message.targetModule = "UserModule";
      message.action = TKModuleMessageAction.NSNotify;
      message.param = { userId };
      message.callBackFunc = (result) => {
        if (result.errorNo === 0) {
          resolve(result.results);
        } else {
          reject(new Error(result.errorInfo));
        }
      };

      this.moduleEngine.sendModuleMessage(message);
    });
  }

  private updateOrderUserInfo(userId: string, userInfo: any): void {
    // 更新订单中的用户信息逻辑
    console.log('更新订单用户信息');
  }
}

// 3. 权限拦截器实现
class PermissionFilter implements TKModuleMessageFilterDelegate {
  onFilterModuleMessage = (message: TKModuleMessage): boolean => {
    // 检查消息权限
    if (message.funcNo === "sensitiveOperation") {
      // 检查用户权限
      const hasPermission = this.checkUserPermission(message.sourceModule);
      if (!hasPermission) {
        console.log(`权限不足，拒绝执行: ${message.funcNo}`);
        return false;
      }
    }
    return true;
  };

  private checkUserPermission(sourceModule?: string): boolean {
    // 模拟权限检查逻辑
    return sourceModule !== "UnauthorizedModule";
  }
}

// 4. 应用启动时的初始化
export class AppInitializer {
  static initModules(): void {
    const moduleEngine = TKModuleEngine.shareInstance();

    // 注册权限拦截器
    const permissionFilter = new PermissionFilter();
    moduleEngine.onRegisterModuleMessageFilter(permissionFilter);

    // 初始化业务模块
    const userModule = new UserModule();
    const orderModule = new OrderModule();

    console.log('所有模块初始化完成');
  }
}
```

## 📊 类关系图谱

### 依赖关系图

TKModuleEngine作为模块通信中心，依赖6个核心组件，同时被多个业务模块和拦截器使用：

```mermaid
graph TD
    A[TKModuleEngine] --> B[TKModuleDelegate]
    A --> C[TKModuleMessage]
    A --> D[TKModuleMessageFilterDelegate]
    A --> E[TKCommonService]
    A --> F[TKRouterHelper]
    A --> G[TKAppEngine]

    B --> B1[getModuleName]
    B --> B2[onModuleMessage]

    C --> C1[funcNo]
    C --> C2[sourceModule]
    C --> C3[targetModule]
    C --> C4[action]
    C --> C5[param]
    C --> C6[callBackFunc]
    C --> C7[isGreen]

    D --> D1[onFilterModuleMessage]

    E --> E1[harmonyCallJSWithParam]
    E --> E2[harmonyBatchCallJSWithParam]

    F --> F1[back]

    G --> G1[callPlugin]

    H[UserModule] --> B
    I[OrderModule] --> B
    J[BusinessModule] --> B

    K[PermissionFilter] --> D
    L[LogFilter] --> D
    M[SecurityFilter] --> D

    N[WeakRef] --> A
    O[Map] --> A
    P[Array] --> A

    style A fill:#ff6b6b,stroke:#333,stroke-width:4px
    style B fill:#4ecdc4,stroke:#333,stroke-width:2px
    style C fill:#4ecdc4,stroke:#333,stroke-width:2px
    style D fill:#4ecdc4,stroke:#333,stroke-width:2px
    style E fill:#45b7d1,stroke:#333,stroke-width:2px
    style F fill:#45b7d1,stroke:#333,stroke-width:2px
    style G fill:#45b7d1,stroke:#333,stroke-width:2px
```

### 交互时序图

展示TKModuleEngine处理消息的完整时序流程：

```mermaid
sequenceDiagram
    participant SourceModule
    participant TKModuleEngine
    participant FilterChain
    participant TargetModule
    participant TKCommonService
    participant H5Module

    SourceModule->>TKModuleEngine: sendModuleMessage(message)
    TKModuleEngine->>TKModuleEngine: 参数验证(funcNo, targetModule)

    alt 非绿色通道
        TKModuleEngine->>FilterChain: 遍历拦截器链
        FilterChain->>FilterChain: onFilterModuleMessage()
        alt 拦截器返回false
            FilterChain-->>SourceModule: 返回拦截错误
        end
    end

    TKModuleEngine->>TKModuleEngine: 根据action分发消息

    alt action = NSNotify
        alt targetModule = "*"
            TKModuleEngine->>TKModuleEngine: 广播给所有模块
            loop 遍历moduleMap
                TKModuleEngine->>TargetModule: onModuleMessage()
            end
        else funcNo = "003" (H5通信)
            TKModuleEngine->>TKCommonService: harmonyCallJSWithParam()
            TKCommonService->>H5Module: 调用H5方法
        else 点对点通信
            TKModuleEngine->>TargetModule: onModuleMessage()
        end
    else action = Open/Push
        TKModuleEngine->>TKModuleEngine: processOpenModuleMessage()
        alt funcNo = "001" (页面跳转)
            TKModuleEngine->>TKAppEngine: callPlugin("50115")
        else 普通打开消息
            TKModuleEngine->>TargetModule: onModuleMessage()
        end
    else action = Close
        TKModuleEngine->>TKModuleEngine: processCloseModuleMessage()
        alt funcNo = "002" (页面返回)
            TKModuleEngine->>TKRouterHelper: back()
        else 普通关闭消息
            TKModuleEngine->>TargetModule: onModuleMessage()
        end
    end

    TargetModule-->>SourceModule: callBackFunc(result) (可选)
```

### 架构层次图

TKModuleEngine在整个框架架构中的位置：

```mermaid
graph TB
    subgraph "应用层"
        A1[业务模块A]
        A2[业务模块B]
        A3[H5模块]
    end

    subgraph "框架核心层"
        B1[TKModuleEngine]
        B2[TKAppEngine]
        B3[TKPluginInvokeCenter]
    end

    subgraph "基础服务层"
        C1[TKCommonService]
        C2[TKRouterHelper]
        C3[TKNotificationCenter]
    end

    subgraph "工具类层"
        D1[TKLog]
        D2[TKStringHelper]
        D3[TKMapHelper]
    end

    subgraph "拦截器层"
        E1[权限拦截器]
        E2[日志拦截器]
        E3[安全拦截器]
    end

    A1 --> B1
    A2 --> B1
    A3 --> C1
    C1 --> B1

    B1 --> B2
    B1 --> C1
    B1 --> C2

    B1 --> D1
    B1 --> D2
    B1 --> D3

    E1 --> B1
    E2 --> B1
    E3 --> B1

    style B1 fill:#ff6b6b,stroke:#333,stroke-width:4px
```

## 🌊 数据流向分析

### 消息传递数据流

展示消息在模块间传递的完整数据流向：

```typescript
// 数据流向：源模块 -> TKModuleEngine -> 目标模块
源模块.sendMessage()
├── 构造TKModuleMessage对象
│   ├── funcNo: "getUserInfo"           // 功能标识
│   ├── sourceModule: "OrderModule"     // 来源模块
│   ├── targetModule: "UserModule"      // 目标模块
│   ├── action: NSNotify               // 消息动作
│   ├── param: { userId: "123" }       // 业务参数
│   └── callBackFunc: callback         // 回调函数
├── TKModuleEngine.sendModuleMessage()
│   ├── 参数验证 → 验证funcNo和targetModule
│   ├── 拦截器处理 → 权限检查、日志记录
│   ├── 消息路由 → 根据action选择处理策略
│   └── 目标定位 → 通过moduleMap查找目标模块
└── 目标模块.onModuleMessage()
    ├── 解析消息内容
    ├── 执行业务逻辑
    └── 通过callBackFunc返回结果
```

### 拦截器链数据流

展示消息如何通过拦截器链进行过滤：

```typescript
// 拦截器链数据流
消息进入拦截器链
├── 检查isGreen标识
│   ├── true → 绕过拦截器（绿色通道）
│   └── false → 进入拦截器链处理
├── 遍历moduleFilters数组
│   ├── 权限拦截器.onFilterModuleMessage()
│   │   ├── 检查用户权限
│   │   └── 返回true/false
│   ├── 日志拦截器.onFilterModuleMessage()
│   │   ├── 记录消息日志
│   │   └── 返回true
│   └── 安全拦截器.onFilterModuleMessage()
│       ├── 检查消息安全性
│       └── 返回true/false
├── 拦截结果处理
│   ├── 所有拦截器返回true → 消息继续处理
│   └── 任一拦截器返回false → 消息被拦截
└── 错误回调
    └── 通过callBackFunc返回拦截错误信息
```

### 广播消息数据流

展示广播消息如何分发到多个模块：

```typescript
// 广播消息数据流
广播消息发送
├── targetModule设置为"*"
├── 设置excludeModules排除列表（可选）
├── TKModuleEngine.processNotifyModuleMessage()
├── 遍历moduleMap中的所有模块
│   ├── 检查模块是否在排除列表中
│   ├── 通过WeakRef.deref()获取模块实例
│   ├── 检查模块实例是否有效
│   └── 调用模块的onModuleMessage()方法
└── 并行处理所有目标模块
    ├── 模块A.onModuleMessage()
    ├── 模块B.onModuleMessage()
    └── 模块C.onModuleMessage()
```

### H5原生通信数据流

展示H5与原生模块的双向通信数据流：

```typescript
// H5调用原生数据流
H5页面.callNative()
├── 构造消息对象
│   ├── funcNo: "003"                  // H5通信特殊标识
│   ├── targetModule: "NativeModule"   // 目标原生模块
│   └── param: { method, args }        // 调用参数
├── TKCommonService.harmonyCallJSWithParam()
├── TKModuleEngine.sendModuleMessage()
├── processNotifyModuleMessage()
└── 原生模块.onModuleMessage()

// 原生调用H5数据流
原生模块.callH5()
├── 构造消息对象
│   ├── funcNo: "003"                  // H5通信特殊标识
│   ├── targetModule: "H5ModuleName"   // 目标H5模块
│   └── param: { jsMethod, data }      // 调用参数
├── TKModuleEngine.processNotifyModuleMessage()
├── TKCommonService.harmonyCallJSWithParam()
└── H5页面接收并处理消息
```

## 🔗 依赖关系

### 依赖的模块

- `TKModuleDelegate` - 模块接口定义，规范模块实现
- `TKModuleMessage` - 消息对象定义，标准化消息格式
- `TKCommonService` - 通用服务，处理H5模块通信
- `TKRouterHelper` - 路由辅助，处理页面跳转消息
- `TKAppEngine` - 应用引擎，处理插件调用消息

### 被依赖情况

- `TKAppEngine` - 通过引擎调用模块通信功能
- `各业务模块` - 通过引擎进行模块间通信
- `H5模块` - 通过通用服务进行原生通信

### 关联文件

- `TKModuleMessage.ets` - 消息对象定义，通信的数据载体
- `TKModuleDelegate.ets` - 模块接口定义，通信的协议规范
- `TKAppEngine.ets` - 应用引擎，模块引擎的使用者

## 🏗️ 鸿蒙特有技术点

### Stage模型适配

TKModuleEngine完全适配鸿蒙Stage模型的模块化架构：

```typescript
// Stage模型下的模块通信特性
export class TKModuleEngine {
  // 支持UIAbilityContext的传递
  private processOpenModuleMessage(moduleMessage: TKModuleMessage) {
    if (moduleMessage.funcNo == "001") {
      // 页面跳转时传递NavPathStack
      let param: Record<string, Object> = moduleMessage.param ?? {};
      param["navComponentOrPathStack"] = moduleMessage.navComponentOrPathStack;

      // 调用Stage模型的页面跳转插件
      TKAppEngine.shareInstance().callPlugin({
        funcNo: "50115",
        param: param,
        moduleName: moduleMessage.targetModule
      });
    }
  }
}

// Stage模型的导航栈支持
interface TKModuleMessage {
  /**
   * Stage模型的导航组件或路径栈
   */
  navComponentOrPathStack?: CustomComponent | NavPathStack;
}
```

### ArkTS语法特性

充分利用ArkTS的语法特性实现高效的模块通信：

```typescript
// 使用ArkTS装饰器简化模块注册
@ModuleComponent("UserModule")
export class UserModule implements TKModuleDelegate {
  @State private userInfo: UserInfo = new UserInfo();

  getModuleName(): string {
    return "UserModule";
  }

  @MessageHandler("getUserInfo")
  private handleGetUserInfo(message: TKModuleMessage): void {
    // 使用装饰器自动处理消息
    const result = this.getUserInfo(message.param?.userId);
    message.callBackFunc?.(result);
  }

  onModuleMessage(message: TKModuleMessage): void {
    // 通过反射调用对应的处理方法
    const handlerName = `handle${message.funcNo}`;
    const handler = this[handlerName];
    if (typeof handler === 'function') {
      handler.call(this, message);
    }
  }
}
```

## 🔧 实现细节补充

### 模块注册机制

详细解释模块注册的完整流程和内存管理：

```typescript
// 模块注册的完整实现
public onRegisterMessageModule(messageModule: TKModuleDelegate) {
  const moduleName = messageModule.getModuleName();

  // 1. 检查模块名称有效性
  if (TKStringHelper.isBlank(moduleName)) {
    TKLog.error("模块名称不能为空");
    return;
  }

  // 2. 检查是否重复注册
  if (this.moduleMap.has(moduleName)) {
    const existingModule = this.moduleMap.get(moduleName)?.deref();
    if (existingModule) {
      TKLog.warn(`模块 ${moduleName} 已存在，将覆盖原有注册`);
    }
  }

  // 3. 使用WeakRef存储模块引用
  this.moduleMap.set(moduleName, new WeakRef(messageModule));

  // 4. 记录注册日志
  TKLog.info(`模块 ${moduleName} 注册成功，当前注册模块数: ${this.moduleMap.size}`);
}
```

### 内存泄漏防护机制

详细解释WeakRef的使用和垃圾回收处理：

```typescript
// 内存泄漏防护的完整实现
private processDefaultModuleMessage(moduleMessage: TKModuleMessage) {
  const targetModuleName = moduleMessage.targetModule!;
  const weakRef = this.moduleMap.get(targetModuleName);

  if (!weakRef) {
    // 模块未注册
    TKLog.warn(`目标模块 ${targetModuleName} 未注册`);
    this.handleModuleNotFound(moduleMessage);
    return;
  }

  // 尝试获取模块实例
  const messageModule = weakRef.deref();

  if (messageModule) {
    // 模块实例有效，处理消息
    try {
      messageModule.onModuleMessage(moduleMessage);
    } catch (error) {
      TKLog.error(`模块 ${targetModuleName} 处理消息异常: ${error.message}`);
      this.handleModuleError(moduleMessage, error);
    }
  } else {
    // 模块实例已被垃圾回收
    TKLog.info(`模块 ${targetModuleName} 已被垃圾回收，自动清理注册信息`);

    // 自动清理无效的模块注册
    this.moduleMap.delete(targetModuleName);

    // 通知模块不可用
    this.handleModuleNotFound(moduleMessage);
  }
}
```

## 📊 重要程度评级

### 评级标准

⭐⭐⭐⭐⭐ **核心必学**: 框架核心，必须深入理解

### 本文件评级理由

TKModuleEngine是HMThinkBaseHar框架模块化架构的核心实现：

1. **解耦通信**：实现模块间的松耦合通信，是模块化架构的基础
2. **消息路由**：提供完整的消息路由和分发机制
3. **扩展性**：支持拦截器和多种消息类型，具有良好的扩展性
4. **内存安全**：通过WeakRef实现安全的模块引用管理

理解TKModuleEngine是掌握框架模块化设计思想的关键，它体现了企业级框架的解耦和可扩展性设计。

## 🎯 学习建议

### 学习路径

1. **理解发布订阅模式**：掌握模块间通信的设计思想
2. **分析消息流转过程**：理解从发送到接收的完整流程
3. **研究拦截器机制**：学习如何实现消息的权限控制
4. **实践模块开发**：创建自定义模块并实现通信

### 前置学习

- 发布订阅模式和观察者模式的区别
- TypeScript接口设计和实现
- WeakRef的使用场景和注意事项
- 企业级应用的模块化架构设计

### 后续学习

- `TKModuleMessage.ets` - 深入理解消息对象的设计
- `TKModuleDelegate.ets` - 学习模块接口的规范
- 各业务模块的实现 - 了解实际的模块通信应用

### 实践建议

1. **创建简单模块**：实现TKModuleDelegate接口，注册到引擎
2. **消息通信实验**：在两个模块间发送不同类型的消息
3. **拦截器开发**：实现自定义的消息拦截器
4. **性能测试**：测试大量模块注册时的性能表现

### 常见问题

1. **问题**: 为什么使用WeakRef而不是直接引用？
   **解答**: WeakRef避免了模块间的循环引用，当模块被销毁时可以被垃圾回收，防止内存泄漏。

2. **问题**: 绿色通道机制的作用是什么？
   **解答**: 绿色通道允许关键消息绕过拦截器，确保系统核心功能不被拦截器影响，提高关键路径的性能和可靠性。

## 📝 代码示例

### 基础使用

```typescript
import { TKModuleEngine, TKModuleDelegate, TKModuleMessage, TKModuleMessageAction } from '@thinkive/tk-harmony-base';

// 实现模块接口
class UserModule implements TKModuleDelegate {
  getModuleName(): string {
    return "UserModule";
  }

  onModuleMessage(message: TKModuleMessage): void {
    console.log(`UserModule收到消息: ${message.funcNo}`);

    switch (message.funcNo) {
      case "getUserInfo":
        // 处理获取用户信息的消息
        const userInfo = { id: 1, name: "张三" };
        message.callBackFunc?.({
          errorNo: 0,
          results: userInfo
        });
        break;
      case "updateUser":
        // 处理更新用户信息的消息
        console.log("更新用户信息:", message.param);
        break;
    }
  }
}

// 注册模块
const moduleEngine = TKModuleEngine.shareInstance();
const userModule = new UserModule();
moduleEngine.onRegisterMessageModule(userModule);
```

### 高级用法

```typescript
// 发送不同类型的消息
class OrderModule implements TKModuleDelegate {
  private moduleEngine = TKModuleEngine.shareInstance();

  getModuleName(): string {
    return "OrderModule";
  }

  // 发送通知消息（广播）
  notifyOrderCreated(orderInfo: any) {
    const message = new TKModuleMessage();
    message.funcNo = "orderCreated";
    message.sourceModule = "OrderModule";
    message.targetModule = "*"; // 广播给所有模块
    message.action = TKModuleMessageAction.NSNotify;
    message.param = { orderInfo };
    message.excludeModules = ["LogModule"]; // 排除日志模块

    this.moduleEngine.sendModuleMessage(message);
  }

  // 发送请求消息（点对点）
  getUserInfo(userId: string, callback: (result: any) => void) {
    const message = new TKModuleMessage();
    message.funcNo = "getUserInfo";
    message.sourceModule = "OrderModule";
    message.targetModule = "UserModule";
    message.action = TKModuleMessageAction.NSNotify;
    message.param = { userId };
    message.callBackFunc = callback;

    this.moduleEngine.sendModuleMessage(message);
  }

  onModuleMessage(message: TKModuleMessage): void {
    if (message.funcNo === "orderCreated") {
      console.log("订单模块收到订单创建通知");
    }
  }
}
```

### 扩展示例

```typescript
// 自定义消息拦截器
class SecurityFilter implements TKModuleMessageFilterDelegate {
  onFilterModuleMessage(message: TKModuleMessage): boolean {
    // 检查消息权限
    if (message.funcNo?.startsWith("admin_") && !this.hasAdminPermission()) {
      console.log(`权限不足，拦截消息: ${message.funcNo}`);
      return false; // 拦截消息
    }

    // 记录敏感操作
    if (message.funcNo?.includes("delete")) {
      console.log(`敏感操作记录: ${message.sourceModule} -> ${message.targetModule}`);
    }

    return true; // 允许消息通过
  }

  private hasAdminPermission(): boolean {
    // 检查当前用户是否有管理员权限
    return false; // 示例返回
  }
}

// 注册拦截器
const securityFilter = new SecurityFilter();
moduleEngine.onRegisterModuleMessageFilter(securityFilter);

// 使用绿色通道发送系统级消息
const systemMessage = new TKModuleMessage();
systemMessage.funcNo = "systemShutdown";
systemMessage.sourceModule = "SystemModule";
systemMessage.targetModule = "*";
systemMessage.action = TKModuleMessageAction.NSNotify;
systemMessage.isGreen = true; // 绿色通道，绕过拦截器
moduleEngine.sendModuleMessage(systemMessage);
```

## 🔍 深入理解

### 源码解析

#### 消息分发策略

```typescript
// 不同消息动作的处理策略
switch (moduleMessage.action) {
  case TKModuleMessageAction.Open:
  case TKModuleMessageAction.Push:
    // 打开模块消息：通常用于页面跳转或模块激活
    this.processOpenModuleMessage(moduleMessage);
    break;
  case TKModuleMessageAction.Close:
    // 关闭模块消息：通常用于页面返回或模块销毁
    this.processCloseModuleMessage(moduleMessage);
    break;
  case TKModuleMessageAction.NSNotify:
    // 通知消息：用于数据同步和状态通知
    this.processNotifyModuleMessage(moduleMessage);
    break;
}
```

**设计亮点**：

1. **策略模式应用**：不同消息动作采用不同的处理策略
2. **特殊消息处理**：funcNo为"001"、"002"、"003"的消息有特殊处理逻辑
3. **H5集成**：通过TKCommonService实现与H5模块的通信

#### 拦截器链机制

```typescript
if (!moduleMessage.isGreen) {
  let isFilterFlag: boolean = false;
  this.moduleFilters.forEach((moduleFilter, index) => {
    if (moduleFilter && !moduleFilter.onFilterModuleMessage(moduleMessage)) {
      // 消息被拦截，记录错误并停止处理
      let resultVO: TKResultVO = new TKResultVO();
      resultVO.errorNo = -1002;
      resultVO.errorInfo = `消息号${moduleMessage.funcNo}：被[${moduleFilter.constructor.name}]拦截！`;
      if (moduleMessage.callBackFunc) {
        moduleMessage.callBackFunc(resultVO);
      }
      isFilterFlag = true;
      return;
    }
  });
  if (isFilterFlag) {
    return; // 消息被拦截，停止处理
  }
}
```

**责任链模式实现**：每个拦截器都有机会处理消息，任何一个拦截器返回false都会中断消息传递。

### 架构思考

#### 为什么选择发布订阅模式？

1. **解耦合**：发送方和接收方不需要直接依赖
2. **可扩展**：新增模块无需修改现有代码
3. **灵活性**：支持一对一、一对多的通信模式
4. **可测试**：模块可以独立测试，便于单元测试

#### 消息路由的设计考虑

1. **性能优化**：使用Map进行O(1)的模块查找
2. **内存安全**：WeakRef避免内存泄漏
3. **错误隔离**：单个模块异常不影响其他模块
4. **扩展性**：支持自定义消息处理逻辑

### 扩展点

#### 如何添加自定义消息类型？

1. **扩展TKModuleMessageAction枚举**
2. **在sendModuleMessage中添加新的case处理**
3. **实现对应的处理方法**
4. **更新相关模块的消息处理逻辑**

#### 如何实现消息持久化？

1. **在拦截器中记录消息**
2. **扩展TKModuleMessage添加持久化标识**
3. **集成消息队列系统**
4. **实现消息重试机制**

## 📚 相关资源

### 官方文档

- TypeScript官方文档 - 接口和泛型
- 设计模式文档 - 发布订阅模式

### 参考资料

- 《JavaScript设计模式与开发实践》- 发布订阅模式
- 《企业应用架构模式》- 消息路由模式

### 相关文件

- `核心架构-TKAppEngine-解释文档.md` - 应用引擎，模块引擎的调用者
- `TKModuleMessage.ets` - 消息对象定义
- `TKModuleDelegate.ets` - 模块接口规范
