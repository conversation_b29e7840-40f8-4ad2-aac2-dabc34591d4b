{
  // Markdown Preview Enhanced 配置 - 支持图表导出和放大
  "markdown-preview-enhanced.enableMermaid": true,
  "markdown-preview-enhanced.mermaidTheme": "default",
  "markdown-preview-enhanced.previewTheme": "github-light.css",
  "markdown-preview-enhanced.codeBlockTheme": "github.css",
  "markdown-preview-enhanced.scrollSync": true,
  "markdown-preview-enhanced.liveUpdate": true,
  "markdown-preview-enhanced.enableExtendedTableSyntax": true,
  "markdown-preview-enhanced.enableTypographer": true,
  "markdown-preview-enhanced.enableLinkify": true,
  "markdown-preview-enhanced.enableEmojiSyntax": true,
  "markdown-preview-enhanced.enableCheckboxSyntax": true,
  "markdown-preview-enhanced.enableTaskList": true,
  "markdown-preview-enhanced.singlePreview": true,
  "markdown-preview-enhanced.automaticallyShowPreviewOfMarkdownBeingEdited": false,

  // 启用图表导出功能 - 关键配置
  "markdown-preview-enhanced.enableScriptExecution": true,
  "markdown-preview-enhanced.mermaidRenderingOnSave": false,
  "markdown-preview-enhanced.mermaidRenderingFormat": "svg",
  "markdown-preview-enhanced.mermaidRenderingWidth": 1200,
  "markdown-preview-enhanced.mermaidRenderingHeight": 800,
  "markdown-preview-enhanced.mermaidRenderingBackgroundColor": "white",

  // Mermaid 图表配置 - 优化显示
  "markdown-preview-enhanced.mermaidConfig": {
    "startOnLoad": true,
    "theme": "default",
    "flowchart": {
      "useMaxWidth": false,
      "htmlLabels": true,
      "curve": "basis"
    },
    "sequence": {
      "useMaxWidth": false,
      "wrap": true,
      "width": 150,
      "height": 40
    },
    "gantt": {
      "useMaxWidth": false
    }
  },

  // 通用 Markdown 设置
  "markdown.preview.breaks": false,
  "markdown.preview.linkify": true,
  "markdown.preview.typographer": true,

  // 文件关联
  "files.associations": {
    "*.md": "markdown"
  },

  // 编辑器设置
  "editor.wordWrap": "on",
  "editor.quickSuggestions": {
    "comments": false,
    "strings": false,
    "other": false
  },

  // 工作区推荐扩展
  "extensions.recommendations": [
    "shd101wyy.markdown-preview-enhanced",
    "bierner.markdown-mermaid",
    "yzhang.markdown-all-in-one"
  ]
}
  "markdown-preview-enhanced.zenModeHideSideBar": true,
  "markdown-preview-enhanced.zenModeHideEditorTabs": true,
  "markdown-preview-enhanced.zenModeHideLineNumbers": true,
  "markdown-preview-enhanced.zenModeHideFoldingIndicators": true,
  "markdown-preview-enhanced.zenModeHideIndentGuides": true,
  "markdown-preview-enhanced.zenModeHideScrollbar": true,
  "markdown-preview-enhanced.zenModeHideMinimap": true,
  "markdown-preview-enhanced.zenModeHideWordWrap": true,
  "markdown-preview-enhanced.zenModeHideGutter": true,
  "markdown-preview-enhanced.zenModeHideOverviewRuler": true,
  "markdown-preview-enhanced.zenModeHideRuler": true,
  "markdown-preview-enhanced.zenModeHideCodeLens": true,
  "markdown-preview-enhanced.zenModeHideParameterHints": true,
  "markdown-preview-enhanced.zenModeHideHover": true,
  "markdown-preview-enhanced.zenModeHideContextMenu": true,
  "markdown-preview-enhanced.zenModeHideQuickSuggestions": true,
  "markdown-preview-enhanced.zenModeHideWordBasedSuggestions": true,
  "markdown-preview-enhanced.zenModeHideParameterHintsMenu": true,
  "markdown-preview-enhanced.zenModeHideSuggestOnTriggerCharacters": true,
  "markdown-preview-enhanced.zenModeHideAcceptSuggestionOnEnter": true,
  "markdown-preview-enhanced.zenModeHideAcceptSuggestionOnCommitCharacter": true,
  "markdown-preview-enhanced.zenModeHideTabCompletion": true,
  "markdown-preview-enhanced.zenModeHideWordWrapColumn": true,
  "markdown-preview-enhanced.zenModeHideRulers": true,
  "markdown-preview-enhanced.zenModeHideColorDecorators": true,
  "markdown-preview-enhanced.zenModeHideCodeActionsOnSave": true,
  "markdown-preview-enhanced.zenModeHideFormatOnSave": true,
  "markdown-preview-enhanced.zenModeHideFormatOnType": true,
  "markdown-preview-enhanced.zenModeHideFormatOnPaste": true,
  "markdown-preview-enhanced.zenModeHideTrimAutoWhitespace": true,
  "markdown-preview-enhanced.zenModeHideInsertSpaces": true,
  "markdown-preview-enhanced.zenModeHideDetectIndentation": true,
  "markdown-preview-enhanced.zenModeHideTrimTrailingWhitespace": true,
  "markdown-preview-enhanced.zenModeHideRenderWhitespace": true,
  "markdown-preview-enhanced.zenModeHideRenderControlCharacters": true,
  "markdown-preview-enhanced.zenModeHideRenderIndentGuides": true,
  "markdown-preview-enhanced.zenModeHideHighlightActiveIndentGuide": true,
  "markdown-preview-enhanced.zenModeHideShowFoldingControls": true,
  "markdown-preview-enhanced.zenModeHideFoldingStrategy": true,
  "markdown-preview-enhanced.zenModeHideShowUnused": true,
  "markdown-preview-enhanced.zenModeHideOccurrencesHighlight": true,
  "markdown-preview-enhanced.zenModeHideCodeLensFont": true,
  "markdown-preview-enhanced.zenModeHideAccessibilitySupport": true,
  "markdown-preview-enhanced.zenModeHideMultiCursorModifier": true,
  "markdown-preview-enhanced.zenModeHideMultiCursorMergeOverlapping": true,
  "markdown-preview-enhanced.zenModeHideMultiCursorPaste": true,
  "markdown-preview-enhanced.zenModeHideColorDecoratorsLimit": true,
  "markdown-preview-enhanced.zenModeHideMaxTokenizationLineLength": true,
  "markdown-preview-enhanced.zenModeHideLargeFileOptimizations": true,
  "markdown-preview-enhanced.zenModeHideStablePeek": true,
  "markdown-preview-enhanced.zenModeHideMaxComputationTime": true,
  "markdown-preview-enhanced.zenModeHideSemanticHighlighting": true,
  "markdown-preview-enhanced.zenModeHideColorDecoratorsActivatedOn": true,
  "markdown-preview-enhanced.zenModeHideSelectionHighlight": true,
  "markdown-preview-enhanced.zenModeHideSelectionClipboard": true,
  "markdown-preview-enhanced.zenModeHideFind": true,
  "markdown-preview-enhanced.zenModeHideWordSeparators": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingBrackets": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingQuotes": true,
  "markdown-preview-enhanced.zenModeHideAutoSurround": true,
  "markdown-preview-enhanced.zenModeHideAutoIndent": true,
  "markdown-preview-enhanced.zenModeHideEmptySelectionClipboard": true,
  "markdown-preview-enhanced.zenModeHideCopyWithSyntaxHighlighting": true,
  "markdown-preview-enhanced.zenModeHideMultiCursorLimit": true,
  "markdown-preview-enhanced.zenModeHideColumnSelection": true,
  "markdown-preview-enhanced.zenModeHideGotoLocation": true,
  "markdown-preview-enhanced.zenModeHideDefinitionLinkOpensInPeek": true,
  "markdown-preview-enhanced.zenModeHideShowDeprecated": true,
  "markdown-preview-enhanced.zenModeHideInlayHints": true,
  "markdown-preview-enhanced.zenModeHideUnicodeHighlight": true,
  "markdown-preview-enhanced.zenModeHideUnusedVariables": true,
  "markdown-preview-enhanced.zenModeHideUnusedVariablesUseColors": true,
  "markdown-preview-enhanced.zenModeHideLinks": true,
  "markdown-preview-enhanced.zenModeHideDropIntoEditor": true,
  "markdown-preview-enhanced.zenModeHideStickyScroll": true,
  "markdown-preview-enhanced.zenModeHideGuides": true,
  "markdown-preview-enhanced.zenModeHideBracketPairs": true,
  "markdown-preview-enhanced.zenModeHideMatchBrackets": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingDelete": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingOvertype": true,
  "markdown-preview-enhanced.zenModeHideQuickSuggestions": true,
  "markdown-preview-enhanced.zenModeHideParameterHints": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingComments": true,
  "markdown-preview-enhanced.zenModeHideComments": true,
  "markdown-preview-enhanced.zenModeHideFoldingImportsByDefault": true,
  "markdown-preview-enhanced.zenModeHideUnfoldOnClickAfterEndOfLine": true,
  "markdown-preview-enhanced.zenModeHideGlyphMargin": true,
  "markdown-preview-enhanced.zenModeHideFolding": true,
  "markdown-preview-enhanced.zenModeHideShowFoldingHighlight": true,
  "markdown-preview-enhanced.zenModeHideUnfoldOnClickAfterEndOfLine": true,
  "markdown-preview-enhanced.zenModeHideMatchBrackets": true,
  "markdown-preview-enhanced.zenModeHideRenderLineHighlight": true,
  "markdown-preview-enhanced.zenModeHideRenderLineHighlightOnlyWhenFocus": true,
  "markdown-preview-enhanced.zenModeHideHideCursorInOverviewRuler": true,
  "markdown-preview-enhanced.zenModeHideScrollBeyondLastLine": true,
  "markdown-preview-enhanced.zenModeHideScrollBeyondLastColumn": true,
  "markdown-preview-enhanced.zenModeHideSmoothScrolling": true,
  "markdown-preview-enhanced.zenModeHideCursorBlinking": true,
  "markdown-preview-enhanced.zenModeHideCursorSmoothCaretAnimation": true,
  "markdown-preview-enhanced.zenModeHideCursorStyle": true,
  "markdown-preview-enhanced.zenModeHideCursorWidth": true,
  "markdown-preview-enhanced.zenModeHideFontLigatures": true,
  "markdown-preview-enhanced.zenModeHideDisableLayerHinting": true,
  "markdown-preview-enhanced.zenModeHideDisableMonospaceOptimizations": true,
  "markdown-preview-enhanced.zenModeHideHideCursorInOverviewRuler": true,
  "markdown-preview-enhanced.zenModeHideRenderWhitespace": true,
  "markdown-preview-enhanced.zenModeHideRenderControlCharacters": true,
  "markdown-preview-enhanced.zenModeHideRenderIndentGuides": true,
  "markdown-preview-enhanced.zenModeHideHighlightActiveIndentGuide": true,
  "markdown-preview-enhanced.zenModeHideRenderLineHighlight": true,
  "markdown-preview-enhanced.zenModeHideCodeLens": true,
  "markdown-preview-enhanced.zenModeHideRenderCodeLens": true,
  "markdown-preview-enhanced.zenModeHideRenderValidationDecorations": true,
  "markdown-preview-enhanced.zenModeHideScrollbar": true,
  "markdown-preview-enhanced.zenModeHideMinimap": true,
  "markdown-preview-enhanced.zenModeHideFixedOverflowWidgets": true,
  "markdown-preview-enhanced.zenModeHideOverviewRulerLanes": true,
  "markdown-preview-enhanced.zenModeHideOverviewRulerBorder": true,
  "markdown-preview-enhanced.zenModeHideCursorSurroundingLines": true,
  "markdown-preview-enhanced.zenModeHideCursorSurroundingLinesStyle": true,
  "markdown-preview-enhanced.zenModeHideRevealHorizontalRightPadding": true,
  "markdown-preview-enhanced.zenModeHideRoundedSelection": true,
  "markdown-preview-enhanced.zenModeHideExtraEditorClassName": true,
  "markdown-preview-enhanced.zenModeHideReadOnly": true,
  "markdown-preview-enhanced.zenModeHideLinkedEditing": true,
  "markdown-preview-enhanced.zenModeHideRenameOnType": true,
  "markdown-preview-enhanced.zenModeHideColorDecorators": true,
  "markdown-preview-enhanced.zenModeHideLightbulb": true,
  "markdown-preview-enhanced.zenModeHideCodeActionsOnSave": true,
  "markdown-preview-enhanced.zenModeHideCodeActionsOnSaveTimeout": true,
  "markdown-preview-enhanced.zenModeHideSelectionHighlight": true,
  "markdown-preview-enhanced.zenModeHideOccurrencesHighlight": true,
  "markdown-preview-enhanced.zenModeHideCodeLensFont": true,
  "markdown-preview-enhanced.zenModeHideAccessibilitySupport": true,
  "markdown-preview-enhanced.zenModeHideAccessibilityPageSize": true,
  "markdown-preview-enhanced.zenModeHideFind": true,
  "markdown-preview-enhanced.zenModeHideGotoLocation": true,
  "markdown-preview-enhanced.zenModeHideHover": true,
  "markdown-preview-enhanced.zenModeHideLinks": true,
  "markdown-preview-enhanced.zenModeHideColorDecoratorsLimit": true,
  "markdown-preview-enhanced.zenModeHideRenderValidationDecorations": true,
  "markdown-preview-enhanced.zenModeHideDefinitionLinkOpensInPeek": true,
  "markdown-preview-enhanced.zenModeHideShowDeprecated": true,
  "markdown-preview-enhanced.zenModeHideInlayHints": true,
  "markdown-preview-enhanced.zenModeHideUnicodeHighlight": true,
  "markdown-preview-enhanced.zenModeHideUnusedVariables": true,
  "markdown-preview-enhanced.zenModeHideUnusedVariablesUseColors": true,
  "markdown-preview-enhanced.zenModeHideDropIntoEditor": true,
  "markdown-preview-enhanced.zenModeHideStickyScroll": true,
  "markdown-preview-enhanced.zenModeHideGuides": true,
  "markdown-preview-enhanced.zenModeHideBracketPairs": true,
  "markdown-preview-enhanced.zenModeHideMatchBrackets": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingDelete": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingOvertype": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingComments": true,
  "markdown-preview-enhanced.zenModeHideComments": true,
  "markdown-preview-enhanced.zenModeHideFoldingImportsByDefault": true,
  "markdown-preview-enhanced.zenModeHideUnfoldOnClickAfterEndOfLine": true,
  "markdown-preview-enhanced.zenModeHideGlyphMargin": true,
  "markdown-preview-enhanced.zenModeHideFolding": true,
  "markdown-preview-enhanced.zenModeHideShowFoldingHighlight": true,
  "markdown-preview-enhanced.zenModeHideUnfoldOnClickAfterEndOfLine": true,
  "markdown-preview-enhanced.zenModeHideMatchBrackets": true,
  "markdown-preview-enhanced.zenModeHideRenderLineHighlight": true,
  "markdown-preview-enhanced.zenModeHideRenderLineHighlightOnlyWhenFocus": true,
  "markdown-preview-enhanced.zenModeHideHideCursorInOverviewRuler": true,
  "markdown-preview-enhanced.zenModeHideScrollBeyondLastLine": true,
  "markdown-preview-enhanced.zenModeHideScrollBeyondLastColumn": true,
  "markdown-preview-enhanced.zenModeHideSmoothScrolling": true,
  "markdown-preview-enhanced.zenModeHideCursorBlinking": true,
  "markdown-preview-enhanced.zenModeHideCursorSmoothCaretAnimation": true,
  "markdown-preview-enhanced.zenModeHideCursorStyle": true,
  "markdown-preview-enhanced.zenModeHideCursorWidth": true,
  "markdown-preview-enhanced.zenModeHideFontLigatures": true,
  "markdown-preview-enhanced.zenModeHideDisableLayerHinting": true,
  "markdown-preview-enhanced.zenModeHideDisableMonospaceOptimizations": true,
  "markdown-preview-enhanced.zenModeHideHideCursorInOverviewRuler": true,
  "markdown-preview-enhanced.zenModeHideRenderWhitespace": true,
  "markdown-preview-enhanced.zenModeHideRenderControlCharacters": true,
  "markdown-preview-enhanced.zenModeHideRenderIndentGuides": true,
  "markdown-preview-enhanced.zenModeHideHighlightActiveIndentGuide": true,
  "markdown-preview-enhanced.zenModeHideRenderLineHighlight": true,
  "markdown-preview-enhanced.zenModeHideCodeLens": true,
  "markdown-preview-enhanced.zenModeHideRenderCodeLens": true,
  "markdown-preview-enhanced.zenModeHideRenderValidationDecorations": true,
  "markdown-preview-enhanced.zenModeHideScrollbar": true,
  "markdown-preview-enhanced.zenModeHideMinimap": true,
  "markdown-preview-enhanced.zenModeHideFixedOverflowWidgets": true,
  "markdown-preview-enhanced.zenModeHideOverviewRulerLanes": true,
  "markdown-preview-enhanced.zenModeHideOverviewRulerBorder": true,
  "markdown-preview-enhanced.zenModeHideCursorSurroundingLines": true,
  "markdown-preview-enhanced.zenModeHideCursorSurroundingLinesStyle": true,
  "markdown-preview-enhanced.zenModeHideRevealHorizontalRightPadding": true,
  "markdown-preview-enhanced.zenModeHideRoundedSelection": true,
  "markdown-preview-enhanced.zenModeHideExtraEditorClassName": true,
  "markdown-preview-enhanced.zenModeHideReadOnly": true,
  "markdown-preview-enhanced.zenModeHideLinkedEditing": true,
  "markdown-preview-enhanced.zenModeHideRenameOnType": true,
  "markdown-preview-enhanced.zenModeHideColorDecorators": true,
  "markdown-preview-enhanced.zenModeHideLightbulb": true,
  "markdown-preview-enhanced.zenModeHideCodeActionsOnSave": true,
  "markdown-preview-enhanced.zenModeHideCodeActionsOnSaveTimeout": true,
  "markdown-preview-enhanced.zenModeHideSelectionHighlight": true,
  "markdown-preview-enhanced.zenModeHideOccurrencesHighlight": true,
  "markdown-preview-enhanced.zenModeHideCodeLensFont": true,
  "markdown-preview-enhanced.zenModeHideAccessibilitySupport": true,
  "markdown-preview-enhanced.zenModeHideAccessibilityPageSize": true,
  "markdown-preview-enhanced.zenModeHideFind": true,
  "markdown-preview-enhanced.zenModeHideGotoLocation": true,
  "markdown-preview-enhanced.zenModeHideHover": true,
  "markdown-preview-enhanced.zenModeHideLinks": true,
  "markdown-preview-enhanced.zenModeHideColorDecoratorsLimit": true,
  "markdown-preview-enhanced.zenModeHideRenderValidationDecorations": true,
  "markdown-preview-enhanced.zenModeHideDefinitionLinkOpensInPeek": true,
  "markdown-preview-enhanced.zenModeHideShowDeprecated": true,
  "markdown-preview-enhanced.zenModeHideInlayHints": true,
  "markdown-preview-enhanced.zenModeHideUnicodeHighlight": true,
  "markdown-preview-enhanced.zenModeHideUnusedVariables": true,
  "markdown-preview-enhanced.zenModeHideUnusedVariablesUseColors": true,
  "markdown-preview-enhanced.zenModeHideDropIntoEditor": true,
  "markdown-preview-enhanced.zenModeHideStickyScroll": true,
  "markdown-preview-enhanced.zenModeHideGuides": true,
  "markdown-preview-enhanced.zenModeHideBracketPairs": true,
  "markdown-preview-enhanced.zenModeHideMatchBrackets": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingDelete": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingOvertype": true,
  "markdown-preview-enhanced.zenModeHideAutoClosingComments": true,
  "markdown-preview-enhanced.zenModeHideComments": true,
  "markdown-preview-enhanced.zenModeHideFoldingImportsByDefault": true,
  "markdown-preview-enhanced.zenModeHideUnfoldOnClickAfterEndOfLine": true,
  "markdown-preview-enhanced.zenModeHideGlyphMargin": true,
  "markdown-preview-enhanced.zenModeHideFolding": true,
  "markdown-preview-enhanced.zenModeHideShowFoldingHighlight": true,
  "markdown-preview-enhanced.zenModeHideUnfoldOnClickAfterEndOfLine": true,
  "markdown-preview-enhanced.zenModeHideMatchBrackets": true,
  "markdown-preview-enhanced.zenModeHideRenderLineHighlight": true,
  "markdown-preview-enhanced.zenModeHideRenderLineHighlightOnlyWhenFocus": true,
  "markdown-preview-enhanced.zenModeHideHideCursorInOverviewRuler": true,
  "markdown-preview-enhanced.zenModeHideScrollBeyondLastLine": true,
  "markdown-preview-enhanced.zenModeHideScrollBeyondLastColumn": true,
  "markdown-preview-enhanced.zenModeHideSmoothScrolling": true,
  "markdown-preview-enhanced.zenModeHideCursorBlinking": true,
  "markdown-preview-enhanced.zenModeHideCursorSmoothCaretAnimation": true,
  "markdown-preview-enhanced.zenModeHideCursorStyle": true,
  "markdown-preview-enhanced.zenModeHideCursorWidth": true,
  "markdown-preview-enhanced.zenModeHideFontLigatures": true,
  "markdown-preview-enhanced.zenModeHideDisableLayerHinting": true,
  "markdown-preview-enhanced.zenModeHideDisableMonospaceOptimizations": true
}
