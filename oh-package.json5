{"name": "@thinkive/tk-harmony-base", "version": "1.3.0", "description": "思迪信息鸿蒙基础框架：1、TKWeb实现H5混合开发框架，提供系统常用的插件库（60+）  2、TKWeb支持H5本地化加载，支持H5在线热更新及APP版本更新   3、标准网络通信层，支持http，socket通信，支持证书双向认证，加密签名  4、支持多站点多机房机制，支持APP智能测速和站点更新  5、支持主题换肤，满足适老化，暗黑模式等多主题场景  6、统一数据存储中心，支持内存存储，文件存储，数据库存储等多种模式，支持数据存储加密，过期等功能  7、统一路由组件，兼容router跳转和NavPathStack跳转，上层无感，不同环境智能切换  8、模块间消息通信引擎，通过统一通信协议，实现子模块可插拔开发，降低模块间的耦合性", "main": "Index.ets", "author": "深圳市思迪信息技术股份有限公司", "license": "thinkvie", "dependencies": {"pako": "^2.1.0"}, "devDependencies": {}, "metadata": {"sourceRoots": ["./src/main"], "debug": true, "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": false}